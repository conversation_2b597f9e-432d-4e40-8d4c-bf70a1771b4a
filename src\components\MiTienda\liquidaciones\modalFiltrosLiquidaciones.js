import React, { useEffect, useState } from 'react';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import { getListadoClientesByName } from '../../../redux/actions/mitienda.actions';
import { But<PERSON>, TextField } from '@mui/material';

const customStyles = {
    content: {
        top: '50%',
        left: '50%',
        right: 'auto',
        bottom: 'auto',
        marginRight: '-50%',
        transform: 'translate(-50%, -50%)',
        width: '30%',
        height: 'auto',
        padding: '20px 30px',
        borderRadius: '12px',
    },
};

export const ModalFiltrosLiquidaciones = ({
    show,
    handleClose,
    handleChangeClienteid,
    clienteid,
    handleChangeNrocomprobante,
    nrocomprobante,
    setClienteId,
    handleChangeFechaDesde,
    fechaDesde,
    handleChangeFechaHasta,
    fechaHasta,
    reset,
    search
}) => {
    const dispatch = useDispatch();
    const clientes = useSelector((state) => state.mitienda.clientesByName);
    const [clienteInput, setClienteInput] = useState('');

    useEffect(() => {
        if (clienteInput.length > 2) {
            dispatch(getListadoClientesByName(clienteInput, ''));
        }
    }, [clienteInput, dispatch]);

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: 15 }}>
                <h4 style={{ margin: 0 }}>Filtros de Liquidaciones</h4>
                <button
                    onClick={handleClose}
                    style={{ all: "unset", cursor: "pointer", fontSize: 18, lineHeight: 1 }}
                    aria-label="Cerrar"
                >
                    ✕
                </button>
            </div>

            <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
                {/* <div>
                    <label style={{ fontSize: 14, marginBottom: 4, display: "block" }}>Número de Liquidación</label>
                    <TextField
                        variant="outlined"
                        fullWidth
                        name="nrocomprobante"
                        onChange={handleChangeNrocomprobante}
                        value={nrocomprobante}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize: 16,
                            },
                        }}
                    />
                </div> */}

                <div style={{ display: "flex", justifyContent: "space-between", gap: 10 }}>
                    <div style={{ flex: 1 }}>
                        <label style={{ fontSize: 14, marginBottom: 4, display: "block" }}>Fecha Liquidación desde</label>
                        <TextField
                            type="date"
                            variant="outlined"
                            fullWidth
                            name="fechaDesde"
                            onChange={handleChangeFechaDesde}
                            value={fechaDesde}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize: 16,
                                },
                            }}
                        />
                    </div>
                    <div style={{ flex: 1 }}>
                        <label style={{ fontSize: 14, marginBottom: 4, display: "block" }}>Fecha Liquidación hasta</label>
                        <TextField
                            type="date"
                            variant="outlined"
                            fullWidth
                            name="fechaHasta"
                            onChange={handleChangeFechaHasta}
                            value={fechaHasta}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize: 16,
                                },
                            }}
                        />
                    </div>
                </div>

                <div style={{ display: "flex", justifyContent: "space-between", marginTop: 10 }}>
                    <Button
                        variant="outlined"
                        sx={{ height: 40, width: '48%' }}
                        onClick={reset}
                    >
                        Limpiar
                    </Button>
                    <Button
                        variant="contained"
                        sx={{ height: 40, width: '48%' }}
                        onClick={search}
                    >
                        Buscar
                    </Button>
                </div>
            </div>
        </Modal>
    );
};