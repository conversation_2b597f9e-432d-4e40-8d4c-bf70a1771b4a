import React, { useState } from "react"
import Modal from 'react-modal';

import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Divider from '@mui/material/Divider';
import ArticleIcon from '@mui/icons-material/Article';
import { Button, ListItemIcon, ListSubheader, Paper, TextField } from "@mui/material";

Modal.setAppElement("#root")

export const ModalMiPlan = ({show, handleClose}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Datos de facturacion</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{marginTop:15, marginBottom:15}}>
                  <TextField 
                      id="nombre-tienda" 
                      label="Nombre completo / Razon social"  
                      variant="outlined" 
                      fullWidth margin="normal" 
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                  />
                  <TextField
                      id="nombre-tienda" 
                      label="CUIT/DNI"  
                      variant="outlined" 
                      fullWidth margin="normal" 
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                  />
                </div>
                <div style={{marginBottom:15}}>
                    <h5 style={{color:"grey"}}>Informacion de facturacion actual</h5>
                    <h6 style={{color:"grey"}}>Razon social: DEVELONE S.A.S</h6>
                    <h6 style={{color:"grey"}}>Condicion: Responsable inscripto</h6>
                </div>
                <Divider/>
                <div align="right">
                <Button size="large" variant="contained" sx={{mt: 3}}>Guardar</Button>
                </div>
        </Modal>
    )
}