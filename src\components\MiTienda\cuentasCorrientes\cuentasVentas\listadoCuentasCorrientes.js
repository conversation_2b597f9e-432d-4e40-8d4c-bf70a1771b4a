import React, { useEffect, useState } from 'react';
import { 
    Button, 
    Checkbox, 
    FormControlLabel, 
    FormControl, 
    InputLabel, 
    MenuItem, 
    Select, 
    TextField, 
    InputAdornment,
    ListSubheader
} from '@mui/material';

import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { 
    getCuentasCorrientes, 
    getListadoClientesByName 
} from '../../../../redux/actions/mitienda.actions'
import { ModalImprimir } from './modalImprimir';
import { Article, AttachMoney, Print, Search } from '@mui/icons-material';
import { ModalRegistrarPagoCuentaCorriente } from './modalRegistrarPagoCuentaCorriente';
import ClipLoader from "react-spinners/ClipLoader";

export const GestionCuentasCorrientes = () => {

    const info = useSelector((state) => state.mitienda.cuentasCorrientes)
    const clientes = useSelector((state) => state.mitienda.clientesByName)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaCuentasCorrientes)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [solo_deuda, setSoloDeuda] = useState(false)
    const [clienteid, setClienteid] = useState('')
    const [cuit, setCuit] = useState('')

    const dispatch = useDispatch()

    const [factura, setFactura] = useState('')

    const [showPdf, setShowPdf] = useState('');
    const handleClosePdf = () => setShowPdf(false);
    const handleShowPdf = (p) => {
        setFactura(p)
        setShowPdf(true);
    }

    const [showPagos, setShowPagos] = useState('');
    const handleClosePagos = () => setShowPagos(false);
    const handleShowPagos = (p) => {
        setFactura(p)
        setShowPagos(true);
    }
    const [search, setSearch] = useState(false)

    const onSearch = () => {
        setSearch(true)
        dispatch(getCuentasCorrientes(page,clienteid,solo_deuda,cuit))
        dispatch(getListadoClientesByName('',''))
        setClienteid('')
    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    const [nombre, setNombre] = useState('')
    const [selectedOption, setSelectedOption] = useState('');
  
    useEffect(() =>{
      if(nombre.length > 4){
          dispatch(getListadoClientesByName(nombre,''))
      }
    }, [nombre])

    useEffect(() =>{
        setLoading(true)
        dispatch(getCuentasCorrientes(page,clienteid,solo_deuda,cuit))
    },[page])

    useEffect(() =>{
        if(search === true && nombre === ''){
            dispatch(getCuentasCorrientes(page,clienteid,solo_deuda,cuit))
            dispatch(getListadoClientesByName('',''))
            setSelectedOption('')
            setSearch(false)
        }
    }, [search,nombre])

    return (
        <div className="notificaciones-container">
            <ModalRegistrarPagoCuentaCorriente
                show={showPagos}
                handleClose={handleClosePagos}
                info={factura}
            />
            <ModalImprimir
                show={showPdf}
                handleClose={handleClosePdf}
                info={factura}
            />
            <div>
                <header className="titulo-notificaciones" style={{display:"flex", flexDirection:"column"}}>
                    <h3>Gesti&oacute;n de cuentas corrientes</h3>
                    <div style={{display:"flex", alignItems:"center"}}>
                    <FormControl fullWidth sx={{width:600, marginTop:1, marginRight:2}} size="small">
                    <InputLabel id="search-select-label">Cliente</InputLabel>
                        <Select
                            style={{height:40}}
                            MenuProps={{ autoFocus: false,  disablePortal: true }}
                            labelId="search-select-label"
                            id="search-select"
                            value={selectedOption.clienteid || ''}
                            label="Cliente"
                            size="small"
                            renderValue={() => selectedOption.nombre+' '+selectedOption.apellido}
                        >
                    <ListSubheader>
                    <TextField
                        size="small"
                        autoFocus
                        placeholder="Buscar cliente..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombre(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                        value={nombre}
                    />
                    </ListSubheader>
                    {clientes.map((option, i) => (
                        <MenuItem key={i} value={option.clienteid} onClick={() => {
                            setSelectedOption(option)
                            setNombre(option.nombre+' '+option.apellido)
                            setClienteid(option.clienteid)
                        }}>
                        {option.nombre+' '+option.apellido}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>
                    <TextField 
                        label="CUIT"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{width:300, marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        value={cuit}
                        onChange={(e) => setCuit(e.target.value)}
                    />
                    <FormControlLabel
                        control={
                        <Checkbox/>
                        }
                        style={{color:"black", marginLeft:1, marginTop:15, marginRight:20}}
                        onChange={(e)=> setSoloDeuda(e.target.checked)}
                        checked={solo_deuda}
                        label="Solo deuda"
                    />
                    <Button onClick={onSearch} variant='contained' style={{height:40, marginLeft:10, marginTop:10}} >Buscar</Button>
                    </div>
                </header>
            
            </div>
            {
                permisos_acciones?.listar !== "0" && 
                <div style={{display:"flex", flexDirection:"column", width:"95%", marginTop:5, marginLeft:50}}>
                <TableContainer component={Paper} sx={{margin:5,width:"100%", alignSelf:"center"}}>
                    <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Cliente</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">CUIT</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Importe adeudado</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Importe a favor</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Saldo</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Imprimir</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Detalle</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Pagos</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {
                            loading ? 
                            <TableRow sx={{p:20}}>
                                <TableCell colSpan={13} align='center'>
                                    <ClipLoader 
                                        loading={loading}
                                        size={50}
                                    />
                                </TableCell>
                            </TableRow> :
                            info.data.length > 0 ? info.data.map((row) => (
                                <TableRow
                                    key={row.productoid}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                >
                                    <TableCell style={{fontSize:"80%", padding:0, height:40, width:200}} align="center">
                                        {row.nombre}
                                    </TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.cuit}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        ${row.totalEnContra}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                        ${row.totalAFavor}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                        ${row.saldo}</TableCell>
                                    <TableCell 
                                        style={{fontSize:"17px", width:"50px", height:"40px", padding: 0, cursor:"pointer"}} 
                                        align="center"
                                        onClick={(e)=>handleShowPdf(row)}>
                                        <Print/>
                                    </TableCell>
                                    <TableCell 
                                        style={{fontSize:"17px", width:"50px", height:"40px", padding: 0, cursor:"pointer"}} 
                                        align="center">
                                        <a href={`/Mitienda/CuentasCorrientes/Detalle/${row.clienteid}`}>
                                            <Article style={{color:"black"}}/>
                                        </a>
                                    </TableCell>
                                    <TableCell 
                                        style={{fontSize:"17px", width:"50px", height:"40px", padding: 0, cursor:"pointer"}} 
                                        align="center"
                                        onClick={()=> handleShowPagos(row)}
                                    >
                                        <AttachMoney/>
                                    </TableCell>
                                </TableRow>
                            )):
                            <TableRow>
                                <TableCell colSpan={8}>
                                    <h5>No hay informaci&oacute;n para mostrar</h5>
                                </TableCell>
                            </TableRow>}

                        </TableBody>
                    </Table>
                </TableContainer>
                <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
            </div>
            }
        </div>
    )
}
