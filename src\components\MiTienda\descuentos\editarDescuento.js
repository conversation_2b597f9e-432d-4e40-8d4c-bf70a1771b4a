import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { editarDescuento } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalEditarDescuento = ({show, handleClose, descuento, handleClickAlert}) =>{

  const [input, setInput] = useState()

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value })
}

const handleCheck = (e) =>{
    e.preventDefault()
    if(e.target.checked === true){
        setInput({...input, [e.target.name] : "1"})
    }else{
        setInput({...input, [e.target.name] : "0"})
    }
}

  const dispatch = useDispatch()

  const handleClick = () => {
    dispatch(editarDescuento(input))
    setTimeout(function(){
        handleClickAlert()
    }, 1000);
    handleClose()
}
  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    useEffect(() => {
        setInput(descuento)
    }, [descuento])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Editar descuento</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{marginTop:15, marginBottom:15}}>
                <TextField 
                    name="nombredescuento"
                    label="Nombre descuento"  
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    value={input.nombredescuento || ''}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <TextField 
                    name="descuento"
                    label="Porcentaje"  
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    value={input.descuento|| ''}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <div style={{marginTop:5, marginBottom:5}}>
                    <FormControlLabel
                        control={
                        <Checkbox/>
                        }
                        style={{color:"black"}}
                        onChange={(e)=> handleCheck(e)}
                        checked={input.activo == "1" ? true : false}
                        label="Activo"
                        name="activo"
                    />
                </div>
                <div style={{marginTop:5, marginBottom:5}}>
                    <FormControlLabel
                        control={
                        <Checkbox/>
                        }
                        style={{color:"black"}}
                        onChange={(e)=> handleCheck(e)}
                        checked={input.ecommerce == "1" ? true : false}
                        label="Ecommerce"
                        name="ecommerce"
                    />
                </div>
            </div>
            <Divider/>
            <div align="right">
                <Button 
                size="large" 
                variant="contained" 
                onClick={(e)=>(handleClick(e))}
                sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>
    </Modal>
    )
}