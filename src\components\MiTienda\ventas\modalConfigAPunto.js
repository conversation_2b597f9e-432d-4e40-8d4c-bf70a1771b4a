import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, MenuItem, Select } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { 
    agregarConceptoEnvio, 
    cotizarEnvioAPunto, 
    getClienteById,
    getPuntosDeRetiro
} from "../../../redux/actions/mitienda.actions";
import { useEffect } from "react";
import { ModalMapaPuntos } from "./modalMapa";

Modal.setAppElement("#root")

export const ModalConfigAPunto = ({
    show, 
    handleClose, 
    clienteid, 
    cliente, 
    infoTienda, 
    cotizacion, 
    productos, 
    facturaid, 
    getCalcularCarrito,
    puntoId,
    setPuntoId
}) =>{

    const puntosDeRetiroPickit = useSelector((state) =>  state.mitienda.puntosDeRetiroPickit)

    const [punto, setPunto] = useState('')
    const handleChange = (id) => {
        dispatch(cotizarEnvioAPunto(productos, infoTienda, cliente, id))
        setPuntoId(id)
    }
    
    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "700px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const handleCotizarEnvio = () =>{
        dispatch(cotizarEnvioAPunto(productos, infoTienda, cliente, puntosDeRetiroPickit[0].id))
    }

    const handleGuardarEnvio = () =>{
        dispatch(agregarConceptoEnvio(clienteid,facturaid,cotizacion.price,cotizacion.tax,cotizacion.totalPrice))
        setTimeout(function(){
            dispatch(getCalcularCarrito(clienteid))
        }, 2000);
        handleClose()
    }

    const [showMapa, setShowMapa] = useState('');
    const handleCloseMapa = () => setShowMapa(false);
    const handleShowMapa = () => {
        setShowMapa(true);
    }

    useEffect(() => {
        if(clienteid !== undefined && clienteid !== ''){
            dispatch(getClienteById(clienteid))
        }
    },[clienteid])

    useEffect(() => {
        if(cliente !== ''){
            dispatch(getPuntosDeRetiro(cliente.postalCode))
        }
    },[cliente])

    useEffect(() => {
        if(puntosDeRetiroPickit.length > 0){
            setPunto(puntosDeRetiroPickit[0])
        }
    },[puntosDeRetiroPickit])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <ModalMapaPuntos
                show={showMapa}
                handleClose={handleCloseMapa}
                points={puntosDeRetiroPickit}
                point={punto}
                setPoint={setPunto}
                setPuntoId={setPuntoId}
                handleChange={handleChange}
            />
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Configurar Env&iacute;o a punto</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{margin:15, display:"flex", justifyContent:"space-around", alignItems:"center" }}>   
                <Button onClick={handleCotizarEnvio} variant="outlined" style={{width:"40%", margin:10}}>
                    Cotizar env&iacute;o a punto
                </Button>
                <div style={{display:"flex", flexDirection:"column", marginTop:10}}>
                {
                    cotizacion === '' ?
                    <div>
                        <p style={{fontSize:18}}><strong>Precio:</strong> $0.00</p>
                        <p style={{fontSize:18}}><strong>Impuestos:</strong> 0.00%</p>
                        <p style={{fontSize:18}}><strong>Total:</strong> $0.00</p>
                    </div> :
                    <div>
                        <p style={{fontSize:18}}><strong>Precio:</strong> ${cotizacion.price+".00" || ''}</p>
                        <p style={{fontSize:18}}><strong>Impuestos:</strong> {cotizacion.tax || ''}%</p>
                        <p style={{fontSize:18}}><strong>Total:</strong> ${cotizacion.totalPrice+".00" || ''}</p>
                    </div>
                }
                </div>
            </div>
            <Divider/>
            <div style={{width:"90%", margin:15, display:"flex", alignItems:"center", justifyContent:"space-between"}}>
                <div style={{width:"80%", marginRight:15}}>
                    <h6><strong>Puntos de retiro</strong></h6>
                    <Select
                        size="small"
                        fullWidth
                        value={puntoId || ""}
                        name='puntoId'
                        style={{height:35}}
                        onChange={(e)=> handleChange(e.target.value)}
                        MenuProps={{ keepMounted: true, disablePortal: true }}
                        >
                        {               
                            puntosDeRetiroPickit && puntosDeRetiroPickit.map((t) =>
                                <MenuItem value={t.id} key={t.id}>{t.name}</MenuItem>
                            )
                        }
                    </Select>
                </div>
                <Button style={{marginTop:30}} onClick={handleShowMapa} variant="outlined">Mapa</Button>
            </div>
            <Divider/>
            <div align="right">
                <Button size="large"
                variant="contained"
                onClick={handleGuardarEnvio}
                sx={{mt: 3, mr:1}}>Guardar</Button>
            </div>
        </Modal>
    )
}