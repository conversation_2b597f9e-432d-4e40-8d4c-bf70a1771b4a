import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getComprobantesByLiquidacion,
  getLiquidaciones,
  getListadoClientesByName,
} from "../../../redux/actions/mitienda.actions";
import { Link } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  TextField,
  Tooltip,
  IconButton,
  InputAdornment,
  Popper,
  ClickAwayListener,
  Grow,
  Paper as MuiPaper,
  MenuList,
  MenuItem,
  ListItemText,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  CircularProgress,
  Typography,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  DialogActions,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import { RemoveRedEye, Search, Download, Clear } from "@mui/icons-material";
import MuiAlert from "@mui/material/Alert";
import moment from "moment";
import * as XLSX from "xlsx";
import { ModalVerPdf } from "../ventas/verPdf";
import ClipLoader from "react-spinners/ClipLoader";
import { ModalFiltrosLiquidaciones } from "./modalFiltrosLiquidaciones";
import { formatDate } from "../../shared/utility";
import AttachFileIcon from "@mui/icons-material/AttachFile";

export const Liquidaciones = () => {
  let fechaActual = moment();
  let ultimosDias = moment().subtract(60, "days");

  const liquidaciones = useSelector(
    (state) => state.mitienda.liquidaciones?.data || {}
  );
  const paginaUltima = useSelector(
    (state) => state.mitienda.liquidaciones?.paginaUltima || 1
  );
  const clientesByName = useSelector(
    (state) => state.mitienda.clientesByName || []
  );

  let pathname = window.location.pathname;
  let permisos_acciones =
    Object.values(
      JSON.parse(localStorage.getItem("permisos_acciones") || "{}")
    ).filter((p) =>
      p?.archivo?.toLowerCase().includes(pathname.toLocaleLowerCase())
    )[0] || {};

  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const [clienteid, setClienteid] = useState("");
  const [facturaid, setFacturaid] = useState("");
  const [nrocomprobante, setNrocomprobante] = useState("");

  const [openComprobantesModal, setOpenComprobantesModal] = useState(false);
  const [currentComprobantes, setCurrentComprobantes] = useState([]);
  const comprobantesState = useSelector((state) => state.mitienda.comprobantes);

  const handleViewComprobantes = async (liquidacionid) => {
  try {
    setOpenComprobantesModal(true);
    await dispatch(getComprobantesByLiquidacion(liquidacionid));
  } catch (error) {
    if (error.message !== "NO_COMPROBANTES") {
      console.error("Error fetching comprobantes:", error);
    }
  }
};

const handleCloseComprobantesModal = () => {
  setOpenComprobantesModal(false);
};

  const [fechaDesde, setFechaDesde] = useState(
    ultimosDias.format("YYYY-MM-DD")
  );
  const handleChangeFechaDesde = (e) => {
    setFechaDesde(e.target.value);
  };

  const [fechaHasta, setFechaHasta] = useState(
    fechaActual.format("YYYY-MM-DD")
  );
  const handleChangeFechaHasta = (e) => {
    setFechaHasta(e.target.value);
  };

  // Client search states
  const [clienteNombre, setClienteNombre] = useState("");
  const [clienteSeleccionado, setClienteSeleccionado] = useState(null);
  const [clienteDropdownOpen, setClienteDropdownOpen] = useState(false);
  const clienteInputRef = React.useRef(null);

  const [liquidacion, setLiquidacion] = useState("");
  const [showPreviewPdf, setShowPreviewPdf] = useState(false);
  const handleClosePreviewPdf = () => setShowPreviewPdf(false);
  const handleShowPreviewPdf = (p) => {
    setLiquidacion(p);
    setShowPreviewPdf(true);
  };

  const dispatch = useDispatch();

  const reset = () => {
    const defaultDesde = ultimosDias.format("YYYY-MM-DD");
    const defaultHasta = fechaActual.format("YYYY-MM-DD");

    dispatch(getLiquidaciones(defaultDesde, defaultHasta, 1, "", ""));
    setClienteid("");
    setFacturaid("");
    setNrocomprobante("");
    setFechaDesde(defaultDesde);
    setFechaHasta(defaultHasta);
    setClienteNombre("");
    setClienteSeleccionado(null);
    handleClose();
  };

  const clearClienteSearch = () => {
    setClienteNombre("");
    setClienteSeleccionado(null);
    setClienteid("");
    setPage(1);
    dispatch(getLiquidaciones(fechaDesde, fechaHasta, 1, "", nrocomprobante));
  };

  const clearComprobanteSearch = () => {
    setNrocomprobante("");
    setPage(1);
    dispatch(getLiquidaciones(fechaDesde, fechaHasta, 1, clienteid, ""));
  };

  const search = () => {
    setPage(1);
    dispatch(
      getLiquidaciones(fechaDesde, fechaHasta, 1, clienteid, nrocomprobante)
    );
    handleClose();
  };

  const downloadExcel = () => {
    if (!liquidaciones || Object.keys(liquidaciones).length === 0) return;

    const dataForExcel = Object.values(liquidaciones).map((item) => ({
      "Número Liquidación": item.facturaid,
      "Nro Cliente": item.clienteid,
      Cliente: item.nombrecliente,
      "Tipo Factura": item.tipofacturanom,
      "Fecha Liquidación": item.fechaliquidacion
        ? formatDate(item.fechaliquidacion)
        : "-",
      "Fecha Ingreso": item.fechaingreso ? formatDate(item.fechaingreso) : "-",
      Total: item.total?.replace(",", ".") || "0.00",
      Estado: item.descripcionestado || "Pendiente",
    }));

    const workSheet = XLSX.utils.json_to_sheet(dataForExcel);
    const workBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workBook, workSheet, "Liquidaciones");
    XLSX.writeFile(workBook, "Liquidaciones.xlsx");
  };

  const getEstadoColor = (estado) => {
    switch (estado) {
      case "Liquidado":
        return "success";
      case "Pendiente":
        return "warning";
      case "Vencido":
        return "error";
      default:
        return "default";
    }
  };

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(false);
  }, [liquidaciones]);

  useEffect(() => {
    setLoading(true);
    dispatch(
      getLiquidaciones(fechaDesde, fechaHasta, page, clienteid, nrocomprobante)
    ).finally(() => {
      setLoading(false);
    });
  }, [page, dispatch, clienteid, nrocomprobante, fechaDesde, fechaHasta]);

  useEffect(() => {
    if (clienteNombre.length > 2) {
      dispatch(getListadoClientesByName(clienteNombre, ""));
      setClienteDropdownOpen(true);
    } else {
      setClienteDropdownOpen(false);
    }
  }, [clienteNombre, dispatch]);

  const handleClienteSelect = (cliente) => {
    setClienteSeleccionado(cliente);
    setClienteNombre(cliente.nombre + " " + cliente.apellido);
    setClienteid(cliente.clienteid);
    setClienteDropdownOpen(false);

    setPage(1);
    dispatch(
      getLiquidaciones(
        fechaDesde,
        fechaHasta,
        1,
        cliente.clienteid,
        nrocomprobante
      )
    );
  };

  const handleClickAway = () => {
    setClienteDropdownOpen(false);
  };

  return (
    <div className="ventas-container">
      <ModalVerPdf
        show={showPreviewPdf}
        handleClose={handleClosePreviewPdf}
        id={liquidacion}
        tipoDocumento="Liquidacion"
        tipoComprobante="130"
      />

      <ModalFiltrosLiquidaciones
        show={show}
        handleClose={handleClose}
        handleChangeClienteid={(e) => setClienteid(e.target.value)}
        clienteid={clienteid}
        handleChangeNrocomprobante={(e) => setNrocomprobante(e.target.value)}
        nrocomprobante={nrocomprobante}
        setClienteId={setClienteid}
        handleChangeFechaDesde={handleChangeFechaDesde}
        fechaDesde={fechaDesde}
        handleChangeFechaHasta={handleChangeFechaHasta}
        fechaHasta={fechaHasta}
        reset={reset}
        search={search}
      />

      <div className="titulo-ventas">
        <h3 className="text-ventas">Liquidaciones</h3>
        <div className="text-ventas">
          <h4>
            Las liquidaciones que se muestran se comprenden entre la fecha
            actual y 60 días para atrás.
          </h4>
        </div>
        <div
          className="list-ventas"
          style={{ display: "flex", width: "100%", flexWrap: "wrap" }}
        >
          <TextField
            label="Buscar por número de liquidación"
            variant="outlined"
            margin="normal"
            style={{ width: 400, marginLeft: 40, marginRight: 10 }}
            InputLabelProps={{
              style: {
                marginBottom: 10,
                marginTop: -7,
              },
            }}
            inputProps={{
              style: {
                height: 40,
                fontSize: 17,
                paddingRight: 40,
              },
            }}
            name="nrocomprobante"
            onChange={(e) => setNrocomprobante(e.target.value)}
            value={nrocomprobante}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  {nrocomprobante && (
                    <IconButton onClick={clearComprobanteSearch} edge="end">
                      <Clear />
                    </IconButton>
                  )}
                  <IconButton onClick={search} edge="end">
                    <Search />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          {/* Cliente Autocomplete */}
          <div style={{ position: "relative", marginRight: 10 }}>
            <TextField
              label="Buscar por cliente"
              variant="outlined"
              margin="normal"
              style={{ width: 300 }}
              ref={clienteInputRef}
              InputLabelProps={{
                style: {
                  marginBottom: 10,
                  marginTop: -7,
                },
              }}
              inputProps={{
                style: {
                  height: 40,
                  fontSize: 17,
                },
              }}
              name="clienteNombre"
              onChange={(e) => setClienteNombre(e.target.value)}
              value={clienteNombre}
              onClick={() => {
                if (clienteNombre.length > 2) {
                  setClienteDropdownOpen(true);
                }
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {clienteNombre && (
                      <IconButton onClick={clearClienteSearch} edge="end">
                        <Clear />
                      </IconButton>
                    )}
                  </InputAdornment>
                ),
              }}
            />
            <Popper
              open={clienteDropdownOpen && clientesByName.length > 0}
              anchorEl={clienteInputRef.current}
              placement="bottom-start"
              transition
              style={{ zIndex: 1300, width: 300 }}
            >
              {({ TransitionProps }) => (
                <Grow {...TransitionProps} style={{ transformOrigin: "top" }}>
                  <MuiPaper>
                    <ClickAwayListener onClickAway={handleClickAway}>
                      <MenuList
                        autoFocusItem={clienteDropdownOpen}
                        style={{ maxHeight: 300, overflow: "auto" }}
                      >
                        {clientesByName.map((cliente, i) => (
                          <MenuItem
                            key={i}
                            onClick={() => handleClienteSelect(cliente)}
                          >
                            <ListItemText
                              primary={cliente.nombre + " " + cliente.apellido}
                            />
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </MuiPaper>
                </Grow>
              )}
            </Popper>
          </div>

          <div style={{ display: "flex", marginLeft: 10 }}>
            <Button
              variant="outlined"
              sx={{ height: 40, width: 150, marginTop: 2 }}
              onClick={handleShow}
            >
              <FilterListIcon /> Filtrar
            </Button>
            <Button
              variant="outlined"
              sx={{ height: 40, width: 150, marginTop: 2, marginLeft: 5 }}
              onClick={downloadExcel}
            >
              <Download /> Exportar
            </Button>
          </div>
        </div>
      </div>

      {permisos_acciones?.listar !== "0" && (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyItems: "center",
          }}
        >
          <TableContainer
            component={Paper}
            sx={{
              marginTop: 5,
              width: "95%",
              alignSelf: "center",
              marginLeft: 3,
              marginBottom: 5,
            }}
          >
            <Table aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell style={{ fontWeight: "bold", fontSize: "110%" }}>
                    #
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%", padding: 0 }}
                    align="center"
                  >
                    Cliente
                  </TableCell>
                  {/* <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Tipo Factura
                  </TableCell> */}
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Fecha Liquidación
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Fecha Ingreso
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Total
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Estado
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Ver
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow sx={{ p: 20 }}>
                    <TableCell colSpan={8} align="center">
                      <ClipLoader loading={loading} size={50} />
                    </TableCell>
                  </TableRow>
                ) : liquidaciones && Object.keys(liquidaciones).length > 0 ? (
                  Object.values(liquidaciones).map((row) => (
                    <TableRow
                      key={row.facturaid}
                      sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                    >
                      <TableCell
                        style={{
                          fontSize: "16px",
                          width: "50px",
                          height: "40px",
                          padding: 3,
                        }}
                        component="th"
                        scope="row"
                      >
                        <Link
                          to={`/Mitienda/Liquidaciones/${row.facturaid}/${row.clienteid}`}
                        >
                          {row.facturaid}
                        </Link>
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "90px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.nombrecliente}
                      </TableCell>
                      {/* <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.tipofacturanom}
                      </TableCell> */}
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.fechaliquidacion
                          ? formatDate(row.fechaliquidacion)
                          : "-"}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.fechaingreso ? formatDate(row.fechaingreso) : "-"}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        ${row.total?.replace(",", ".") || "0.00"}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        <Chip
                          label={row.descripcionestado || "Pendiente"}
                          color={getEstadoColor(row.descripcionestado)}
                          size="small"
                        />
                      </TableCell>
                      {/* <TableCell
                        style={{
                          fontSize: "14px",
                          width: "50px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        <Tooltip title="Ver liquidación">
                          <IconButton
                            onClick={() => handleShowPreviewPdf(row.facturaid)}
                          >
                            <RemoveRedEye />
                          </IconButton>
                        </Tooltip>
                      </TableCell> */}
                      <TableCell
  style={{
    fontSize: "14px",
    width: "50px",
    height: "40px",
    padding: 0,
  }}
  align="center"
>
  <Tooltip title={row.url_comprobante && row.url_comprobante !== "#" ? "Ver comprobantes" : "No hay comprobantes"}>
    <IconButton
      onClick={() => handleViewComprobantes(row.facturaid)}
      disabled={!row.url_comprobante || row.url_comprobante === "#"}
    >
      <RemoveRedEye color={row.url_comprobante && row.url_comprobante !== "#" ? "primary" : "disabled"} />
    </IconButton>
  </Tooltip>
</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <h5>No hay información para mostrar</h5>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Pagination
            count={paginaUltima}
            page={page}
            onChange={handleChangePage}
            size="large"
            sx={{ alignSelf: "center" }}
          />
        </div>
      )}

      <Dialog
  open={openComprobantesModal}
  onClose={handleCloseComprobantesModal}
  maxWidth="md"
  fullWidth
>
  <DialogTitle>Comprobantes de Liquidación</DialogTitle>
  <DialogContent>
    {comprobantesState.loading ? (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
        <CircularProgress />
      </div>
    ) : comprobantesState.error ? (
      comprobantesState.error === "NO_COMPROBANTES" ? (
        <Typography variant="body1" align="center" style={{ padding: '20px' }}>
          No se encontraron comprobantes para esta liquidación
        </Typography>
      ) : (
        <Alert severity="error">{comprobantesState.error}</Alert>
      )
    ) : comprobantesState.data ? (
      <List>
        {Object.values(comprobantesState.data).map((doc) => (
          <ListItem 
            key={doc.factura_imagenesid}
            button
            component="a"
            href={doc.url_documento}
            target="_blank"
            rel="noopener noreferrer"
          >
            <ListItemIcon>
              <AttachFileIcon />
            </ListItemIcon>
            <ListItemText primary={doc.nombre} />
          </ListItem>
        ))}
      </List>
    ) : (
      <Typography variant="body1" align="center" style={{ padding: '20px' }}>
        No hay comprobantes disponibles
      </Typography>
    )}
  </DialogContent>
  <DialogActions>
    <Button onClick={handleCloseComprobantesModal}>Cerrar</Button>
  </DialogActions>
</Dialog>
    </div>
  );
};
