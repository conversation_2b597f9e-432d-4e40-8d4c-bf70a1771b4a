import React, { useEffect, useState } from 'react';
import { 
    Button, 
    Checkbox, 
    FormControl, 
    FormControlLabel,
    InputAdornment,
    InputLabel,
    ListSubheader,
    MenuItem,
    Select,
    TextField, 
} from '@mui/material';

import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { 
    getCuentasCorrientesProveedor,
    getListadoProveedorByName, 
} from '../../../../redux/actions/mitienda.actions'
import { ModalImprimir } from './modalImprimir';
import { Article, AttachMoney, Print, Search } from '@mui/icons-material';
import { ModalRegistrarPagoCuentaCorrienteProveedor } from './modalRegistrarPagoCuentaCorrienteProveedor';
import { SelectSearchInputProveedor } from '../../SelectInputs/SelectSearchInputProveedor';
import ClipLoader from "react-spinners/ClipLoader";

export const GestionCuentasCorrientesProveedor = () => {

    const info = useSelector((state) => state.mitienda.cuentasCorrientesProveedor)
    const proveedores = useSelector((state) => state.mitienda.proveedoresByName)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaCuentasCorrientes)

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [solo_deuda, setSoloDeuda] = useState(false)
    const [clienteid, setClienteid] = useState('')

    const dispatch = useDispatch()

    const [factura, setFactura] = useState('')

    const [showPdf, setShowPdf] = useState('');
    const handleClosePdf = () => setShowPdf(false);
    const handleShowPdf = (p) => {
        setFactura(p)
        setShowPdf(true);
    }

    const [showPagos, setShowPagos] = useState('');
    const handleClosePagos = () => setShowPagos(false);
    const handleShowPagos = (p) => {
        setFactura(p)
        setShowPagos(true);
    }

    const onSearch = () => {
        setSearch(true)
        dispatch(getCuentasCorrientesProveedor(1,proveedorID,solo_deuda))
        dispatch(getListadoProveedorByName(''))
        setProveedorID('')
    }

    const [nombreAux, setNombreAux] = useState('')
    const [selectedOption, setSelectedOption] = useState('')
    const [proveedorID, setProveedorID] = useState('')
    const [search, setSearch] = useState(false)

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        if(nombreAux.length > 4){
            dispatch(getListadoProveedorByName(nombreAux))
        }
    }, [nombreAux])

    useEffect(() =>{
        if(nombreAux === ''){
            setLoading(true)
            dispatch(getCuentasCorrientesProveedor(page,proveedorID,solo_deuda))
        }
    },[page,nombreAux])

    useEffect(() =>{
        if(search === true && nombreAux === ''){
            dispatch(getCuentasCorrientesProveedor(page,proveedorID,solo_deuda))
            dispatch(getListadoProveedorByName(''))
            setSelectedOption('')
            setSearch(false)
        }
    }, [search,nombreAux])

    return (
        <div className="notificaciones-container">
            <ModalRegistrarPagoCuentaCorrienteProveedor
                show={showPagos}
                handleClose={handleClosePagos}
                info={factura}
            />
            <ModalImprimir
                show={showPdf}
                handleClose={handleClosePdf}
                info={factura}
            />

            <div>
                <header className="titulo-notificaciones" style={{display:"flex", flexDirection:"column"}}>
                    <h3>Gesti&oacute;n de cuentas corrientes proveedores</h3>
                    <div style={{display:"flex", alignItems:"center", marginTop:10}}>
                    <FormControl style={{marginTop:5, marginBottom:10, width:600, marginRight:10}} size="small">
                        <InputLabel id="search-select-label">Proveedor</InputLabel>
                            <Select
                                style={{height:40}}
                                MenuProps={{ autoFocus: false,  disablePortal: true }}
                                labelId="search-select-label"
                                id="search-select"
                                value={selectedOption.proveedorid || ''}
                                label="Proveedor"
                                size="small"
                                renderValue={() => selectedOption.nombre}
                            >
                        <ListSubheader>
                        <TextField
                            size="small"
                            autoFocus
                            placeholder="Buscar proveedor..."
                            fullWidth
                            InputProps={{
                                startAdornment: (
                                <InputAdornment position="start">
                                    <Search/>
                                </InputAdornment>
                                )
                            }}
                            inputProps={{
                                style: {
                                    height: 35,
                                    fontSize:17
                                },
                            }}
                            onChange={(e) => setNombreAux(e.target.value)}
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                            value={nombreAux}
                        />
                        </ListSubheader>
                        {proveedores.map((option, i) => (
                            <MenuItem key={i} value={option.clienteid} onClick={() => {
                                setSelectedOption(option)
                                setNombreAux(option.nombre)
                                setProveedorID(option.proveedorid)
                            }}>
                            {option.nombre}
                            </MenuItem>
                        ))}
                        </Select>
                    </FormControl>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black",width:200}}
                            onChange={(e)=> setSoloDeuda(e.target.checked)}
                            checked={solo_deuda}
                            label="Solo deuda"
                        />
                    <Button onClick={onSearch} variant='contained' style={{height:40, marginLeft:10}} >Buscar</Button>
                    </div>
                </header>
            
            </div>
            <div style={{display:"flex", flexDirection:"column", width:"95%", marginTop:5, marginLeft:50}}>
                <TableContainer component={Paper} sx={{margin:5,width:"100%", alignSelf:"center"}}>
                    <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Cliente</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">CUIT</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Importe adeudado</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Importe a favor</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Saldo</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Imprimir</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Detalle</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Pagos</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {
                                loading ? 
                                <TableRow sx={{p:20}}>
                                    <TableCell colSpan={8} align='center'>
                                        <ClipLoader 
                                            loading={loading}
                                            size={50}
                                        />
                                    </TableCell>
                                </TableRow> :
                                info.data.length > 0 ? info.data.map((row) => (
                                <TableRow
                                    key={row.proveedorid}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                >
                                    <TableCell style={{fontSize:"80%", padding:0, height:40, width:200}} align="center">
                                        {row.nombre}
                                    </TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.cuit}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        ${row.totalEnContra}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                        ${row.totalAFavor}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                        ${row.saldo}</TableCell>
                                    <TableCell 
                                        style={{fontSize:"17px", width:"50px", height:"40px", padding: 0, cursor:"pointer"}} 
                                        align="center"
                                        onClick={(e)=>handleShowPdf(row)}>
                                        <Print/>
                                    </TableCell>
                                    <TableCell 
                                        style={{fontSize:"17px", width:"50px", height:"40px", padding: 0, cursor:"pointer"}} 
                                        align="center">
                                        <a href={`/Mitienda/CuentasCorrientesProveedores/Detalle/${row.proveedorid}`}>
                                            <Article style={{color:"black"}}/>
                                        </a>
                                    </TableCell>
                                    <TableCell 
                                        style={{fontSize:"17px", width:"50px", height:"40px", padding: 0, cursor:"pointer"}} 
                                        align="center"
                                        onClick={()=> handleShowPagos(row)}
                                    >
                                        <AttachMoney/>
                                    </TableCell>
                                </TableRow>
                            )):
                            <TableRow>
                                <TableCell align="center" colSpan={8}>
                                    <h5>No hay informaci&oacute;n para mostrar</h5>
                                </TableCell>
                            </TableRow>
                            }

                        </TableBody>
                    </Table>
                </TableContainer>
            </div>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}
