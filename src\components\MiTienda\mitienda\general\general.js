import { Paper, TextField, Button, Snackbar, FormControl, InputLabel, MenuItem, Select, FormControlLabel, Checkbox } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { configAfip, configTienda, getConfigAfip, getConfigTienda, getListadoProvincias, getMonedas, getTiposDocumentoTienda, limpiarGeneral, postCertificados, postConfigAfip } from "../../../../redux/actions/mitienda.actions";
import "./general.css"
import MuiAlert from '@mui/material/Alert';
import { ModalCertificadosAfip } from "./modalCertificados";
import { ModalPrivateKey } from "./modalPrivateKey";
import { Download } from "@mui/icons-material";
import pdf from './certafip.pdf'

export const General =()=>{

    const ok = useSelector((state) => state.mitienda.generalOk)

    const infoTienda = useSelector((state) => state.mitienda.infoTienda)
    const okInfoTienda = useSelector((state) => state.mitienda.okInfoTienda)
    const monedas = useSelector((state) => state.mitienda.monedas)
    const provincias = useSelector((state) => state.mitienda.provincias)
    const configAfip = useSelector((state) => state.mitienda.configAfip.webservice)
    const okConfigAfip = useSelector((state) => state.mitienda.okConfigAfip)
    const okCertificados = useSelector((state) => state.mitienda.okConfigAfip)

    const tiposwebservice = [{id: "1", nombre: 'TESTING'}, 
    {id: "2", nombre: 'HOMOLOGACION'},
    {id: "3", nombre: 'DESARROLLO'},
    {id: "4", nombre: 'PRODUCCION'}]

    const dispatch = useDispatch()

    const [input, setInput] = useState('')
    const [inputAfip, setInputAfip] = useState('')
    const [certificado, setCertificado] = useState('')
    const [privateKey, setPrivateKey] = useState('')

    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () =>  setShow(true);

    const [showPrivateKey, setShowPrivateKey] = useState(false);
    const handleClosePrivateKey = () => setShowPrivateKey(false);
    const handleShowPrivateKey = () =>  setShowPrivateKey(true);

    const handleChange = (e) =>{
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value})
    }

    const handleChangeAfip = (e) =>{
        e.preventDefault()
        setInputAfip({...inputAfip, [e.target.name]: e.target.value})
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInputAfip({...inputAfip, [e.target.name] : "1"})
        }else{
            setInputAfip({...inputAfip, [e.target.name] : "0"})
        }
    }

    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(configTienda(input))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
    }

    const handleOnClickAfip = (e) => {
        e.preventDefault()
        dispatch(postConfigAfip(inputAfip))
        setTimeout(function(){
            handleClickAlertConfigAfip()
        }, 1000);
    }

    const handleOnClickCertificadosAfip = (e) => {
        e.preventDefault()
        dispatch(postCertificados(inputAfip.empresaconfid, certificado, privateKey))
        setTimeout(function(){
            handleClickAlertCertificados()
        }, 1000);
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openConfigAfip, setOpenConfigAfip] = React.useState(false);
    const handleClickAlertConfigAfip = () => {
      setOpenConfigAfip(true);
    };
    const handleCloseAlertConfigAfip = () => {
        setOpenConfigAfip(false);
    };

    const [openCertificados, setOpenCertificados] = React.useState(false);
    const handleClickAlertCertificados = () => {
      setOpenCertificados(true);
    };
    const handleCloseAlertCertificados = () => {
        setOpenCertificados(false);
    };

    useEffect(() =>{
        dispatch(getConfigTienda())
        dispatch(getConfigAfip())
        dispatch(limpiarGeneral())
        dispatch(getMonedas())
        dispatch(getListadoProvincias())
    }, [])

    useEffect(() => {
        setInput(infoTienda)
    }, [infoTienda])

    useEffect(() => {
        setInputAfip(configAfip)
    }, [configAfip])

    return (
    <section className="container-general">
        <Snackbar
            open={open} 
            autoHideDuration={10000} onClose={handleCloseAlert}
            anchorOrigin={
                {vertical: 'top',
                horizontal: 'center',}
            }>
            <Alert onClose={handleCloseAlert} 
                severity={ok ? "success" : "error"}
                sx={{ width: 400 }}>
                <h5>Informaci&oacute;n modificada con &eacute;xito!</h5>
            </Alert>
        </Snackbar>
        <Snackbar
            open={openConfigAfip} 
            autoHideDuration={10000} onClose={handleCloseAlertConfigAfip}
            anchorOrigin={
                {vertical: 'top',
                horizontal: 'center',}
            }>
            <Alert onClose={handleCloseAlertConfigAfip} 
                severity={okConfigAfip ? "success" : "error"}
                sx={{ width: 400 }}>
                <h5>{okConfigAfip.mensaje}</h5>
            </Alert>
        </Snackbar>
        <Snackbar
            open={openCertificados} 
            autoHideDuration={10000} onClose={handleCloseAlertCertificados}
            anchorOrigin={
                {vertical: 'top',
                horizontal: 'center',}
            }>
            <Alert onClose={handleCloseAlertCertificados} 
                severity={okCertificados ? "success" : "error"}
                sx={{ width: 400 }}>
                {okCertificados.success === true ?
                    <h5>Certificados modificados con &eacute;xito</h5> : 
                    <h5>Hubo un problema al modificar los certificados</h5>
                }
            </Alert>
        </Snackbar>
        <ModalCertificadosAfip
            show={show}
            handleClose={handleClose}
            setCertificado={setCertificado}
        />
        <ModalPrivateKey
            show={showPrivateKey}
            handleClose={handleClosePrivateKey}
            setPrivateKey={setPrivateKey}
        />
        <header className="header-general">
            <h3>Configuraciones generales</h3>
        </header>
        <Paper className="form-mitienda">{ okInfoTienda ?
        <div style={{width:"100%"}}>
            <TextField 
                sx={{mb:2, mt:1}}
                label="Nombre tienda"  
                helperText="Nombre de tu negocio" 
                variant="outlined" 
                margin="normal" 
                fullWidth
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                        height: 40,
                        fontSize:17
                    },
                }}
                name="nombre"
                value={input.nombre || ''}
                onChange={(e) => handleChange(e)}
            />
            <TextField 
                sx={{mb:2, mt:1}}
                id="telefono-tienda" 
                label="Telefono tienda (opcional)"  
                helperText="Puedes agregar un numero de telefono para consultas" 
                variant="outlined" 
                fullWidth margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
                name="telefono"
                value={input.telefono || ''}
                onChange={(e) => handleChange(e)}
            />
            <FormControl sx={{mb:2, mt:1}} fullWidth>
                <InputLabel id="provincialabel">Provincia</InputLabel>
                <Select
                labelId="provincialabel"
                id="provincia"
                label="Provincia"
                size="small"
                name="provinciaid"
                value={input.provinciaid || ''}
                onChange={handleChange}
                >
                <MenuItem value={0}>Seleccione una opcion</MenuItem>
                {
                    provincias && provincias.map((m) => 
                        <MenuItem value={m.provinciaid} key={m.provinciaid}>{m.nombre}</MenuItem>
                    )
                }
                </Select>
            </FormControl>
            <TextField 
                sx={{mb:2, mt:1}}
                id="ciudad" 
                label="Ciudad"  
                variant="outlined" 
                fullWidth margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
                name="ciudad"
                value={input.ciudad || ''}
                onChange={(e) => handleChange(e)}
            />
            <TextField 
                sx={{mb:2, mt:1}}
                id="codigo-postal" 
                label="C&oacute;digo postal"  
                variant="outlined" 
                fullWidth margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
                name="codigopostal"
                value={input.codigopostal || ''}
                onChange={(e) => handleChange(e)}
            />
            <TextField 
                sx={{mb:2, mt:1}}
                id="direccion" 
                label="Calle"  
                variant="outlined" 
                fullWidth margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
                name="direccion"
                value={input.direccion || ''}
                onChange={(e) => handleChange(e)}
            />
            <TextField 
                sx={{mb:2, mt:1}}
                id="altura" 
                label="Altura"  
                variant="outlined" 
                fullWidth margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
                name="altura"
                value={input.altura || ''}
                onChange={(e) => handleChange(e)}
            />
            <FormControl sx={{mb:2, mt:1}} fullWidth>
                <InputLabel id="monedalabel">Moneda</InputLabel>
                <Select
                labelId="monedalabel"
                id="moneda"
                label="Moneda"
                size="small"
                name="default_monedaid"
                value={input.default_monedaid || ''}
                onChange={handleChange}
                >
                <MenuItem value={0}>Seleccione una opcion</MenuItem>
                {
                    monedas && monedas.map((m) => 
                        <MenuItem value={m.monedaid} key={m.monedaid}>{m.nombre}</MenuItem>
                    )
                }
                </Select>
            </FormControl>
            <div align="right">
            <Button 
                size="large" 
                variant="contained" 
                onClick={(e)=>(handleOnClick(e))}
                sx={{mt: 3, mr:2}}>Guardar</Button>
            </div>
        </div> : null }
        </Paper>
        <header className="header-general" style={{marginTop:30}}>
            <h3>Configuraciones AFIP</h3>
        </header>
        <Paper className="form-mitienda">{ inputAfip ?
        <div style={{width:"100%"}}>
            {/* <TextField 
                sx={{mb:2, mt:2}}
                label="Empresa"  
                variant="outlined" 
                margin="normal" 
                fullWidth
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                        height: 40,
                        fontSize:17
                    },
                }}
                name="nombre"
                value={input.nombre || ''}
                onChange={(e) => handleChange(e)}
            /> */}
            <TextField 
                sx={{mb:2, mt:2}}
                label="CUIT"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="cuit"
                value={inputAfip.cuit || ''}
                onChange={(e) => handleChangeAfip(e)}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                sx={{mb:2, mt:2}}
                label="Punto de venta AFIP"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="puntoventanro"
                value={inputAfip.puntoventanro || ''}
                onChange={(e) => handleChangeAfip(e)}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                sx={{mb:2, mt:2}}
                label="Tipo documento AFIP"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="tipo_documentoafip"
                value={inputAfip.tipo_documentoafip || ''}
                onChange={(e) => handleChangeAfip(e)}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <FormControl fullWidth sx={{mb:2, mt:2}}>
                <InputLabel id="tipodocumento-select-label">Tipo de Webservice</InputLabel>
                <Select
                labelId="tipodocumento-select-label"
                label="Tipo documento"
                size="small"
                name="tipowebserviceid"
                value={inputAfip.tipowebserviceid || ''}
                onChange={(e) => handleChangeAfip(e)}
                
                >
                <MenuItem value="">Seleccione una opcion</MenuItem>
                {               
                    tiposwebservice.map((t) =>
                        <MenuItem value={t.id} key={t.id}>{t.nombre}</MenuItem>
                    )
                }
                </Select>
            </FormControl>
            <div style={{display:"flex", width:"100%", justifyContent:"center", marginBottom:15}}>
                <div style={{display:"flex", width:"100%", marginTop:15, marginBottom:15}}>
                    <h6 style={{ marginTop:10}}>Fecha desde</h6>
                        <TextField 
                        style={{marginLeft:10, width:"75%"}}
                        variant="outlined" 
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        name="fechadesde"
                        value={inputAfip.fechadesde || ''}
                        onChange={(e) => handleChangeAfip(e)}
                        type="date"
                        />
                </div>  
                <div style={{display:"flex", width:"100%", marginTop:15, marginBottom:15}}>
                    <h6 style={{ marginTop:10}}>Fecha hasta</h6>
                    <TextField 
                        style={{marginLeft:10, width:"75%"}}
                        variant="outlined" 
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        name="fechahasta"
                        value={inputAfip.fechahasta || ''}
                        onChange={(e) => handleChangeAfip(e)}
                        type="date"
                    /> 
                </div>
            </div>
            <TextField 
                sx={{mb:2, mt:2}}
                label="Path de Archivos Certificados de AFIP"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="directorio_certificados"
                value={inputAfip.directorio_certificados || ''}
                onChange={(e) => handleChangeAfip(e)}
                
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                sx={{mb:2, mt:2}}
                label="Passphrase"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="passphrase"
                value={inputAfip.passphrase || ''}
                onChange={(e) => handleChangeAfip(e)}
                
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <div style={{display:"flex", alignItems:"center"}}>
                <TextField 
                    sx={{mb:2, mt:2, mr:2}}
                    label="CERT (Certificado)"  
                    variant="outlined" 
                    fullWidth margin="normal" 
                    name="certificado"
                    value={inputAfip.certificado || ''}
                    onChange={(e) => handleChangeAfip(e)}
                    
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <Button 
                    style={{width:250, height:40}} 
                    variant="contained"
                    onClick={handleShow}
                >Subir certificado</Button>
            </div>
            <div style={{display:"flex", alignItems:"center"}}>
                <TextField 
                    sx={{mb:2, mt:2, mr:2}}
                    label="PRIVATE KEY (Certificado Privado)"  
                    variant="outlined" 
                    fullWidth margin="normal" 
                    name="certificadopriv"
                    value={inputAfip.certificadopriv || ''}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <Button 
                    style={{width:250, height:40}} 
                    variant="contained"
                    onClick={handleShowPrivateKey}
                >Subir private key</Button>
            </div>
            <TextField 
                sx={{mb:2, mt:2}}
                label="wsdlconexion"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="wsdlconexion"
                value={inputAfip.wsdlconexion || ''}
                onChange={(e) => handleChangeAfip(e)}
                
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                sx={{mb:2, mt:2}}
                label="urlconexion"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="urlconexion"
                value={inputAfip.urlconexion || ''}
                onChange={(e) => handleChangeAfip(e)}
                
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                sx={{mb:2, mt:2}}
                label="urlfev1"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="urlfev1"
                value={inputAfip.urlfev1 || ''}
                onChange={(e) => handleChangeAfip(e)}
                
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                sx={{mb:2, mt:2}}
                label="wsdlfev1"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="wsdlfev1"
                value={inputAfip.wsdlfev1 || ''}
                onChange={(e) => handleChangeAfip(e)}
                
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                label="Tope Factura Credito electronica MiPyMEs (FCE)"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="tope_fce"
                value={inputAfip.tope_fce || ''}
                onChange={(e) => handleChangeAfip(e)}
                
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <div style={{display:"flex", width:"100%", justifyContent:"space-around"}}>
                <FormControlLabel
                    sx={{mb:2, mt:2}}
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black", marginLeft:1}}
                    onChange={(e)=> handleCheck(e)}
                    checked={inputAfip.activo === "1" ? true : false}
                    name="activo"
                    label="Activo"
                />
                <FormControlLabel
                    sx={{mb:2, mt:2}}
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black", marginLeft:1}}
                    onChange={(e)=> handleCheck(e)}
                    checked={inputAfip.ptovta === "1" ? true : false}
                    name="ptovta"
                    label="CAE en Punto de Venta"
                />
                <FormControlLabel
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black", marginLeft:1}}
                    onChange={(e)=> handleCheck(e)}
                    checked={inputAfip.pedidos === "1" ? true : false}
                    name="pedidos"
                    label="CAE en Pedido"
                />
                <FormControlLabel
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black", marginLeft:1}}
                    onChange={(e)=> handleCheck(e)}
                    checked={inputAfip.email_fc === "1" ? true : false}
                    name="email_fc"
                    label="Enviar Mail Factura en Pedido"
                />
            </div>
            <TextField 
                label="Informaci&oacute;n tecnica"  
                variant="outlined" 
                fullWidth margin="normal" 
                name="informacion_tecncia"
                value={inputAfip.informacion_tecncia || ''}
                onChange={(e) => handleChangeAfip(e)}
                multiline
            />
            <div style={{display:"flex", width:"100%", justifyContent:"flex-end"}} align="right">
            
            <a
                href={pdf}
                download="manual_afip.pdf"
                target="_blank"
                rel="noreferrer"
                style={{marginTop:3}}
            >
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3, mr:2}}
            >Manual AFIP
            </Button>
            </a>
            <Button 
                size="large" 
                variant="contained" 
                onClick={(e)=>(handleOnClickCertificadosAfip(e))}
                sx={{mt: 3, mr:2}}>Guardar certificados</Button>
            <Button 
                size="large" 
                variant="contained" 
                onClick={(e)=>(handleOnClickAfip(e))}
                disabled={inputAfip.cuit === '' || 
                inputAfip.puntoventanro === '' || 
                inputAfip.tipo_documentoafip === '' || 
                inputAfip.passphrase === '' || 
                inputAfip.wsdlconexion === '' || 
                inputAfip.urlconexion === '' || 
                inputAfip.urlfev1 === '' || 
                inputAfip.wsdlfev1 === ''
                }
                sx={{mt: 3, mr:2}}>Guardar configuracion</Button>
            </div>
        </div> : null }
        </Paper>
    </section>
    )
}