.container-general {
    width: 100%;
    min-height: 110vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.header-general{
    color: black;
    margin-left: 55px;
}

.form-container-general{
    width: 90%;
}

.form-mitienda{
    background-color: white;
    height: auto;
    width: 95%;
    padding: 20px;
    align-self: center;
    border-radius:10px;
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 40px;
}

@media only screen and (max-width: 1200px) {
    .container-general{
        width: 100%;
        min-height: 120vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
        padding: 20px;
    }
}