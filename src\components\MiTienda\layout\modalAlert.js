import React from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { useDispatch } from "react-redux";
import image from '../../../media/sitioenmantenimiento.png'

Modal.setAppElement("#root")

export const ModalAlert = ({show}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };
    return (
        show && 
        <Modal isOpen={show} style={customStyles}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>El sitio se encuentra en mantenimiento. <br/>
                    Disculpe las molestias.</h4>
                </div>
                <Divider/>
                <div style={{display:"flex", justifyContent:"center", margin:15}}>   

                    <img src={image} style={{width:300}}/>
                </div>
        </Modal>
    )
}