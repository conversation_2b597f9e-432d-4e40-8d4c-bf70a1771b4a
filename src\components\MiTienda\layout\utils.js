function crearObjeto (menues) {

    let lista = menues.map(m => m.titulo)

    let objeto = {}

    for(let i=0; i < lista.length; i++){
        objeto[lista[i]] = false
    }

    return objeto
}

function filtrar (menues) {

    let lista = menues.filter(m => m.nombredireccion == "MiTienda")

    let array = []
    let myset = ''

    let nombresDireccion = lista.map(l => l.nombremenu)
    let setNombresDireccion = new Set(nombresDireccion)
    nombresDireccion = Array.from(setNombresDireccion)

    for(let i=0; i < nombresDireccion.length; i++){
        array[i] = []
        array[i] = {
            nombredireccion: nombresDireccion[i],
            menues: []
        }
    }

    for(let j=0; j < array.length; j++){
        myset = new Set(lista.filter(a => a.nombremenu === array[j].nombredireccion).map(b => ({
            nombre: b.nombresubmenu,
            url: b.url,
            urlsubmenu: b.urlsubmenu,
            urlsubmenu2: b.urlsubmenu2
        })))
        array[j].menues = Array.from(myset)     
    }

    return array
}

module.exports = {
    crearObjeto,
    filtrar
}