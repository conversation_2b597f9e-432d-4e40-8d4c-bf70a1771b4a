import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, FormControl, InputLabel, MenuItem, Select, TextField,} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getDefaultMetodosDePago, getMonedas, registrarPago } from "../../../redux/actions/mitienda.actions";
import { useEffect } from "react";

Modal.setAppElement("#root")

export const ModalRegistrarPago = ({show, handleClose,pedido, handleClickAlert}) =>{

    const metodosDePago = [{id: "1", nombre: "Contado"}, {id: "7", nombre: "Mercado Libre"}]

    const monedas = useSelector((state) => state.mitienda.monedas)

    const dispatch = useDispatch()

    const [input, setInput] = useState({
        fecha: new Date().toJSON().slice(0,10),
        moneda:"",
        montoreal: "",
        numerorecibo:"",
        descripcion:"",
        tipopago: "",
        pedidoid: "",
        facturaid: "",
    })

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(registrarPago(input))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        setInput({
            fecha: new Date().toJSON().slice(0,10),
            moneda:'',
            montoreal: pedido[0].monto,
            numerorecibo:"",
            descripcion:"",
            tipopago: "",
            pedidoid: pedido[0].facturaid_pedido
        })
    }

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const matchMoneda = (moneda,monedas) => {
        if(moneda !== '' || moneda !== undefined || monedas !== undefined){
            let id = monedas.filter((c) => c.nombre === moneda )
    
            if(id.length > 0){
                return id[0].monedaid
            }else{
                return ''
            }
        }else{
            return ''
        }
    }

    useEffect(() =>{
        if(pedido){
            setInput({...input, 
                montoreal: pedido[0].monto, 
                moneda: matchMoneda(pedido[0].moneda,monedas), 
                pedidoid: pedido[0].pedidoplacaid,
                facturaid: pedido[0].facturaid_pedido
            })
        }
    },[pedido])

    useEffect(() =>{
        dispatch(getDefaultMetodosDePago())
        dispatch(getMonedas())
    },[])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Registrar pago - Pedido #{pedido[0].pedidoplacaid}</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>   
                <TextField
                    fullWidth
                    style={{marginTop:20,marginBottom:20}}
                    label="Fecha"  
                    variant="outlined"  
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="fecha"
                    value={input.fecha}
                    type="date"
                  />
                  <FormControl fullWidth sx={{mb:2, mt:2}}>
                        <InputLabel id="moneda-select-label">Moneda</InputLabel>
                        <Select
                        labelId="moneda-select-label"
                        label="Moneda"
                        size="small"
                        name="moneda"
                        value={input.moneda || ''}
                        onChange={handleChange}
                        MenuProps={{ keepMounted: true, disablePortal: true }}
                        >
                        <MenuItem value="">Seleccione una opcion</MenuItem>
                        {               
                            monedas && monedas.map((t) =>
                                <MenuItem value={t.monedaid} key={t.monedaid}>{t.nombre}</MenuItem>
                            )
                        }
                        </Select>
                    </FormControl>
                    <FormControl fullWidth sx={{mb:2, mt:2}}>
                        <InputLabel id="tipodepago-select-label">Tipo de pago</InputLabel>
                        <Select
                        labelId="tipodepago-select-label"
                        label="Tipo de pago"
                        size="small"
                        name="tipopago"
                        value={input.tipopago || ''}
                        onChange={handleChange}
                        MenuProps={{ keepMounted: true, disablePortal: true }}
                        >
                        <MenuItem value="">Seleccione una opcion</MenuItem>
                        {               
                            metodosDePago && metodosDePago.map((t) =>
                                <MenuItem value={t.id} key={t.id}>{t.nombre}</MenuItem>
                            )
                        }
                        </Select>
                    </FormControl>
                    <TextField
                        fullWidth
                        style={{marginTop:20,marginBottom:20}}
                        label="Monto"  
                        variant="outlined"  
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        name="montoreal"
                        value={input.montoreal}
                        onChange={handleChange}
                  />
                <TextField
                    fullWidth
                    style={{marginTop:20,marginBottom:20}}
                    label="Numero de recibo"  
                    variant="outlined"  
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="numerorecibo"
                    value={input.numerorecibo}
                    onChange={handleChange}
                  />
                </div>
                <Divider/>
                <div align="right">
                    <Button 
                    disabled={input.moneda == "" || input.tipopago == ""}
                    size="large"
                    variant="contained"
                    onClick={handleOnClick}
                    sx={{mt: 3, mr:1}}>Registrar</Button>
                </div>
        </Modal>
    )
}