import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { configSubCategoria } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const AgregarSubcategoria = ({show, handleClose, nombre, id,  handleClickAlert}) =>{

  const [input, setInput] = useState({
    subcategoriaid: 0,
    nombre: "",
  })

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value})
  }

  const dispatch = useDispatch()

  const handleClick = () => {
    dispatch(configSubCategoria(input, id))
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    setInput({
      subcateogriaid: 0,
      nombre: "",
    })
    handleClose()
  }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    useEffect(() => {
      setInput({
        subcateogriaid: 0,
        nombre: "",
      })
    },[])

    return (
        show ?
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex",justifyContent:"space-between", marginBottom:10}}>
                    <div style={{display:"flex", flexDirection:"column"}}>
                        <h4>Agregar subcategoria</h4>
                        <h5>Dentro de {nombre}</h5>
                    </div>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:40, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{marginTop:15, marginBottom:15}}>
                  <TextField 
                      name="nombre"
                      label="Nombre"  
                      variant="outlined" 
                      fullWidth margin="normal" 
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                      value={input.nombre}
                      onChange={(e)=> handleChange(e)}
                  />
                </div>
                <Divider/>
                <div align="right">
                <Button size="large" variant="contained" sx={{mt: 3}} onClick={handleClick}>Agregar</Button>
                </div>
        </Modal> : null
    )
}