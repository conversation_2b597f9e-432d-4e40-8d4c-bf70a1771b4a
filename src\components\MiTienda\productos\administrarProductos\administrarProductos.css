.container-adm-productos{
    width: 100%;
    min-height: 110vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.form-productos-editar{
    background-color: white;
    min-height: 100vh;
    width: 80%;
    padding: 20px;
    align-self: left;
    border-radius:10px;
    align-items: left;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    margin-bottom: 20px;
}

.paper-adm-productos{
    padding:10px;
    width: 95%;
    height: 210px;
    display: flex;
    justify-content: space-between;
}

.list-adm-productos{
    display:flex;
    margin-bottom:10px;
    margin-left:40px;
}

.header-adm-productos{
    display:flex;
    margin-bottom:10px; 
    margin-left:40px; 
    justify-content:space-between;
}

.list-icons-producto{
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar{
    width: 70px;
    height: 70px;
    align-self:center ;
}

.image-producto{
    width: 200px;
    height: 100px;
    overflow: hidden;
}


.lista-imagenes{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    float: left;
    width: 100%;
}

@media only screen and (max-width: 1200px) {
    .container-adm-productos{
        padding-left: 40px;
        padding-top: 20px;
        width: 100%;
        min-height: 110vh;
        height: auto;  
        background-color: #EEEEEE;
        background-attachment: fixed;
        padding-bottom: 20px;
        display: flex;
        flex-direction: column;
    }

    .paper-adm-productos{
        padding:10px;
        width: 70%;
        height: 280px;
        display: flex;
        justify-content: space-between;
    }

    .list-adm-productos{
        display:flex;
        flex-direction:column;
        margin-bottom:10px;
        margin-left:40px;
    }

    .header-adm-productos{
        display:flex;
        margin-bottom:10px; 
        margin-left:40px; 
        flex-direction: column;
    }

    .avatar{
        display: none;
    }
}

.avatar-productos{
    width: 20%;
    height: 95%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.card-producto{
    width: 95%;
    height: 25vh;
    background-color: white;
    border-radius: 15px;
    box-shadow: 0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12);
    display: flex;
    align-items: center;
}

.titulo-producto{
    display: flex;
    align-items: center;
}

.datos-producto{
    width: 70%;
    padding: 10px;
    height: 25vh;
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.datos-producto-listado{
    display: flex;
}

.dato{
    margin-top:10px;
    margin-right: 10px;
}

.datos-info{
    display: flex;
}

.datos-precios{
    display: flex;
}


@media only screen and (max-width: 1200px) {
    
    .card-producto{
        width: 95%;
        height: 25vh;
        background-color: white;
        border-radius: 15px;
        box-shadow: 0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12);
        display: flex;
    }
    
    .avatar-productos{
        width: 25%;
        height: 25vh;
        border-top-left-radius: 15px;
        border-bottom-left-radius: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .datos-producto{
        width: 65%;
        padding: 20px;
    }

    .datos-producto-listado{
        display: flex;
        flex-direction: column;
    }

    .dato{
        margin-top:10px;
        margin-right: 10px;
    }

    .datos-info{
        display: flex;
    }

    .datos-precios{
        display: flex;
    }

    .list-icons-producto{
        display: flex;
    }

    .icon-producto{
        float: left;
    }
}

@media only screen and (max-width: 960px) {
    .card-producto{
        width: 80%;
        height: 70vh;
        background-color: white;
        border-radius: 15px;
        box-shadow: 0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .avatar-productos{
        width: 20%;
        height: 25vh;
        margin-top: -40px;
        border-top-left-radius: 15px;
        border-bottom-left-radius: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .datos-producto{
        width: 100%;
        padding: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .datos-producto-listado{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .dato{
        margin-top:10px;
        margin-right: 10px;
    }

    .datos-info{
        display: flex;
    }

    .datos-precios{
        display: flex;
    }

    .list-icons-producto{
        display: flex;
        flex-direction: row;
        margin: 0;
        margin-bottom: -30px;
    }

    .icon-producto{
        float: left;
    }

    .titulo-producto{
        display: flex;
        align-items: center;
        flex-direction: column;
    }
}

/* @media only screen and (max-width: 1200px) {
    .container-adm-mensajes{
        padding-left: 40px;
        padding-top: 20px;
        width: 100%;
        min-height: 110vh;
        height: auto;  
        background-color: #EEEEEE;
        background-attachment: fixed;
        padding-bottom: 20px;
        display: flex;
        flex-direction: column;
    }

    .paper-adm-mensajes{
        padding:10px;
        width: 70%;
        height: 280px;
        display: flex;
        justify-content: space-between;
    }

    .list-adm-mensajes{
        display:flex;
        flex-direction:column;
        margin-bottom:10px;
        margin-left:40px;
    }

    .header-adm-mensajes{
        display:flex;
        margin-bottom:10px; 
        margin-left:40px; 
        flex-direction: column;
    }

    .list-icons{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .avatar{
        display: none;
    }
}  */