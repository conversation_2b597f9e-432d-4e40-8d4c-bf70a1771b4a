//w original
// function crearLabels (info) {
    
//     let aux = new Set(info.map((i) => i.fechafactura))
//     let labels = Array.from(aux)
//     let aux2 = ''

//     for(let k=0; k < labels.length; k++){
//         aux2 = labels[k].split('-')
//         labels[k] = aux2[2]+'-'+aux2[1]+'-'+aux2[0]
//     }

//     return labels
// }

//f
// function crearLabels(info) {
//     if (!info || info.length === 0) return [];
    
//     // Try different date field names
//     const dateField = info[0].fechaliquidacion ? 'fechaliquidacion' : 
//                      info[0].fechafactura ? 'fechafactura' : null;
    
//     if (!dateField) return [];

//     let aux = new Set(info.map((i) => i[dateField] ? i[dateField].split(' ')[0] : '').filter(Boolean));
//     let labels = Array.from(aux);
//     let aux2 = '';

//     for (let k = 0; k < labels.length; k++) {
//         aux2 = labels[k].split('-');
//         if (aux2.length === 3) {
//             labels[k] = aux2[2] + '-' + aux2[1] + '-' + aux2[0];
//         }
//     }

//     return labels;
// }

//f with sorted dates
function crearLabels(info) {
    if (!info || info.length === 0) return [];
    
    // Try different date field names
    const dateField = info[0].fechaliquidacion ? 'fechaliquidacion' : 
                     info[0].fechafactura ? 'fechafactura' : null;
    
    if (!dateField) return [];

    // Get unique dates and format them
    let aux = new Set(info.map((i) => i[dateField] ? i[dateField].split(' ')[0] : '').filter(Boolean));
    let labels = Array.from(aux);
    
    // Convert to Date objects for proper sorting
    let dateObjects = labels.map(dateStr => {
        const parts = dateStr.split('-');
        return new Date(parts[0], parts[1] - 1, parts[2]);
    });

    // Sort dates in ascending order
    dateObjects.sort((a, b) => a - b);

    // Convert back to formatted strings
    labels = dateObjects.map(date => {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    });

    return labels;
}

//w original
// function crearData (info,fechas) {

//     let data = []
//     let valor = 0
//     let aux, aux2

//     for(let i = 0; i < fechas.length; i++){
//         for(let j = 0; j < info.length; j++){
//             aux = info[j].fechafactura.split('-')
//             aux2 = aux[2]+'-'+aux[1]+'-'+aux[0]
//             if( aux2 == fechas[i]){
//                 valor += parseInt(info[j].total)
//             }
//         }
//         data.push(valor)
//         valor = 0
//     }

//     return data
// }



//f
// function crearData(info, fechas) {
//     if (!info || info.length === 0 || !fechas || fechas.length === 0) return [];
    
//     // Try different date field names
//     const dateField = info[0].fechaliquidacion ? 'fechaliquidacion' : 
//                      info[0].fechafactura ? 'fechafactura' : null;
//     const totalField = info[0].total ? 'total' : null;
    
//     if (!dateField || !totalField) return [];

//     let data = [];
//     let valor = 0;
//     let aux, aux2;

//     for (let i = 0; i < fechas.length; i++) {
//         for (let j = 0; j < info.length; j++) {
//             if (info[j][dateField]) {
//                 const datePart = info[j][dateField].split(' ')[0];
//                 aux = datePart.split('-');
//                 aux2 = aux[2] + '-' + aux[1] + '-' + aux[0];
//                 if (aux2 === fechas[i]) {
//                     valor += parseInt(info[j][totalField].replace(/\./g, '').replace(',', '.') || 0);
//                 }
//             }
//         }
//         data.push(valor);
//         valor = 0;
//     }

//     return data;
// }

//f with sorted dates

function crearData(info, fechas) {
    if (!info || info.length === 0 || !fechas || fechas.length === 0) return [];
    
    // Try different date field names
    const dateField = info[0].fechaliquidacion ? 'fechaliquidacion' : 
                     info[0].fechafactura ? 'fechafactura' : null;
    const totalField = info[0].total ? 'total' : null;
    
    if (!dateField || !totalField) return [];

    let data = [];
    
    // Process each date in the sorted labels array
    for (let i = 0; i < fechas.length; i++) {
        let valor = 0;
        const targetDate = fechas[i]; // Already in DD-MM-YYYY format
        
        for (let j = 0; j < info.length; j++) {
            if (info[j][dateField]) {
                const datePart = info[j][dateField].split(' ')[0];
                const parts = datePart.split('-');
                const formattedDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
                
                if (formattedDate === targetDate) {
                    const amount = info[j][totalField];
                    valor += parseFloat(
                        amount.replace(/\./g, '').replace(',', '.')
                    ) || 0;
                }
            }
        }
        data.push(valor);
    }

    return data;
}

function totalVentas (info) {

    let total = 0

    for(let i=0; i < info.length; i++){
        total += parseInt(info[i].total)
    }

    return {total: total, cantidad: info.length}
}

function matchMoneda(moneda,monedas){
    let id = monedas.filter((c) => c.nombre == moneda )

    return id[0].monedaid
}


module.exports = {
    crearLabels,
    crearData,
    totalVentas,
    matchMoneda
}