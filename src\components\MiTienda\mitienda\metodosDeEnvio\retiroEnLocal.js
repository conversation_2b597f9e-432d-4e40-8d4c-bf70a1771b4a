import React, { useState } from "react";
import Button from '@mui/material/Button';
import { Divider, Paper  } from "@mui/material";

import "./metodosDeEnvio.css"
import { useDispatch, useSelector } from "react-redux";
import { configRetiroEnLocal } from "../../../../redux/actions/mitienda.actions";

export const RetiroEnLocal = () => {

    const retiroEnLocal = useSelector((state) => state.mitienda.okDefaultRetiroLocal)

    const dispatch = useDispatch()

    const [ok, setOk] = useState(0)

    const handleClick = () => {
        setOk(ok === 0 ? 1 : 0)
        dispatch(configRetiroEnLocal(ok))
    }

    return (
        <div className="container-retiro-local">
            <div className="header-retiro-local">
                <h3>Retiros en local</h3>
                {retiroEnLocal && ok === 1 ? <h5 style={{color:"green"}}>Activado con exito!</h5> : null}
                <Button 
                    size="large" 
                    variant="contained"
                    className="btn-miplan"
                    onClick={handleClick}
                >{ok === 0 ? "Activar" : "Desactivar"}</Button>
            </div>
           <h4 style={{color:"grey", width:"95%", marginLeft:35}}>En el momento de la compra, los clientes tienen la opci&oacute;n
            de elegir en que local o domicilio poder retirarla.
           </h4>
           {
                ok === 1 ?
                <Paper sx={{height:"50%", marginTop:5, width:"70%"}}>
                    <h4 style={{padding:10}}>Mis locales</h4>
                    <Divider/>
                    <h5 style={{padding:10}}>No hay datos de tus locales</h5>
                </Paper>
                : null
           }
        </div>
    )
}