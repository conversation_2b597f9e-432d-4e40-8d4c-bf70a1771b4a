import React from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from '@mui/material';

export const ModalFiltrosRemito = ({
  show,
  handleClose,
  clientId,
  handleChangeClientId,
  fechaDesde,
  handleChangeFechaDesde,
  fechaHasta,
  handleChangeFechaHasta,
  search,
  reset,
}) => {
  return (
    <Dialog open={show} onClose={handleClose}>
      <DialogTitle>Filtros</DialogTitle>
      <DialogContent>
        <TextField
          label="ID Cliente"
          variant="outlined"
          fullWidth
          margin="normal"
          value={clientId}
          onChange={handleChangeClientId}
        />
        <TextField
          label="Fecha desde"
          type="date"
          variant="outlined"
          fullWidth
          margin="normal"
          value={fechaDesde}
          onChange={handleChangeFechaDesde}
          InputLabelProps={{ shrink: true }}
        />
        <TextField
          label="Fecha hasta"
          type="date"
          variant="outlined"
          fullWidth
          margin="normal"
          value={fechaHasta}
          onChange={handleChangeFechaHasta}
          InputLabelProps={{ shrink: true }}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={reset}>Limpiar</Button>
        <Button onClick={search} variant="contained">
          Buscar
        </Button>
      </DialogActions>
    </Dialog>
  );
};