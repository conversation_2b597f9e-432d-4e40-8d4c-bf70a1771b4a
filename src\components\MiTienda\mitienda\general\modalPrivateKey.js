import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";

Modal.setAppElement("#root")

export const ModalPrivateKey = ({show, handleClose, setPrivateKey}) =>{

    const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    return ( show && 
    <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
      <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
        <h4>Private key</h4>
        <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
      </div>
      <Divider/>
        <div style={{marginTop:15, marginBottom:15}}>
          <TextField 
            variant="standard"
            fullWidth margin="normal" 
            onChange={(e)=>setPrivateKey(e.target.files[0])}
            type="file"
            InputLabelProps={{
              style: {
                marginBottom:10,
                marginTop: -7
              },
            }}
            inputProps={{
              style: {
                height: 40,
                fontSize:17
              },
            }}
          />
        </div>
      <Divider/>
      <div align="right">
      <Button 
        size="large" 
        variant="contained" 
        sx={{mt: 3}}
        onClick={handleClose}
      >Guardar</Button>
      </div>
    </Modal>
  )
}