import React, { useEffect, useState } from "react";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  InputAdornment,
  ListSubheader,
  IconButton,
} from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import Pagination from "@mui/material/Pagination";
import { useDispatch, useSelector } from "react-redux";
import {
  getCuentasCorrientes_2,
  getListadoClientesByName,
} from "../../../../redux/actions/mitienda.actions";
import { Search, Clear } from "@mui/icons-material";
import ClipLoader from "react-spinners/ClipLoader";
import FilterListIcon from "@mui/icons-material/FilterList";
import { ModalFiltrosReporteCuentasCorrientes } from "./modalFiltrosReporteCuentasCorrientes";

export const ReporteCuentasCorrientes = () => {
  const info = useSelector((state) => state.mitienda.cuentasCorrientes);
  const clientes = useSelector((state) => state.mitienda.clientesByName);
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaCuentasCorrientes
  );

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const [show, setShow] = useState(false);
  const [codigo, setCodigo] = useState("");

  // Add date filter states with default values
  const [fechaPedido, setFechaPedido] = useState(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth(), today.getDate())
      .toISOString()
      .split("T")[0];
  });

  // Client search states
  const [clienteInput, setClienteInput] = useState("");
  const [clienteid, setClienteid] = useState("");
  const [selectedOption, setSelectedOption] = useState({
    nombre: "",
    apellido: "",
  });

  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(false);
  }, [info]);

  useEffect(() => {
    if (clienteInput.length > 1) {
      dispatch(getListadoClientesByName(clienteInput, ""));
    }
  }, [clienteInput]);

  useEffect(() => {
    setLoading(true);
    dispatch(getCuentasCorrientes_2(page, clienteid, codigo, fechaPedido));
  }, [page]);

  const handleClose = () => setShow(false);

  const handleChangeCodigo = (e) => {
    setCodigo(e.target.value);
    setPage(1);
    dispatch(getCuentasCorrientes_2(1, clienteid, e.target.value, fechaPedido));
  };

  const handleChangeFechaPedido = (e) => {
    setFechaPedido(e.target.value);
  };

  const searchByFilters = () => {
    setPage(1);
    dispatch(getCuentasCorrientes_2(1, clienteid, codigo, fechaPedido));
    handleClose();
  };

  const reset = () => {
    const today = new Date();
    const formattedToday = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      .toISOString()
      .split("T")[0];

    setFechaPedido(formattedToday);
    setPage(1);
    setCodigo("");
    setClienteid("");
    setClienteInput("");
    setSelectedOption({ nombre: '', apellido: '' });
    dispatch(getCuentasCorrientes_2(1, "0", "0", "")); // Using "0" instead of empty string
    handleClose();
  };

  return (
    <div className="notificaciones-container">
      <div style={{ padding: "20px" }}>
        <h3>Reporte de Cuentas Corrientes</h3>
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            alignItems: "center",
          }}
        >
          <Button
            variant="outlined"
            onClick={() => setShow(true)}
            startIcon={<FilterListIcon />}
          >
            Filtros
          </Button>

          <FormControl sx={{ minWidth: 300 }}>
            <Select
              size="small"
              value={clienteid}
              onChange={(e) => {
                setClienteid(e.target.value);
                setPage(1);
              }}
              displayEmpty
              renderValue={() =>
                clienteid
                  ? `${selectedOption.nombre} ${selectedOption.apellido}`
                  : "Buscar cliente..."
              }
              onOpen={() => {
                setClienteInput("");
              }}
            >
              <ListSubheader>
                <TextField
                  size="small"
                  autoFocus
                  placeholder="Buscar cliente..."
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  onChange={(e) => setClienteInput(e.target.value)}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onKeyDown={(e) => {
                    e.stopPropagation();
                  }}
                  value={clienteInput}
                />
              </ListSubheader>
              {clientes &&
                clientes.map((option) => (
                  <MenuItem
                    key={option.clienteid}
                    value={option.clienteid}
                    onClick={() => {
                      setSelectedOption(option);
                      setClienteInput("");
                    }}
                  >
                    {option.nombre} {option.apellido}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <TextField
            size="small"
            placeholder="Buscar por Nº Pedido..."
            value={codigo}
            onChange={handleChangeCodigo}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {codigo && (
                    <IconButton size="small" onClick={() => {
                      setCodigo("");
                      setPage(1);
                      dispatch(getCuentasCorrientes_2(1, clienteid, "", fechaPedido));
                    }}>
                      <Clear />
                    </IconButton>
                  )}
                </InputAdornment>
              ),
              style: {
                height: "40px",
              },
            }}
            sx={{
              minWidth: 200,
              "& .MuiOutlinedInput-root": {
                padding: "0 8px",
              },
            }}
          />
        </div>
      </div>

      <ModalFiltrosReporteCuentasCorrientes
        show={show}
        handleClose={handleClose}
        search={searchByFilters}
        reset={reset}
        fechaPedido={fechaPedido}
        handleChangeFechaPedido={handleChangeFechaPedido}
      />

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: "95%",
          marginTop: 5,
          marginLeft: 50,
        }}
      >
        <TableContainer
          component={Paper}
          sx={{ margin: 5, width: "100%", alignSelf: "center" }}
        >
          <Table aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Cliente
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Nro. de Cliente
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Fecha emisión Factura
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Tipo
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Nro. de Folio
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Remito
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Vencimiento de Remito
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Días de mora
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Moneda
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Monto
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Pendiente
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  tc
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Observaciones
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Original ARS
                </TableCell>
                <TableCell
                  style={{ fontWeight: "bold", fontSize: "90%", padding: 2 }}
                  align="center"
                >
                  Saldo ARS
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow sx={{ p: 20 }}>
                  <TableCell colSpan={15} align="center">
                    <ClipLoader loading={loading} size={50} />
                  </TableCell>
                </TableRow>
              ) : info.data && info.data.length > 0 ? (
                info.data.map((row) => (
                  <TableRow
                    key={row.clienteid + row.codigo}
                    sx={{
                      "&:last-child td, &:last-child th": { border: 0 },
                    }}
                  >
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 40,
                        width: 200,
                      }}
                      align="center"
                    >
                      {row.nombre || "-"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 80,
                      }}
                      align="center"
                    >
                      {row.clienteid}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 80,
                      }}
                      align="center"
                    >
                      {row.fechaComprobante || "-"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 80,
                      }}
                      align="center"
                    >
                      {row.origen || "-"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 80,
                      }}
                      align="center"
                    >
                      {row.nro || "-"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 80,
                      }}
                      align="center"
                    >
                      {row.nro_remito || "-"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 80,
                      }}
                      align="center"
                    >
                      {row.fechavencimiento || "-"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 50,
                      }}
                      align="center"
                    >
                      {row.dias_mora || "0"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 50,
                      }}
                      align="center"
                    >
                      {row.moneda || "-"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 90,
                      }}
                      align="center"
                    >
                      ${row.totalcomprobante || "0,00"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 90,
                      }}
                      align="center"
                    >
                      ${row.totalrestanteafectar || "0,00"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 90,
                      }}
                      align="center"
                    >
                      {row.tipo_cambio || "1"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 90,
                      }}
                      align="center"
                    >
                      {row.observaciones || "-"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 90,
                      }}
                      align="center"
                    >
                      ${row.original_ars || "0,00"}
                    </TableCell>
                    <TableCell
                      style={{
                        fontSize: "80%",
                        padding: 0,
                        height: 45,
                        width: 90,
                      }}
                      align="center"
                    >
                      ${row.saldo_ars || "0,00"}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={15} align="center">
                    <h5>No hay información para mostrar</h5>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {paginaUltima > 1 && (
          <Pagination
            count={paginaUltima}
            page={page}
            onChange={handleChangePage}
            size="large"
            sx={{ alignSelf: "center" }}
          />
        )}
      </div>
    </div>
  );
};
