import React, { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { getReporteListaDePrecios } from '../../../../redux/actions/mitienda.actions'
import { ModalFiltrosListaDePrecios } from './modalFiltrosListaDePrecios';
import ClipLoader from "react-spinners/ClipLoader";

export const ReporteListaDePrecios = () => {

    //Obtengo la informacion para la tabla
    const info = useSelector((state) => state.mitienda.reporteListaDePrecios)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    //Obtengo la ultima pagina para el componente Pagination
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaListaDePreciosPaginado)
    //Creo la constante que usare para cambiar las paginas y su handle change
    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    //Declaro la variable para despachar acciones
    const dispatch = useDispatch()

    //Manejo el abrir y cerrar Modal de Filtros
    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    //Declaro las constantes que utilizare para filtrar la informacion
    const [marcaid, setMarcaid] = useState(0)
    const handleChangeMarcaid = (e) =>{
        setMarcaid(e.target.value)
    }

    const [categoriaid, setCategoriaid] = useState(0)
    const handleChangeCategoriaid = (e) =>{
        setCategoriaid(e.target.value)
    }

    const [codigo, setCodigoArticulo] = useState('')
    const handleCodigoArticulo = (e) =>{
        setCodigoArticulo(e.target.value)
    }

    const [nombre, setNombre] = useState('')
    const handleChangeNombre = (e) =>{
        setNombre(e.target.value)
    }
    const [listaid, setLista] = useState(0)
    const handleChangeLista = (e) =>{
        setLista(e.target.value)
    }

    //Funcion para el boton Aplicar filtrps
    const searchByFilters = () =>{
        setPage(1)
        dispatch(getReporteListaDePrecios(marcaid,categoriaid,nombre,listaid, codigo, page))
        handleClose()
    }

    //Funcion para el boton Limpiar filtros, se reinician las constantes a sus valores iniciales
    const reset = (e) =>{
        setCategoriaid(0)
        setMarcaid(0)
        setCodigoArticulo(0)
        setNombre('')
        setLista(0)
    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() => {
        setLoading(true)
        dispatch(getReporteListaDePrecios(marcaid, categoriaid, nombre, listaid, codigo, page))
    },[page])

    return (
        <div className="notificaciones-container">
            {
                <ModalFiltrosListaDePrecios
                    show={show}
                    handleClose={handleClose}
                    search={searchByFilters}
                    reset={reset}
                    setMarca={setMarcaid}
                    setCategoriaid={setCategoriaid}
                    codigo={codigo}
                    handleChangeCodigo={handleCodigoArticulo}
                    nombre={nombre}
                    handleChangeNombre={handleChangeNombre}
                    lista={listaid}
                    handleChangeLista={handleChangeLista}
                />
            }
            <div>
                <header className="titulo-notificaciones">
                    <h3>Reporte de lista de precio</h3>
                    <Button
                        variant="outlined"
                        sx={{height:40,width:150}}
                        onClick={handleShow}>
                        <FilterListIcon/> Filtrar
                    </Button>
                </header>
            </div>
            {
                permisos_acciones?.listar !== "0" && 
                <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">C&oacute;digo</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Marca</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Categor&iacute;a</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Nombre</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Estilo</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Color</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Medida</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Disponible</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Reservado</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Precio S/IVA</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">IVA</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Precio Vta</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Descuento %</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                    {   
                        loading ? 
                        <TableRow sx={{p:20}}>
                            <TableCell colSpan={13} align='center'>
                                <ClipLoader 
                                    loading={loading}
                                    size={50}
                                />
                            </TableCell>
                        </TableRow> :
                        info.data.length > 0 ? info.data.map((row) => (
                        <TableRow
                            key={row.productoid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                            style={{backgroundColor: row.colorfila || 'white'}}
                        >
                            <TableCell style={{fontSize:"80%", padding:0, height:40, width:100}} align="center">
                                {row.codigoarticulo}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombremarca}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombrecategoria}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:140}} align="center">{row.nombre}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">{row.nombreestilo}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">{row.nombrecolor}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">{row.nombremedida}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.cantidadactiva}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.cantidadpasiva}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.preciosiniva}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:60}} align="center">{row.ivaproducto}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.precioVenta}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.descuento}</TableCell>
                        </TableRow>
                        )):
                        <TableRow>
                            <TableCell colSpan={12}>
                                <h5>No hay informaci&oacute;n para mostrar</h5>
                            </TableCell>
                        </TableRow>}
                    </TableBody>
                    </Table>
                </TableContainer>
                <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
            </div>
            }
        </div>
    )
}
