import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { But<PERSON>, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { editarSubCategoria } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const EditarSubCategoria = ({show, handleClose, subcategoria, handleClickAlert}) =>{

  const ok = useSelector((state) => state.mitienda.editarSubCategoria)

  const [input, setInput] = useState()

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value})
  }

  const dispatch = useDispatch()

  const handleClick = (e) => {
    e.preventDefault()
    dispatch(editarSubCategoria(input))
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    useEffect(() => {
        setInput(subcategoria)
    }, [subcategoria])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                {
                  ok && <h5 style={{color:"green"}}>Editado satisfactoriamente!</h5>
                }
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Editar subcategoria</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{marginTop:15, marginBottom:15}}>
                  <TextField 
                      name="nombre"
                      label="Nombre"  
                      variant="outlined" 
                      fullWidth margin="normal" 
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                      value={input.nombre || ''}
                      onChange={(e)=> handleChange(e)}
                  />
                </div>
                <Divider/>
                <div align="right">
                <Button 
                  size="large" 
                  variant="contained" 
                  sx={{mt: 3}}
                  onClick={handleClick}
                  >Editar</Button>
                </div>
        </Modal>
    )
}