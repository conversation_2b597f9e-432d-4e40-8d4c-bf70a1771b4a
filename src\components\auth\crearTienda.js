import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Button, Paper, Snackbar, TextField } from "@mui/material"

import logo from '../../media/logo_develshops.png'

import { registrarTienda } from '../../redux/actions/user.actions'

import MuiAlert from '@mui/material/Alert';
import ReCAPTCHA from "react-google-recaptcha";
import "./styles.css"

export const RegistrarTienda = () => {

    const ok = useSelector((state) => state.user.okRegistrarTienda)

    const [input, setInput] = useState({
        nombredelatienda:'',
        email:'',
        clave: '',
        confirmarclave: '',
        tipocontribuyente: "4",
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const dispatch = useDispatch()

    const recaptcha = useRef();
    const [open2, setOpen2] = React.useState(false);
    const handleClickAlert2 = () => {
      setOpen2(true);
    };
    const handleCloseAlert2 = () => {
        setOpen2(false);
    };

    const handleLogin = () => {
        const captchaValue = recaptcha.current.getValue();
        if (!captchaValue) {
            handleClickAlert2()
        } else {
            dispatch(registrarTienda(input))
            setTimeout(function(){
                handleClickAlert()
            }, 3000);
            setInput({
                nombredelatienda:'',
                email:'',
                clave: '',
                confirmarclave: '',
                tipocontribuyente: "4",
            })
        }
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    useEffect(() =>{
        setInput({
            nombredelatienda:'',
            email:'',
            clave: '',
            confirmarclave: '',
            tipocontribuyente: "4",
        })
    },[])

    return(
        <div className="window">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success ? "success" : "error"}
                        sx={{ width: 500 }}>
                        <h5>{ok.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={open2} 
                    autoHideDuration={10000} onClose={handleCloseAlert2}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert2} 
                        severity="warning"
                        sx={{ width: 500 }}>
                        <h5>Debe verificar el captcha!</h5>
                    </Alert>
                </Snackbar>
            }
           <Paper className="form-login">
                <img className="logo-login" src={logo} alt="develone"/>
                    <div className="text-field">
                        <h6>Nombre de la tienda</h6>
                        <TextField
                            name="nombredelatienda"
                            fullWidth
                            value={input.nombredelatienda}
                            onChange={(e) => handleChange(e)}
                            size="medium"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                height: 20,
                                fontSize:17
                                },
                            }}
                        />  
                    </div>
                    <div className="text-field">
                        <h6>Email</h6>
                        <TextField
                            name="email"
                            fullWidth
                            value={input.email}
                            onChange={(e) => handleChange(e)}
                            size="medium"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                height: 20,
                                fontSize:17
                                },
                            }}
                        />  
                    </div>
                    <div className="text-field">
                        <h6>Contrase&ntilde;a</h6>
                        <TextField
                            name="clave"
                            type="password"
                            fullWidth
                            value={input.clave}
                            onChange={(e) => handleChange(e)}
                            size="medium"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                height: 20,
                                fontSize:17
                                },
                            }}
                        />  
                    </div>
                    <div className="text-field">
                        <h6>Confirmar contrase&ntilde;a</h6>
                        <TextField
                            name="confirmarclave"
                            type="password"
                            fullWidth
                            value={input.confirmarclave}
                            onChange={(e) => handleChange(e)}
                            size="medium"
                            error={input.clave !== input.confirmarclave}
                            helperText={input.clave !== input.confirmarclave && "Las contraseñas no coinciden"}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                height: 20,
                                fontSize:17
                                },
                            }}
                        />  
                    </div>    
                    <div style={{margin:10}}>
                        <ReCAPTCHA
                            sitekey={process.env.REACT_APP_RECAPTCHA_KEY}
                            ref={recaptcha}
                        /> 
                    </div>  
                    <Button variant="contained" size="large" onClick={handleLogin} 
                    disabled={input.nombredelatienda === '' || 
                        input.clave === '' || input.email === ''
                    }>Registrar</Button>
                <h6 style={{marginTop:40}}>Desarrollado por Develone</h6>
           </Paper>
            <div className="welcome">
                <h2 className="h2">
                Bienvenido a <br /> 
                    <span className="software">
                    {process.env.REACT_APP_TITLE_LOG_IN}
                    </span>
                </h2>
            </div>
        </div>
    )
}