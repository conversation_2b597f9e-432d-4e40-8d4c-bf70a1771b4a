import React from "react";
import { Button, Checkbox, FormControlLabel, InputAdornment, Paper, Snackbar, TextField } from "@mui/material"
import { ContentCopy, Done } from "@mui/icons-material";
import MuiAlert from '@mui/material/Alert';
import logo from '../../../../media/google-analytics-logo.png'

export const FacebookPixel =()=>{


    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const [open, setOpen] = React.useState(false);

    const handleClickAlert = () => {
      setOpen(true);
    };
  
    const handleCloseAlert = () => {
        setOpen(false);
    };

    return (
        <section className="container-facebook-pixel">
            <div className="container-form">
                <header className="header-meta-ads">
                    <h3 style={{marginTop:20, marginBottom:20, display:"flex"}}>
                    Facebook Pixel
                    </h3>
                    <div>
                        <h5>Es una herramienta que te ayuda a medir y optimizar tus campa&ntilde;as publicitarias en esa plataforma.
                        </h5>
                        <h5>Te permite medir las conversiones con el fin de monitorizar y analizar las acciones que realizan
                            los usuarios en tu tienda tras ingresar a ella mediante tu anuncio de Facebook.
                        </h5>
                    </div>
                </header>
                <Paper className="form-meta-ads">
                    <TextField 
                        helperText="Pegar ac&aacute; el c&oacute;digo brindado por Facebook"
                        label="C&oacute;digo (opcional)"  
                        fullWidth margin="normal" 
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 50,
                            fontSize:17
                            },
                        }}
                    />
                </Paper>
                <Paper className="texto-facebook-pixel">
                    <h4>Eventos que se activan autom&aacute;ticamente al hacer click en el check.</h4>
                    <br/>
                    <p>
                        <h5>Agregar al carrito / AddToCart</h5>
                        <h6>Mide cuando una persona agregar un producto al carrito.</h6>
                        <h6>Indica autom&aacute;ticamente el total en pesos y el id del producto.</h6>
                    </p>
                    <br/>
                    <p>
                        <h5>Contacto / Contact</h5>
                        <h6>Mide cuando una persona env&iacute;a un formulario de contacto o consulta por un producto.</h6>
                    </p>
                    <br/>
                    <p>
                        <h5>Iniciar compra / InitiateCheckout</h5>
                        <h6>Mide cuando una persona hace click en "Iniciar compra".</h6>
                        <h6>Indica autom&aacute;ticamente el total en pesos y el id de/los producto/s de la orden.</h6>
                    </p>
                    <br/>
                    <p>
                        <h5>Compra finalizada / Purchase</h5>
                        <h6>Mide cuando una persona finaliza la compra y es dirigida a la p&aacute;gina de "Gracias por tu compra".</h6>
                        <h6>Indica autom&aacute;ticamente el total en pesos y el id de/los producto/s de la orden.</h6>
                    </p>
                    <br/>
                    <p>
                        <h5>Buscar producto / Search</h5>
                        <h6>Mide cuando una persona utiliza el buscador de la tienda.</h6>
                        <h6>Indica autom&aacute;ticamente el id de/los producto/s que aparecen en la b&uacute;squeda.</h6>
                    </p>
                    <br/>
                    <p>
                        <h5>Ver producto / ViewContent</h5>
                        <h6>Mide cuando una persona entra a ver un producto en particular.</h6>
                        <h6>Indica autom&aacute;ticamente el total en pesos y el id del producto.</h6>
                    </p>
                    <FormControlLabel
                        style={{marginTop:30}}
                        control={
                            <Checkbox/>
                        }
                        label="Activar autom&aacute;ticamente los eventos de Pixel de facebook. Record&aacute; eliminar los que hayas configrado manualmente para no replicar informaci&oacute;n"
                    />
                </Paper>
            <div style={{alignSelf:"start"}}>
                <Button 
                    size="large" 
                    variant="contained" 
                    disabled={permisos_acciones?.agregar === "0" || permisos_acciones?.modificar === "0"}
                    sx={{mt: 3, mb:3}}>Guardar</Button>
            </div>
            </div>
        </section>
    )
}