import React, { useEffect, useState } from "react";
import '../../MiTienda/mitienda.css'
import { useDispatch, useSelector } from "react-redux";
import { Button, Checkbox, FormControlLabel, Pagination, Paper, Snackbar, TextField } from "@mui/material";
import { agregarDescuento, getDescuentos } from "../../../redux/actions/mitienda.actions";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import MuiAlert from '@mui/material/Alert';
import { Check, Clear, Edit } from "@mui/icons-material";
import { ModalEditarDescuento } from "./editarDescuento";
import ClipLoader from "react-spinners/ClipLoader";

export const Descuentos = () =>{

    const info = useSelector((state) => state.mitienda.descuentos)
    const ok = useSelector((state) => state.mitienda.okDescuento)
    const okEditar = useSelector((state) => state.mitienda.okEditarDescuento)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaDescuentos)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const dispatch = useDispatch()

    const [input, setInput] = useState({
        nombre:'',
        porcentaje: '',
        activo: '0',
        ecommerce: "0"
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }
    
    const handleClick = () => {
        dispatch(agregarDescuento(input))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        setInput({
            nombre:'',
            porcentaje: '',
            activo: '0',
        })
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [descuento, setDescuento] = useState('')

    const [showEditar, setShowEditar] = useState(false);
    const handleCloseEditar = () => setShowEditar(false);
    const handleShowEditar = (e,row) => {
        e.preventDefault()
        setShowEditar(true);
        setDescuento(row)
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openEditar, setOpenEditar] = React.useState(false);
    const handleClickAlertEditar = () => {
      setOpenEditar(true);
    };
    const handleCloseAlertEditar = () => {
        setOpenEditar(false);
    };


    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        setLoading(true)
        dispatch(getDescuentos(page))
    },[ok, okEditar, page])

    return (
        <div className="container-default">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{ok.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEditar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditar} 
                        severity={okEditar.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEditar.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <ModalEditarDescuento 
                    show={showEditar}   
                    handleClose={handleCloseEditar}   
                    descuento={descuento}
                    handleClickAlert={handleClickAlertEditar}
                />
            }
            <header className="header-default">
                <h3>Agregar descuento</h3>
            </header>
            <Paper className="paper-form">
            <TextField 
                name="nombre"
                label="Nombre descuento"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.nombre || ''}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                name="porcentaje"
                label="Porcentaje"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.porcentaje || ''}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <div style={{marginTop:5, marginBottom:5}}>
                <FormControlLabel
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black"}}
                    onChange={(e)=> handleCheck(e)}
                    checked={input.activo == "1" ? true : false}
                    label="Activo"
                    name="activo"
                />
            </div>
            <div style={{marginTop:5, marginBottom:5}}>
                <FormControlLabel
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black"}}
                    onChange={(e)=> handleCheck(e)}
                    checked={input.ecommerce == "1" ? true : false}
                    label="Ecommerce"
                    name="ecommerce"
                />
            </div>
            </Paper>
            <div align="right">
                <Button 
                    disabled={
                        input.nombre === '' ||
                        input.porcentaje === '' ||
                        permisos_acciones?.agregar === "0"
                    }
                    size="large" 
                    variant="contained" 
                    onClick={(e)=>(handleClick(e))}
                    sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>

            {
                permisos_acciones?.listar !== "0" &&  <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                            <TableContainer component={Paper} style={{width:"95%", alignSelf:"center", marginTop:40, marginLeft:40}}>
                                <Table aria-label="simple table">
                                <TableHead>
                                    <TableRow>
                                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Nombre</TableCell>
                                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Descuento</TableCell>
                                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Activo</TableCell>
                                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Acciones</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                {
                            loading ? 
                                <TableRow sx={{p:20}}>
                                    <TableCell colSpan={13} align='center'>
                                        <ClipLoader 
                                            loading={loading}
                                            size={50}
                                        />
                                    </TableCell>
                                </TableRow> :                                
                                info.length > 0 ? info.map((row) => (
                                    <TableRow
                                        key={row.descuentovtaid}
                                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                    >
                                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                                            {row.nombre}
                                        </TableCell>
                                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                                            {row.descuento}
                                        </TableCell>
                                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                                            {row.activo === "1" ? 
                                                <Check color="success"/> :
                                                <Clear color="error"/>
                                            }
                                        </TableCell>
                                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                                            <Button
                                            style={{color:"black"}} disabled={permisos_acciones?.modificar === "0"}
                                            onClick={(e)=>handleShowEditar(e,row)}
                                            ><Edit/></Button>
                                        </TableCell>
                                    </TableRow> 
                                    )):
                                    <TableRow>
                                        <TableCell colSpan={2}></TableCell>
                                        <TableCell colSpan={1}>
                                            <h5>No hay informaci&oacute;n para mostrar</h5>
                                        </TableCell>
                                        <TableCell></TableCell>
                                    </TableRow>}
                                </TableBody>
                                </Table>
                            </TableContainer>
                            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", marginTop:5}} />
                        </div>
            }
        </div>
    )
}