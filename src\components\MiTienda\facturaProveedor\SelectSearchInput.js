import React, { useEffect, useState } from "react"
import './ofertas.css'

export const SelectSearchInput = ({
    info,
    value,
    setValue,
    setProductoId,
    open, 
    setOpen,
    setPrecio,
}) =>{

    return (
        <div className="input-grup-ofertas">
            <input onClick={(e)=> {
                setOpen(true)
            }} value={value} onChange={(e)=> setValue(e.target.value)}/>
            <div className="list-info"  > 
                {
                    info.length > 0 && open ? 
                    info.map((i) =>
                        <div className="menu-items" onClick={(e)=> {
                            setProductoId(i.productoid)
                            setValue(i.nombreproducto)
                            setPrecio(i.precioVenta)
                            setOpen(false)
                        }}>
                            {i.nombreproducto}
                        </div>
                    ) : null
                }
            </div>  
        </div>
    )
}