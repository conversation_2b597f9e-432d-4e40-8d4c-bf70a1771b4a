import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, FormControl, InputLabel, MenuItem, Select, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { editarCotizacion, getMonedas } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalEditarCotizacion = ({show, handleClose, cotizacion, handleClickAlert}) =>{

    const monedas = useSelector((state) => state.mitienda.monedas)

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
      };

    const [input, setInput] = useState()

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }   

    const dispatch = useDispatch()

    const handleClick = () => {
        dispatch(editarCotizacion(input))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        handleClose()
    }   

    useEffect(() => {
        setInput(cotizacion)
    }, [cotizacion])

    useEffect(() => {
        dispatch(getMonedas())
    },[])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Editar descuento</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{marginTop:15, marginBottom:15}}>
                <FormControl sx={{mb:2, mt:1}} fullWidth>
                    <InputLabel id="monedalabel">Moneda</InputLabel>
                    <Select
                    labelId="monedalabel"
                    id="moneda"
                    label="Moneda"
                    size="small"
                    name="monedaid"
                    value={input.monedaid || ''}
                    onChange={handleChange}
                    >
                    <MenuItem value={0}>Seleccione una opcion</MenuItem>
                    {
                        monedas && monedas.map((m) => 
                            <MenuItem value={m.monedaid} key={m.monedaid}>{m.nombre}</MenuItem>
                        )
                    }
                    </Select>
                </FormControl>
                <TextField 
                    name="fecha"
                    label="Fecha"  
                    type="date"
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    value={input.fecha || ''}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <TextField 
                    name="valorcompra"
                    label="Valor compra"  
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    value={input.valorcompra || ''}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <TextField 
                    name="valorventa"
                    label="Valor venta"  
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    value={input.valorventa || ''}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
            <div align="right">
                <Button 
                disabled={
                    input.moneda === '' ||
                    input.fecha === '' ||
                    input.valorcompra === '' ||
                    input.valorventa === ''
                }
                size="large" 
                variant="contained" 
                onClick={(e)=>(handleClick(e))}
                sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>
        </div>
    </Modal>
    )
}