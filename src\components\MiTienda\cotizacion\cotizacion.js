import React, { useEffect, useState } from "react";
import '../../MiTienda/mitienda.css'
import { useDispatch, useSelector } from "react-redux";
import { Button, FormControl, InputLabel, MenuItem, Pagination, Paper, Select, Snackbar, TextField } from "@mui/material";
import { agregarCotizacion, getCotizaciones, getMonedas } from "../../../redux/actions/mitienda.actions";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import MuiAlert from '@mui/material/Alert';
import { Edit } from "@mui/icons-material";
import { ModalEditarCotizacion } from "./editarCotizacion";
import ClipLoader from "react-spinners/ClipLoader";

export const Cotizacion = () =>{

    const info = useSelector((state) => state.mitienda.cotizaciones)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaCotizaciones)
    const monedas = useSelector((state) => state.mitienda.monedas)
    const ok = useSelector((state) => state.mitienda.okCotizacion)
    const okEditar = useSelector((state) => state.mitienda.okEditarCotizacion)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const dispatch = useDispatch()

    let fechaActual = new Date().toJSON().slice(0,10)

    const [input, setInput] = useState({
        moneda: '',
        fecha: fechaActual,
        valorCompra: '',
        valorVenta: ''
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }
    
    const handleClick = () => {
        dispatch(agregarCotizacion(input))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        setInput({
            moneda: '',
            fecha: fechaActual,
            valorCompra: '',
            valorVenta: '',
        })
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [cotizacion, setCotizacion] = useState('')

    const [showEditar, setShowEditar] = useState(false);
    const handleCloseEditar = () => setShowEditar(false);
    const handleShowEditar = (e,row) => {
        e.preventDefault()
        setShowEditar(true);
        setCotizacion(row)
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openEditar, setOpenEditar] = React.useState(false);
    const handleClickAlertEditar = () => {
      setOpenEditar(true);
    };
    const handleCloseAlertEditar = () => {
        setOpenEditar(false);
    };

    const formatoFecha = (fecha) =>{
        let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() => {
        dispatch(getMonedas())
    },[])

    useEffect(() =>{
        setLoading(true)
        dispatch(getCotizaciones(page))
    },[ok, okEditar, page])

    return (
        <div className="container-default">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{ok.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEditar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditar} 
                        severity={okEditar.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEditar.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <ModalEditarCotizacion
                    show={showEditar}   
                    handleClose={handleCloseEditar}   
                    cotizacion={cotizacion}
                    handleClickAlert={handleClickAlertEditar}
                />
            }
            <header className="header-default">
                <h3>Cotizar moneda</h3>
            </header>
            <Paper className="paper-form">
            <a href="https://dolarhoy.com/" style={{width:200}} target="_blank">
                Ver cotizaciones
            </a>
            <FormControl sx={{mb:2, mt:1}} fullWidth>
                <InputLabel id="monedalabel">Moneda</InputLabel>
                <Select
                labelId="monedalabel"
                id="moneda"
                label="Moneda"
                size="small"
                name="moneda"
                value={input.moneda || ''}
                onChange={handleChange}
                >
                <MenuItem value={0}>Seleccione una opcion</MenuItem>
                {
                    monedas && monedas.map((m) => 
                        <MenuItem value={m.monedaid} key={m.monedaid}>{m.nombre}</MenuItem>
                    )
                }
                </Select>
            </FormControl>
            <TextField 
                name="fecha"
                label="Fecha"  
                type="date"
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.fecha || ''}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                name="valorCompra"
                label="Valor compra"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.valorCompra || ''}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                name="valorVenta"
                label="Valor venta"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.valorVenta || ''}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            </Paper>
            <div align="right">
                <Button 
                    disabled={
                        input.moneda === '' ||
                        input.fecha === '' ||
                        input.valorCompra === '' ||
                        input.valorVenta === '' ||
                        permisos_acciones?.agregar === "0"
                    }
                    size="large" 
                    variant="contained" 
                    onClick={(e)=>(handleClick(e))}
                    sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>
            {
                permisos_acciones?.listar !== "0" &&
                <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>

            <TableContainer component={Paper} style={{width:"95%", alignSelf:"center", marginTop:40, marginLeft:40}}>
                <Table aria-label="simple table">
                <TableHead>
                    <TableRow>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Moneda</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Fecha</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Compra</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Venta</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Acciones</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                {
                    loading ? 
                    <TableRow sx={{p:20}}>
                        <TableCell colSpan={13} align='center'>
                            <ClipLoader
                                loading={loading}
                                size={50}
                            />
                        </TableCell>
                    </TableRow> :                 
                    info.length > 0 ? info.map((row) => (
                    <TableRow
                        key={row.cotizacion_monedaid}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                    >
                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                            {row.nombremoneda}
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                            {formatoFecha(row.fecha)}
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                            ${row.valorcompra}
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                            ${row.valorventa}
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                            <Button onClick={(e)=>handleShowEditar(e,row)} 
                            style={{color:"black"}} disabled={permisos_acciones?.modificar === "0"}><Edit/></Button>
                        </TableCell>
                    </TableRow> 
                    )):
                    <TableRow>
                        <TableCell colSpan={5} align="center">
                            <h5>No hay informaci&oacute;n para mostrar</h5>
                        </TableCell>
                    </TableRow>}
                </TableBody>
                </Table>
            </TableContainer>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", marginTop:5}} />

                </div>
            }
        </div>
    )
}