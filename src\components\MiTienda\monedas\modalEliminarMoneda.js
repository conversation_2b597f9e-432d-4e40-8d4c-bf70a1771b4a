import React from "react"
import Modal from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { useDispatch } from "react-redux";
import { eliminarMoneda, limpiarEliminarMoneda } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalEliminarMoneda = ({show, handleClose, datos, handleClickAlert}) =>{

    const dispatch = useDispatch()

    const handleOnClick = () => {
        dispatch(eliminarMoneda(datos))
        handleClose()
        dispatch(limpiarEliminarMoneda())
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        handleClose()
    }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show ?
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Eliminar moneda</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{margin:15}}>
                <h5>Esta seguro de eliminar la moneda?</h5>
            </div>
            <Divider/>
            <div align="right">
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}}
                onClick={handleOnClick}
            >Eliminar</Button>
            </div>
        </Modal> : null
    )
}