import React, { useEffect, useState } from "react"

import { Button, 
    InputAdornment, 
    Paper, 
    Snackbar, 
TextField } from "@mui/material";
import FacebookIcon from '@mui/icons-material/Facebook';
import InstagramIcon from '@mui/icons-material/Instagram';
import TelegramIcon from '@mui/icons-material/Telegram';
import TwitterIcon from '@mui/icons-material/Twitter';
import { useDispatch, useSelector } from "react-redux";
import { configRedes, getRedesSociales, limpiarRedesSociales } from "../../../../redux/actions/mitienda.actions";

import "./redesSociales.css"
import { EmailOutlined } from "@material-ui/icons";
import MuiAlert from '@mui/material/Alert';

export const RedesSociales =()=>{

    const redes = useSelector((state) => state.mitienda.redesSociales)
    const ok = useSelector((state) => state.mitienda.okRedesSociales)

    const dispatch = useDispatch()

    const [input, setInput] = useState('')

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }

    const handleClick = () => {
        dispatch(configRedes(input))
        setTimeout(function(){
            handleClickAlert()
          }, 1000);
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    useEffect(() => {
        setInput(redes)
    },[redes])

    useEffect(() => {
        dispatch(getRedesSociales())
        dispatch(limpiarRedesSociales())
    },[])

    return (
        <section className="container-redes">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Informaci&oacute;n modificada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            <div className="form-container-general">
                <header className="header-redes">
                    <h3 style={{marginTop:20, marginBottom:20}}>Redes sociales</h3>
                    <div>
                        <h4>Esta secci&oacute;n podes colocar los nombres de usuario de tus
                            redes sociales para que aparezcan en tu tienda online.
                        </h4>
                        <h4>
                            Recorda agregar el link de tu tienda a tus redes sociales
                            para tener todo vinculado.
                        </h4>
                    </div>
                </header>
                <Paper className="form-redes">
                    <TextField 
                        name="mail_direccion"
                        label="Email"  
                        fullWidth margin="normal" 
                        placeholder="<EMAIL>"
                        onChange={(e)=>handleChange(e)}
                        value={input.mail_direccion || ''}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <EmailOutlined/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                    <TextField 
                        name="facebook_url"
                        id="pagina-facebook" 
                        label="Pagina de facebook (opcional)"  
                        helperText="Debes poner la url" 
                        fullWidth margin="normal" 
                        placeholder="https://www.facebook.com/miejemplo"
                        value={input.facebook_url || ''}
                        onChange={(e)=>handleChange(e)}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <FacebookIcon/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7,
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                    <TextField 
                        name="instagram_url"
                        label="Instagram (opcional)"  
                        helperText="Debes poner la url" 
                        fullWidth margin="normal" 
                        placeholder="https://www.instagram.com/miejemplo"
                        value={input.instagram_url || ''}
                        onChange={(e)=>handleChange(e)}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <InstagramIcon/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                    <TextField 
                        name="twitter_url"
                        label="Twitter (opcional)"  
                        helperText="Debes poner la url" 
                        fullWidth margin="normal" 
                        placeholder="https://twitter.com/miejemplo"
                        value={input.twitter_url || ''}
                        onChange={(e)=>handleChange(e)}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <TwitterIcon/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                    <TextField 
                        name="telegram_url"
                        label="Telegram (opcional)"  
                        helperText="Debes poner la url" 
                        fullWidth margin="normal" 
                        placeholder="https://web.telegram.org/z/miejemplo"
                        onChange={(e)=>handleChange(e)}
                        value={input.telegram_url || ''}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <TelegramIcon/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                </Paper>
                <div align="right">
                    <Button 
                        size="large" 
                        variant="contained" 
                        onClick={(e)=>(handleClick(e))}
                        sx={{mt: 3, mr:7}}>Guardar</Button>
                </div>
            </div>
        </section>
    )
}