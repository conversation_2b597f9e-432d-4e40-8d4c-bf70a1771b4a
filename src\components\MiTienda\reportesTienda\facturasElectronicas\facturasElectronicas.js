import React, { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { getFacturasElectronicas } from '../../../../redux/actions/mitienda.actions'
import { ModalFiltrosFacturasElectronicas } from './modalFiltrosFacturasElectronicas';
import ClipLoader from "react-spinners/ClipLoader";

export const FacturasElectronicas = () => {

    //Obtengo la informacion para la tabla
    const info = useSelector((state) => state.mitienda.facturasElectronicas)

    //Obtengo la ultima pagina para el componente Pagination
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaFacturasElectronicasPaginado)
    //Creo la constante que usare para cambiar las paginas y su handle change
    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    //Declaro la variable para despachar acciones
    const dispatch = useDispatch()

    //Manejo el abrir y cerrar Modal de Filtros
    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    //Declaro las constantes que utilizare para filtrar la informacion
    const [fecha_desde, setFecha_desde] = useState(0)
    const handleChangeFecha_desde = (e) =>{
        setFecha_desde(e.target.value)
    }

    const [fecha_hasta, setFecha_hasta] = useState(0)
    const handleChangeFecha_hasta = (e) =>{
        setFecha_hasta(e.target.value)
    }

    //Funcion para el boton Aplicar filtrps
    const searchByFilters = () =>{
        setPage(1)
        dispatch(getFacturasElectronicas(fecha_desde, fecha_hasta, page))
        handleClose()
    }


    //Funcion para el boton Limpiar filtros, se reinician las constantes a sus valores iniciales
    const reset = (e) =>{
        setFecha_desde(0)
        setFecha_hasta(0)

    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        setLoading(true)
        dispatch(getFacturasElectronicas(fecha_desde, fecha_hasta, page))
    },[page])

    return (
        <div className="notificaciones-container">
            {
                <ModalFiltrosFacturasElectronicas
                    show={show}
                    handleClose={handleClose}
                    search={searchByFilters}
                    reset={reset}
                    fecha_desde={fecha_desde}
                    handleChangeFecha_desde={handleChangeFecha_desde}
                    fecha_hasta={fecha_hasta}
                    handleChangeFecha_hasta={handleChangeFecha_hasta}

                />
            }
            <div>
                <header className="titulo-notificaciones">
                    <h3>Reporte: Facturas Electronicas</h3>
                    <Button
                        variant="outlined"
                        sx={{height:40,width:150}}
                        onClick={handleShow}>
                        <FilterListIcon/> Filtrar
                    </Button>
                </header>
            </div>
            <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                    <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Cliente</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Pedido</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Fecha</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Numero</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">CAE</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Fecha Vto</TableCell>
                                 <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">CUIT</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Razon Social</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Total</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Pagos</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Saldo</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {   
                            loading ? 
                            <TableRow sx={{p:20}}>
                                <TableCell colSpan={11} align='center'>
                                    <ClipLoader 
                                        loading={loading}
                                        size={50}
                                    />
                                </TableCell>
                            </TableRow> :
                            info.data.length ? info.data.map((row) => (
                                <TableRow
                                    key={row.id}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                >
                                    <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                                        {row.nombrecliente}
                                    </TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:150}} align="center">
                                        {row.pedidoplacaid}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.fechafactura}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.numerofactura}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">
                                        {row.numerocae}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.fechavencimientocae}</TableCell>
                                     <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.cuit}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.razonsocial}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.total}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.totalPago}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.saldo}</TableCell>
                                </TableRow>
                            )):
                            <TableRow>
                                <TableCell colSpan={11}>
                                    <h5>No hay informaci&oacute;n para mostrar</h5>
                                </TableCell>
                            </TableRow>}

                        </TableBody>
                    </Table>
                </TableContainer>
            </div>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}
