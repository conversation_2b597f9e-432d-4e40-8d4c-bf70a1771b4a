{"name": "client-admin", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mui/icons-material": "^5.8.4", "@mui/lab": "^5.0.0-alpha.76", "@mui/material": "^5.5.3", "@mui/styles": "^5.6.0", "@mui/x-data-grid": "^6.2.0", "@mui/x-date-pickers": "^5.0.8", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.3", "@testing-library/user-event": "^13.5.0", "@vis.gl/react-google-maps": "^0.5.4", "axios": "^0.26.1", "bootstrap": "^5.1.3", "buffer": "^6.0.3", "chart.js": "^4.2.1", "dayjs": "^1.11.9", "emoji-picker-react": "^4.4.7", "formik": "^2.2.9", "fs": "^0.0.1-security", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "i18next": "^21.6.14", "i18next-browser-languagedetector": "^6.1.4", "install": "^0.13.0", "jodit-react": "^1.3.39", "jspdf": "^2.5.1", "material-table": "^1.36.0", "md5": "^2.3.0", "moment": "^2.29.4", "npm": "^8.5.3", "react": "^17.0.2", "react-bootstrap": "^2.2.3", "react-chartjs-2": "^5.2.0", "react-country-flag": "^3.0.2", "react-dom": "^17.0.2", "react-google-recaptcha": "^3.1.0", "react-horizontal-stacked-bar-chart": "^8.15.2", "react-i18next": "^11.16.2", "react-icons": "^4.3.1", "react-modal": "^3.14.4", "react-quill": "^2.0.0", "react-redux": "^7.2.6", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.0", "react-spinners": "^0.11.0", "react-to-print": "^2.15.1", "react-webcam": "^7.1.1", "redux": "^4.1.2", "redux-thunk": "^2.4.1", "socket.io-client": "^4.6.1", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "browser": {"fs": false}}