import React from "react"
import <PERSON><PERSON> from 'react-modal';

Modal.setAppElement("#root")

export const ModalImagenProducto = ({show, handleClose, producto}) => {

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
          display:"flex",
          justifyContent:"center"
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show &&
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", marginBottom:10, flexDirection:"column"}}>
            <div align="right">
            <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            {
                producto.imagenes[1].hasOwnProperty('error') ?
                <h4>{producto.imagenes[1].error}</h4> :
                <img
             style={{width:300}}    
             src={process.env.REACT_APP_SERVER+producto.imagenes[1].pathImagenProducto+(producto.cant_img === 0 ? '' : producto.productoid)+'/'+producto.imagenes[1].file}/>
            }   
            </div>
        </Modal> 
    )
}