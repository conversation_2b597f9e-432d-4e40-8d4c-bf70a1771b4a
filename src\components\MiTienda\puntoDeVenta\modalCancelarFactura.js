import React from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button,} from "@mui/material";
import { useDispatch } from "react-redux";
import { cancelarFactura, cancelarPedido } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalCancelarFactura = ({
    show, 
    handleClose, 
    info, 
    tipofactura, 
    handleOnClickAlert,
    setProduct,
    setCantidad,
    getProductosByNameSkuNuevaVenta,
    getCarritoDetalle
}) =>{
    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const handleCancelar = () =>{
        dispatch(cancelarFactura(info,tipofactura))
        setTimeout(function(){
            handleOnClickAlert()
        }, 3000);
        handleClose()
    }

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Cancelar factura</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>   
                    Esta seguro que quiere cancelar la factura?
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="outlined" 
                    color="success"
                    onClick={handleCancelar}
                    sx={{mt: 3, mr:1}}>Si</Button>
                    <Button size="large"
                    variant="outlined"
                    color="error"
                    onClick={handleClose}
                    sx={{mt: 3}}>No</Button>
                </div>
        </Modal>
    )
}