import React, { useEffect, useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, FormControl, InputAdornment, InputLabel, ListSubheader, MenuItem, Select, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { SelectSearchInputProveedor } from "../SelectInputs/SelectSearchInputProveedor";
import { getListadoProveedorByName } from "../../../redux/actions/mitienda.actions";
import { Search } from "@mui/icons-material";
Modal.setAppElement("#root")

export const ModalFiltrosFacturaProveedor = ({
    show, 
    handleClose, 
    handleChangeNumero_comprob,
    numero_comprob,
    handleChangeFecha_desde,
    fecha_desde,
    handleChangeFecha_hasta,
    fecha_hasta,
    setProveedorID,
    setNombreProveedor,
    nombreProveedor,
    search, 
    reset
}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          height: "60vh",
          borderRadius: "8px",
          maxWidth: "650px",
          right: '50px',
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const dispatch = useDispatch()

    const proveedores = useSelector((state) => state.mitienda.proveedoresByName)

    const [nombre, setNombre] = useState('')
    const [selectedOption, setSelectedOption] = useState('');

    useEffect(() =>{
        if(nombre.length > 4){
            dispatch(getListadoProveedorByName(nombre))
        }
    }, [nombre])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>FILTRAR</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <h5 style={{marginTop:10}}>Solo se puede buscar por un filtro a la vez</h5>
                <Divider/>
                <div style={{display:"flex", flexDirection:"column", marginTop:10}}>
                    <div style={{display:"flex", flexDirection:"column", marginTop:15}}>
                        <h6>Fecha desde</h6>
                        <TextField
                            style={{marginBottom:20}}
                            variant="outlined"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                            name="fechaDesde"
                            onChange={(e) => handleChangeFecha_desde(e)}
                            value={fecha_desde}
                            type="date"
                        />
                    </div>

                    <div style={{display:"flex", flexDirection:"column"}}>
                        <h6>Fecha hasta</h6>
                        <TextField
                            style={{marginBottom:20}}
                            variant="outlined"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                            name="fecha_hasta"
                            onChange={(e) => handleChangeFecha_hasta(e)}
                            value={fecha_hasta}
                            type="date"
                        />
                    </div>

                    <FormControl fullWidth style={{marginTop:5, marginBottom:10}} size="small">
                        <InputLabel id="search-select-label">Proveedor</InputLabel>
                            <Select
                                style={{height:40}}
                                MenuProps={{ autoFocus: false,  disablePortal: true }}
                                labelId="search-select-label"
                                id="search-select"
                                value={selectedOption.proveedorid || ''}
                                label="Proveedor"
                                onClose={() => {
                                    dispatch(getListadoProveedorByName(""))
                                }}
                                size="small"
                                renderValue={() => selectedOption.nombre}
                            >
                        <ListSubheader>
                        <TextField
                            size="small"
                            autoFocus
                            placeholder="Buscar proveedor..."
                            fullWidth
                            InputProps={{
                                startAdornment: (
                                <InputAdornment position="start">
                                    <Search/>
                                </InputAdornment>
                                )
                            }}
                            inputProps={{
                                style: {
                                    height: 35,
                                    fontSize:17
                                },
                            }}
                            onChange={(e) => setNombre(e.target.value)}
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                        />
                        </ListSubheader>
                        {proveedores.map((option, i) => (
                            <MenuItem key={i} value={option.clienteid} onClick={() => {
                                setSelectedOption(option)
                                setNombre(option.nombre)
                                setProveedorID(option.proveedorid)
                            }}>
                            {option.nombre}
                            </MenuItem>
                        ))}
                        </Select>
                    </FormControl>
                    
                    <TextField
                        style={{marginTop:15}}
                        value={numero_comprob}
                        name="numero_comprob"
                        label="N&uacute;mero de comprobante"
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                            }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => handleChangeNumero_comprob(e)}
                    />
                </div>
            <div align="center">
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={search}
            >Aplicar filtro</Button>
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={() => {
                    reset()
                    setNombre('')
                    setSelectedOption('')
                }}
            >Limpiar filtro</Button>
            </div>
        </Modal>
    )
}