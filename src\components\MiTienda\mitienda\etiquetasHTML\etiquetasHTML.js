import React, { useEffect, useState } from "react"

import { Alert, Button, 
    Paper, 
    Snackbar, 
TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { configSEO, getEtiquetasHtml, limpiarEtiquetas } from "../../../../redux/actions/mitienda.actions";
import MuiAlert from '@mui/material/Alert';
import "./etiquetasHTML.css"

export const EtiquetasHTML =()=>{

    const etiquetas = useSelector((state) => state.mitienda.etiquetas)

    const ok = useSelector((state) => state.mitienda.seo)

    const dispatch = useDispatch()

    const [input, setInput] = useState('')

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }

    const handleClick = () => {
        dispatch(configSEO(input))
        setTimeout(function(){
            handleClickAlert()
          }, 1000);
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    useEffect(() =>{
        let isMounted = true;
        if(isMounted){
            dispatch(getEtiquetasHtml())
            dispatch(limpiarEtiquetas())
        }
        return () => {
            isMounted = false
        }
    }, [])

    useEffect(() => {
        setInput(etiquetas)
    }, [etiquetas])

    return (
        <section className="container-etiquetas">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Informaci&oacute;n modificada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
                <header className="header-etiquetas">
                    <h3>Etiquetas HTML</h3>
                    <div>
                        <h4>Esta funci&oacute;n te permite agregar etiquetas HTML en tu tienda online.</h4>
                        <h4>
                            Luego de colocar tus etiquetas, recomendamos revisar tu tienda ya que una etiqueta HTML mal puesta
                            podr&iacute;a ocacionar problemas visuales y/o de funcionamiento.
                            <br/>En dicho caso te recomendamos que la borres y te comuniques con nosotros.
                        </h4>
                    </div>
                </header>
                <Paper className="form-etiquetas">
                    <TextField 
                        sx={{mb:3}}
                        name="ecommerce_mtatitle"
                        label="(SEO) Meta Title"  
                        fullWidth margin="normal" 
                        // placeholder="Pega tu codigo para <head> aca"
                        value={input.ecommerce_mtatitle || ''}
                        onChange={(e)=>handleChange(e)}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7,
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                    <TextField 
                        sx={{mb:3}}
                        name="ecommerce_mtadesc"
                        label="(SEO) Meta Descripcion"  
                        fullWidth margin="normal" 
                        // placeholder="Pega tu codigo para <body> aca"
                        value={input.ecommerce_mtadesc || ''}
                        onChange={(e)=>handleChange(e)}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                    <TextField 
                        sx={{mb:3}}
                        name="ecommerce_mtagooglervfi"
                        label="(SEO) Meta Varify Google"  
                        fullWidth margin="normal" 
                        // placeholder="Pega tu codigo para <body> aca"
                        value={input.ecommerce_mtagooglervfi || ''}
                        onChange={(e)=>handleChange(e)}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                            }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                    />    
                    <TextField 
                        sx={{mb:3}}
                        name="ecommerce_mtarobots"
                        label="(SEO) Meta Robots"  
                        fullWidth margin="normal" 
                        // placeholder="Pega tu codigo para <body> aca"
                        value={input.ecommerce_mtarobots || ''}
                        onChange={(e)=>handleChange(e)}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                            }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                    />
                    {/* <TextField 
                        sx={{mb:3}}
                        name="default_skin"
                        label="Skin ID"  
                        fullWidth margin="normal" 
                        // placeholder="Pega tu codigo para <body> aca"
                        value={input.default_skin || ''}
                        onChange={(e)=>handleChange(e)}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                            }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                    /> */}
                    </Paper>
                    <div align="right">
                        <Button 
                            size="large" 
                            variant="contained" 
                            onClick={(e)=>(handleClick(e))}
                            sx={{mt: 3, mr:2}}>Guardar</Button>
                    </div>
        </section>
    )
}