.welcome{
  background-image: linear-gradient( 135deg, #5EFCE8 10%, #736EFE 100%);
  width: 60%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.window{
  display: flex;
}

.form-login{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40%;
  height: 100vh;
  padding: 20px;
}

.logo-login{
  width: 150px;
}

.text-field{
  width: 60%;
  margin: 10px;
}

.forgot-password{
  font-size: 17px ;
  margin: 10px;
}

.forgot-password:hover {
  color:blue
}

.software{
  color: #ffcd82
}

.h2{
  font-size: 72px;
  color: rgb(238, 238, 238);
  margin: 0;
}

.country-flag{
  background-color: white;
  border: none;
  margin: 5px;
}

@media only screen and (max-width: 1500px)  {
  .window {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .form-login{
    width: 100%;
  }

  .text-field{
    width: 30%;
    margin: 10px;
  }

  .welcome{
   display: none;
  }
}

.forgot-pass:hover {
  color:blue
}

.backgroundRecovery{
  width: 100%;    
  height: 100vh;   
  background-color: #DADDFC;
  background-position: center center;    
  background-repeat: no-repeat;    
  background-size: cover;  
  overflow-y: auto;  
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.formRecovery{
  background-color: white;
  height: auto;
  width: 1000px;
  align-self: center;
  margin: 20px;
  border-radius: 8px;
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.titleRecovery{
  display: inline-block;
  width: 100px;
}

.labelInputRecovery{
  display: flex;
  margin:20px;
}

.textFieldRecovery{
  width: 350px;
}

.text-center{
  margin:15px;
}

.container-crear-usuario{
  width: 100%;    
  height: auto;
  min-height: 100vh;   
  background-color: #DADDFC;
  background-position: center center;    
  background-repeat: no-repeat;    
  background-size: cover;  
  overflow-y: auto;  
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 60px;
}

.titulo-registrar{
  font-size: 30px;
  font-weight: 500;
  position: relative;
}

.titulo-registrar::before{
  content: '';
  position: absolute;
  height: 3px;
  width: 30px;
  background:  linear-gradient(14deg, rgba(22,12,190,1) 0%, rgba(69,175,219,1) 55%, rgba(0,202,244,1) 91%);
  left: 0;
  bottom: 0;
}

.form-paper{
  display: flex;
  flex-wrap: wrap;
  margin: 20px;
}

.button-registrar{
  background-color: #5ca9fb;
  background-image:  linear-gradient(14deg, rgba(22,12,190,1) 0%, rgba(69,175,219,1) 55%, rgba(0,202,244,1) 91%);
  width: 80%;
  align-self: center;
  min-height: 40px;
}

.texto-button{
  color: white;
  font-size: 15px;
}