import React from "react"

export const SelectSearchInputTalla = ({
    info,
    nombreTalle,
    setNombreTalle,
    setTalleID,
    open, 
    setOpen, 
}) =>{

    return (
        <div className="input-grup-ofertas">
            <h6>Medida</h6>
            <input onClick={(e)=> {
                setOpen(true)
            }} value={nombreTalle} onChange={(e)=> setNombreTalle(e.target.value)}/>
            <div className="list-info"  > 
                {
                    info.length > 0 && open ? 
                    info.map((i) =>
                        <div className="menu-items" onClick={(e)=> {
                            setOpen(false)
                            setTalleID(i.tallaid)
                            setNombreTalle(i.nombretalla)
                        }}>
                            {i.nombretalla}
                        </div>
                    ) : null
                }
            </div>  
        </div>
    )
}