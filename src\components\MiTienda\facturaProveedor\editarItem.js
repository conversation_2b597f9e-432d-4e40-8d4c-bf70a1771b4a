import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { But<PERSON>, TextField, InputAdornment } from "@mui/material";
import { useDispatch } from "react-redux";
import { editarItemFacturaProveedor, getConceptos } from "../../../redux/actions/mitienda.actions";
import { AttachMoney } from "@mui/icons-material";
Modal.setAppElement("#root")

export const ModalEditarItem = ({show, handleClose, info, handleClickAlert}) =>{
    
    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [input, setInput] = useState('')

    const handleChange = (e) => {
      e.preventDefault()
      setInput({...input, [e.target.name]: e.target.value })
    }

    const dispatch = useDispatch()

    //facturacompraid,facturacompraproductoid,productoid,concepto,cantidad,descuento,preciocompra
    const handleOnclick = () => {
        dispatch(editarItemFacturaProveedor(
            input.facturacompraid,
            input.facturacompraproductoid,
            info.productoid,
            info.conceptoid,
            input.cantidad,
            '',
            input.preciocosto
        ))
        handleClose()
    }

    useEffect(() => {
        dispatch(getConceptos())
    },[])

    useEffect(() => {
        setInput(info)
    }, [info])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Editar item</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{margin:5}}>
            <div style={{width:"100%", margin:10}}>
                <h6><strong>Nombre</strong></h6>
                <TextField
                    style={{width:"100%"}}
                    value={input.nombreproducto}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
            </div>
            <div style={{width:"100%", margin:10}}>
                <h6><strong>Precio</strong></h6>
                <TextField
                    style={{width:"100%"}}
                    name='preciocosto'
                    value={input.preciocosto}
                    onChange={(e)=>handleChange(e)}
                    InputProps={{
                        startAdornment:<InputAdornment position="start">
                        <AttachMoney/>
                        </InputAdornment>,
                    }}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
            </div>
            <div style={{width:"100%", margin:10}}>
                <h6><strong>Cantidad</strong></h6>
                <TextField
                    style={{width:"100%"}}
                    type='number'
                    name='cantidad'
                    value={input.cantidad}
                    onChange={(e)=>handleChange(e)}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
            </div>
            </div>
            <Divider/>
            <div align="right">
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}}
                onClick={handleOnclick}
            >Editar</Button>
            </div>
        </Modal>
    )
}