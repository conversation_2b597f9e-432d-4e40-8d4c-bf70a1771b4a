import { TextField } from '@material-ui/core';
import { Check, Clear, Delete, Edit, FormatListBulleted } from '@mui/icons-material';
import React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { agregarPromocion, getPromociones } from '../../../redux/actions/mitienda.actions';
import { useSelector } from 'react-redux';
import Pagination from '@mui/material/Pagination';
import { useState } from 'react';
import { Button, Checkbox, FormControlLabel, Snackbar } from '@mui/material';
import MuiAlert from '@mui/material/Alert';
import { ModalEditarPromocion } from './editarPromocion';
import { ModalEliminarPromocion } from './eliminarPromocion';
import { ModalProductoPromocion } from './modalProductosPromocion';
import { ClipLoader } from 'react-spinners';

export const Promociones = () => {
    
    const info = useSelector((state) => state.mitienda.promociones)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaPromocion)
    const okCrear = useSelector((state) => state.mitienda.okAgregarPromocion)
    const okEditar = useSelector((state) => state.mitienda.okEditarPromocion)
    const okEliminar = useSelector((state) => state.mitienda.okEliminarPromocion)

    const dispatch = useDispatch()
    
    var tzoffset = (new Date()).getTimezoneOffset() * 60000;
    var fechaActual = (new Date(Date.now() - tzoffset)).toISOString().slice(0,16);

    const [promocion, setPromocion] = useState('')

    const [input, setInput] = useState({
        nombre: '',
        fechainicio: fechaActual,
        fechafin: fechaActual,
        observacion: '',
        descuento: '',
        codigo: '',
        activo: '',
        ecommerce: ''
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }

    const [showEditar, setShowEditar] = useState(false);
    const handleCloseEditar = () => setShowEditar(false);
    const handleShowEditar = (e,row) => {
        e.preventDefault()
        setShowEditar(true);
        setPromocion(row)
    }

    const [showEliminar, setShowEliminar] = useState(false);
    const handleCloselimintar = () => setShowEliminar(false);
    const handleShowEliminar = (e,row) => {
        e.preventDefault()
        setShowEliminar(true);
        setPromocion(row)
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [openAgregar, setOpenAgregar] = React.useState(false);
    const handleClickAlertAgregar = () => {
      setOpenAgregar(true);
    };
    const handleCloseAlertAgregar = () => {
        setOpenAgregar(false);
    };

    const [openEditar, setOpenEditar] = React.useState(false);
    const handleClickAlertEditar = () => {
      setOpenEditar(true);
    };
    const handleCloseAlertEditar = () => {
        setOpenEditar(false);
    };

    const [openEliminar, setOpenEliminar] = React.useState(false);
    const handleClickAlertEliminar = () => {
      setOpenEliminar(true);
    };
    const handleCloseAlertEliminar = () => {
        setOpenEliminar(false);
    };

    const handleClickAgregar = () => {
        dispatch(agregarPromocion(input))
        setTimeout(function(){
            handleClickAlertAgregar()
        }, 1000);
        setInput({
            nombre: '',
            fechainicio: fechaActual,
            fechafin: fechaActual,
            observacion: '',
            descuento: '',
            codigo: '',
            activo: '',
            ecommerce: ''
        })
    }

    const [showAgregarItems, setShowAgregarItems] = useState('');
    const handleCloseAgregarItems = () => setShowAgregarItems(false);
    const handleShowAgregarItems = (e,row) => {
        setShowAgregarItems(true);
        setPromocion(row)
    }
    const formatoFecha = (fecha) =>{
        let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        setLoading(true)
        dispatch(getPromociones(page))
    },[page,okCrear,okEditar,okEliminar])

    return (
        <div className="ventas-container">
            <Snackbar
                open={openAgregar} 
                autoHideDuration={10000} onClose={handleCloseAlertAgregar}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertAgregar} 
                    severity={okCrear.success ? "success" : "error"}
                    sx={{ width: 500 }}>
                    <h5>{okCrear.mensaje}</h5>
                </Alert>
            </Snackbar>
            <Snackbar
                open={openEditar} 
                autoHideDuration={10000} onClose={handleCloseAlertEditar}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertEditar} 
                    severity={okEditar.success ? "success" : "error"}
                    sx={{ width: 500 }}>
                    <h5>{okEditar.mensaje}</h5>
                </Alert>
            </Snackbar>
            <Snackbar
                open={openEliminar} 
                autoHideDuration={10000} onClose={handleCloseAlertEliminar}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertEliminar} 
                    severity={okEliminar.success ? "success" : "error"}
                    sx={{ width: 500 }}>
                    <h5>{okEliminar.mensaje}</h5>
                </Alert>
            </Snackbar>

            <ModalEditarPromocion
                show={showEditar}
                handleClickAlert={handleClickAlertEditar}
                handleClose={handleCloseEditar}
                info={promocion}
            />

            <ModalEliminarPromocion
                show={showEliminar}
                handleClickAlert={handleClickAlertEliminar}
                handleClose={handleCloselimintar}
                id={promocion.promocion_prodid}
            />

            <ModalProductoPromocion
                show={showAgregarItems}
                handleClose={handleCloseAgregarItems}
                promo={promocion}
            />

            <div className="titulo-ventas">
                <h3 className="text-ventas">Promociones</h3>
            </div>

            <Paper className="paper-form" style={{marginLeft:"15px"}}>
            <TextField 
                style={{marginTop:15, marginBottom:15}}
                name="nombre"
                variant="outlined"
                label="Nombre descuento"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.nombre}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                style={{marginTop:15, marginBottom:15}}               
                name="descuento"
                variant="outlined"
                label="Descuento %"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.descuento}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                style={{marginTop:15, marginBottom:15}} 
                type="datetime-local"
                variant="outlined"
                name="fechainicio"
                label="Fecha de inicio"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.fechainicio}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                style={{marginTop:15, marginBottom:15}} 
                type="datetime-local"
                variant="outlined"
                name="fechafin"
                label="Fecha de finalizaci&oacute;n"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.fechafin}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <div style={{marginTop:5, marginBottom:5}}>
                <FormControlLabel
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black"}}
                    onChange={(e)=> handleCheck(e)}
                    checked={input.activo == "1" ? true : false}
                    label="Activo"
                    name="activo"
                />
            </div>
            <div style={{marginTop:5, marginBottom:5}}>
                <FormControlLabel
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black"}}
                    onChange={(e)=> handleCheck(e)}
                    checked={input.ecommerce == "1" ? true : false}
                    label="Ecommerce"
                    name="ecommerce"
                />
            </div>
            </Paper>
            <div align="right">
                <Button 
                    disabled={
                        input.nombre === '' ||
                        input.porcentaje === ''
                    }
                    size="large" 
                    variant="contained" 
                    onClick={(e)=>(handleClickAgregar(e))}
                    sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>
           
            <TableContainer component={Paper} sx={{zIndex:99, marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                <Table aria-label="simple table">
                <TableHead>
                    <TableRow>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Nombre</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Descuento(%)</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Fecha inicio</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Fecha finalizo</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Activo</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Productos</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Modificar</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Borrar</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                {
                    loading ? 
                    <TableRow sx={{p:20}}>
                        <TableCell colSpan={8} align='center'>
                            <ClipLoader 
                                loading={loading}
                                size={50}
                            />
                        </TableCell>
                    </TableRow> :
                    info.length > 0 ? info.map((row) => (
                    <TableRow
                        key={row.promocion_prodid}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 }}}
                    >
                        <TableCell style={{fontSize:"17px", width:"400px", height:"40px", padding: 1}} align="center"
                        >{row.nombre}</TableCell>
                        <TableCell style={{fontSize:"17px", width:"150px", height:"40px", padding: 0}} align="center"
                        >{row.descuento}</TableCell>
                        <TableCell style={{fontSize:"17px", width:"200px", height:"40px", padding: 0}} align="center"
                        >{formatoFecha(row.fechainicio)}</TableCell>
                        <TableCell style={{fontSize:"17px", width:"200px", height:"40px", padding: 0}} align="center"
                        >{formatoFecha(row.fechafin)}</TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40}} align="center">
                            {row.activo === "1" ? 
                                <Check color="success"/> :
                                <Clear color="error"/>
                            }
                        </TableCell>
                        <TableCell style={{fontSize:"100%"}} align="center"
                        onClick={(e)=>handleShowAgregarItems(e,row)}><div><FormatListBulleted/></div></TableCell>
                        <TableCell style={{fontSize:"100%"}} align="center" 
                        onClick={(e)=>handleShowEditar(e,row)}><div><Edit/></div></TableCell>
                        <TableCell style={{fontSize:"100%"}} align="center" 
                        onClick={(e)=>handleShowEliminar(e,row)}><div><Delete/></div></TableCell>
                    </TableRow>
                    )):
                    <TableRow>
                        <TableCell colSpan={8}>
                            <h5>No hay informaci&oacute;n para mostrar</h5>
                        </TableCell>
                    </TableRow>}
                </TableBody>
                </Table>
            </TableContainer>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}