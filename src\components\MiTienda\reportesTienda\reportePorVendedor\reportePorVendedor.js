import React, { useEffect, useState } from 'react';
import { Box, Button, Collapse, IconButton, Typography } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { useDispatch, useSelector } from 'react-redux';
import { getDetalleReporteVendedor, getReportePorVendedor } from '../../../../redux/actions/mitienda.actions'
import { ModalFiltrosReportePorVendedor } from './modalFiltrosReportePorVendedor';
import { addDetail, calcularTotales } from './utilsReporteVendedor';
import * as XLSX from "xlsx";
import { Description, Download, KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material';
import moment from 'moment';
import { ClipLoader } from 'react-spinners';

function Row ({row, formatoFecha}) {

    const [open, setOpen] = React.useState(false);
  
    return (
      <React.Fragment>
        <TableRow style={{backgroundColor:`${row.color}`}}>
        <TableCell>
            <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
            >
            {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
            </IconButton>
        </TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45}} align="center">{row.numerofactura}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nrocomprobante}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.facturanota}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.facturaremito}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">{formatoFecha(row.fechafactura)}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombrecliente}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombretienda}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombrevendedor}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.tipopagos}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.intereses}</TableCell>
        <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.total}</TableCell>
        </TableRow>
        <TableRow>
          <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={12}>
            <Collapse in={open} timeout="auto" unmountOnExit>
              <Box sx={{ margin: 1 }}>
                <Typography variant="h6" gutterBottom component="div">
                  Detalle
                </Typography>
                <Table size="small">
                  <TableHead> 
                    <TableRow>
                      <TableCell align="center" style={{fontWeight:"bold"}}>C&oacute;digo</TableCell>
                      <TableCell align="center" style={{fontWeight:"bold"}}>Producto ID</TableCell>
                      <TableCell align="center" style={{fontWeight:"bold"}}>C&oacute;digo de barras</TableCell>
                      <TableCell align="center" style={{fontWeight:"bold"}}>Cantidad</TableCell>
                      <TableCell align="center" style={{fontWeight:"bold"}}>Nombre</TableCell>
                      <TableCell align="center" style={{fontWeight:"bold"}}>Precio</TableCell>
                      <TableCell align="center" style={{fontWeight:"bold"}}>Precio IVA</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {row.detalle.length > 0 ? row.detalle.map((historyRow) => (
                      <TableRow key={historyRow.productoid} >
                        <TableCell align="center" component="th" scope="row">
                          {historyRow.facturaid}
                        </TableCell>
                        <TableCell align="center" style={{width:130}}>{historyRow.productoid}</TableCell>
                        <TableCell align="center" style={{width:150}}>{historyRow.codigobarras}</TableCell>
                        <TableCell align="center" style={{width:80}}>{historyRow.cantidad}</TableCell>
                        <TableCell align="center" style={{width:230}}>{historyRow.nombreproducto}</TableCell>
                        <TableCell align="center" style={{width:150}}>${historyRow.precio}</TableCell>
                        <TableCell align="center" style={{width:150}}>${historyRow.precioiva}</TableCell>
                      </TableRow>
                    )) :                       
                    <TableRow>
                        <TableCell align="center" component="th" scope="row">
                        </TableCell>
                        <TableCell align="center"></TableCell>
                        <TableCell align="center"></TableCell>
                        <TableCell align="center" style={{width:300, fontSize:18, margin:10}}>No hay datos cargados</TableCell>
                        <TableCell align="center"></TableCell>
                        <TableCell align="center"></TableCell>
                        <TableCell align="center"></TableCell>
                    </TableRow>
                    }                    
                  </TableBody>
                </Table>
              </Box>
            </Collapse>
          </TableCell>
        </TableRow>
      </React.Fragment>
    );
  }

export const ReportePorVendedor = () => {

    //Obtengo la informacion para la tabla 
    const info = useSelector((state) => state.mitienda.reportePorVendedor)
    const detalle = useSelector((state) => state.mitienda.detalleFacturaVendedor)
    //Declaro la variable para despachar acciones
    const dispatch = useDispatch()

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    //Manejo el abrir y cerrar Modal de Filtros
    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    const formatoFecha = (fecha) =>{
      if(fecha !== null){
        let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
      }else{
        return null
      }
    }

    let fechaActual = moment()
    let ultimosDias = moment().day(-30)

    //Declaro las constantes que utilizare para filtrar la informacion
    const [fecha_desde, setFecha_desde] = useState(ultimosDias.toISOString().substring(0,10))
    const handleChangeFecha_desde = (e) =>{
        setFecha_desde(e.target.value)
    }

    const [fecha_hasta, setFecha_hasta] = useState(fechaActual.toISOString().substring(0,10))
    const handleChangeFecha_hasta = (e) =>{
        setFecha_hasta(e.target.value)
    }

    const [vendedor, setVendedor] = useState("")
    const handleChangeVendedor = (e) =>{
        setVendedor(e.target.value)
    }

    //Funcion para el boton Aplicar filtrps
    const searchByFilters = () =>{
      setLoading(true)
      dispatch(getReportePorVendedor(fecha_desde, fecha_hasta, vendedor))
      handleClose()
    }

    //Funcion para el boton Limpiar filtros, se reinician las constantes a sus valores iniciales
    const reset = (e) =>{
      setFecha_desde(0)
      setFecha_hasta(0)
      setVendedor('')
      handleClose()
      dispatch(getReportePorVendedor(
        ultimosDias.toISOString().substring(0,10), 
        fechaActual.toISOString().substring(0,10), 
        ""
      ))
    }

    const downloadExcel = () => {
        const workSheet = XLSX.utils.json_to_sheet(info.reporte)
        const workBook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workBook, workSheet, "info")
        let buffer = XLSX.write(workBook,{bookType:"xlsx", type:"buffer"})
        XLSX.write(workBook,{bookType:"xlsx", type:"binary"})
    
        XLSX.writeFile(workBook, "ReporteVendedor.xlsx")
    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
      setLoading(false)
    },[info])

    useEffect(() =>{
      dispatch(getReportePorVendedor(fecha_desde, fecha_hasta, vendedor))
    },[]) 

    return (
        <div className="notificaciones-container">
            {
                <ModalFiltrosReportePorVendedor
                  show={show} 
                  handleClose={handleClose}
                  search={searchByFilters}
                  reset={reset}
                  fecha_desde={fecha_desde}
                  handleChangeFecha_desde={handleChangeFecha_desde}
                  fecha_hasta={fecha_hasta}
                  handleChangeFecha_hasta={handleChangeFecha_hasta}
                  vendedor={vendedor}
                  handleChangeVendedor={handleChangeVendedor}
                />
            }
            <div>
                <header className="titulo-notificaciones">
                    <h3>Reporte: Por Vendedor</h3>
                    <div style={{display:"flex", marginLeft:"50px", marginBottom:20}}>
                    <Button 
                        variant="outlined" 
                        sx={{height:40,width:150, marginRight:10}}
                        onClick={handleShow}>
                        <FilterListIcon/> Filtrar
                    </Button>
                    <Button variant="outlined" 
                    sx={{height:40,width:150}} 
                    onClick={downloadExcel}>
                        <Download/> Exportar
                    </Button>
                    </div>
                </header>
            </div>
            {
              permisos_acciones?.listar !== "0" && 
              <div className='container-tabla'>
                <TableContainer component={Paper} className='tabla'>
                    <Table aria-label="simple table">
                    <TableHead>
                            <TableCell style={{width:"50px"}} />
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">N&uacute;mero</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Comprobante</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Tipo</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Remito</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Fecha</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Cliente</TableCell>
                            <TableCell style={{width: 200, fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Sucursal</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Vendedor</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Tipo pago</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Interes %</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Total</TableCell>
                    </TableHead>
                    <TableBody>
                      {
                        loading ? 
                        <TableRow sx={{p:20}}>
                            <TableCell colSpan={13} align='center'>
                                <ClipLoader 
                                    loading={loading}
                                    size={50}
                                />
                            </TableCell>
                        </TableRow> :
                        info.reporte.length > 0 ? info.reporte.map((row) => (
                            <Row key={row.facturaid} row={row} formatoFecha={formatoFecha} />
                        )) :
                        <TableRow >
                          <TableCell align='center' colSpan={12}>
                            <h5>No hay informaci&oacute;n para mostrar</h5>
                          </TableCell>
                        </TableRow>
                        }
                        <TableRow>
                            <TableCell colSpan={9}/>
                            <TableCell colSpan={1} style={{fontWeight:700, fontSize:"100%", width:150}}>Total</TableCell>
                            <TableCell align="center" style={{fontWeight:700, fontSize:"100%"}}>${info.totales.totalfactura}</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell colSpan={9}/>
                            <TableCell colSpan={1} style={{fontWeight:700, fontSize:"100%"}}>Comisi&oacute;n</TableCell>
                            <TableCell align="center" style={{fontWeight:700, fontSize:"100%"}}>${info.totales.comisionvta}</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell colSpan={9}/>
                            <TableCell colSpan={1} style={{fontWeight:700, fontSize:"100%", width:200}}>Total + Comisi&oacute;n</TableCell>
                            <TableCell align="center" style={{fontWeight:700, fontSize:"100%"}}>${info.totales.total_mas_comision}</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                    </TableBody>
                    </Table>
                </TableContainer>
            </div>
            }
        </div>
    )
}