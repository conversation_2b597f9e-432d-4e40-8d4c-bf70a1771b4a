import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { useEffect } from "react";
import { agregarDatosFooter, editarDatosFooter } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const EditarMenu = ({show, handleClose, handleClickAlert, info}) =>{

  const [input, setInput] = useState('')

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value})
  }

  const dispatch = useDispatch()

  const handleClick = () => {
    dispatch(editarDatosFooter(info.titulo,input))
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "850px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    useEffect(() =>{
      setInput(info.row)
    },[info])

    return (
        show ? 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
          <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
              <h4>Editar</h4>
              <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
          </div>
          <Divider/>
          <div style={{marginTop:15, marginBottom:15}}>
            <TextField 
              name="subtitulo"
              label="Subtitulo"  
              margin="normal" 
              onChange={(e)=> handleChange(e)}
              value={input && input.subtitulo || ''}
              style={{width:"95%"}}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
              inputProps={{
                  style: {
                  height: 40,
                  fontSize:17
                },
              }}
            />
            <TextField 
              name="url"
              label="URL"  
              margin="normal" 
              onChange={(e)=> handleChange(e)}
              value={input && input.url || ''}
              style={{width:"95%"}}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
            />
            <TextField 
              name="orden"
              label="Orden"  
              margin="normal" 
              onChange={(e)=> handleChange(e)}
              value={input && input.orden || ''}
              style={{width:"95%"}}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
            />
            <TextField 
                name="descripcion"
                label="Descripcion"  
                margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input && input.descripcion || ''}
                style={{width:"95%"}}
                InputLabelProps={{
                  style: {
                    marginBottom:10,
                    marginTop: -7
                  },
                }}
                minRows={8}
                multiline
            />
          </div>
          <Divider/>
          <div align="right">
            <Button 
              size="large" 
              variant="contained" 
              sx={{mt: 3}}
              onClick={handleClick}
              >Editar</Button>
          </div>
        </Modal> : null 
    )
}