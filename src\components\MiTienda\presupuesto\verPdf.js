import React from "react"
import Modal from 'react-modal';


Modal.setAppElement("#root")

export const ModalVerPdf = ({show, handleClose, id}) =>{

    const customStyles = {
        content: {
          inset: "unset",
          width: "90%",
          height: "95vh",
          borderRadius: "8px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Presupuesto #{id}</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <iframe src={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=123&codigocomprobante=${id}&tipoaccionpdf=0`} width="100%" height="90%" />
        </Modal>
    )
}