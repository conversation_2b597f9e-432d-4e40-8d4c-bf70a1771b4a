import React, { useState, useEffect } from "react"
import Mo<PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getConfigMP, modificarPickit   } from "../../../../redux/actions/mitienda.actions";
import ManualApp from './Manual App Comercio.pdf';
import PresentacionPickit from './Nueva Presentacion_LATAM pickit 2023.pdf'
import PropuestaComercial from './Propuesta Comercial  Develone 22112023.pdf'

Modal.setAppElement("#root")

export const ModalPickit = ({show, handleClose, handleClickAlert}) => {

    const defaultPickit = useSelector((state) => state.mitienda.defaultMp)
    const metodos = useSelector((state) => state.mitienda.metodos)
    const config = useSelector((state) => state.mitienda.configMP)

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const dispatch = useDispatch()

    const [input, setInput] = useState('')
    const [activo, setActivo] = useState('')

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const handleCheck = (e) => {
        e.preventDefault()
        if(e.target.checked === true){
            setActivo("1")
        }else{
            setActivo("0")
        }
    }

    const handleConfig = () => {
        dispatch(modificarPickit(activo,input.pickitkey,input.pickittoken))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
    }

    useEffect(() =>{
        dispatch(getConfigMP())
    }, [])

    useEffect(() => {
        setInput({pickitkey : config.pickitkey,
            pickittoken: config.pickittoken})
        setActivo(metodos.default_pickit)
    }, [config])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Configuracion Pickit</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                    <div style={{marginTop:15, marginBottom:15}}>
                        <div style={{marginTop:15, marginBottom:15}}>
                            <FormControlLabel
                                control={
                                <Checkbox/>
                                }
                                disabled={permisos_acciones?.activar === "0"}
                                style={{color:"black"}}
                                onChange={(e)=> handleCheck(e)}
                                checked={activo === "1" ? true : false}
                                label="Activar/Desactivar Pickit"
                            />
                        </div>
                        <TextField
                            label="Token"    
                            variant="outlined" 
                            fullWidth margin="normal" 
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                                }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                            value={input.pickittoken || ''}
                            onChange={handleChange}
                            name="pickittoken"
                        />
                        <TextField
                            label="Apikey"    
                            variant="outlined" 
                            fullWidth margin="normal" 
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                                }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                            value={input.pickitkey || ''}
                            onChange={handleChange}
                            name="pickitkey"
                        />
                    </div> 
                <Divider/>
                <div>
                    <a
                        href={PropuestaComercial}
                        download="PropuestaComercial.pdf"
                        target="_blank"
                        rel="noreferrer"
                        style={{margin:5}}
                    >
                        <Button variant="contained">Propuesta comercial</Button>
                    </a>
                    <a
                        href={ManualApp}
                        download="ManualApp.pdf"
                        target="_blank"
                        rel="noreferrer"
                        style={{margin:5}}
                    >
                        <Button variant="contained">Manual App</Button>
                    </a>
                    <a
                        href={PresentacionPickit}
                        download="PresentacionPickit.pdf"
                        target="_blank"
                        rel="noreferrer"
                        style={{margin:5}}
                    >
                        <Button variant="contained">Presentaci&oacute;n Pickit</Button>
                    </a>
                </div>
                <div align="right">
                <Button 
                    size="large" 
                    variant="contained" 
                    sx={{mt: 3}}
                    onClick={handleConfig}
                >Guardar</Button>
                </div> 
        </Modal>
    )
}