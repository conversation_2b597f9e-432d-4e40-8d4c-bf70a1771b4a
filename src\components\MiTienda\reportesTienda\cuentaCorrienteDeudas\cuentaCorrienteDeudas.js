import React, { useEffect, useState } from 'react';
import { Button, FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { 
    getListadoClientesByName, 
    getReporteCuentaCorrienteDeudas 

} from '../../../../redux/actions/mitienda.actions'
import ClipLoader from "react-spinners/ClipLoader";

export const CuentaCorrienteDeudas = () => {

    //Obtengo la informacion para la tabla
    const info = useSelector((state) => state.mitienda.reporteCuentaCorrienteDeudas)
    const clientes = useSelector((state) => state.mitienda.clientesByName)

    //Obtengo la ultima pagina para el componente Pagination
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaReporteCuentaCorrienteDeudas)
    //Creo la constante que usare para cambiar las paginas y su handle change
    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [diasdeuda, setDiasdeuda] = useState('30')
    const [clienteid, setClienteid] = useState('')

    //Declaro la variable para despachar acciones
    const dispatch = useDispatch()

    const [clienteInput, setClienteInput] = useState("")
    const handleChangeClienteInput = (e) => {
        e.preventDefault()
        setClienteInput(e.target.value)
    }

    const onSearch = () => {
        dispatch(getReporteCuentaCorrienteDeudas(diasdeuda,clienteid,1))
        dispatch(getListadoClientesByName('',''))
        clienteid('')
        clienteInput('')
    }

    useEffect(() =>{
        if(clienteInput.length > 4){
            dispatch(getListadoClientesByName(clienteInput,''))
        }
    }, [clienteInput])

    useEffect(() =>{
        dispatch(getReporteCuentaCorrienteDeudas(diasdeuda,clienteid,page))
    },[page])

    return (
        <div className="notificaciones-container">
            <div>
                <header className="titulo-notificaciones" style={{display:"flex", flexDirection:"column"}}>
                    <h3>Reporte: Cuentas Corrientes Deudas</h3>
                    <div style={{display:"flex"}}>
                    <FormControl sx={{mt:2, width:400, marginRight:5}}>
                        <InputLabel id="cliente-select-label">Cliente</InputLabel>
                        <Select
                            labelId="cliente-select-label"
                            id="cliente"
                            label="Cliente"
                            size="small"
                            name="clienteid"
                            value={clienteid || ''}
                            onChange={(e)=>setClienteid(e.target.value)}
                            >
                            <TextField
                                value={clienteInput}
                                name="clienteInput"
                                onChange={handleChangeClienteInput}
                                fullWidth
                                style={{padding:10}}
                            />
                            {               
                                clientes && clientes.map((c) =>
                                    <MenuItem value={c.clienteid} key={c.clienteid}>
                                    {(c.nombre.length + c.apellido.length) > 30 ?
                                        c.cuit+'-'+c.nombre.slice(0,10)+'.. '+c.apellido.slice(0,10)+'..' :
                                        c.cuit+'-'+c.nombre+' '+c.apellido
                                    }</MenuItem>
                                )
                            }
                        </Select>
                    </FormControl>
                    <TextField 
                        label="D&iacute;as de deuda"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{width:300, marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        value={diasdeuda}
                        name="diasdeuda"
                        onChange={(e) => setDiasdeuda(e.target.value)}
                    />
                    <Button onClick={onSearch} variant='contained' style={{height:40, marginTop:15}} >Buscar</Button>
                    </div>
                </header>
            
            </div>
            <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                    <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Cliente</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">CUIT</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Importe adeudado</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Importe a favor</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Saldo</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {
                            info.loading ? 
                            <TableRow sx={{p:20}}>
                                <TableCell colSpan={13} align='center'>
                                    <ClipLoader 
                                        loading={info.loading}
                                        size={50}
                                    />
                                </TableCell>
                            </TableRow> :
                            info.data.length > 0 ? info.data.map((row) => (
                                <TableRow
                                    key={row.productoid}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                >
                                    <TableCell style={{fontSize:"80%", padding:0, height:40, width:200}} align="center">
                                        {row.nombre}
                                    </TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.cuit}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        ${row.totalEnContra}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                        ${row.totalAFavor}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                        ${row.saldo}</TableCell>
                                </TableRow>
                            )):
                            <TableRow>
                                <TableCell colSpan={4}></TableCell>
                                <TableCell colSpan={4}>
                                    <h5>No hay informaci&oacute;n para mostrar</h5>
                                </TableCell>
                                <TableCell></TableCell>
                            </TableRow>}

                        </TableBody>
                    </Table>
                </TableContainer>
            </div>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}
