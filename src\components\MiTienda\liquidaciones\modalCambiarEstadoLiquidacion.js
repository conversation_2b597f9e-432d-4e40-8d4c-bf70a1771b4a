import React, { useState, useEffect } from "react";
import Modal from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, FormControl, InputLabel, MenuItem, Select } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { cambiarEstadoLiquidacion, getEstadosPedido } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root");

export const ModalCambiarEstadoLiquidacion = ({ show, handleClose, liquidacion, handleClickAlert }) => {
  const estados = useSelector((state) => state.mitienda.lista_estado_liquidacion);
  const [estado, setEstado] = useState(0);
  const dispatch = useDispatch();

  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      maxHeight: "90vh",
      borderRadius: "8px",
      maxWidth: "650px",
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  const handleChange = () => {
    // console.log('Selected estado:', estado);
    // console.log('Type of estado:', typeof estado);
    
    const estadoNum = Number(estado);
    if (!estado || isNaN(estadoNum) || estadoNum === 0) {
      // console.log('Invalid estado selected:', estado);
      handleClickAlert();
      return;
    }

    dispatch(cambiarEstadoLiquidacion(liquidacion[0].facturaid, estadoNum))
      .then((response) => {
        handleClose();
        setTimeout(function(){
          handleClickAlert();
        }, 2000);
      })
      .catch(error => {
        console.error("Error al cambiar estado de la liquidación:", error);
        handleClose();
        setTimeout(function(){
          handleClickAlert();
        }, 2000);
      });
  };

  // Debug useEffect
  // useEffect(() => {
  //   console.log('Estados loaded:', estados);
  //   if (estados && estados.length > 0) {
  //     console.log('First estado item:', estados[0]);
  //     console.log('pedidoplacaestadoid type:', typeof estados[0].pedidoplacaestadoid);
  //   }
  // }, [estados]);
  
  useEffect(() => {
    dispatch(getEstadosPedido());
  }, [dispatch]);

  useEffect(() => {
    if(liquidacion && liquidacion[0]){
      const estadoId = liquidacion[0].estadoid;
      // console.log('Setting estado from liquidacion:', estadoId, 'Type:', typeof estadoId);
      
      // More robust conversion
      const estadoNumber = estadoId ? Number(estadoId) : 0;
      // console.log('Converted to number:', estadoNumber);
      
      setEstado(isNaN(estadoNumber) ? 0 : estadoNumber);
    }
  }, [liquidacion]);

  return (
    show && 
    <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
      <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
        <h4>Cambiar estado de la liquidación</h4>
        <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
      </div>
      <Divider/>
      <div style={{margin:15}}>   
        <FormControl style={{marginTop:20}} fullWidth>
          <InputLabel id="estado">Estado</InputLabel>
          <Select
            labelId="estadolabel"
            id="estado"
            label="Estado"  
            size="small"
            name="estado"
            value={estado}
            onChange={e => {
              // console.log('Select onChange - Raw value:', e.target.value, 'Type:', typeof e.target.value);
              const numValue = Number(e.target.value);
              // console.log('Converted to number:', numValue);
              setEstado(numValue);
            }}
            MenuProps={{ keepMounted: true, disablePortal: true }}
          >
            <MenuItem value={0}>Seleccione una opción</MenuItem>
            {estados && estados.map((m) => {
              // Use the correct property name: pedidoplacaestadoid
              const pedidoplacaEstadoId = m.pedidoplacaestadoid;
              const numericValue = Number(pedidoplacaEstadoId);
              
              // console.log(`MenuItem ${m.nombre}: original=${pedidoplacaEstadoId}, converted=${numericValue}, isNaN=${isNaN(numericValue)}`);
              
              // Only render if we have a valid numeric value
              if (isNaN(numericValue)) {
                console.warn(`Skipping invalid pedidoplacaestadoid for ${m.nombre}:`, pedidoplacaEstadoId);
                return null;
              }
              
              return (
                <MenuItem value={numericValue} key={m.orden || m.pedidoplacaestadoid}>
                  {m.nombre.toUpperCase()}
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>
      </div>
      <Divider/>
      <div align="right">
        <Button 
          size="large"
          variant="contained"
          onClick={handleChange}
          sx={{ 
            mt: 3, 
            mr: 1,
            backgroundColor: '#1976d2',
            color: '#fff'
          }}
        >
          Cambiar
        </Button>
      </div>
    </Modal>
  );
};