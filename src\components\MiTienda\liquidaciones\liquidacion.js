import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  CircularProgress,
  Divider,
  IconButton,
  Snackbar,
  Tooltip,
  Typography,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  DialogActions,
} from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../ventas/ventas.css";
import { useDispatch } from "react-redux";
import {
  getComprobantesByLiquidacion,
  getLiquidacion,
} from "../../../redux/actions/mitienda.actions";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import MuiAlert from "@mui/material/Alert";
import { Edit } from "@mui/icons-material";
import DescriptionIcon from "@mui/icons-material/Description";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import { ModalCambiarEstadoLiquidacion } from "./modalCambiarEstadoLiquidacion";
import AttachFileIcon from "@mui/icons-material/AttachFile";

export const Liquidacion = (props) => {
  const id = props.match.params.id;
  const clienteid = props.match.params.clienteid;

  const data = useSelector((state) => state.mitienda.liquidacion);
  const dispatch = useDispatch();

  const [liquidacion, setLiquidacion] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [showEstadoModal, setShowEstadoModal] = useState(false);
  const handleCloseEstadoModal = () => setShowEstadoModal(false);
  const handleShowEstadoModal = () => setShowEstadoModal(true);

  const [isVendedor, setIsVendedor] = useState(false);

  const [openAlertEstado, setOpenAlertEstado] = useState(false);
  const handleClickAlertEstado = () => {
    setOpenAlertEstado(true);
  };
  const handleCloseAlertEstado = () => {
    setOpenAlertEstado(false);
  };

  const [openComprobantesModal, setOpenComprobantesModal] = useState(false);
  const comprobantesState = useSelector((state) => state.mitienda.comprobantes);

  useEffect(() => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    setIsVendedor(profileValues.includes(18) && !profileValues.includes(7));
  }, []);

  const handleViewComprobantes = async (liquidacionid) => {
    try {
      setOpenComprobantesModal(true);
      await dispatch(getComprobantesByLiquidacion(liquidacionid));
    } catch (error) {
      // Silently handle "no comprobantes found" case
      if (error.message !== "NO_COMPROBANTES") {
        console.error("Error fetching comprobantes:", error);
      }
    }
  };

  const handleCloseComprobantesModal = () => {
    setOpenComprobantesModal(false);
  };

  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });

  const estadoLiquidacion = useSelector(
    (state) => state.mitienda.cambio_estado_liquidacion
  );
  const [currentEstado, setCurrentEstado] = useState("");

  useEffect(() => {
    dispatch(getLiquidacion(id, clienteid));
  }, [dispatch, id, clienteid]);

  useEffect(() => {
    if (!data.loading) {
      if (data.data && data.data.length > 0) {
        setLiquidacion(data.data);
        setCurrentEstado(data.data[0].descripcionestado || "Sin estado");
        setIsLoading(false);
      }
    }
  }, [data]);

  useEffect(() => {
    if (estadoLiquidacion && estadoLiquidacion.success) {
      dispatch(getLiquidacion(id, clienteid));
    }
  }, [estadoLiquidacion, dispatch, id, clienteid]);

  const convertirANumero = (n) => {
    if (!n) return 0;
    if (typeof n === "number") return n;
    if (typeof n !== "string") n = String(n);

    let split = n.split(/\.|\,/);
    if (split.length > 2) {
      let aux = split[0] + split[1];
      let aux2 = parseFloat(aux + "." + split[2]);
      return aux2;
    } else if (split.length === 2) {
      let nuevo = parseFloat(split[0] + "." + split[1]);
      return nuevo;
    }
    return parseFloat(n);
  };

  const formatoFecha = (fecha) => {
    if (!fecha) return null;
    let aux = fecha.slice(0, 10);
    let aux2 = aux.split("-");
    return aux2[2] + "-" + aux2[1] + "-" + aux2[0];
  };

  const formatoFechaHora = (fecha) => {
    if (!fecha) return null;
    let aux = fecha.split(" ");
    let aux2 = aux[0].split("-");
    return (
      aux2[2] +
      "-" +
      aux2[1] +
      "-" +
      aux2[0] +
      " " +
      (aux[1] ? aux[1].slice(0, 5) : "")
    );
  };

  const getEstadoColor = (estado) => {
    switch (estado) {
      case "Liquidado":
        return "success";
      case "Pendiente":
        return "warning";
      case "Vencido":
        return "error";
      default:
        return "primary";
    }
  };

  return (
    <div className="ventas-container">
      {isLoading ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh",
          }}
        >
          <CircularProgress />
        </div>
      ) : liquidacion === "" ? (
        <div style={{ textAlign: "center", marginTop: "2rem" }}>
          <Typography variant="h6">
            No se encontró información de la liquidación
          </Typography>
          <Button
            component={Link}
            to="/MiTienda/Liquidaciones"
            variant="contained"
            startIcon={<ArrowBackIcon />}
            style={{ marginTop: "1rem" }}
          >
            Volver a Liquidaciones
          </Button>
        </div>
      ) : (
        <div>
          <div>
            <ModalCambiarEstadoLiquidacion
              show={showEstadoModal}
              handleClose={handleCloseEstadoModal}
              liquidacion={liquidacion}
              handleClickAlert={handleClickAlertEstado}
            />

            <Snackbar
              open={openAlertEstado}
              autoHideDuration={6000}
              onClose={handleCloseAlertEstado}
              anchorOrigin={{ vertical: "top", horizontal: "center" }}
            >
              <Alert
                onClose={handleCloseAlertEstado}
                severity={
                  estadoLiquidacion && estadoLiquidacion.success
                    ? "success"
                    : "error"
                }
                sx={{ width: 400 }}
              >
                <h5>
                  {estadoLiquidacion && estadoLiquidacion.mensaje
                    ? estadoLiquidacion.mensaje
                    : estadoLiquidacion && !estadoLiquidacion.success
                    ? "Error al actualizar el estado"
                    : "Estado actualizado correctamente"}
                </h5>
              </Alert>
            </Snackbar>
          </div>

          <div style={{ margin: 20 }}>
            <Link
              to="/Mitienda/Liquidaciones"
              style={{ color: "black", display: "inline-block" }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  fontSize: "130%",
                  marginBottom: 20,
                  width: "fit-content",
                }}
              >
                <ArrowBackIcon style={{ fontSize: "130%" }} />
                <span style={{ marginLeft: "5px" }}>
                  Volver a Liquidaciones
                </span>
              </div>
            </Link>

            <div className="div-titulo">
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  width: "25%",
                }}
              >
                <h3>Liquidación #{liquidacion && liquidacion[0].facturaid}</h3>
                <h4
                  style={{
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <Button
                      variant="contained"
                      color={getEstadoColor(currentEstado)}
                      style={{ width: 150, justifyContent: "center" }}
                    >
                      {currentEstado}
                    </Button>
                    {/* <div
                      onClick={handleShowEstadoModal}
                      style={{ cursor: "pointer", marginLeft: "5px" }}
                    >
                      <Tooltip title="Editar estado">
                        <IconButton size="large" color="primary">
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    </div> */}
                    <div
                      onClick={!isVendedor ? handleShowEstadoModal : undefined}
                      style={{
                        cursor: isVendedor ? "not-allowed" : "pointer",
                        marginLeft: "5px",
                      }}
                    >
                      <Tooltip
                        title={
                          isVendedor
                            ? "No tiene permisos para editar estado"
                            : "Editar estado"
                        }
                      >
                        <IconButton
                          size="large"
                          color="primary"
                          disabled={isVendedor}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    </div>
                  </div>
                </h4>
              </div>

              <div>
                {liquidacion[0].url_comprobante &&
                  liquidacion[0].url_comprobante !== "#" && (
                    // <Button
                    //   variant="contained"
                    //   style={{
                    //     width: 150,
                    //     justifyContent: "center",
                    //     margin: 5,
                    //   }}
                    //   component="a"
                    //   href={liquidacion[0].url_comprobante}
                    //   target="_blank"
                    //   rel="noopener noreferrer"
                    //   startIcon={<PictureAsPdfIcon />}
                    // >
                    //   Ver Comprobante
                    // </Button>
                    <Button
                      variant="contained"
                      style={{
                        width: 150,
                        justifyContent: "center",
                        margin: 5,
                      }}
                      startIcon={<PictureAsPdfIcon />}
                      onClick={() =>
                        handleViewComprobantes(liquidacion[0].facturaid)
                      }
                    >
                      Ver Comprobante
                    </Button>
                  )}
              </div>
            </div>
          </div>

          <div className="columnas">
            <div
              style={{
                width: "100%",
                display: "flex",
                flexWrap: "wrap",
                gap: "20px",
              }}
            >
              {/* Left Card - Información del cliente */}
              <Paper
                style={{
                  flex: "1 1 calc(50% - 10px)",
                  minWidth: "300px",
                  padding: "20px",
                  minHeight: "200px",
                  alignSelf: "flex-start",
                }}
              >
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <h4>
                    <strong>Información del cliente</strong>
                  </h4>
                </div>
                <Divider />
                <div style={{ padding: 10 }}>
                  <h6>
                    <strong>Nombre:</strong>{" "}
                    {(liquidacion && liquidacion[0].nombrecliente) ||
                      "No especificado"}
                  </h6>
                  <h6>
                    <strong>CUIT:</strong>{" "}
                    {(liquidacion && liquidacion[0].cuit) || "No especificado"}
                  </h6>
                  <h6>
                    <strong>Dirección:</strong>{" "}
                    {(liquidacion && liquidacion[0].direccion) ||
                      "No especificada"}
                  </h6>
                  <h6>
                    <strong>Teléfono:</strong>{" "}
                    {(liquidacion && liquidacion[0].telefono) ||
                      "No especificado"}
                  </h6>
                  <h6>
                    <strong>Email:</strong>{" "}
                    {(liquidacion && liquidacion[0].email) || "No especificado"}
                  </h6>
                </div>
              </Paper>

              {/* Right Card - Información de la liquidación */}
              <Paper
                style={{
                  flex: "1 1 calc(50% - 10px)",
                  minWidth: "300px",
                  padding: "20px",
                  minHeight: "200px",
                  alignSelf: "flex-start",
                }}
              >
                <h4>
                  <strong>Información de la liquidación</strong>
                </h4>
                <Divider />
                <div style={{ padding: 10 }}>
                  <h6>
                    <strong>Fecha liquidación:</strong>{" "}
                    {liquidacion && liquidacion[0].fechaliquidacion
                      ? formatoFechaHora(liquidacion[0].fechaliquidacion)
                      : "No especificada"}
                  </h6>
                  <h6>
                    <strong>N° Liquidación ARCA:</strong>{" "}
                    {(liquidacion && liquidacion[0].nro_liquidacion_arca) ||
                      "No especificado"}
                  </h6>
                  <h6>
                    <strong>Total:</strong> $
                    {(liquidacion &&
                      convertirANumero(liquidacion[0].total).toFixed(2)) ||
                      "0.00"}
                  </h6>
                </div>
              </Paper>

              {/* Bottom Card - Detalle de la liquidación */}
              <Paper
                style={{
                  flex: "1 1 100%",
                  minWidth: "300px",
                  padding: "20px",
                  minHeight: "300px",
                  alignSelf: "flex-start",
                }}
              >
                <h4>
                  <strong>Detalle de la liquidación</strong>
                </h4>
                <Divider style={{ marginBottom: "15px" }} />
                <TableContainer style={{ width: "100%" }}>
                  <Table aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell style={{ fontWeight: "bold" }}>
                          Descripción
                        </TableCell>
                        <TableCell
                          style={{ fontWeight: "bold" }}
                          align="center"
                        >
                          Código
                        </TableCell>
                        <TableCell
                          style={{ fontWeight: "bold" }}
                          align="center"
                        >
                          Cantidad
                        </TableCell>
                        <TableCell
                          style={{ fontWeight: "bold" }}
                          align="center"
                        >
                          Precio Unitario
                        </TableCell>
                        <TableCell
                          style={{ fontWeight: "bold" }}
                          align="center"
                        >
                          IVA %
                        </TableCell>
                        <TableCell
                          style={{ fontWeight: "bold" }}
                          align="center"
                        >
                          Descuento
                        </TableCell>
                        <TableCell
                          style={{ fontWeight: "bold" }}
                          align="center"
                        >
                          Total
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {liquidacion &&
                        liquidacion[0].detalle &&
                        liquidacion[0].detalle.map((row) => (
                          <TableRow key={row.facturadetalleid}>
                            <TableCell>{row.descripcion}</TableCell>
                            <TableCell align="center">
                              {row.codigoarticulo}
                            </TableCell>
                            <TableCell align="center">{row.cantidad}</TableCell>
                            <TableCell align="center">
                              ${convertirANumero(row.preciosiniva).toFixed(2)}
                            </TableCell>
                            <TableCell align="center">{row.iva}%</TableCell>
                            <TableCell align="center">
                              ${convertirANumero(row.descuento).toFixed(2)}
                            </TableCell>
                            <TableCell align="center">
                              ${convertirANumero(row.precioventa).toFixed(2)}
                            </TableCell>
                          </TableRow>
                        ))}
                      <TableRow>
                        <TableCell colSpan={6} align="right">
                          <strong>Total:</strong>
                        </TableCell>
                        <TableCell align="center">
                          <strong>
                            $
                            {liquidacion &&
                              convertirANumero(liquidacion[0].total).toFixed(2)}
                          </strong>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </div>
          </div>
        </div>
      )}

      <Dialog
        open={openComprobantesModal}
        onClose={handleCloseComprobantesModal}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Comprobantes de Liquidación #
          {liquidacion && liquidacion[0]?.facturaid}
        </DialogTitle>
        <DialogContent>
          {comprobantesState.loading ? (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                padding: "20px",
              }}
            >
              <CircularProgress />
            </div>
          ) : comprobantesState.error ? (
            comprobantesState.error === "NO_COMPROBANTES" ? (
              <Typography
                variant="body1"
                align="center"
                style={{ padding: "20px" }}
              >
                No se encontraron comprobantes para esta liquidación
              </Typography>
            ) : (
              <MuiAlert severity="error">{comprobantesState.error}</MuiAlert>
            )
          ) : comprobantesState.data ? (
            <List>
              {Object.values(comprobantesState.data).map((doc) => (
                <ListItem
                  key={doc.factura_imagenesid}
                  button
                  component="a"
                  href={doc.url_documento}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ListItemIcon>
                    <AttachFileIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={doc.nombre}
                    secondary={`Haz clic para ver el documento`}
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography
              variant="body1"
              align="center"
              style={{ padding: "20px" }}
            >
              No hay comprobantes disponibles
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseComprobantesModal}>Cerrar</Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
