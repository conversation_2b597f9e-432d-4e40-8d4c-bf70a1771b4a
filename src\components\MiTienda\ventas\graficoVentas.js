import { Button, Paper } from '@mui/material';
import React, { useEffect } from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
  } from 'chart.js';

import { Line } from 'react-chartjs-2';
import { getPedidosGrafico } from '../../../redux/actions/mitienda.actions';
import { crearData, crearLabels, totalVentas } from './utilsVentas';
import { useDispatch, useSelector } from 'react-redux';
import { useState } from 'react';
import { FilterListOutlined } from '@mui/icons-material';
import { ModalFiltrosGraficoVentas } from './modalFiltrosGrafico';
import moment from 'moment';

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
);


export const GraficoVentas = () => {

    const options = {
        responsive: true,
        scales: {
            y: {
                ticks: {
                    // Include a dollar sign in the ticks
                    callback: function(value, index, ticks) {
                        return '$' + value;
                    }
                }
            }
        }
    };

    const pedidos = useSelector((state) => state.mitienda.pedidosGrafico)

    const dispatch = useDispatch()

    let labels = crearLabels(pedidos)
    let data2 = crearData(pedidos,labels)
    let info = totalVentas(pedidos)

    const data = {
        labels,
        datasets: [
          {
            label: 'Compras',
            data: data2.map((p) => p),
            borderColor: 'rgb(39, 158, 255)',
            backgroundColor: 'rgb(120, 193, 243)',
          },
        ],
    };

    const [show, setShow] = useState('');
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    let fechaActual = moment()
    // let ultimosDias = moment().day(-30)
    let ultimosDias = moment().subtract(30, 'days')

    const [fechaDesde, setFechaDesde] = useState(ultimosDias.toISOString().substring(0,10))
    const handleChangeFechaDesde = (e) =>{
        setFechaDesde(e.target.value)
    }

    const [fechaHasta, setFechaHasta] = useState(fechaActual.toISOString().substring(0,10))
    const handleChangeFechaHasta = (e) =>{
        setFechaHasta(e.target.value)
    }

    const reset = (e) =>{
        setFechaDesde(ultimosDias.toISOString().substring(0,10))
        setFechaHasta(fechaActual.toISOString().substring(0,10))
    }

    const search = () =>{
        dispatch(getPedidosGrafico('','','','',fechaDesde,fechaHasta,''))
        handleClose()
    }

    useEffect(() => {
        dispatch(getPedidosGrafico('','','','',fechaDesde,fechaHasta,''))
    }, [])

    return (
        <div className='ventas-container'>
            {
                <ModalFiltrosGraficoVentas      
                    show={show}     
                    handleClose={handleClose}      
                    handleChangeFechaDesde={handleChangeFechaDesde}    
                    fechaDesde={fechaDesde}   
                    handleChangeFechaHasta={handleChangeFechaHasta}
                    fechaHasta={fechaHasta}
                    reset={reset}
                    search={search}
                />
            }
            <div className="titulo-ventas" style={{ width:"90%", marginLeft:50}}>
                <div style={{display:"flex", justifyContent:"space-between", width:"95%" }}>
                    <h3>M&eacute;tricas</h3>  
                    <Button variant="outlined" onClick={handleShow}>
                        <FilterListOutlined/> Filtrar
                    </Button> 
                </div>   
                <div style={{display:"flex", marginTop:20, marginLeft:-10}}>
                <Paper className="paper-home">
                    <h6 className="h6-home">TOTAL EN VENTAS </h6>
                    <h5>${info.total}</h5>
                </Paper>
                <Paper className="paper-home">
                    <h6 className="h6-home">CANTIDAD VENTAS</h6>
                    <h5>{info.cantidad}</h5>
                </Paper>
                </div>
            </div> 
            <div style={{width:'95%', marginTop:50, marginBottom:50, marginLeft:50}}>
                <Paper style={{padding:40}}>
                    <h4>Ventas</h4>
                    <Line options={options} data={data} style={{width:'90%'}}/>
                </Paper>
            </div>
        </div>
    )
}