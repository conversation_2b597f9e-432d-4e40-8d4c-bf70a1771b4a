import React, { useEffect, useState } from "react";
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { useDispatch, useSelector } from "react-redux";
import { configPagos, getDefaultMetodosDePago, getMetodosPago, limpiarConfigMetodos } from "../../../../redux/actions/mitienda.actions";

import "./metodosDePago.css"

import Modal from 'react-modal';
import { ConfigMetodos } from "./configMetodos";
import MuiAlert from '@mui/material/Alert';
import { Snackbar } from "@mui/material";

Modal.setAppElement("#root")

export const MetodosDePago = () => {

    const defaultMp = useSelector((state) => state.mitienda.defaultMp)
    const mensaje = useSelector((state) => state.mitienda.metodosModificados)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()) || p.archivo.toLowerCase().includes("Mitienda/MetodosDePago".toLocaleLowerCase()))[0]

    const [show, setShow] = useState(false);

    const handleCloseModal = () => {
        dispatch(limpiarConfigMetodos())
        dispatch(getDefaultMetodosDePago())
        setShow(false);
    }
    const handleShow = () => setShow(true);

    const [input, setInput] = useState({
        default_efectivo: '',
        default_mercadopago: ''
    })

    const [nombreMetodo, setNombreMetodo] = useState("") 

    const handleConfigMetodo = (e, nombre) => {
        e.preventDefault()
        handleShow()
        setNombreMetodo(nombre)
    }

    const dispatch = useDispatch()

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    useEffect(() =>{

        dispatch(getDefaultMetodosDePago())
        
    }, [mensaje])

    useEffect(() => {
        if(defaultMp !== ''){
            setInput({
                default_efectivo: defaultMp.default_efectivo,
                default_mercadopago: defaultMp.default_mercadopago
            })
        }
    }, [defaultMp])

    return (
        <div className="container-mp">
            {
                <ConfigMetodos 
                    handleClose={handleCloseModal} 
                    nombre={nombreMetodo} 
                    show={show} 
                    handleClickAlert={handleClickAlert}
                    getDefaultMetodosDePago={getDefaultMetodosDePago}
                />
            }
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={mensaje.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{mensaje.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <header className="header-mde">
                <h3 style={{marginBottom:20}}>M&eacute;todos de pago</h3>
            </header>
            {
                permisos_acciones?.listar === "0" ? null :
                <div className="list">
                <Card sx={{ width: 350, height:360, float:"left",position:"relative", margin:5}}>
                    <CardMedia
                        component="img"
                        height="200"
                        image="https://images.prismic.io/statrys/c91fa6f6-40e7-4a57-ad60-972dc1e51138_ENG_int-trade-payment-methods.png?auto=compress%2Cformat"
                        alt="Contado"
                    />
                    <CardContent>
                        <Typography gutterBottom variant="h5" component="div">
                            Contado
                        </Typography>
                        <Typography>
                        <h6 style={{display:"flex",marginBottom:-10}}>Metodo&nbsp;{input.default_efectivo === "1" ? <h6 style={{color:"green"}}> activado</h6> : <h6 style={{color:"red"}}> desactivado</h6>} </h6>
                        </Typography>
                    </CardContent>
                    <CardActions>
                        <Button
                            id="basic-button"
                            onClick={(e) => handleConfigMetodo(e,'Contado')}
                            variant="outlined" fullWidth
                            disabled={permisos_acciones?.modificar === "0"}
                        >
                            Configuracion
                        </Button>
                    </CardActions>
                </Card>
                <Card sx={{ width: 350, height:360, float:"left",position:"relative", margin:5}}>
                    <CardMedia
                        component="img"
                        height="200"
                        image="https://images.prismic.io/statrys/c91fa6f6-40e7-4a57-ad60-972dc1e51138_ENG_int-trade-payment-methods.png?auto=compress%2Cformat"
                        alt="mercadolibre"
                    />
                    <CardContent>
                        <Typography gutterBottom variant="h5" component="div">
                        Mercado Pago
                        </Typography>
                        <Typography>
                        <h6 style={{display:"flex",marginBottom:-10}}>Metodo&nbsp;{input.default_mercadopago === "1" ? <h6 style={{color:"green"}}> activado</h6> : <h6 style={{color:"red"}}> desactivado</h6>} </h6>
                        </Typography>
                    </CardContent>
                    <CardActions>
                        <Button
                            id="basic-button"
                            onClick={(e) => handleConfigMetodo(e,'Mercado Pago')}
                            variant="outlined" fullWidth
                            disabled={permisos_acciones?.modificar === "0"}
                        >
                            Configuracion
                        </Button>
                    </CardActions>
                </Card>

                </div>
            
            }
              
        </div>
    )
}