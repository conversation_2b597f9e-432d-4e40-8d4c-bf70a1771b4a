import React, { useEffect } from "react";
import { getPresupuesto } from "../../../redux/actions/mitienda.actions";
import { useDispatch, useSelector } from "react-redux";
import {
  Button,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { ArrowBack } from "@mui/icons-material";
import { Link } from "react-router-dom";

export const Presupuesto = (props) => {
  const info = useSelector((state) => state.mitienda.presupuesto);

  const id = props.match.params.id;

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getPresupuesto(id));
  }, [id]);

  return (
    <div className="ventas-container">
      <div style={{ margin: 20 }}>
        <Link to="/Mitienda/Presupuestos" style={{ color: "black" }}>
          <div style={{ display: "flex", fontSize: "130%", marginBottom: 20 }}>
            <ArrowBack style={{ fontSize: "130%" }} />
            Volver a Presupuesto
          </div>
        </Link>
        <div
          className="div-titulo"
          style={{ marginLeft: 20, marginBottom: 15 }}
        >
          <div
            style={{ display: "flex", flexDirection: "column", width: "45%" }}
          >
            <h3>Presupuesto #{id}</h3>
            <h4>Fecha: {info !== "" ? info.cabecera.fechapresupuesto : ""}</h4>
            <h5>
              Nombre:{" "}
              {info !== "" ? info.cabecera.presupuesto_nombre : "No definido"}
            </h5>
          </div>
          <div>
            <Button
              variant="contained"
              style={{ width: 150, justifyContent: "center", margin: 5 }}
            >
              {/* <a 
                            href={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=123&codigocomprobante=${id}&tipoaccionpdf=1`}
                            target="_blank"
                            style={{color:"white"}}
                        >  */}
              <a
                href={`${
                  process.env.REACT_APP_SERVER
                }dompdf/generarcomprobante_pdf.php?tipocomprobante=123&codigocomprobante=${id}&tipoaccionpdf=1&tiendaid=${localStorage.getItem(
                  "tiendausuario"
                )}`}
                target="_blank"
                style={{ color: "white" }}
              >
                Imprimir
              </a>
            </Button>
          </div>
        </div>
        <div style={{ display: "flex" }}>
          <div className="columna1-tabla">
            <TableContainer component={Paper} style={{ width: "100%" }}>
              <Table aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell
                      style={{
                        fontWeight: "bold",
                        fontSize: "110%",
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <h4>
                        <strong>Detalle del presupuesto</strong>
                      </h4>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {info &&
                    Object.values(info.detalle).map(
                      (row) =>
                        typeof row === "object" && (
                          <TableRow
                            key={row.codigoarticulo}
                            sx={{
                              "&:last-child td, &:last-child th": { border: 0 },
                            }}
                          >
                            <TableCell
                              style={{ fontSize: "100%" }}
                              align="left"
                            >
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                }}
                              >
                                <div>
                                  <h6>
                                    <strong>
                                      {row.nombre_producto} {row.nombrecolor}{" "}
                                      {row.nombretalla}
                                    </strong>
                                  </h6>
                                  <h6>Codigo: {row.codigoarticulo}</h6>
                                  <h6>Cantidad: {row.cantidad}</h6>
                                </div>
                                <h6>
                                  <strong>${row.preciototal}</strong>
                                </h6>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                    )}
                </TableBody>
              </Table>
            </TableContainer>
          </div>

          <div className="columna2">
            <Paper
              className="columna2-papper-info-cliente"
              style={{ marginTop: 0 }}
            >
              <div style={{ display: "flex", justifyContent: "space-between" }}>
                <h4>
                  <strong>Informaci&oacute;n del cliente</strong>
                </h4>
              </div>
              <Divider />
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  overflow: "hidden",
                  marginTop: 10,
                }}
              >
                <h6>
                  <strong>Nombre:</strong>{" "}
                  {info !== "" ? info.cabecera.nombrecliente : ""}
                </h6>
                <h6>
                  <strong>CUIT:</strong>{" "}
                  {info !== "" ? info.cabecera.cuitcliente : ""}
                </h6>
                <h6>
                  <strong>Direcci&oacute;n:</strong>
                  {info !== "" ? info.cabecera.direccioncliente : ""}
                </h6>
              </div>
            </Paper>
          </div>
        </div>
      </div>
    </div>
  );
};
