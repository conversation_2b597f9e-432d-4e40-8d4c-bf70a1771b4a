function replaceNumberForName(dia){
    switch (dia) {
        case 1:
            return "Lunes"
        case 2:
            return "<PERSON>es"
        case 3:
            return "Miercoles"
        case 4:
            return "Jueves"
        case 5:
            return "Viernes"
        case 6:
            return "Sabado"
        case 7:
            return "<PERSON>"
    }
}

function newSchedule (schedule) {

    let array = []

    for(let j = 0; j < 7; j++){
        array[j] = {
            dia: j+1,
            horarios: []
        }
    }

    for(let i = 0; i < schedule.length; i++){
        for(let k=0; k < array.length; k++){
            if(array[k].dia === schedule[i].day){
                array[k].horarios.push({
                    open: schedule[i].open,
                    close: schedule[i].close
                })
            }
        }
    }

    for(let h = 0; h < array.length; h++){
        array[h].dia = replaceNumberForName(array[h].dia)
    }

    let limpio = array.filter(a => a.horarios.length > 0)

    return limpio
}

module.exports = {
    newSchedule,
}