/* .container-home {
    width: 100%;    
    height: 100vh;   
    background-color: #EEEEEE; 
    background-position: center center;    
    background-repeat: no-repeat;    
    background-size: cover;  
    overflow-y: auto;  
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
} */

.container-home {
    width: 100%;
    min-height: 100vh; /* Changed from height */
    background-color: #EEEEEE;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    /* Remove justify-content: center */
    align-items: center;
    padding: 20px 0; /* Add some vertical padding */
}

.container-home::-webkit-scrollbar {
    display: none;
}



.header-home{
    display: flex;
    margin-bottom: 30px;
}



.img-home{
    width: 620px;
    height: 500px;
}

.h2-home{
    color: black;
    align-self:center; 
    margin-left:30px;
}

/* .list-cards{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    float: left;
} */

.list-cards {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    max-width: 1400px;
    padding: 0 20px 40px; /* Added bottom padding */
    margin-top: 20px;
    /* Remove float: left */
}

.paper-home{
    width: 250px;
    height: 100px;
    padding: 20px;
    margin: 10px;
}

.h6-home{
    color: grey;
}

.graphs-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}



.graph-paper {
  padding: 20px;
  flex: 1;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.graph-stats {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.stat-item {
  background: #f5f5f5;
  padding: 10px 20px;
  border-radius: 4px;
  text-align: center;
}

.stat-item h6 {
  margin: 0;
  color: #666;
}

.stat-item h5 {
  margin: 5px 0 0;
  color: #333;
}


.graphs-container {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.graph-paper {
    flex: 1;
    padding: 20px;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.graph-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
}

@media only screen and (max-width: 1200px) {

    .container-home {
        width: 100%;    
        height: 110vh;   
        background-color: #EEEEEE; 
        background-position: center center;    
        background-repeat: no-repeat;    
        background-size: cover;  
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .header-home{
        display: flex;
        flex-direction: column;
    }

    .img-home{
        width: 520px;
        height: 400px;
    }
}

@media only screen and (max-width:539px){
    .header-home{
        display: flex;
        flex-direction: column;
    }

    .img-home{
        width: 420px;
        height: 300px;
    }

    .h2-home{
        color: black;
        align-self:center; 
        font-size: 30px;
        padding:10px;
    }

    .container-home {
        width: 100%;    
        height: 100vh;   
        background-color: #EEEEEE; 
        background-position: center center;    
        background-repeat: no-repeat;    
        background-size: cover;  
        overflow-y: auto;  
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

@media only screen and (max-width:400px){
    .header-home{
        display: flex;
        flex-direction: column;
    }

    .img-home{
        width: 320px;
        height: 200px;
    }

    .h2-home{
        color: black;
        align-self:center; 
        font-size: 30px;
        padding:10px;
    }

    .container-home {
        width: 100%;    
        height: 110vh;   
        background-color: #EEEEEE; 
        background-position: center center;    
        background-repeat: no-repeat;    
        background-size: cover;  
        overflow-y: auto;  
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

@media only screen and (max-height:1000px){

    .container-home {
        width: 100%;    
        height: 120vh;   
        background-color: #EEEEEE; 
        background-position: center center;    
        background-repeat: no-repeat;    
        background-size: cover;  
        overflow-y: auto;  
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

@media only screen and (max-height:601px){

    .container-home {
        width: 100%;    
        height: 120vh;   
        background-color: #EEEEEE; 
        background-position: center center;    
        background-repeat: no-repeat;    
        background-size: cover;  
        overflow-y: auto;  
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}


