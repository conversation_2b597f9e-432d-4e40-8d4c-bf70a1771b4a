import React from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";

Modal.setAppElement("#root")

export const ModalFiltros = ({
  show, 
  handleClose, 
  handleChangeNombre, 
  nombre,
  handleChangeCodigo, 
  codigo,
  search, 
  reset
}) =>{

  const customStyles = {
    content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
    },
  };


  return (
      show ? 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
            <h4>FILTRAR</h4>
            <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
        </div>
        <Divider/>
        <div style={{display:"flex", flexDirection:"column"}}>
                  
            <TextField 
                sx={{mt:3}}
                label="Codigo articulo"  
                variant="outlined" 
                margin="codigo" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                        height: 40,
                        fontSize:17
                    },
                }}
                name="nombre"
                onChange={(e) => handleChangeCodigo(e)}
                value={codigo}
            />
            <TextField 
                sx={{mt:4}}
                label="Nombre articulo"  
                variant="outlined" 
                margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                        height: 40,
                        fontSize:17
                    },
                }}
                name="nombre"
                onChange={(e) => handleChangeNombre(e)}
                value={nombre}
            />

        </div>
        <div align="center">
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={search}
            >Aplicar filtros</Button>
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={() => {
                    reset()
                }}
            >Limpiar filtros</Button>
        </div>
      </Modal> : null
  ) 
}