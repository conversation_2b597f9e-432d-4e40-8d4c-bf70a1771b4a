import React, { useEffect } from "react"
import Modal from 'react-modal';
import Divider from '@mui/material/Divider';
import Select from '@mui/material/Select';
import { useDispatch, useSelector } from "react-redux";
import { Button, FormControl, InputLabel, MenuItem, TextField } from "@mui/material";
import { getVendedores } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalFiltrosReportePorVendedor = ({
  show, 
  handleClose, 
  handleChangeFecha_desde,
  fecha_desde,
  handleChangeFecha_hasta,
  fecha_hasta,
  handleChangeVendedor,
  vendedor,
  search,
  reset
}) =>{

  const vendedores = useSelector((state)=> state.mitienda.vendedores)  

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };

  const dispatch = useDispatch()

  //esta funcion se ejecuta una sola vez al montar el componente
  //aca obtenemos la informacion para los selects de los filtros
  useEffect(() =>{
      dispatch(getVendedores())
  }, [])

  return (
      show && 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
              <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                  <h4>FILTRAR</h4>
                  <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
              </div>
              <Divider/>
              <div style={{display:"flex", flexDirection:"column"}}>
                  <FormControl sx={{mt:4}}>
                      <InputLabel id="vendedor-select-label">Vendedor</InputLabel>
                      <Select
                      labelId="vendedor-select-label"
                      id="vendedor-select"
                      label="Vendedores"
                      size="small"
                      name="vendedor"
                      value={vendedor}
                      onChange={handleChangeVendedor}
                      MenuProps={{ keepMounted: true, disablePortal: true }}
                      >
                      <MenuItem value="">Seleccione una opcion</MenuItem>
                      {
                          vendedores && vendedores.map((c) =>
                              <MenuItem value={c.usuarioid} key={c.usuarioid}>{c.nombre+' '+c.apellido}</MenuItem>
                          )
                      }
                      </Select>
                  </FormControl>
                  <div style={{display:"flex", flexDirection:"column", marginTop:15}}>
                      <h6>Fecha desde</h6>
                      <TextField
                          style={{marginBottom:20}}
                          variant="outlined"
                          InputLabelProps={{
                              style: {
                                  marginBottom:10,
                                  marginTop: -7
                              },
                          }}
                          inputProps={{
                              style: {
                                  height: 40,
                                  fontSize:17
                              },
                          }}
                          name="fechaDesde"
                          onChange={(e) => handleChangeFecha_desde(e)}
                          value={fecha_desde}
                          type="date"
                      />
                  </div>

                  <div style={{display:"flex", flexDirection:"column"}}>
                      <h6>Fecha hasta</h6>
                      <TextField
                          style={{marginBottom:20}}
                          variant="outlined"
                          InputLabelProps={{
                              style: {
                                  marginBottom:10,
                                  marginTop: -7
                              },
                          }}
                          inputProps={{
                              style: {
                                  height: 40,
                                  fontSize:17
                              },
                          }}
                          name="fechaHasta"
                          onChange={(e) => handleChangeFecha_hasta(e)}
                          value={fecha_hasta}
                          type="date"
                      />
                  </div>
              </div>
              <div align="center">
              <Button
                  size="large" 
                  variant="contained" 
                  sx={{mt: 3}} fullWidth 
                  onClick={search}
              >Aplicar filtros</Button>
              <Button 
                  size="large" 
                  variant="contained" 
                  sx={{mt: 3}} fullWidth 
                  onClick={reset}
              >Limpiar filtros</Button>
              </div>
      </Modal>
  )
}