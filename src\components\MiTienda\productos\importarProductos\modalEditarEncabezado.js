import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel, MenuItem, Select, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { editarEncabezadoImportacion, getProveedoresSinPaginado } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalEditarEncabezado = ({show, handleClose, datos, handleClickAlert}) =>{

    const proveedores = useSelector((state) => state.mitienda.proveedoresSinPaginado)

    const [input, setInput] = useState('')

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [archivo, setArchivo] = useState('')

    const dispatch = useDispatch()

    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(editarEncabezadoImportacion(input))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    }

    useEffect(()=>{
        setInput(datos)
    },[datos])

    useEffect(()=>{
        dispatch(getProveedoresSinPaginado())
    },[])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Editar</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>
                <div style={{display:"flex", flexDirection:"column"}}>
                    <h6>Proveedor</h6>
                    <Select
                    size="small"
                    name="proveedorid"
                    value={input.proveedorid || ''}
                    onChange={handleChange}
                    style={{height:45, marginBottom:10}}
                    MenuProps={{ keepMounted: true, disablePortal: true }}
                    fullWidth
                    >
                    <MenuItem value="">Seleccione una opcion</MenuItem>
                    {               
                        proveedores && proveedores.map((t) =>
                            <MenuItem value={t.proveedorid} key={t.proveedorid}>{t.nombre}</MenuItem>
                        )
                    }
                </Select>
                </div>
                <div style={{display:"flex", flexDirection:"column", marginBottom:15}}>
                <h6>T&iacute;tulo</h6>
                <TextField 
                    name="titulo"
                    value={input.titulo || ''}
                    onChange={(e)=> handleChange(e)}
                    fullWidth 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                </div>
                <div style={{display:"flex", flexDirection:"column", marginBottom:15}}>
                <h6>Descripci&oacute;n</h6>
                <TextField 
                    name="descripcion"
                    value={input.descripcion || ''}
                    onChange={(e)=> handleChange(e)}
                    fullWidth
                    multiline
                    minRows={5}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                </div>
                <FormControlLabel
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black"}}
                    onChange={(e)=> handleCheck(e)}
                    checked={input.activo == "1" ? true : false}
                    label="Activo"
                    name="activo"
                />
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large" 
                        variant="contained" 
                        sx={{mt: 3}}
                        onClick={(e) => handleOnClick(e)}
                    > Guardar
                    </Button>
                </div>
        </Modal>
    )
}