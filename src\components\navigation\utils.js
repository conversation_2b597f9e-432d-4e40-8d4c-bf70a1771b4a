function sort(lista){
   
    let array = [];
    let nombresDireccion = lista.map(l => l.nombredireccion)
    let setNombresDireccion = new Set(nombresDireccion)
    nombresDireccion = Array.from(setNombresDireccion)

    for(let i=0; i < nombresDireccion.length; i++){
        array[i] = []
        if(nombresDireccion[i] === 'Bed Sensor'){
            array[i] = {
                nombredireccion: 'SleepWellnessTracker',
                menues: []
            }
        }else{
            array[i] = {
                nombredireccion: nombresDireccion[i],
                menues: []
            }
        }
        
    }

    let myset = ''
    let aux = ''

    for(let j=0; j < array.length; j++){
        myset = new Set(lista.filter(a => a.nombredireccion === array[j].nombredireccion).map(b => b.nombremenu))
        array[j].menues = Array.from(myset)        
    }

    for(let k=0; k < array.length; k++){
        for(let h=0; h < array[k].menues.length; h++){
            aux = array[k].menues[h]
            array[k].menues[h] = {
                nombremenu: aux,
                submenues: []
            }
        }
    }

    for(let k=0; k < array.length; k++){
        for(let h=0; h < array[k].menues.length; h++){
            myset = new Set(lista.filter(a => a.nombremenu === array[k].menues[h].nombremenu).map(b => ({
                nombresubmenu: b.nombresubmenu,
                urlsubmenu: b.urlsubmenu,
                urlsubmenu2: b.urlsubmenu2
            })))
            myset = Array.from(myset)
            for(let j=0; j < myset.length; j++){ 
                array[k].menues[h].submenues[j] = myset[j]
            }
        }
    }

    for(let a=0; a < array.length; a++){
        for(let b=0; b < array[a].menues.length; b++){
            for(let c=0; c < array[a].menues[b].submenues.length; c++){
                array[a].menues[b].submenues[c].urlsubmenu = array[a].menues[b].submenues[c].urlsubmenu
                array[a].menues[b].submenues[c].urlsubmenu2 = array[a].menues[b].submenues[c].urlsubmenu2
                array[a].menues[b].submenues[c].nombresubmenu = array[a].menues[b].submenues[c].nombresubmenu
            }
        }
    }

    return array
}

module.exports = {
    sort
}