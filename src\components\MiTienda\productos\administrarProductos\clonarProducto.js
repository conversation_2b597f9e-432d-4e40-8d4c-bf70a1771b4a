import { Paper, TextField, Button, InputAdornment, MenuItem, InputLabel, Select, FormControl, Snackbar, FormControlLabel, Checkbox } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { 
    clonarProducto, 
    getProductoById, 
    getTallas,
    getTasasIva, 
    getMonedas, 
    getTipoVenta, 
    getCategoriasSinPag, 
    getMarcasTienda, 
    getColoresTienda, 
    getSubCategoriasByCategoria,
    getDescuentosSinPaginado
} from "../../../../redux/actions/mitienda.actions";
import { AgregarCategoria } from "../../categorias/categorias/agregarCategoria";
import { AgregarMarca } from "../../marcasProductos/marcasProductos/agregarMarcaProducto";
import { AgregarColorTienda } from "../../coloresTienda/agregarColorTienda";
import { AgregarTalla } from "../../tallas/tallas/agregarTalla";
import { AgregarSubcategoria } from "../../categorias/categorias/agregarSubcategoria";
import JoditEditor from 'jodit-react';
import MuiAlert from '@mui/material/Alert';
import { useRef } from "react";
import { ArrowBack } from "@mui/icons-material";

export const ClonarProducto =(props)=>{

    const id = props.match.params.id

    const producto = useSelector((state) => state.mitienda.productoById)

    const okProductoClonado= useSelector((state) => state.mitienda.okClonarProducto)

    const tasas = useSelector((state) => state.mitienda.tasas)
    const monedas = useSelector((state) => state.mitienda.monedas)
    const colores = useSelector((state) => state.mitienda.colores)
    const marcas = useSelector((state) => state.mitienda.marcas)
    const categorias = useSelector((state) => state.mitienda.categoriasSinPag)
    const tallas = useSelector((state) => state.mitienda.tallas)
    const okCategoria = useSelector((state) => state.mitienda.okConfigCategoria)
    const okMarca = useSelector((state) => state.mitienda.okConfigMarca)
    const okColor = useSelector((state) => state.mitienda.okConfigColorTienda)
    const okSubcategoria = useSelector((state) => state.mitienda.okConfigSubCategoria)
    const okMedida = useSelector((state) => state.mitienda.okConfigTalla)
    const subcategorias = useSelector((state) => state.mitienda.subcategorias)
    const descuentos = useSelector((state) => state.mitienda.descuentosSinPaginado)

    const editor = useRef(null);
	const [content, setContent] = useState('');
    
    const [input, setInput] = useState('') 

    const handleChange = (e) =>{
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value})
    }

    const dispatch = useDispatch()

    const handleSelect = (event) => {
        setInput({
            ...input,
            [event.target.name]: event.target.value
        })
    }

    const [check, setCheck] = useState("0") 

    const handleCheck = (e) => {
        e.preventDefault()
        if(e.target.checked === true){
            setCheck("1")
        }else{
            setCheck("0")
        }
    }

    const [precio, setPrecio] = useState('')

    const handleAdd = (e) =>{
        e.preventDefault()
        let numero = input.precioCosto
        if(input.precioCosto.includes(",") && input.precioCosto.includes(".")){
            let array = input.precioCosto.split(",")
            let array2 = array[0].split(".")
            numero = array2[0]+array2[1]+"."+array[1]
        }else{
            if(input.precioCosto.includes(",") && !input.precioCosto.includes(".")){
                let array = input.precioCosto.split(",")
                numero = array[0]+"."+array[1]
            }else{
                if(!input.precioCosto.includes(",") && !input.precioCosto.includes(".")){
                    numero = input.precioCosto+".00"
                }
            }
        }
        input.precioCosto = numero
        input.descripcion = content
        dispatch(clonarProducto(input,check))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
    }

    const [show, setShow] = useState();
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    const [showAgregarMarca, setShowAgregarMarca] = useState();
    const handleCloseAgregarMarca = () => setShowAgregarMarca(false);
    const handleShowAgregarMarca = () => setShowAgregarMarca(true);

    const [showAgregarColor, setShowAgregarColor] = useState();
    const handleCloseAgregarColor = () => setShowAgregarColor(false);
    const handleShowAgregarColor = () => setShowAgregarColor(true);

    const [showAgregarMedida, setShowAgregarMedida] = useState();
    const handleCloseAgregarMedida = () => setShowAgregarMedida(false);
    const handleShowAgregarMedida = () => setShowAgregarMedida(true);

    const [showAgregarSubcategoria, setShowAgregarSubcategoria] = useState();
    const handleCloseAgregarSubcategoria = () => setShowAgregarSubcategoria(false);
    const handleShowAgregarSubcategoria = () => setShowAgregarSubcategoria(true);

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });
    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
        setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openAgregarCategoria, setOpenAgregarCategoria] = React.useState(false);
    const handleClickAlertAgregarCategoria = () => {
      setOpenAgregarCategoria(true);
    };
    const handleCloseAlertAgregarCategoria = () => {
        setOpenAgregarCategoria(false);
    };

    const [openAgregarMarca, setOpenAgregarMarca] = React.useState(false);
    const handleClickAlertAgregarMarca = () => {
      setOpenAgregarMarca(true);
    };
    const handleCloseAlertAgregarMarca = () => {
        setOpenAgregarMarca(false);
    };

    const [openAgregarMedida, setOpenAgregarMedida] = React.useState(false);
    const handleClickAlertAgregarMedida = () => {
      setOpenAgregarMedida(true);
    };
    const handleCloseAlertAgregarMedida = () => {
        setOpenAgregarMedida(false);
    };

    const [openAgregarColor, setOpenAgregarColor] = React.useState(false);
    const handleClickAlertAgregarColor = () => {
      setOpenAgregarColor(true);
    };
    const handleCloseAlertAgregarColor = () => {
        setOpenAgregarColor(false);
    };

    const [openAgregarSubcategoria, setOpenAgregarSubcategoria] = React.useState(false);
    const handleClickAlertAgregarSubcategoria = () => {
      setOpenAgregarSubcategoria(true);
    };
    const handleCloseAlertAgregarSubcategoria = () => {
        setOpenAgregarSubcategoria(false);
    };

    useEffect(() => {
        setInput({...producto, codigoproducto: ''})
        setPrecio(producto.precioVenta)
    }, [producto])

    useEffect(() => {
        setInput(producto)
        dispatch(getSubCategoriasByCategoria(producto.categoriaid))
        setContent(producto.descripcion)
        
    }, [producto])

    useEffect(() =>{
        let isMounted = true;
        if(isMounted){
            dispatch(getProductoById(id))
            dispatch(getTasasIva())
            dispatch(getMonedas())
            dispatch(getColoresTienda())
            dispatch(getMarcasTienda())
            dispatch(getCategoriasSinPag())
            dispatch(getTallas())
            dispatch(getDescuentosSinPaginado())
        }
        return () => {
            isMounted = false
        }
    }, [])

    useEffect(() => {
        if(input !== ''){
            dispatch(getCategoriasSinPag())
            input.categoriaid = okCategoria.id
        }
    },[okCategoria])

    useEffect(() => {
        if(input !== ''){
            dispatch(getMarcasTienda())
            input.marcaid = okMarca.id
        }
    },[okMarca])

    useEffect(() => {
        if(input !== ''){
            dispatch(getColoresTienda())
            input.colorid = okColor.colorid
        }
    },[okColor])

    useEffect(() => {
        if(input !== ''){
            dispatch(getSubCategoriasByCategoria(input.categoriaid))
            setInput({
                ...input,
                subcategoriaid: [okSubcategoria.subcategoriaid]
            })
        }
    },[okSubcategoria])

    useEffect(() => {
        if(input !== ''){
            dispatch(getTallas())
            setInput({
                ...input,
                tallaid: [okMedida.tallaid]
            })
        }
    },[okMedida])

    return (
        <section className="container-productos">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={okProductoClonado ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Informaci&oacute;n modificada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarCategoria} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarCategoria}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarCategoria} 
                        severity={okCategoria.ok ? "success" : "error"}
                        sx={{ width: 400 }}>
                        {okCategoria.ok ? <h5>Categor&iacute;a agregada con &eacute;xito!</h5> : <h5>Hubo un problema al crear la categor&iacute;a</h5>}
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarMarca} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarMarca}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarMarca} 
                        severity={okMarca.ok === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        {okMarca.ok ? <h5>Marca creada con &eacute;xito!</h5> : <h5>Hubo un problema al crear la marca</h5>}
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarSubcategoria} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarSubcategoria}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarSubcategoria} 
                        severity={okSubcategoria.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        {okSubcategoria.success ? <h5>Subcategoria creada con &eacute;xito!</h5> : <h5>Hubo un problema al crear la subcategoria</h5>}
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarColor} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarColor}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarColor} 
                        severity={okColor.success === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okColor.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarMedida} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarMedida}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarMedida} 
                        severity={okMedida.success === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okMedida.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <AgregarCategoria 
                    show={show} 
                    handleClose={handleClose} 
                    handleClickAlert={handleClickAlertAgregarCategoria}
                />
            }
            {
                <AgregarMarca 
                    show={showAgregarMarca} 
                    handleClose={handleCloseAgregarMarca} 
                    handleClickAlert={handleClickAlertAgregarMarca}
                />
            }
            {
                <AgregarColorTienda
                    show={showAgregarColor} 
                    handleClose={handleCloseAgregarColor} 
                    handleClickAlert={handleClickAlertAgregarColor}
                />
            }
            {
                <AgregarTalla 
                    show={showAgregarMedida} 
                    handleClose={handleCloseAgregarMedida} 
                    handleClickAlert={handleClickAlertAgregarMedida}
                />
            }
            {
                <AgregarSubcategoria 
                    show={showAgregarSubcategoria} 
                    handleClose={handleCloseAgregarSubcategoria} 
                    nombre={input.categoriaid} 
                    id={input.categoriaid} 
                    handleClickAlert={handleClickAlertAgregarSubcategoria}
                />
            }
            <a href="/Mitienda/Administrar" style={{color:'black'}}>
                <div style={{display:"flex", fontSize:"140%", marginBottom:40, marginLeft:10}}>
                    <ArrowBack/>
                    Volver a Administrar Productos
                </div>
            </a>
            <header style={{marginLeft:20}}>
                <h3 className="header">Clonar producto</h3>
            </header>
            <Paper className="form-productos">
                <TextField 
                        label="Nombre de producto"  
                        variant="outlined" 
                        fullWidth margin="normal" 
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        name="nombreproducto"
                        onChange={(e) => handleChange(e)}
                        value={input.nombreproducto || ''}
                    />
                    <FormControl fullWidth sx={{mt: 2, mb:3}}>
                      <InputLabel id="tipodelista-select-label">Tipo de lista</InputLabel>
                      <Select
                          labelId="tipodelista-select-label"
                          id="tipodelista"
                          label="Tipo de lista"
                          size="small"
                          name="tipo_venta"
                          value={input.tipo_venta || ''}
                          onChange={handleSelect}
                      >
                          <MenuItem value={0}>Seleccione una opcion</MenuItem>
                          {
                                descuentos && descuentos.map((c) =>
                                <MenuItem value={c.descuentovtaid} key={c.descuentovtaid}>{c.nombre}</MenuItem>
                              )
                          }
                      </Select>
                  </FormControl>
                    {/* <FormControl fullWidth sx={{mb:2, mt:2}}>
                        <InputLabel id="tipodelista-select-label">Tipo de lista</InputLabel>
                        <Select
                        //sx={{marginRight:5}}
                        labelId="tipodelista-select-label"
                        id="tipodelista"
                        label="Tipo de lista"
                        size="small"
                        name="tipo_venta"
                        value={input.tipo_venta || ''}
                        onChange={handleSelect}
                        >
                        <MenuItem value="0">Seleccione una opcion</MenuItem>
                        {               
                            tiposDeVenta && tiposDeVenta.map((t) =>
                                <MenuItem value={t.id} key={t.id}>{t.tipo}</MenuItem>
                            )
                        }
                        </Select>
                    </FormControl> */}
                    <JoditEditor
                        ref={editor}
                        value={content}
                        tabIndex={5} // tabIndex of textarea
                        onBlur={newContent => setContent(newContent)} // preferred to use only this option to update the content for performance reasons
                        onChange={newContent => setContent(newContent)}
                    />
                </Paper>
                <Paper className="otros">
                <div style={{display:"flex", alignItems:"center"}}>
                        <FormControl sx={{mb:2, mt:2, mr:10}} style={{width:"60%"}}>
                            <InputLabel id="categoria-select-label">Categoria</InputLabel>
                            <Select
                            labelId="categoria-select-label"
                            id="categoria-select"
                            label="Categorias"
                            size="small"
                            name="categoriaid"
                            value={input.categoriaid}
                            onChange={handleSelect}
                            >
                            <MenuItem value={0}>Seleccione una opcion</MenuItem>
                            {
                                categorias && categorias.map((c) =>
                                    <MenuItem value={c.categoriaid} key={c.categoriaid}>{c.nombrecategoria}</MenuItem>
                                )
                            }
                            </Select>
                        </FormControl>
                        <Button 
                            variant="contained"
                            className="btn-miplan"
                            onClick={handleShow}
                            sx={{width:250}}
                        >AGREGAR CATEGORIA</Button>
                    </div>
                    <div style={{display:"flex", alignItems:"center"}}>
                    <FormControl sx={{mb:2, mt:2, mr:10}} style={{width:"60%"}}>
                        <InputLabel id="categoria-select-label">Subcategoria</InputLabel>
                        <Select
                        labelId="categoria-select-label"
                        id="categoria-select"
                        label="Subcategorias"
                        size="small"
                        name="subcategoriaid"
                        value={input.subcategoriaid || ''}
                        onChange={handleSelect}
                        >
                        <MenuItem value={0}>Seleccione una opcion</MenuItem>
                        {
                            subcategorias && subcategorias.map((c) =>
                                <MenuItem value={c.subcategoriaid} key={c.subcategoriaid}>{c.nombre}</MenuItem>
                            )
                        }
                        </Select>
                    </FormControl>
                    <Button 
                        variant="contained"
                        className="btn-miplan"
                        onClick={handleShowAgregarSubcategoria}
                        sx={{width:250}}
                    >AGREGAR SUBCATEGORIA</Button>
                </div>
                    <div style={{display:"flex", alignItems:"center"}}>
                        <FormControl sx={{mb:2, mt:2, mr:10}} style={{width:"60%"}}>
                        <InputLabel id="marcalabel">Marca</InputLabel>
                            <Select
                            labelId="marcalabel"
                            id="marca"
                            label="Marca"  
                            size="small"
                            name="marcaid"
                            value={input.marcaid}
                            onChange={(event)=>handleSelect(event)}
                            >
                            <MenuItem value={0}>Seleccione una opcion</MenuItem>
                            {
                                marcas && marcas.map((m) => 
                                    <MenuItem value={m.marcaid}>{m.nombremarca}</MenuItem>
                                )
                            }
                            </Select>
                        </FormControl>
                        <Button 
                            variant="contained"
                            className="btn-miplan"
                            onClick={handleShowAgregarMarca}
                            sx={{width:250}}
                        >AGREGAR MARCA</Button>
                    </div>
                    <div style={{display:"flex", alignItems:"center"}}>
                        <FormControl sx={{mb:2, mt:2, mr:10}} style={{width:"60%"}}>
                        <InputLabel id="colorlabel">Color</InputLabel>
                            <Select
                            labelId="colorlabel"
                            id="color"
                            label="Color"
                            size="small"
                            name="colorid"
                            value={input.colorid}
                            onChange={(event)=>handleSelect(event)}
                            >
                            <MenuItem value={0}>Seleccione una opcion</MenuItem>
                            {
                                colores && colores.map((c) => 
                                    <MenuItem value={c.colorid}>{c.nombre}</MenuItem>
                                )
                            }
                            </Select>
                        </FormControl>
                        <Button 
                            sx={{width:250}}
                            variant="contained"
                            className="btn-miplan"
                            onClick={handleShowAgregarColor}
                        >AGREGAR COLOR</Button>
                    </div>
                    <div style={{display:"flex", alignItems:"center"}}>
                        <FormControl sx={{mb:2, mt:2, mr:10}} style={{width:"60%"}}>
                        <InputLabel id="talla-select-label">Medida</InputLabel>
                            <Select
                            labelId="talla-select-label"
                            id="talla-select"
                            label="Medidas"
                            size="small"
                            name="tallaid"
                            value={input.tallaid}
                            onChange={(event)=>handleSelect(event)}
                            MenuProps={{ keepMounted: true, disablePortal: true }}
                            >
                            <MenuItem value={0}>Seleccione una opcion</MenuItem>
                            {
                                tallas && tallas.map((c) =>
                                    <MenuItem value={c.tallaid} key={c.tallaid}>{c.nombretalla}</MenuItem>
                                )
                            }
                            </Select>
                        </FormControl>
                        <Button 
                        sx={{width:250}}
                            variant="contained"
                            className="btn-miplan"
                            onClick={handleShowAgregarMedida}
                        >AGREGAR MEDIDA</Button>
                    </div>
                </Paper>
                <Paper className="precios">
                <FormControl sx={{mb:3}}>
                        <InputLabel id="monedalabel">Moneda</InputLabel>
                        <Select
                        sx={{marginRight:5}}
                        labelId="monedalabel"
                        id="moneda"
                        label="Moneda"
                        size="small"
                        name="moneda"
                        value={input.moneda || ''}
                        onChange={(event)=>handleSelect(event)}
                        >
                        <MenuItem value={0}>Seleccione una opcion</MenuItem>
                        {
                            monedas && monedas.map((m) => 
                                <MenuItem value={m.monedaid}>{m.nombre}</MenuItem>
                            )
                        }
                        </Select>
                    </FormControl>
                    <FormControl sx={{mb:3}}>
                        <InputLabel id="demo-simple-select-label">Producto IVA</InputLabel>
                        <Select
                        sx={{marginRight:5}}
                        labelId="demo-simple-select-label"
                        id="demo-simple-select"
                        label="Usar atributos"
                        size="small"
                        name="ivaproducto"
                        value={input.productoivaid}
                        onChange={handleSelect}
                        >
                        <MenuItem value={0}>Seleccione una opcion</MenuItem>
                        {
                            tasas && tasas.map((t) => 
                                <MenuItem value={t.productoivaid}>{t.iva}</MenuItem>
                            )
                        }
                        </Select>
                    </FormControl>
                    <TextField 
                        label="Precio costo"  
                        variant="outlined" 
                        margin="normal"
                        sx={{marginRight:5}}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17,
                            },
                        }}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            $
                            </InputAdornment>,
                        }}
                        name="precioCosto"
                        helperText="Agregar el precio con punto (.) Ejemplo 100.00"
                        onChange={(e) => handleChange(e)}
                        value={input.precioCosto|| ''}
                    />
                    <TextField 
                        label="Stock (opcional)"  
                        variant="outlined" 
                        sx={{marginRight:5}} margin="normal" 
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                        name="cantidadactiva"
                        onChange={(e) => handleChange(e)}
                        value={input.cantidadactiva || ''}
                    />
                    <FormControlLabel
                        control={
                            <Checkbox
                            name="mueve_stock"
                            onChange={(e)=> handleCheck(e)}
                            checked={check == "1" ? true : false}
                            />
                        }
                        label="Incrementa stock (Si esta tildado el valor ingresado se suma al valor actual del stock, de lo contrario se resta)"
                    />
                    <TextField 
                        label="SKU (opcional)"  
                        variant="outlined" 
                        margin="normal" 
                        placeholder="342131232"
                        sx={{marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                        name="codigoproducto"
                        onChange={(e) => handleChange(e)}
                        value={input.codigoproducto || ''}
                    />
                </Paper>
                <Paper className="atributos">
                    <h4>Atributos</h4>
                    <h5 style={{marginTop:10, marginBottom:10}}>Sirven para productos que poseen diferentes atributos, por ejemplo: talle, color u otros <br/>
                        Vas a poder diferenciar el stock de tu producto en funci&oacute;n a ellos y darle la posibilidad al cliente
                        que elija entre ellos.
                    </h5>
                    <FormControl fullWidth sx={{mt:3}}>
                        <InputLabel id="demo-simple-select-label">Usar atributos</InputLabel>
                        <Select
                        labelId="demo-simple-select-label"
                        id="demo-simple-select"
                        label="Usar atributos"
                        size="small"
                        name="usarAtributos"
                        value={input.usar_atributos || ''}
                        >
                        <MenuItem value={0}>No</MenuItem>
                        </Select>
                    </FormControl>
                </Paper>
                <Paper  className="dimensiones">
                    <h4>Dimensiones y peso</h4>
                    <h5>Las dimensiones y el peso son importantes para que pueda calcular el costo del env&iacute;o.<br/>
                        Debes ingresar las dimensiones del paquete o caja del producto (individualmente).
                    </h5>
                    <div style={{display:"flex"}}>
                    <TextField 
                        label="Alto"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        InputProps={{
                            endAdornment:<InputAdornment position="end">
                            cm
                            </InputAdornment>,
                        }}
                        name="alto"
                        onChange={(e) => handleChange(e)}
                        value={input.alto || ''}
                    />
                    <TextField 
                        label="Ancho"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        InputProps={{
                            endAdornment:<InputAdornment position="end">
                            cm
                            </InputAdornment>,
                        }}
                        name="ancho"
                        onChange={(e) => handleChange(e)}
                        value={input.ancho || ''}
                    />
                    <TextField 
                        label="Profundidad"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        InputProps={{
                            endAdornment:<InputAdornment position="end">
                            cm
                            </InputAdornment>,
                        }}
                        name="profundidad"
                        onChange={(e) => handleChange(e)}
                        value={input.profundidad || ''}
                    />
                    <TextField 
                        label="Peso"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        InputProps={{
                            endAdornment:<InputAdornment position="end">
                            kg
                            </InputAdornment>,
                        }}
                        name="peso"
                        onChange={(e) => handleChange(e)}
                        value={input.peso || ''}
                    />
                    </div>               
                </Paper>
            <div align="right">
                <a href="/Mitienda/Administrar" style={{color:'black'}}>
                    <Button size="large" 
                    variant="contained" 
                    style={{backgroundColor:"white", color:"black"}}
                    sx={{mt: 3, mb: 3, mr:5}}>
                        Volver a Administrar
                    </Button>
                </a>
                <Button 
                    size="large" 
                    variant="contained" 
                    sx={{mt: 3, mb: 3, mr:6}}
                    onClick={(e) => handleAdd(e)}
                >Clonar producto</Button>
            </div>
        </section>
    )
}