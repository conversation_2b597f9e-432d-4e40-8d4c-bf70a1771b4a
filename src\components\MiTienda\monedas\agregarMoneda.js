import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { useEffect } from "react";
import { configColorTienda, configMoneda } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const AgregarMoneda = ({show, handleClose, handleClickAlert}) =>{

  const [input, setInput] = useState({
    nombre: "",
    activo: "0",
    simbolo: ''
  })

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value})
  }

  const dispatch = useDispatch()

  const handleCheck = (e) =>{
    e.preventDefault()
    if(e.target.checked === true){
        setInput({...input, [e.target.name] : "1"})
    }else{
        setInput({...input, [e.target.name] : "0"})
    }
  }

  const handleClick = () => {
    dispatch(configMoneda(input))
    setInput({
      nombre: "",
      activo: "0",
      simbolo: ''
    })
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    useEffect(() => {
      setInput({
        nombre: "",
        activo: "0",
        simbolo: ''
      })
    },[])

    return (
        show ? 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
          <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
              <h4>Agregar moneda</h4>
              <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
          </div>
          <Divider/>
          <div style={{marginTop:15, marginBottom:15}}>
            <TextField 
              name="nombre"
              label="Nombre"  
              variant="outlined" 
              fullWidth margin="normal" 
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
              inputProps={{
                style: {
                  height: 40,
                  fontSize:17
                },
              }}
              value={input.nombre}
              onChange={(e)=> handleChange(e)}
            />
            <TextField 
              name="simbolo"
              label="Simbolo"  
              variant="outlined" 
              fullWidth margin="normal" 
              value={input.simbolo}
              onChange={(e)=> handleChange(e)}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
              inputProps={{
                style: {
                  height: 40,
                  fontSize:20
                },
              }}
            />
            <div style={{marginTop:15, marginBottom:5}}>
              <FormControlLabel
                control={
                <Checkbox/>
                }
                style={{color:"black"}}
                onChange={(e)=> handleCheck(e)}
                checked={input.activo === "1" ? true : false}
                label="Activo"
                name="activo"
              />
            </div>
          </div>
          <Divider/>
          <div align="right">
            <Button 
              disabled={
                input.nombre === ''
              }
              size="large" 
              variant="contained" 
              sx={{mt: 3}}
              onClick={handleClick}
              >Agregar</Button>
          </div>
        </Modal> : null 
    )
}