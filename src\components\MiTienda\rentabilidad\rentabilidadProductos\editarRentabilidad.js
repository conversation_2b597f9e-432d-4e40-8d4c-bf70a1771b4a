import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { editarRentabilidad } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalEditarRentabilidad = ({show, handleClose, rentabilidad, handleClickAlert}) =>{

  const [input, setInput] = useState()

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value })
  }

  const dispatch = useDispatch()

  const handleClick = () => {
    dispatch(editarRentabilidad(input))
    setTimeout(function(){
        handleClickAlert()
    }, 1000);
    handleClose()
}
  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    useEffect(() => {
        setInput(rentabilidad)
    }, [rentabilidad])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Editar rentabilidad</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{marginTop:15, marginBottom:15}}>
                <TextField 
                    name="rentabilidad"
                    label="Porcentaje"  
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    value={input.rentabilidad || ''}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
            </div>
            <Divider/>
            <div align="right">
                <Button 
                size="large" 
                variant="contained" 
                onClick={(e)=>(handleClick(e))}
                sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>
    </Modal>
    )
}