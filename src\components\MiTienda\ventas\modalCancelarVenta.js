import React from "react"
import Mo<PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button,} from "@mui/material";
import { useDispatch } from "react-redux";
import { cancelarPedido } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalCancelar = ({show, handleClose,pedido}) =>{

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const handleCancelar = () => {
        dispatch(cancelarPedido(pedido))
        handleClose()
    }

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Cancelar venta</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>   
                    Esta seguro que quiere cancelar la venta?
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="outlined" 
                    color="success"
                    onClick={handleCancelar}
                    sx={{mt: 3, mr:1}}>Si</Button>
                    <Button size="large"
                    variant="outlined"
                    color="error"
                    onClick={handleClose}
                    sx={{mt: 3}}>No</Button>
                </div>
        </Modal>
    )
}