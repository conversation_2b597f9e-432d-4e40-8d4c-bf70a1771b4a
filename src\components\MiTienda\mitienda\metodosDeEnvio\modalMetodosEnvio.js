import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { configEnvioADomicilio  } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalMetodosEnvio = ({show, handleClose,handleClickAlert}) => {

    const metodos = useSelector((state) => state.mitienda.metodos)

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [input, setInput] = useState()

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const handleCheck = (e) => {
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }

    const dispatch = useDispatch()

    const handleClick = (e) => {
        e.preventDefault()
        dispatch(configEnvioADomicilio(input))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
    }

    useEffect(() => {
        setInput(metodos)
    },[])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Configuracion Envio a Domicilio</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                    <div style={{marginTop:15, marginBottom:15}}>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            disabled={permisos_acciones?.activar === "0"}
                            name="default_enviocliente"
                            style={{color:"black"}}
                            onChange={(e)=> handleCheck(e)}
                            checked={input === undefined ?  null : input.default_enviocliente == "1" ? true : false}
                            label="Activar/Desactivar envio a domicilio"
                        />
                    </div>    
                <Divider/>
                <div align="right">
                <Button size="large" variant="contained" sx={{mt: 3}} onClick={(e)=> handleClick(e)}>Guardar</Button>
                </div>
        </Modal>
    )
}