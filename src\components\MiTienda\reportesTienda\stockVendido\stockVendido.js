import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { useDispatch, useSelector } from 'react-redux';
import {getStockVendido} from '../../../../redux/actions/mitienda.actions'
import { ModalFiltrosStockVendido } from './modalFiltrosStockVendido';
import moment from 'moment';
import ClipLoader from "react-spinners/ClipLoader";

export const StockVendido = () => {

    //Obtengo la informacion para la tabla 
    const info = useSelector((state) => state.mitienda.stockVendido)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    //Declaro la variable para despachar acciones
    const dispatch = useDispatch()

    //Manejo el abrir y cerrar Modal de Filtros
    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    //Defino las fechas 
    let fechaActual = moment()
    let ultimosDias = moment().day(-60)

    //Declaro las constantes que utilizare para filtrar la informacion
    const [fecha_desde, setFecha_desde] = useState(ultimosDias.toISOString().substring(0,10))
    const handleChangeFecha_desde = (e) =>{
        setFecha_desde(e.target.value)
    }

    const [fecha_hasta, setFecha_hasta] = useState(fechaActual.toISOString().substring(0,10))
    const handleChangeFecha_hasta = (e) =>{
        setFecha_hasta(e.target.value)
    }

    //Funcion para el boton Aplicar filtrps
    const searchByFilters = () =>{

        dispatch(getStockVendido(fecha_desde, fecha_hasta))
        handleClose()
    }


    //Funcion para el boton Limpiar filtros, se reinician las constantes a sus valores iniciales
    const reset = (e) =>{
        setFecha_desde(0)
        setFecha_hasta(0)

    }

    useEffect(() =>{
        dispatch(getStockVendido(fecha_desde, fecha_hasta))
    },[])

    return (
        <div className="notificaciones-container">
            {
                <ModalFiltrosStockVendido
                    show={show} 
                    handleClose={handleClose}
                    search={searchByFilters}
                    reset={reset}
                    fecha_desde={fecha_desde}
                    handleChangeFecha_desde={handleChangeFecha_desde}
                    fecha_hasta={fecha_hasta}
                    handleChangeFecha_hasta={handleChangeFecha_hasta}

                />
            }
            <div>
                <header className="titulo-notificaciones">
                    <h3>Reporte: Stock Vendido</h3>
                    <Button 
                        variant="outlined" 
                        sx={{height:40,width:150}}
                        onClick={handleShow}>
                        <FilterListIcon/> Filtrar
                    </Button>
                </header>
            </div>
            {
                permisos_acciones?.listar !== "0" && 
                <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Codigo</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Marca</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Categoria</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Estilo</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Color</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Medida</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Producto</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Cantidad</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Precio Venta</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Precio Total</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                    {   
                        info.loading ? 
                        <TableRow sx={{p:20}}>
                            <TableCell colSpan={13} align='center'>
                                <ClipLoader 
                                    loading={info.loading}
                                    size={50}
                                />
                            </TableCell>
                        </TableRow> :
                        info.data.length > 0 ? info.data.map((row) => (
                        <TableRow
                            key={row.id}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:100}} align="center">{row.codigoarticulo}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombremarca}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombrecategoria}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">{row.nombreestilo}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center" >{row.nombrecolor}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombremedida}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombreproducto}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.cantidad}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.precioVenta}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.precioTotal}</TableCell>

                        </TableRow>
                        )):
                        <TableRow>
                            <TableCell colSpan={10}>
                                <h5>No hay informaci&oacute;n para mostrar</h5>
                            </TableCell>
                        </TableRow>}
                    </TableBody>
                    </Table>
                </TableContainer>
            </div>
            }
        </div>
    )
}