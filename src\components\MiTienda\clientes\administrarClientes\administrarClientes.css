.container-adm-clientes{
    width: 100%;
    min-height: 120vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.titulo-clientes{
    width: 80%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 45px;
}

.paper-clientes{
    margin-top: 3px;
    width: 95%;
    align-self: "center";
    margin-left: 40px;
}

@media only screen and (max-width: 1250px) {

    .container-adm-clientes{
        padding: 20px;
        width: 120%;
        min-height: 120vh;
        height: 100%;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }

    .paper-clientes{
        width: 95%;
        align-self: "center";
    }
}