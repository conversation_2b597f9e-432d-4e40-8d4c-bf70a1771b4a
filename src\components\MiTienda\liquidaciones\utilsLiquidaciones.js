function crearLabels(info) {
  if (!info || info.length === 0) return [];
  
  let aux = new Set(info.map((i) => i.fechaliquidacion.split(' ')[0]));
  let labels = Array.from(aux);
  let aux2 = '';

  for (let k = 0; k < labels.length; k++) {
    aux2 = labels[k].split('-');
    labels[k] = aux2[2] + '-' + aux2[1] + '-' + aux2[0];
  }

  return labels;
}

function crearData(info, fechas) {
  if (!info || info.length === 0) return [];

  let data = [];
  let valor = 0;
  let aux, aux2;

  for (let i = 0; i < fechas.length; i++) {
    for (let j = 0; j < info.length; j++) {
      aux = info[j].fechaliquidacion.split(' ')[0].split('-');
      aux2 = aux[2] + '-' + aux[1] + '-' + aux[0];
      if (aux2 === fechas[i]) {
        // Convert "15.387,41" format to 15387.41
        const amountStr = info[j].total.replace(/\./g, '').replace(',', '.');
        valor += parseFloat(amountStr);
      }
    }
    data.push(valor);
    valor = 0;
  }

  return data;
}

function totalLiquidaciones(info) {
  if (!info || info.length === 0) {
    return { total: 0, cantidad: 0 };
  }

  let total = 0;

  for (let i = 0; i < info.length; i++) {
    // Convert "15.387,41" format to 15387.41 and add to total
    const amountStr = info[i].total.replace(/\./g, '').replace(',', '.');
    total += parseFloat(amountStr);
  }

  // Format total back to locale string for display
  const formattedTotal = total.toLocaleString('es-AR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  return { 
    total: formattedTotal, 
    cantidad: info.length 
  };
}

module.exports = {
  crearLabels,
  crearData,
  totalLiquidaciones
};