import React, { useEffect, useState } from "react"
import Mo<PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { <PERSON><PERSON>, But<PERSON>, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { procesarListado, procesarListadoListaPrecios } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalProcesarArchivo = ({show, handleClose, datos, handleClickAlert}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [archivo, setArchivo] = useState('')

    const dispatch = useDispatch()

    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(procesarListadoListaPrecios(datos.listaprecioid))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    }

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Procesar Lista de Precios Manual en Excel</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>
                    <div>
                        <h5>Procesar lista de precios nuevos</h5>
                        <h6>Haga click en procesar para actualizar la lista de precios nuevos</h6>
                    </div>
                    <div align="right">
                        <Button size="large" 
                            variant="contained" 
                            sx={{mt: 3}}
                            onClick={(e) => handleOnClick(e)}> Procesar
                        </Button>
                    </div>
                </div>
                <Divider/>
        </Modal>
    )
}