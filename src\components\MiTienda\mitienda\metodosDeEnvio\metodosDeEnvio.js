import React, { useState } from "react";
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { Snackbar } from "@mui/material";
import MuiAlert from '@mui/material/Alert';
import "./metodosDeEnvio.css"
import { ModalMetodosEnvio } from "./modalMetodosEnvio";
import { ModalRetiroLocal } from "./modalRetiroLocal";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getMetodosEnvio, 
    limpiarOkEnvioCliente, 
    limpiarOkRetiro 
} from "../../../../redux/actions/mitienda.actions";
import pickit from '../../../../media/logoPickit.png'
import retiroporellocal from '../../../../media/retiroporellocal.png'
import envioadomicilio from '../../../../media/envioadomicilio.png'
import { ModalPickit } from "./configPickit";

export const MetodosDeEnvio= () => {

    const metodos = useSelector((state) => state.mitienda.metodos)
    const ok = useSelector((state) => state.mitienda.okEnvioDomicilio)
    const okRetiroLocal = useSelector((state) => state.mitienda.okDefaultRetiroLocal)
    const okPickit = useSelector((state) => state.mitienda.okConfigPickit)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const dispatch = useDispatch()

    const [show, setShow] = useState(false);
    const handleCloseModal = () => {
        dispatch(limpiarOkEnvioCliente())
        dispatch(getMetodosEnvio())
        setShow(false);
    }
    const handleShow = () => setShow(true);

    const [show2, setShow2] = useState(false);
    const handleCloseModal2 = () => {
        dispatch(limpiarOkRetiro())
        setShow2(false);
    }
    const handleShow2 = () => setShow2(true);

    const [showPickit, setShowPickit] = useState(false);
    const handleCloseModalPickit = () => {
        setShowPickit(false);
    }
    const handleShowPickit = () => setShowPickit(true);

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openRetiroLocal, setOpenRetiroLocal] = React.useState(false);
    const handleClickAlertRetiroLocal = () => {
      setOpenRetiroLocal(true);
    };
    const handleCloseAlertRetiroLocal = () => {
        setOpenRetiroLocal(false);
    };
    
    const [openPickit, setOpenPickit] = React.useState(false);
    const handleClickAlertPickit = () => {
      setOpenPickit(true);
    };
    const handleCloseAlertPickit = () => {
        setOpenPickit(false);
    };

    useEffect(() => {
        dispatch(getMetodosEnvio())
    },[ok, okRetiroLocal, okPickit])

    return (
        <div className="container-mde">
            {
                <ModalMetodosEnvio 
                handleClose={handleCloseModal} 
                show={show}
                handleClickAlert={handleClickAlert}
                getMetodos={getMetodosEnvio}
                />
            }
            {
                <ModalRetiroLocal 
                handleClose={handleCloseModal2} 
                show={show2}
                handleClickAlert={handleClickAlertRetiroLocal}
                getMetodos={getMetodosEnvio}
                />
            }
            {
                <ModalPickit
                handleClose={handleCloseModalPickit} 
                show={showPickit}
                handleClickAlert={handleClickAlertPickit}
                />
            }
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Informaci&oacute;n modificada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openRetiroLocal} 
                    autoHideDuration={10000} onClose={handleCloseAlertRetiroLocal}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertRetiroLocal} 
                        severity={okRetiroLocal ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Informaci&oacute;n modificada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openPickit} 
                    autoHideDuration={10000} onClose={handleCloseAlertPickit}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertPickit} 
                        severity={okPickit.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okPickit.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <header className="header-mde">
                <h3 style={{marginBottom:20}}>M&eacute;todos de env&iacute;o</h3>
            </header>
            {
                permisos_acciones.listar === "0" ? null :
                <div className="list-metodos-de-envio">
                <Card sx={{ width: 345, height:360, margin:5}}>
                    <CardMedia
                        component="img"
                        height="200"
                        image={retiroporellocal}
                        alt="retiroporellocal"
                    />
                    <CardContent>
                        <Typography gutterBottom variant="h5" component="div">
                        Retiro en el local
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            <h6 style={{display:"flex",marginBottom:-10}}>
                                Metodo&nbsp;{metodos.default_retirolocal === "1" ? <h6 style={{color:"green"}}> activado</h6> : 
                            <h6 style={{color:"red"}}> desactivado</h6>} </h6>
                        </Typography>
                    </CardContent>
                    <CardActions>
                        <Button disabled={permisos_acciones?.modificar === "0"} variant="outlined" fullWidth onClick={handleShow2}>Configuracion</Button>
                    </CardActions>
                    </Card>
                <Card sx={{ width: 345, height:360, margin:5}}>
                    <CardMedia
                        component="img"
                        height="200"
                        image={envioadomicilio}
                        alt="envioadomicilio"
                    />
                    <CardContent>
                        <Typography gutterBottom variant="h5" component="div">
                        Env&iacute;o a Domicilio
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                        <h6 style={{display:"flex",marginBottom:-10}}>
                                Metodo&nbsp;{metodos.default_enviocliente === "1" ? <h6 style={{color:"green"}}> activado</h6> : 
                            <h6 style={{color:"red"}}> desactivado</h6>} </h6>
                        </Typography>
                    </CardContent>
                    <CardActions>
                        <Button disabled={permisos_acciones?.modificar === "0"} variant="outlined" fullWidth onClick={handleShow}>Configuracion</Button>
                    </CardActions>
                </Card>
                <Card sx={{ width: 345, height:360, margin:5}}>
                    <CardMedia
                        component="img"
                        height="200"
                        image={pickit}
                        alt="image"
                    />
                    <CardContent>
                        <Typography gutterBottom variant="h5" component="div">
                        Pickit
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            <h6 style={{display:"flex",marginBottom:-10}}>
                                Metodo&nbsp;{metodos.default_pickit === "1" ? <h6 style={{color:"green"}}> activado</h6> : 
                            <h6 style={{color:"red"}}> desactivado</h6>} </h6>
                        </Typography>
                    </CardContent>
                    <CardActions>
                        <Button disabled={permisos_acciones?.modificar === "0"} variant="outlined" fullWidth onClick={handleShowPickit}>Configuracion</Button>
                    </CardActions>
                    </Card>
            </div>
            }
        </div>
    )
}