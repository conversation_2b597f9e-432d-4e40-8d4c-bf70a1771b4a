import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { useDispatch } from "react-redux";
import { desactivarSubCategoria } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalEliminarSubCategoria = ({show, handleClose, subcategoria}) =>{

    const dispatch = useDispatch()

    const handleOnClick = () => {
        if(subcategoria.activo == '1'){
            subcategoria.activo = '0'
        }else{
            subcategoria.activo = '1'
        }
        dispatch(desactivarSubCategoria(subcategoria))
    }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>{subcategoria.activo == '1' ? 'Desactivar' : 'Activar'} subcategoria</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>
                    {subcategoria.activo == '1' ? <h5>Al desactivar la subcategoria dejara de ser visible en la tienda hasta volver a activarla. 
                        No se eliminara ni borraran las configuraciones realizadas
                    </h5> : <h5>Al activar la subcategoria volvera a ser visible en la tienda hasta volver a desactivarla. 
                    </h5>}
                    
                </div>
                <Divider/>
                <div align="right">
                    <Button 
                        onClick={handleOnClick}
                        size="large" 
                        variant="contained" 
                        sx={{mt: 3}}>{subcategoria.activo == '1' ? 'Desactivar' : 'Activar'}</Button>
                </div>
        </Modal>
    )
}