.container-chat {
    width: 100%;
    min-height: 110vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.form-chat{
    background-color: white;
    height: auto;
    width: 95%;
    padding: 20px;
    align-self: center;
    border-radius:10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 20px;
}

.header-chat{
    width: 95%;
    margin-left: 50px;
}

@media only screen and (max-width: 1200px) {
    .container-chat{
        padding: 40px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}