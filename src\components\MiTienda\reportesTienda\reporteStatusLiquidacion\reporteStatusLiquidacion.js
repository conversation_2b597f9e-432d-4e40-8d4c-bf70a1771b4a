import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  FormControl,
  Select,
  MenuItem,
  ListSubheader,
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
  Chip,
  CircularProgress,
  DialogActions,
  DialogContent,
  DialogTitle,
  Dialog,
  Typography,
  ListItemText,
  ListItemIcon,
  ListItem,
  List,
  Alert,
  Snackbar,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import {
  Search,
  Clear,
  KeyboardArrowDown,
  MoreHoriz,
} from "@mui/icons-material";
import ClipLoader from "react-spinners/ClipLoader";
import {
  getComprobantesByLiquidacion,
  getLiquidaciones,
  getListadoClientesByName,
  uploadComprobantes,
} from "../../../../redux/actions/mitienda.actions";
import { formatDate } from "../../../shared/utility";
import { Link as RouterLink } from "react-router-dom";
import { ModalFiltrosReporteEntregas } from "../reporteEntregas/modalFiltrosReporteEntregas";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";

const TruncatedCell = ({ text }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) return <span>-</span>;

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: "4px",
      }}
    >
      <div
        style={{
          whiteSpace: isExpanded ? "normal" : "nowrap",
          overflow: isExpanded ? "visible" : "hidden",
          textOverflow: isExpanded ? "unset" : "ellipsis",
          maxWidth: "150px",
          fontSize: "80%",
        }}
      >
        {text}
      </div>
      {text.length > 20 && (
        <Tooltip title={isExpanded ? "Ver menos" : "Ver más"}>
          <IconButton
            size="small"
            onClick={() => setIsExpanded(!isExpanded)}
            style={{ padding: 0 }}
          >
            <MoreHoriz fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
    </div>
  );
};

export const ReporteStatusLiquidacion = () => {
  const dispatch = useDispatch();
  const [page, setPage] = useState(1);
  const [fechaDesde, setFechaDesde] = useState(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth() - 12, 1)
      .toISOString()
      .split("T")[0];
  });
  const [fechaHasta, setFechaHasta] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [show, setShow] = useState(false);

  // state variables for search
  const [clienteInput, setClienteInput] = useState("");
  const [clienteid, setClienteid] = useState("");
  const [selectedOption, setSelectedOption] = useState({
    nombre: "",
    apellido: "",
  });
  const [pedidoId, setPedidoId] = useState("");

  //file upload states
  const [openUploadModal, setOpenUploadModal] = useState(false);
  const [currentLiquidacion, setCurrentLiquidacion] = useState(null);
  const [files, setFiles] = useState([]);
  const [uploadAlert, setUploadAlert] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const [openComprobantesModal, setOpenComprobantesModal] = useState(false);
  const [currentComprobantes, setCurrentComprobantes] = useState([]);
  const comprobantesState = useSelector((state) => state.mitienda.comprobantes);

  const comprobantesUpload = useSelector(
    (state) => state.mitienda.comprobantesUpload
  );

  const loading =
    useSelector((state) => state.mitienda?.liquidaciones?.loading) || false;
  const liquidacionesData = useSelector(
    (state) => state.mitienda?.liquidaciones?.data
  );
  const paginaUltima =
    useSelector((state) => state.mitienda?.liquidaciones?.paginaUltima) || 1;

  const clientes = useSelector((state) => state.mitienda.clientesByName);

  useEffect(() => {
    if (fechaDesde && fechaHasta && page) {
      dispatch(
        getLiquidaciones(fechaDesde, fechaHasta, page, clienteid, pedidoId)
      );
    }
  }, [dispatch, page, clienteid, pedidoId, fechaDesde, fechaHasta]);

  useEffect(() => {
    if (clienteInput.length > 4) {
      dispatch(getListadoClientesByName(clienteInput, ""));
    }
  }, [clienteInput, dispatch]);

  //handlers for file upload
  const handleOpenUploadModal = (liquidacion) => {
    setCurrentLiquidacion(liquidacion);
    setFiles([]);
    setOpenUploadModal(true);
  };

  const handleCloseUploadModal = () => {
    setOpenUploadModal(false);
    setCurrentLiquidacion(null);
    setFiles([]);
  };

  const handleFileUpload = (e) => {
    const newFiles = Array.from(e.target.files);
    setFiles((prev) => [...prev, ...newFiles]);
  };

  const removeFile = (index) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  //w
  // const handleUploadComprobantes = async () => {
  //   if (!currentLiquidacion || files.length === 0) return;

  //   try {
  //     // Convert files to base64
  //     const filePromises = files.map((file) => {
  //       return new Promise((resolve) => {
  //         const reader = new FileReader();
  //         reader.onload = (event) => {
  //           resolve({
  //             remitoid: currentLiquidacion.facturaid,
  //             nombre: file.name,
  //             imagen64: event.target.result.split(",")[1], // Remove data URL prefix
  //           });
  //         };
  //         reader.readAsDataURL(file);
  //       });
  //     });

  //     const imagenes = await Promise.all(filePromises);

  //     await dispatch(uploadComprobantes(imagenes));

  //     setUploadAlert({
  //       open: true,
  //       message: "Comprobantes subidos correctamente",
  //       severity: "success",
  //     });

  //     // Refresh the data
  //     dispatch(
  //       getLiquidaciones(fechaDesde, fechaHasta, page, clienteid, pedidoId)
  //     );

  //     handleCloseUploadModal();
  //   } catch (error) {
  //     setUploadAlert({
  //       open: true,
  //       message: `Error al subir comprobantes: ${error.message}`,
  //       severity: "error",
  //     });
  //   }
  // };

  //f
  const handleUploadComprobantes = async () => {
    if (!currentLiquidacion || !files.length) return;

    try {
      // Convert files to base64
      const filePromises = files.map((file) => {
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = (event) => {
            resolve({
              liquidacionid: parseInt(currentLiquidacion.facturaid),
              nombre: file.name,
              imagen64: event.target.result.split(",")[1], // Remove data URL prefix
            });
          };
          reader.readAsDataURL(file);
        });
      });

      const imagenes = await Promise.all(filePromises);
      console.log("Uploading comprobantes with payload:", imagenes);

      // Pass liquidacionid and imagenes to the action
      await dispatch(
        uploadComprobantes(currentLiquidacion.facturaid, imagenes)
      );

      setUploadAlert({
        open: true,
        message: "Comprobantes subidos correctamente",
        severity: "success",
      });

      // Refresh the liquidation data
      dispatch(
        getLiquidaciones(fechaDesde, fechaHasta, page, clienteid, pedidoId)
      );

      handleCloseUploadModal();
    } catch (error) {
      console.error("Error uploading comprobantes:", error);
      setUploadAlert({
        open: true,
        message: `Error al subir comprobantes: ${error.message}`,
        severity: "error",
      });
    }
  };

  //handler to view comprobantes
  // const handleViewComprobantes = async (liquidacionid) => {
  //   setOpenComprobantesModal(true);
  //   await dispatch(getComprobantesByLiquidacion(liquidacionid));
  // };

  const handleViewComprobantes = async (liquidacionid) => {
    try {
      setOpenComprobantesModal(true);
      await dispatch(getComprobantesByLiquidacion(liquidacionid));
    } catch (error) {
      if (error.message !== "No se encontraron comprobantes") {
        setUploadAlert({
          open: true,
          message: error.message,
          severity: "error",
        });
      }
    }
  };

  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const handleClose = () => setShow(false);

  const handleChangeFechaDesde = (e) => {
    setFechaDesde(e.target.value);
  };

  const handleChangeFechaHasta = (e) => {
    setFechaHasta(e.target.value);
  };

  const searchByFilters = () => {
    setPage(1);
    dispatch(getLiquidaciones(fechaDesde, fechaHasta, 1, clienteid, pedidoId));
    handleClose();
  };

  const reset = () => {
    const today = new Date();
    const initialFechaDesde = "2021-04-01";
    const initialFechaHasta = today.toISOString().split("T")[0];

    setFechaDesde(initialFechaDesde);
    setFechaHasta(initialFechaHasta);
    setPage(1);
    setClienteid("");
    setClienteInput("");
    setSelectedOption({ nombre: "", apellido: "" });
    setPedidoId("");

    dispatch(getLiquidaciones(initialFechaDesde, initialFechaHasta, 1, "", ""));
    handleClose();
  };

  const handlePedidoSearch = (e) => {
    setPedidoId(e.target.value);
    setPage(1);
  };

  const clearClienteSearch = () => {
    setClienteid("");
    setSelectedOption({ nombre: "", apellido: "" });
    setClienteInput("");
    dispatch(getLiquidaciones(fechaDesde, fechaHasta, page, "", pedidoId));
  };

  const clearPedidoSearch = () => {
    setPedidoId("");
    dispatch(getLiquidaciones(fechaDesde, fechaHasta, page, clienteid, ""));
  };

  const handleClienteChange = (e, option) => {
    const newClienteId = e.target.value;
    setClienteid(newClienteId);
    setSelectedOption(option);
    setClienteInput("");
    setPage(1);
  };

  const getEstadoLiquidacionColor = (estado) => {
    switch (estado) {
      case "Liquidado":
        return "success";
      case "Pendiente":
        return "warning";
      case "Vencido":
        return "error";
      default:
        return "default";
    }
  };

  return (
    <div className="notificaciones-container">
      <Snackbar
        open={uploadAlert.open}
        autoHideDuration={6000}
        onClose={() => setUploadAlert({ ...uploadAlert, open: false })}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={() => setUploadAlert({ ...uploadAlert, open: false })}
          severity={uploadAlert.severity}
        >
          {uploadAlert.message}
        </Alert>
      </Snackbar>
      <ModalFiltrosReporteEntregas
        show={show}
        handleClose={handleClose}
        handleChangeFechaDesde={handleChangeFechaDesde}
        fechaDesde={fechaDesde}
        handleChangeFechaHasta={handleChangeFechaHasta}
        fechaHasta={fechaHasta}
        search={searchByFilters}
        reset={reset}
      />
      <div style={{ padding: "20px" }}>
        <h3>Reporte de Estado de Liquidación</h3>
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            alignItems: "center",
          }}
        >
          <Button
            variant="outlined"
            onClick={() => setShow(true)}
            startIcon={<FilterListIcon />}
          >
            Filtrar por Fecha
          </Button>

          <FormControl sx={{ minWidth: 300 }}>
            <Select
              size="small"
              value={clienteid}
              onChange={(e) => {
                const selectedClient = clientes.find(
                  (c) => c.clienteid === e.target.value
                );
                handleClienteChange(
                  e,
                  selectedClient || { nombre: "", apellido: "" }
                );
              }}
              displayEmpty
              renderValue={() =>
                clienteid
                  ? `${selectedOption.nombre} ${selectedOption.apellido}`
                  : "Buscar cliente..."
              }
              onOpen={() => {
                setClienteInput("");
              }}
              IconComponent={() => (
                <div style={{ display: "flex", alignItems: "center" }}>
                  {clienteid !== "" && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearClienteSearch();
                      }}
                      sx={{ marginRight: 1 }}
                    >
                      <Clear />
                    </IconButton>
                  )}
                  <KeyboardArrowDown />
                </div>
              )}
            >
              <ListSubheader>
                <TextField
                  size="small"
                  autoFocus
                  placeholder="Buscar cliente..."
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  inputProps={{
                    style: {
                      height: 35,
                      fontSize: 17,
                    },
                  }}
                  onChange={(e) => setClienteInput(e.target.value)}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  onKeyDown={(e) => {
                    e.stopPropagation();
                  }}
                  value={clienteInput}
                />
              </ListSubheader>
              {clientes &&
                clientes.map((option) => (
                  <MenuItem
                    key={option.clienteid}
                    value={option.clienteid}
                    onClick={() => {
                      setSelectedOption(option);
                      setClienteInput("");
                    }}
                  >
                    {option.nombre} {option.apellido}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <TextField
            size="small"
            placeholder="Buscar por Nº Remito"
            value={pedidoId}
            onChange={handlePedidoSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {pedidoId && (
                    <IconButton size="small" onClick={clearPedidoSearch}>
                      <Clear />
                    </IconButton>
                  )}
                </InputAdornment>
              ),
              style: {
                height: "40px",
              },
            }}
            sx={{
              minWidth: 200,
              "& .MuiOutlinedInput-root": {
                padding: "0 8px",
              },
            }}
          />
        </div>
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyItems: "center",
        }}
      >
        <TableContainer
          component={Paper}
          sx={{
            marginTop: 5,
            width: "90%",
            alignSelf: "center",
            margin: "40px auto",
            marginBottom: 5,
            padding: "0 20px",
          }}
        >
          <Table aria-label="liquidaciones table">
            <TableHead>
              <TableRow>
                {[
                  "Remito",
                  "Nro de Cliente",
                  "Cliente",
                  "Tipo Factura",
                  "Fecha Liquidación",
                  "Fecha Ingreso",
                  "Cant. Original",
                  "Cant. Liquidada",
                  "Cant. Pendiente",
                  "Total",
                  "Estado Liquidación",
                  "Comprobante",
                  "Acciones",
                ].map((header) => (
                  <TableCell
                    key={header}
                    style={{
                      fontWeight: "bold",
                      fontSize: "80%",
                      padding: "8px 2px",
                    }}
                    align="center"
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    <ClipLoader loading={true} size={50} />
                  </TableCell>
                </TableRow>
              ) : liquidacionesData &&
                Object.keys(liquidacionesData).length > 0 ? (
                Object.values(liquidacionesData).map((row) => (
                  <TableRow
                    key={row.facturaid}
                    sx={{
                      "&:hover": { backgroundColor: "#f5f5f5" },
                      "&:last-child td, &:last-child th": { border: 0 },
                      minHeight: "45px",
                    }}
                  >
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.facturaid}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.clienteid}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.nombrecliente}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.tipofacturanom}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.fechaliquidacion
                        ? formatDate(row.fechaliquidacion)
                        : "-"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.fechaingreso ? formatDate(row.fechaingreso) : "-"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.cantidad_original || "0"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.cantidad_liquidada || "0"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.cantidad_pendiente || "0"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      ${row.total?.replace(",", ".") || "0.00"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      <Chip
                        label={row.descripcionestado || "Pendiente"}
                        color={getEstadoLiquidacionColor(row.descripcionestado)}
                        size="small"
                      />
                    </TableCell>

                    {/* <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          gap: "8px",
                        }}
                      >
                        {row.url_comprobante && row.url_comprobante !== "#" ? (
                          <Tooltip title="Ver comprobante">
                            <IconButton
                              size="small"
                              component="a"
                              href={row.url_comprobante}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        ) : (
                          <Tooltip title="No hay comprobante">
                            <IconButton size="small" disabled>
                              <VisibilityOffIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="Adjuntar comprobantes">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenUploadModal(row)}
                          >
                            <AttachFileIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </div>
                    </TableCell> */}

                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          gap: "8px",
                        }}
                      >
                        {/* <Tooltip title="Ver comprobantes">
                          <IconButton
                            size="small"
                            onClick={() =>
                              handleViewComprobantes(row.facturaid)
                            }
                            disabled={
                              !row.url_comprobante ||
                              row.url_comprobante === "#"
                            }
                          >
                            {row.url_comprobante &&
                            row.url_comprobante !== "#" ? (
                              <VisibilityIcon fontSize="small" />
                            ) : (
                              <VisibilityOffIcon fontSize="small" />
                            )}
                          </IconButton>
                        </Tooltip> */}

                        <Tooltip
                          title={
                            row.url_comprobante && row.url_comprobante !== "#"
                              ? "Ver comprobantes"
                              : "No hay comprobantes"
                          }
                        >
                          <IconButton
                            size="small"
                            onClick={() =>
                              handleViewComprobantes(row.facturaid)
                            }
                            disabled={
                              !row.url_comprobante ||
                              row.url_comprobante === "#"
                            }
                          >
                            {row.url_comprobante &&
                            row.url_comprobante !== "#" ? (
                              <VisibilityIcon fontSize="small" />
                            ) : (
                              <VisibilityOffIcon fontSize="small" />
                            )}
                          </IconButton>
                        </Tooltip>

                        {/* Keep the existing upload button */}
                        <Tooltip title="Adjuntar comprobantes">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenUploadModal(row)}
                          >
                            <AttachFileIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </div>
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      <RouterLink
                        // to={`/MiTienda/Remitos/${row.facturaid}/${row.clienteid}`}
                        to={`/MiTienda/Liquidaciones/${row.facturaid}/${row.clienteid}`}
                        style={{ textDecoration: "none" }}
                      >
                        <Button variant="outlined" size="small">
                          Ver Detalle
                        </Button>
                      </RouterLink>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    <h5>No hay información para mostrar</h5>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {paginaUltima > 1 && (
          <Pagination
            count={paginaUltima}
            page={page}
            onChange={handleChangePage}
            color="primary"
            size="large"
            sx={{ alignSelf: "center", marginY: 4 }}
          />
        )}
      </div>

      <Dialog
        open={openUploadModal}
        onClose={handleCloseUploadModal}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Adjuntar comprobantes para Liquidación #
          {currentLiquidacion?.facturaid}
        </DialogTitle>
        <DialogContent>
          <input
            type="file"
            id="file-upload"
            multiple
            style={{ display: "none" }}
            onChange={handleFileUpload}
          />
          <label htmlFor="file-upload">
            <Button
              variant="outlined"
              component="span"
              fullWidth
              startIcon={<CloudUploadIcon />}
              style={{ marginBottom: "20px" }}
            >
              Seleccionar archivos
            </Button>
          </label>

          {files.length > 0 ? (
            <List dense>
              {files.map((file, index) => (
                <ListItem
                  key={index}
                  secondaryAction={
                    <IconButton edge="end" onClick={() => removeFile(index)}>
                      <DeleteIcon />
                    </IconButton>
                  }
                >
                  <ListItemIcon>
                    <AttachFileIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={file.name}
                    secondary={`${(file.size / 1024).toFixed(2)} KB`}
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography variant="body2" color="textSecondary" align="center">
              No hay archivos seleccionados
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseUploadModal}>Cancelar</Button>
          <Button
            onClick={handleUploadComprobantes}
            color="primary"
            variant="contained"
            disabled={files.length === 0 || comprobantesUpload.loading}
          >
            {comprobantesUpload.loading ? (
              <>
                <CircularProgress size={20} style={{ marginRight: 8 }} />
                Subiendo...
              </>
            ) : (
              "Subir comprobantes"
            )}
          </Button>
        </DialogActions>
      </Dialog>

      {/* <Dialog
        open={openComprobantesModal}
        onClose={() => setOpenComprobantesModal(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Comprobantes de Liquidación</DialogTitle>
        <DialogContent>
          {comprobantesState.loading ? (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                padding: "20px",
              }}
            >
              <CircularProgress />
            </div>
          ) : comprobantesState.error ? (
            <Alert severity="error">{comprobantesState.error}</Alert>
          ) : comprobantesState.data ? (
            <List>
              {Object.values(comprobantesState.data).map((doc) => (
                <ListItem
                  key={doc.factura_imagenesid}
                  button
                  component="a"
                  href={doc.url_documento}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ListItemIcon>
                    <AttachFileIcon />
                  </ListItemIcon>
                  <ListItemText primary={doc.nombre} />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography
              variant="body1"
              align="center"
              style={{ padding: "20px" }}
            >
              No hay comprobantes disponibles
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenComprobantesModal(false)}>
            Cerrar
          </Button>
        </DialogActions>
      </Dialog> */}

      <Dialog
        open={openComprobantesModal}
        onClose={() => setOpenComprobantesModal(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Comprobantes de Liquidación</DialogTitle>
        <DialogContent>
          {comprobantesState.loading ? (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                padding: "20px",
              }}
            >
              <CircularProgress />
            </div>
          ) : comprobantesState.error ? (
            <Alert severity="error">{comprobantesState.error}</Alert>
          ) : comprobantesState.data ? (
            <List>
              {Object.values(comprobantesState.data).map((doc) => (
                <ListItem
                  key={doc.factura_imagenesid}
                  button
                  component="a"
                  href={doc.url_documento}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ListItemIcon>
                    <AttachFileIcon />
                  </ListItemIcon>
                  <ListItemText primary={doc.nombre} />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography
              variant="body1"
              align="center"
              style={{ padding: "20px" }}
            >
              No se encontraron comprobantes para esta liquidación
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenComprobantesModal(false)}>
            Cerrar
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
