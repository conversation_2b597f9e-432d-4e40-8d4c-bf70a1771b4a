import React, { useEffect } from "react";
import { Button, InputAdornment, Paper, Snackbar, TextField } from "@mui/material"
import { Percent } from "@mui/icons-material";
import MuiAlert from '@mui/material/Alert';
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getAumentoMasivo, limpiarAumentoMasivo } from "../../../../redux/actions/mitienda.actions";

export const AumentoMasivo =()=>{

    const okAumento = useSelector((state) => state.mitienda.okAumentoMasivo)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const [input, setInput] = useState('')
    const [error, setError] = useState(false)

    const handleChange = (e) => {
        e.preventDefault()
        if(e.target.value > 0){
            setError(false)
            setInput(e.target.value)
        }else{
            setInput(e.target.value)
            setError(true)
        }
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const dispatch = useDispatch()

    const [open, setOpen] = React.useState(false);

    const handleClickAlert = () => {
      setOpen(true);
    };
  
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const handleOnClick = () => {
        dispatch(getAumentoMasivo(input))
        handleClickAlert()
    };

    useEffect(() =>{
        dispatch(limpiarAumentoMasivo())
    },[])
    
    return (
        <section className="container-meta-ads">
            {
                <Snackbar
                    open={open && okAumento.mensaje !== ''} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={okAumento.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okAumento.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <div className="container-form" style={{marginLeft:-20}}>
                <header className="header-meta-ads">
                    <h3 style={{marginTop:20, marginBottom:20, display:"flex"}}>
                        Aumento masivo de precios
                    </h3>
                    <div>
                        <h4>Sirve para aumentar masivamente el precio de todos tus productos en funci&oacute;n 
                            a un porcentaje dado de una manera simple y r&aacute;pida.
                        </h4>
                        <h4>
                            Una vez aumentados los precios de manera masiva, no es posible volver atr&aacute;s. 
                            No se pueden disminuir los precios de manera masiva.
                        </h4>
                        <h4>
                            Si el producto est&aacute; en oferta, se aumenta el precio de ambos (con y sin oferta) teniendo en cuenta
                            el porcentaje configurado.
                        </h4>
                    </div>
                </header>
                <Paper className="form-google-analytics">
                    <TextField 
                        helperText={error && "Introduzca un valor mayor a 0"}
                        error={error}
                        label="Porcentaje de aumento"  
                        fullWidth margin="normal" 
                        value={input || ''}
                        name="ecommerce_analytics"
                        onChange={handleChange}
                        InputProps={{
                            endAdornment:<InputAdornment position="end">
                            <Percent/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 50,
                            fontSize:17
                            },
                        }}
                    />
                    {/* <FormControlLabel
                        style={{marginTop:30, marginBottom:-30}}
                        control={
                            <Checkbox/>
                        }
                        label="Activar Google Analytics"
                    /> */}
                </Paper>
            <div style={{alignSelf:"start"}}>
                <Button 
                    disabled={error || permisos_acciones?.modificar === "0"}
                    size="large" 
                    variant="contained" 
                    onClick={handleOnClick}
                    sx={{mt: 3, mb:3}}>Guardar</Button>
            </div>
            </div>
        </section>
    )
}