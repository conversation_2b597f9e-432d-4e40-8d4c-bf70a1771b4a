import React from "react"

export const SelectSearchInputMarca = ({
    info,
    nombreMarca,
    setNombreMarca,
    setMarcaID,
    open, 
    setOpen, 
}) =>{

    return (
        <div className="input-grup-ofertas">
            <h6>Marca</h6>
            <input onClick={(e)=> {
                setOpen(true)
            }} value={nombreMarca} onChange={(e)=> setNombreMarca(e.target.value)}/>
            <div className="list-info"  > 
                {
                    info.length > 0 && open ? 
                    info.map((i) =>
                        <div className="menu-items" onClick={(e)=> {
                            setOpen(false)
                            setMarcaID(i.marcaid)
                            setNombreMarca(i.nombremarca)
                        }}>
                            {i.nombremarca}
                        </div>
                    ) : null
                }
            </div>  
        </div>
    )
}