.container-categorias{
    padding: 40px;
    width: 100%;
    min-height: 110vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
}

.lista{
    list-style: none;
}

.lista-segundo-li{
    margin-left: 30px;
    margin-top: 10px;
}

.paper{
    width: 95%;
    height: 50px;
    display: flex;
    justify-content: space-between;
}

.paper-primer-div{
    font-size: 20px;
    padding: 15px;
}

.icons{
    padding: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.icons2{
    margin-right: -20px;
    padding: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.icon{
    font-size: 25px;
    margin-right: 10px;
    cursor: pointer;
    margin-top: -20px;
}

.titulo-categorias{
    width: 90%;
    display: flex;
    justify-content: space-between;
    margin-left: 50px;
}

.text-categorias{
    width: 90%;
    margin-left: 50px;
}

@media only screen and (max-width: 1200px) {
    .paper{
        width: 95%;
        height: 90px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .container-categorias{
        padding: 40px;
        width: 100%;
        min-height: 110vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }
    .icons{
        margin-right: -20px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .icons2{
        margin-right: -30px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .icon{
        font-size: 25px;
        margin-right: 5px;
        cursor: pointer;
        margin-top: -20px;
    }
}

@media only screen and (max-width: 715px) {
    .paper{
        width: 95%;
        height: 120px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .container-categorias{
        padding: 40px;
        width: 100%;
        min-height: 110vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }
    .icons{
        margin-right: -40px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .icons2{
        margin-right: -50px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

@media only screen and (max-width: 670px) {
    .paper{
        width: 95%;
        height: 170px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .container-categorias{
        padding: 40px;
        width: 100%;
        min-height: 110vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }
    .icon{
        font-size: 25px;
        margin-right: 2px;
        cursor: pointer;
        margin-top: -20px;
    }
    .icons{
        margin-right: -40px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .icons2{
        margin-right: -50px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

@media only screen and (max-width: 499px) {
    .paper{
        width: 95%;
        height: 170px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .container-categorias{
        padding: 40px;
        width: 100%;
        min-height: 110vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }
    .icon{
        font-size: 25px;
        margin-right: 2px;
        cursor: pointer;
        margin-top: -20px;
    }
    .icons{
        margin-right: -20px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .icons2{
        margin-right: -30px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}