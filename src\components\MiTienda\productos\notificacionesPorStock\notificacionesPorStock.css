.notificaciones-container {
    width: 100%;
    min-height: 110vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.titulo-notificaciones{
    width: 90%;
    display: flex;
    justify-content: space-between;
    margin-left: 50px;
}

.container-tabla{
    display: flex;
    justify-content: center;
    margin: 20px;
    margin-left: 40px;
}

.tabla{
    width: 100%;
    align-self: center;
    min-height: 400px;
    height: auto;
}

@media only screen and (max-width: 1200px) {
    .notificaciones-container {
        width: 100%;
        min-height: 100vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }    
    .titulo-notificaciones{
        width: 70%;
        display: flex;
        justify-content: space-between;
        margin-left: 50px;
    }
    .container-tabla{
        display: flex;
        justify-content: center;
    }
    .tabla{
        width: 60%;
        align-self: center;
        min-height: 500px;
        max-height: 900px;
    }
}

@media only screen and (max-width: 990px) {
    .notificaciones-container {
        width: 100%;
        height: 150%;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }    
    .titulo-notificaciones{
        width: 70%;
        display: flex;
        justify-content: space-between;
        margin-left: 50px;
    }
    .tabla{
        width: 100%;
        align-self: center;
        min-height: 500px;
        max-height: 1000px;
    }
}

@media only screen and (max-width: 560px) {
    .notificaciones-container {
        width: 100%;
        height: auto;
        min-height: 100vh;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }    
    .titulo-notificaciones{
        width: 50%;
        display: flex;
        justify-content: space-between;
        margin-left: 50px;
    }
    .tabla{
        width: 40%;
        align-self: center;
        min-height: 500px;
        max-height: 1400px;
    }
}
