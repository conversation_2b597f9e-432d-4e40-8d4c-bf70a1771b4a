import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Field } from "@mui/material";
import Modal from "react-modal";
import {
  enviarEmail,
  getClienteById,
  limpiarEnviarEmail,
} from "../../../redux/actions/mitienda.actions";
import { useDispatch, useSelector } from "react-redux";

Modal.setAppElement("#root");

export const ModalPdf = ({ show, handleClose, presupuesto }) => {
  const cliente = useSelector((state) => state.mitienda.clienteById);
  const okEnviarEmail = useSelector((state) => state.mitienda.enviarEmail);

  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      maxHeight: "90vh",
      borderRadius: "8px",
      maxWidth: "750px",
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      //   backgroundColor: "#D1D1D1"
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  const dispatch = useDispatch();

  const [input, setInput] = useState("");
  const handleChange = (e) => {
    e.preventDefault();
    setInput(e.target.value);
  };

  useEffect(() => {
    if (presupuesto) {
      dispatch(getClienteById(presupuesto.clienteid));
      limpiarEnviarEmail();
    }
  }, [presupuesto]);

  useEffect(() => {
    setInput(cliente.email);
  }, [cliente]);

  return (
    show && (
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <h4>Descargar comprobante</h4>
          <button
            onClick={handleClose}
            style={{
              all: "unset",
              color: "black",
              paddingBottom: 10,
              cursor: "pointer",
            }}
          >
            X
          </button>
        </div>
        <Divider />
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            padding: 20,
          }}
        >
          <h5>
            Presupuesto #{presupuesto.presupuestoid} -{" "}
            {presupuesto.nombrecliente}
          </h5>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <div
              style={{
                display: "flex",
                width: "100%",
                justifyContent: "center",
              }}
            >
              <Button
                variant="contained"
                style={{ width: 200, margin: 10 }}
                onClick={(e) =>
                  dispatch(enviarEmail(presupuesto.presupuestoid, input, "123"))
                }
              >
                Enviar al email
              </Button>
              <Button variant="contained" style={{ width: 200, margin: 10 }}>
                {/* <a 
                                href={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=123&codigocomprobante=${presupuesto.presupuestoid}&tipoaccionpdf=0`}
                                target="_blank"
                                style={{color:"white"}}
                            >  */}
                <a
                  href={`${
                    process.env.REACT_APP_SERVER
                  }dompdf/generarcomprobante_pdf.php?tipocomprobante=123&codigocomprobante=${
                    presupuesto.presupuestoid
                  }&tipoaccionpdf=0&tiendaid=${localStorage.getItem(
                    "tiendausuario"
                  )}`}
                  target="_blank"
                  style={{ color: "white" }}
                >
                  Descargar
                </a>
              </Button>
            </div>
            <TextField
              value={input || ""}
              onChange={handleChange}
              style={{ width: "80%" }}
            />
          </div>
        </div>
        <Divider />
        {okEnviarEmail !== "" ? (
          <Alert
            severity={okEnviarEmail.success ? "success" : "error"}
            style={{ width: "90%", margin: 10 }}
          >
            {okEnviarEmail.mensaje}
          </Alert>
        ) : null}
      </Modal>
    )
  );
};
