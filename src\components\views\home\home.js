import React, { useEffect } from "react";

import { useDispatch, useSelector } from "react-redux";
import { sort } from "../../navigation/utils";
import BeatLoader from 'react-spinners/BeatLoader'
import { css } from "@emotion/react";

import './home.css'
import { Link } from "react-router-dom";
import { AccountBalance, Bedtime, FiberNewOutlined, PointOfSale, RemoveRedEye, Security, Settings, Shop, ShoppingCart, Store, SupervisorAccount, WhatsApp } from "@mui/icons-material";

const Home = () => {

    const info = useSelector((state) => state.user.menu)
    let ok = info.length === 0 ? false : true
    let lista = sort(info)
    const override = css`
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 250px;
    `;

    return(
        <div className='home'>
            {
                ok ? 
                <div className="listIcons">
                    {
                        lista.map(l =>
                            <Link to={`/${l.nombredireccion}/Home`} class="flip-card">
                                <div class="flip-card-inner">
                                    <div class={`flip-card-front-${l.nombredireccion === 'GestionWhatsapp' ? 'green' : 
                                        l.nombredireccion === 'ControlAcceso' ? 'grey' :
                                        l.nombredireccion === 'Finanzas' ? 'purple2' : 
                                        l.nombredireccion === 'MiTienda' ? 'orange' :
                                        l.nombredireccion === 'Configuracion' ? 'red' :
                                        l.nombredireccion === 'Compras' ? 'pink' :
                                        l.nombredireccion === 'Ventas' ? 'purple' :
                                        l.nombredireccion === 'SleepWellnessTracker' ? 'black' :
                                        l.nombredireccion === 'ComputerVision' ? 'blue' :
                                        'default' }`}>
                                        <h4>
                                            {l.nombredireccion}
                                        </h4>
                                    </div>
                                    <div class={`flip-card-back-${l.nombredireccion === 'GestionWhatsapp' ? 'green' : 
                                    l.nombredireccion === 'ControlAcceso' ? 'grey' :
                                    l.nombredireccion === 'Finanzas' ? 'purple2' : 
                                    l.nombredireccion === 'MiTienda' ? 'orange' :
                                    l.nombredireccion === 'Configuracion' ? 'red' :
                                    l.nombredireccion === 'Compras' ? 'pink' :
                                    l.nombredireccion === 'Ventas' ? 'purple' :
                                    l.nombredireccion === 'SleepWellnessTracker' ? 'black' :
                                    l.nombredireccion === 'ComputerVision' ? 'blue' :
                                    'default' }`}>
                                         <h2 class="title-card">
                                            {l.nombredireccion === 'GestionWhatsapp' ? <WhatsApp sx={{height:80, width:40}}/> : 
                                            l.nombredireccion === 'ControlAcceso' ? <Security sx={{height:80, width:40}}/> :
                                            l.nombredireccion === 'Finanzas' ? <AccountBalance sx={{height:80, width:40}}/> : 
                                            l.nombredireccion === 'MiTienda' ? <Store sx={{height:80, width:40}}/> :
                                            l.nombredireccion === 'Configuracion' ? <Settings sx={{height:80, width:40}}/> :
                                            l.nombredireccion === 'Compras' ? <ShoppingCart sx={{height:80, width:40}}/> :
                                            l.nombredireccion === 'Ventas' ? <PointOfSale sx={{height:80, width:40}}/> :
                                            l.nombredireccion === 'Administracion\n' ? <SupervisorAccount sx={{height:80, width:40}}/> :
                                            l.nombredireccion === 'SleepWellnessTracker' ? <Bedtime sx={{height:80, width:40}}/> :
                                            l.nombredireccion === 'ComputerVision' ? <RemoveRedEye sx={{height:80, width:40}}/> :
                                            <FiberNewOutlined sx={{height:80, width:40}}/>}
                                            &nbsp;</h2>
                                   </div>
                                </div>
                            </Link>
                        )
                    }
                    
                </div> : <BeatLoader color="#0093E9" css={override} size={15}/>
            }   
        </div>
    )
}

export default Home;