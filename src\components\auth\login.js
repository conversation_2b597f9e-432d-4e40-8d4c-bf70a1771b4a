import React, { useState } from 'react'
import { useDispatch } from 'react-redux'
import { Link } from 'react-router-dom'
import { logUser } from '../../redux/actions/user.actions'
import { StyledInput } from '../shared/inputs/styled-inputs'
import logo from '../../media/logo_develone.png'
import background from '../../media/login-brackground.png'
import './styles.css'
import {useTranslation} from "react-i18next"
import ReactCountryFlag from "react-country-flag"


const Login = ({isAuthenticated}) => {

  const [t, i18n] = useTranslation("global")

  const [user, setUser] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')

  const dispatch = useDispatch()

  const handleLogin = (e) => {
    e.preventDefault()
    dispatch(logUser({ user, password })).catch((err) => setError(err.message))
    console.log(error)
  }

  return (
    <div>
      <div className="d-flex vh-100 flex-nowrap">
      <div className="col-4 login h-100 center-all p-5" style={{backgroundColor:"white" }}>
      <div>{isAuthenticated.ok === false ? <div style={{color:"red"}}>{isAuthenticated.info}</div>
      : null}</div>
        <form
          style={{ width: '90%'}}
          className="h-100 center-all"
          onSubmit={handleLogin}
        >
          <img className="mb-5" src={logo} alt="develone" />
          <StyledInput
            inputStyle={{ backgroundColor: 'rgba(246, 246, 245, 0.7)'}}
            style={{ flex: 'unset', width: '100%', color:"black" }}
            label={t("login.user")}
            value={user}
            onChange={(e) => setUser(e.target.value)}
            onFocus={() => setError('')}
          />
          <StyledInput
            inputStyle={{ backgroundColor: 'rgba(246, 246, 245, 0.7)' }}
            style={{ flex: 'unset', width: '100%', color:"black" }}
            label={t("login.password")}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            type="password"
            onFocus={() => setError('')}
          />
          {error && (
            <p className="text-danger font-italic strong-text">
              <i className="fas fa-exclamation-triangle mr-2 my-2" />
              Wrong user or password
            </p>
          )}
          <div className="forgot-pass">
            <Link style={{color:"black"}} to={'/recovery'}>{t("login.forgot-password")}</Link>
          </div>

          <button
            type="submit"
            disabled={!user || !password}
            className="btn btn-light my-3"
            style={{backgroundColor:"#007FFF", color:"white"}}
          >
            {t("login.log-in")}
          </button>
        </form>
      </div>
      <div
        style={{ background: `url(${background})`, backgroundSize: 'cover' }}
        className="col-8 h-100"
      >
        <div className="login-gradient w-100 h-100 center-all">
          <div style={{display:"flex"}}>
            <button onClick={() => i18n.changeLanguage("es")}>
            <ReactCountryFlag
              countryCode="ES"
              svg
              style={{
                fontSize: '2em',
                lineHeight: '2em',
              }}
            />
            </button>
          <button onClick={() => i18n.changeLanguage("en")}>
            <ReactCountryFlag
              countryCode="US"
              svg
              style={{
                fontSize: '2em',
                lineHeight: '2em',
              }}
            />
          </button>
          </div>  
          <h2 className="m-0" style={{ fontSize: '72px', color:"#eeeeee" }}>
          Bienvenido a Control de Acceso <br />
            <span className="text-warning">
              <br />
              Software
            </span>
          </h2>
        </div>
      </div>
    </div>
    </div>
  )
}

export default Login
