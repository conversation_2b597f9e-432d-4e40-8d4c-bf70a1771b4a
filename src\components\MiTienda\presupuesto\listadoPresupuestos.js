import {
  AddBox,
  Block,
  Download,
  FileCopyRounded,
  NoteAdd,
  PictureAsPdf,
  Print,
  RemoveRedEye,
} from "@mui/icons-material";
import { <PERSON><PERSON>, Tooltip } from "@mui/material";
import React from "react";
import FilterListIcon from "@mui/icons-material/FilterList";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  clonarPresupuesto,
  generarPedidoPresupuesto,
  getPresupuestos,
  limpiarEnviarEmail,
} from "../../../redux/actions/mitienda.actions";
import { useSelector } from "react-redux";
import Pagination from "@mui/material/Pagination";
import { useState } from "react";
import * as XLSX from "xlsx";
import { ModalFiltrosPresupuesto } from "./modalFiltros";
import { FaLocationArrow } from "react-icons/fa";
import { ModalAgregarItems } from "./modalAgregarItem";
import { ModalPdf } from "./modalPdf";
import { Link } from "react-router-dom";
import { ModalVerPdf } from "./verPdf";
import ClipLoader from "react-spinners/ClipLoader";

export const ListadoPresupuestos = () => {
  const pedidos = useSelector((state) => state.mitienda.presupuestos);
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaPresupuestos
  );
  const okCrearPresupuesto = useSelector(
    (state) => state.mitienda.okCrearPresupuesto
  );
  const okCrearDetallePresupuesto = useSelector(
    (state) => state.mitienda.okCrearDetallePresupuesto
  );
  const okClonarPresupuesto = useSelector(
    (state) => state.mitienda.okClonarPresupuesto
  );
  const okPedidoPresupuesto = useSelector(
    (state) => state.mitienda.okPedidoPresupuesto
  );

  let pathname = window.location.pathname;
  let permisos_acciones = Object.values(
    JSON.parse(localStorage.getItem("permisos_acciones"))
  ).filter((p) =>
    p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase())
  )[0];

  const [presupuesto, setPresupuesto] = useState("");

  const [show, setShow] = useState("");
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const [showPdf, setShowPdf] = useState("");
  const handleClosePdf = () => {
    dispatch(limpiarEnviarEmail());
    setShowPdf(false);
  };
  const handleShowPdf = (p) => {
    setPresupuesto(p);
    setShowPdf(true);
  };

  const [showPreviewPdf, setShowPreviewPdf] = useState("");
  const handleClosePreviewPdf = () => setShowPreviewPdf(false);
  const handleShowPreviewPdf = (p) => {
    setPresupuesto(p);
    setShowPreviewPdf(true);
  };

  const [showAgregarItems, setShowAgregarItems] = useState("");
  const handleCloseAgregarItems = () => {
    setShowAgregarItems(false);
  };
  const handleShowAgregarItems = () => setShowAgregarItems(true);

  const handleGenerarPedido = (p) => {
    dispatch(generarPedidoPresupuesto(p.presupuestoid));
  };

  const handleClonarPresupuesto = (p) => {
    dispatch(clonarPresupuesto(p.presupuestoid));
  };

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const [clienteid, setClienteid] = useState(0);
  const handleChangeClienteid = (e) => {
    setClienteid(e.target.value);
  };

  const [numerofactura, setNumeroFactura] = useState("");
  const handleChangeNumeroFactura = (e) => {
    setNumeroFactura(e.target.value);
  };

  const [nrocomprobante, setNrocomprobante] = useState("");
  const handleChangeNrocomprobante = (e) => {
    setNrocomprobante(e.target.value);
  };

  const [nombrecliente, setNombrecliente] = useState("");
  const handleChangeNombrecliente = (e) => {
    setNombrecliente(e.target.value);
  };

  const [fechaDesde, setFechaDesde] = useState("");
  const handleChangeFechaDesde = (e) => {
    setFechaDesde(e.target.value);
  };

  const [fechaHasta, setFechaHasta] = useState("");
  const handleChangeFechaHasta = (e) => {
    setFechaHasta(e.target.value);
  };

  const dispatch = useDispatch();

  const reset = (e) => {
    setClienteid(0);
    setNumeroFactura("");
    setNrocomprobante("");
    setFechaDesde("");
    setFechaHasta("");
    setNombrecliente("");
    dispatch(getPresupuestos("", "", "", "", "", "", 1));
    handleClose();
  };

  const search = () => {
    setPage(1);
    dispatch(
      getPresupuestos(
        numerofactura,
        nrocomprobante,
        nombrecliente,
        fechaDesde,
        fechaHasta,
        clienteid,
        1
      )
    );
    handleClose();
  };

  const downloadExcel = () => {
    const workSheet = XLSX.utils.json_to_sheet(pedidos);
    const workBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workBook, workSheet, "info");
    let buffer = XLSX.write(workBook, { bookType: "xlsx", type: "buffer" });
    XLSX.write(workBook, { bookType: "xlsx", type: "binary" });

    XLSX.writeFile(workBook, "Presupuestos.xlsx");
  };

  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(false);
  }, [pedidos]);

  useEffect(() => {
    setLoading(true);
    dispatch(
      getPresupuestos(
        numerofactura,
        nrocomprobante,
        nombrecliente,
        fechaDesde,
        fechaHasta,
        clienteid,
        page
      )
    );
  }, [page, okPedidoPresupuesto, okClonarPresupuesto]);

  return (
    <div className="ventas-container">
      <ModalFiltrosPresupuesto
        show={show}
        handleClose={handleClose}
        setClienteId={setClienteid}
        handleChangeNrocomprobante={handleChangeNrocomprobante}
        nrocomprobante={nrocomprobante}
        handleChangeNombrecliente={handleChangeNombrecliente}
        nombrecliente={nombrecliente}
        handleChangeFechaDesde={handleChangeFechaDesde}
        fechaDesde={fechaDesde}
        handleChangeFechaHasta={handleChangeFechaHasta}
        fechaHasta={fechaHasta}
        reset={reset}
        search={search}
      />
      <ModalPdf
        show={showPdf}
        handleClose={handleClosePdf}
        presupuesto={presupuesto}
      />
      <ModalAgregarItems
        show={showAgregarItems}
        handleClose={handleCloseAgregarItems}
        info={presupuesto}
      />
      <ModalVerPdf
        show={showPreviewPdf}
        handleClose={handleClosePreviewPdf}
        id={presupuesto.presupuestoid}
      />
      <div className="titulo-ventas">
        <h3 className="text-ventas">Presupuestos</h3>
        <div className="list-ventas" style={{ display: "flex" }}>
          <div style={{ display: "flex", marginLeft: 40 }}>
            <Button
              variant="outlined"
              sx={{ height: 40, width: 150, marginTop: 2 }}
              onClick={handleShow}
            >
              <FilterListIcon /> Filtrar
            </Button>
            <Button
              variant="outlined"
              sx={{ height: 40, width: 150, marginTop: 2, marginLeft: 5 }}
              onClick={downloadExcel}
            >
              <Download /> Exportar
            </Button>
          </div>
        </div>
      </div>

      {permisos_acciones?.listar !== "0" && (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyItems: "center",
          }}
        >
          <TableContainer
            component={Paper}
            sx={{
              marginTop: 5,
              width: "95%",
              alignSelf: "center",
              marginLeft: 3,
              marginBottom: 5,
            }}
          >
            <Table aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell style={{ fontWeight: "bold", fontSize: "110%" }}>
                    #
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%", padding: 0 }}
                    align="center"
                  >
                    Nombre
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%", padding: 0 }}
                    align="center"
                  >
                    Cliente
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Fecha
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Cantidad
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Total
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Ver
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    PDF
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Imprimir
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Generar pedido
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Agregar item
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Clonar
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow sx={{ p: 20 }}>
                    <TableCell colSpan={13} align="center">
                      <ClipLoader loading={loading} size={50} />
                    </TableCell>
                  </TableRow>
                ) : pedidos.data.length > 0 ? (
                  pedidos.data.map((row) => (
                    <TableRow
                      key={row.presupuestoid}
                      sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                    >
                      <TableCell
                        style={{
                          fontSize: "16px",
                          width: "50px",
                          height: "40px",
                          padding: 3,
                        }}
                        component="th"
                        scope="row"
                      >
                        <Link
                          to={`/Mitienda/Presupuestos/${row.presupuestoid}`}
                        >
                          {row.presupuestoid}
                        </Link>
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.nombre}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "150px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.nombrecliente}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "50px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.fechapresupuesto}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "50px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.totalCantidad}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "50px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        ${row.totalMonto}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "50px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                        onClick={(e) => handleShowPreviewPdf(row)}
                      >
                        <RemoveRedEye />
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "17px",
                          width: "50px",
                          height: "40px",
                          padding: 0,
                          cursor: "pointer",
                        }}
                        align="center"
                        onClick={(e) => handleShowPdf(row)}
                      >
                        <PictureAsPdf />
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "17px",
                          width: "50px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {/* <a 
                                            href={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=123&codigocomprobante=${row.presupuestoid}&tipoaccionpdf=0`}
                                            target="_blank"
                                            style={{color:"black"}}
                                        >  */}
                        <a
                          href={`${
                            process.env.REACT_APP_SERVER
                          }dompdf/generarcomprobante_pdf.php?tipocomprobante=123&codigocomprobante=${
                            row.presupuestoid
                          }&tipoaccionpdf=0&tiendaid=${localStorage.getItem(
                            "tiendausuario"
                          )}`}
                          target="_blank"
                          style={{ color: "black" }}
                        >
                          <Print />
                        </a>
                      </TableCell>
                      {row.generarpedido ? (
                        <TableCell
                          style={{
                            fontSize: "17px",
                            width: "50px",
                            height: "40px",
                            padding: 0,
                            cursor: "pointer",
                          }}
                          align="center"
                          onClick={(e) => handleGenerarPedido(row)}
                        >
                          <Tooltip
                            title="Generar pedido"
                            sx={{ m: 0.5, fontSize: 26 }}
                          >
                            <NoteAdd />
                          </Tooltip>
                        </TableCell>
                      ) : (
                        <TableCell
                          style={{
                            fontSize: "17px",
                            width: "50px",
                            height: "40px",
                            padding: 0,
                            cursor: "pointer",
                          }}
                          align="center"
                        >
                          <FaLocationArrow />
                        </TableCell>
                      )}
                      {row.generarpedido ? (
                        <TableCell
                          style={{
                            fontSize: "17px",
                            width: "50px",
                            height: "40px",
                            padding: 0,
                            cursor: "pointer",
                          }}
                          align="center"
                        >
                          <Tooltip
                            title="Agregar item"
                            sx={{ m: 0.5, fontSize: 26 }}
                          >
                            <Button
                              onClick={(e) => {
                                setPresupuesto(row);
                                handleShowAgregarItems();
                              }}
                              style={{ color: "black" }}
                              disabled={permisos_acciones?.modificar === "0"}
                            >
                              <AddBox />
                            </Button>
                          </Tooltip>
                        </TableCell>
                      ) : (
                        <TableCell
                          style={{
                            fontSize: "17px",
                            width: "50px",
                            height: "40px",
                            padding: 0,
                          }}
                          align="center"
                        >
                          <Block />
                        </TableCell>
                      )}
                      <TableCell
                        style={{
                          fontSize: "17px",
                          width: "100px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        <Tooltip
                          onClick={(e) => handleClonarPresupuesto(row)}
                          title="Clonar"
                          sx={{ m: 0.5, fontSize: 26 }}
                        >
                          <FileCopyRounded />
                        </Tooltip>
                        {/* <Tooltip title="Borrar" sx={{m:0.5, fontSize:26}}><Delete/></Tooltip> */}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5}></TableCell>
                    <TableCell colSpan={4}>
                      <h5>No hay informaci&oacute;n para mostrar</h5>
                    </TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Pagination
            count={paginaUltima}
            page={page}
            onChange={handleChangePage}
            size="large"
            sx={{ alignSelf: "center" }}
          />
        </div>
      )}
    </div>
  );
};
