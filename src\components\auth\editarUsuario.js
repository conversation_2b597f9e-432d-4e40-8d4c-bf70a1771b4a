import { Button, Checkbox, FormControlLabel, MenuItem, Paper, Select, Snackbar, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import './styles.css'
import { useDispatch, useSelector } from "react-redux";
import { editarUsuario, getUsuario } from "../../redux/actions/user.actions";
import Mui<PERSON>lert from '@mui/material/Alert';
import { getDescuentosSinPaginado } from "../../redux/actions/mitienda.actions";
import { Link } from 'react-router-dom';
import { ArrowBack } from "@mui/icons-material";

export const EditarUsuarioTienda = () =>{

    const ok = useSelector((state) => state.user.editar_usuario)
    const descuentos = useSelector((state) => state.mitienda.descuentosSinPaginado)
    const usuario = useSelector((state) => state.user.usuario_tienda)

    const [input, setInput] = useState('')
    const [error, setError] = useState(false)

    var md5 = require('md5');
    
    const handleChange = (e) => {
        e.preventDefault()
        if(e.target.name === "telefono"){
            let aux = isNaN(e.target.value)
            setError(aux)
        }
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }
    
    const dispatch = useDispatch()

    const handleClick = () => {
        dispatch(editarUsuario({
            usuarioid: input.usuarioid,
            usuario: input.usuario,
            apellido: input.apellido,
            nombre: input.nombre,
            email: input.email,
            telefono: input.telefono,
            domicilio: input.domicilio,
            descuento: input.descuento,
            descuento_producto: input.descuento_producto,
            comisionvta: input.comisionvta,
            activo: input.activo,
            cambiaclave: input.cambiaclave,
            clave: md5(input.clave),
        }))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };
    

    useEffect(() =>{
        setInput({
            usuarioid: usuario.usuarioid,
            usuario: usuario.usuario,
            apellido: usuario.apellido,
            nombre: usuario.nombre,
            email: usuario.email,
            telefono: usuario.telefono,
            domicilio: usuario.domicilio,
            descuento: usuario.descuento,
            descuento_producto: usuario.descuento_producto,
            comisionvta: usuario.comisionvta,
            activo: usuario.activo,
            cambiaclave: "0",
            clave: "",
            confirmarclave:""
        })
    },[usuario])

    useEffect(()=> {
        dispatch(getDescuentosSinPaginado())
        dispatch(getUsuario())
    },[])

    return (
        <div className="container-crear-usuario">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{ok.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <Paper style={{width:"65%", minHeight:"80vh", height:"80%", margin:20, padding:30, display:"flex", justifyContent:"center", flexDirection:"column"}}>
                <div className="titulo-registrar">Editar usuario</div>
                <div className="form-paper">
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Usuario</h4>
                        <TextField
                            fullWidth
                            name="usuario"
                            value={input.usuario || ''}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Nombre</h4>
                        <TextField
                            fullWidth
                            name="nombre"
                            value={input.nombre || ''}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Apellido</h4>
                        <TextField
                            fullWidth
                            name="apellido"
                            value={input.apellido || ''}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Email</h4>
                        <TextField
                            fullWidth
                            name="email"
                            value={input.email || ''}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Tel&eacute;fono</h4>
                        <TextField
                            fullWidth
                            type="tel"
                            name="telefono"
                            error={error === true}
                            helperText={error === true && "Debe ser un numero"}
                            value={input.telefono || ''}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Domicilio</h4>
                        <TextField
                            fullWidth
                            name="domicilio"
                            value={input.domicilio || ''}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", width:400, margin:10, marginTop:35, alignItems:"center"}}>
                        <h4>Usuario activo</h4>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black", marginLeft:1}}
                            onChange={(e)=> handleCheck(e)}
                            checked={input.activo === "1" ? true : false}
                            name="activo"
                        />
                    </div>
                    <div style={{display:"flex", width:400, margin:10, marginTop:35, alignItems:"center"}}>
                        <h4>Aplica descuento producto</h4>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black", marginLeft:1}}
                            onChange={(e)=> handleCheck(e)}
                            checked={input.descuento_producto === "1" ? true : false}
                            name="descuento_producto"
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Descuento</h4>
                        <Select
                            size="small"
                            name="descuento"
                            value={input.descuento || ''}
                            onChange={handleChange}
                            style={{height:35}}
                            >
                            <MenuItem value="">Seleccione una opcion</MenuItem>
                            {               
                                descuentos && descuentos.map((t) =>
                                    <MenuItem value={t.descuentovtaid} key={t.descuentovtaid}>{t.nombre}</MenuItem>
                                )
                            }
                        </Select>
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Comisi&oacute;n</h4>
                        <TextField
                            type="number"
                            fullWidth
                            name="comisionvta"
                            value={input.comisionvta || ''}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", width:400, margin:10, marginTop:35, alignItems:"center"}}>
                        <h4>Cambia contrase&ntilde;a</h4>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black", marginLeft:1}}
                            onChange={(e)=> handleCheck(e)}
                            checked={input.cambiaclave === "1" ? true : false}
                            name="cambiaclave"
                        />
                    </div>
                    <div style={{display:"flex", flexWrap:"wrap"}}>
                        <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                            <h4>Contrase&ntilde;a nueva</h4>
                            <TextField
                                fullWidth
                                type="password"
                                name="clave"
                                value={input.clave}
                                onChange={handleChange}
                                InputLabelProps={{
                                    style: {
                                        marginBottom:10,
                                        marginTop: -7
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 13,
                                        fontSize:17
                                    },
                                }}
                            />
                        </div>  
                        <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                            <h4>Confirmar contrase&ntilde;a</h4>
                            <TextField
                                fullWidth
                                type="password"
                                name="confirmarclave"
                                value={input.confirmarclave}
                                error={input.clave !== input.confirmarclave}
                                helperText={input.clave !== input.confirmarclave && "Las contraseñas no coinciden"}
                                onChange={handleChange}
                                InputLabelProps={{
                                    style: {
                                        marginBottom:10,
                                        marginTop: -7
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 13,
                                        fontSize:17
                                    },
                                }}
                            />
                        </div> 
                    </div>      
                </div>
                <Button 
                    disabled={
                        input.usuario === '' ||
                        input.nombre === '' ||
                        input.apellido === '' ||
                        input.telefono === '' ||
                        input.email === ''
                    }
                    onClick={handleClick}
                    className="button-registrar" 
                    sx={{mt:5}}
                ><div className="texto-button">Editar usuario</div></Button>
            </Paper>
        </div>
    )
}