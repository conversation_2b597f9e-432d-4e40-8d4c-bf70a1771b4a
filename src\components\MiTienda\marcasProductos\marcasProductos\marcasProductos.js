import React, { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux";

import Button from "@mui/material/Button";
import { Paper, Snackbar, Tooltip } from "@mui/material";
import AppsIcon from '@mui/icons-material/Apps';
import EditIcon from '@mui/icons-material/Edit';
import Pagination from '@mui/material/Pagination';

import { AgregarMarca } from "./agregarMarcaProducto";
import { getMarcasTiendaPaginado, limpiarAgregarImagenMarca, limpiarAgregarMarcaTienda, limpiarEditarMarcaTienda,  } from "../../../../redux/actions/mitienda.actions";
import { EditarMarca } from "./editarMarcaProducto";
import { ModalImagenMarca } from "./visualizarImagenMarca";
import { AddPhotoAlternate, Check, Clear, Delete, FilterList, Image } from "@mui/icons-material";
import { ModalEliminarMarcaTienda } from "./modalEliminar";
import { AgregarImagenMarca } from "./agregarImagenMarca";
import MuiAlert from '@mui/material/Alert';
import { ClipLoader } from "react-spinners";
import { ModalFiltros } from "./modalFiltros";

export const MarcasProductos = () =>{

    const info = useSelector((state) => state.mitienda.marcas_tienda_paginado)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaMarcasTienda)
    const okAgregar = useSelector((state) => state.mitienda.okConfigMarca)
    const okEliminar = useSelector((state) => state.mitienda.eliminarMarcaTienda)
    const okEditar = useSelector((state) => state.mitienda.editarMarcaTienda)
    const okImagen = useSelector((state) => state.mitienda.agregarImagenMarca)
    const okEliminarImagen = useSelector((state) => state.mitienda.eliminarImagenMarca)

    const [marca, setMarca] = useState('');

    const dispatch = useDispatch()

    const [show, setShow] = useState();
    const handleClose = () => {
        dispatch(limpiarAgregarMarcaTienda())
        setShow(false)
    };
    const handleShow = () => setShow(true);

    const [showEditar, setShowEditar] = useState();
    const handleCloseEditar = () => {
        setShowEditar(false);
        dispatch(limpiarEditarMarcaTienda())
    }
    const handleShowEditar = (e,a) => {
        setMarca(a)
        setShowEditar(true);
    }

    const [showEliminar, setShowEliminar] = useState();
    const handleCloseEliminar = () => setShowEliminar(false);
    const handleShowEliminar = (e,a) => {
        setMarca(a)
        setShowEliminar(true);
    };

    const [showAgregarImagen, setShowAgregarImagen] = useState();
    const handleCloseAgregarImagen = () => setShowAgregarImagen(false);
    const handleShowAgregarImagen = (e,a) => {
        e.preventDefault()
        setMarca(a)
        setShowAgregarImagen(true);
    }

    const [showImagen, setShowImagen] = useState();
    const handleCloseImagen = () => {
        setShowImagen(false);
        dispatch(limpiarAgregarImagenMarca())
    }
    const handleShowImagen = (e,a) => {
        e.preventDefault()
        setMarca(a)
        setShowImagen(true);
    }

    const [showFiltros, setShowFiltros] = useState();
    const handleCloseFiltros = () => {
        setShowFiltros(false)
    };
    const handleShowFiltros = () => setShowFiltros(true);

    const [nombre, setNombre] = useState('')
    const handleChangeNombre = (e) =>{
        setNombre(e.target.value)
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (e,value) => {
        e.preventDefault()
        setPage(value);
    };

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openAgregarMarca, setOpenAgregarMarca] = React.useState(false);
    const handleClickAlertAgregarMarca = () => {
      setOpenAgregarMarca(true);
    };
    const handleCloseAlertAgregarMarca = () => {
        setOpenAgregarMarca(false);
    };

    const [openEditarMarca, setOpenEditarMarca] = React.useState(false);
    const handleClickAlertEditarMarca = () => {
      setOpenEditarMarca(true);
    };
    const handleCloseAlertEditarMarca = () => {
        setOpenEditarMarca(false);
        limpiarEditarMarcaTienda()
    };

    const [openAgregarImagen, setOpenAgregarImagen] = React.useState(false);
    const handleClickAlertAgregarImagen = () => {
      setOpenAgregarImagen(true);
    };
    const handleCloseAlertAgregarImagen = () => {
        setOpenAgregarImagen(false);
    };

    const [openEliminar, setOpenEliminar] = React.useState(false);
    const handleClickAlertEliminar = () => {
      setOpenEliminar(true);
    };
    const handleCloseAlertEliminar = () => {
        setOpenEliminar(false);
    };

    const reset = (e) =>{
        setNombre('')
        setPage(1)
        dispatch(getMarcasTiendaPaginado(page,''))
    }

    const search = () =>{
        dispatch(getMarcasTiendaPaginado(page,nombre))
        handleCloseFiltros()
    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        setLoading(true)
        dispatch(getMarcasTiendaPaginado(page,''))
    }, [page, okAgregar, okEliminar, okEditar, okImagen, okEliminarImagen])
    
    return (
        <section className="container-categorias">
            {
                <AgregarMarca show={show} handleClose={handleClose} handleClickAlert={handleClickAlertAgregarMarca}/>
            }
            {
                <EditarMarca show={showEditar} handleClose={handleCloseEditar} marca={marca} handleClickAlert={handleClickAlertEditarMarca}/>
            }
            {
                <ModalImagenMarca show={showImagen} handleClose={handleCloseImagen} marca={marca} handleClickAlert={handleClickAlert}/>
            }
            {
                <AgregarImagenMarca show={showAgregarImagen} handleClose={handleCloseAgregarImagen} marca={marca} handleClickAlert={handleClickAlertAgregarImagen}/>
            }
            {
                <ModalEliminarMarcaTienda 
                show={showEliminar} 
                handleClose={handleCloseEliminar} 
                marca={marca}
                handleClickAlert={handleClickAlertEliminar}
                />
            }
            <ModalFiltros
                show={showFiltros}
                handleClose={handleCloseFiltros}
                nombre={nombre}
                handleChangeNombre={handleChangeNombre}
                reset={reset}
                search={search}
            />
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={okEliminarImagen.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEliminarImagen.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarMarca} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarMarca}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarMarca} 
                        severity={okAgregar.ok === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Marca creada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEditarMarca} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditarMarca}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditarMarca} 
                        severity={okEditar === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        {okEditar === true ? <h5>Marca editada con &eacute;xito!</h5> : <h5>Hubo un problema al editar!</h5>}
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarImagen} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarImagen}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarImagen} 
                        severity={okImagen === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Imagen agregada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEliminar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEliminar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEliminar} 
                        severity={okEliminar === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Marca eliminada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            <div  style={{marginBottom:30}}>
                <header className="titulo-categorias">
                    <h3>Marcas</h3>
                    <Button 
                        size="large" 
                        variant="contained"
                        className="btn-miplan"
                        onClick={handleShow}
                    >AGREGAR</Button>
                </header>
                <div className="text-categorias">
                    <h4>En esta secci&oacute;n podr&aacute;s agregar las marcas para luego
                        subir los productos en tu tienda.
                    </h4>
                </div>
            </div>
            <Button 
                variant="outlined" 
                sx={{height:40,width:150, marginLeft:6}}
                onClick={handleShowFiltros}>
                <FilterList/> Filtrar
            </Button>
            {
                loading ? 
                <div style={{display:"flex", justifyContent:"center"}}>
                    <ClipLoader 
                        loading={loading}
                        size={50}
                    />
                </div> :
                info.length > 0 ? 
                <ul className="lista">
                {
                    info.length > 0 && info.map ((a) => 
                        <li key={a.marcaid} style={{marginTop:"20px"}}>
                            <Paper className="paper">
                                <div style={{display:"flex"}}>
                                    <div className="paper-primer-div"><AppsIcon/></div>
                                    <div className="paper-primer-div">{a.nombremarca}</div>
                                </div>
                                <div className="icons">
                                    <Tooltip placement="top" title={a.ecommerce === "1" ? "Se muestra en ecommerce" : "No se muestra en ecommerce"} className="icon">
                                        {
                                            a.ecommerce === "1" ? <Check color="success"/> : <Clear color="error"/>
                                        }
                                    </Tooltip>
                                    <Tooltip placement="top" title={a.activo=== "1" ? "Activada" : "Desactivada"} className="icon">
                                        {
                                            a.activo === "1" ? <Check color="success"/> : <Clear color="error"/>
                                        }
                                    </Tooltip>
                                    <div onClick={(e) => handleShowEditar(e, a)} className="icon">
                                        <Tooltip title="Editar">
                                            <EditIcon style={{color:"black"}}/>
                                        </Tooltip>
                                    </div>
                                    <div onClick={(e) => handleShowAgregarImagen(e, a)} className="icon">
                                        <Tooltip title="Agregar imagen">
                                            <AddPhotoAlternate style={{color:"black"}}/>
                                        </Tooltip>
                                    </div>
                                    <div onClick={(e) => handleShowImagen(e, a)} className="icon">
                                        <Tooltip title="Ver imagen">
                                            <Image style={{color:"black"}}/>
                                        </Tooltip>
                                    </div>
                                    <div onClick={(e) => handleShowEliminar(e, a)} className="icon">
                                        <Tooltip title="Eliminar">
                                            <Delete style={{color:"black"}}/>
                                        </Tooltip>
                                    </div>
                                </div>
                            </Paper>
                        </li>
                    )
                }
                </ul> :
                <div style={{display:"flex", justifyContent:"center"}}>
                    <h3>No hay informaci&oacute;n para mostrar</h3>
                </div>
            }
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </section>
    )
}