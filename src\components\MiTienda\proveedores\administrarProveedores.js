import React, { useEffect, useState } from 'react';
import { Button, Snackbar, Tooltip } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { useDispatch, useSelector } from 'react-redux';
import Pagination from '@mui/material/Pagination';
import { getProveedoresTienda } from '../../../redux/actions/mitienda.actions';
import { Edit } from '@mui/icons-material';
import { ModalEditarProveedor } from './modalEditarProveedor';
import MuiAlert from '@mui/material/Alert';
import { ModalFiltrosProveedores } from './modalFiltros';
import ClipLoader from "react-spinners/ClipLoader";

export const Proveedores = () => {

    const info = useSelector((state) => state.mitienda.proveedores)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaProveedores)
    const ok = useSelector((state) => state.mitienda.editarProveedor)

    let pathname = window.location.pathname 
    
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const dispatch = useDispatch()
    const [proveedor, setProveedor] = useState('')

    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => {
        setShow(true);
    }

    const [showEditar, setShowEditar] = useState(false);
    const handleCloseEditar = () => setShowEditar(false);
    const handleShowEditar = (e,row) => {
        e.preventDefault()
        setShowEditar(true);
        setProveedor(row)
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [nombre, setNombre] = useState('')
    const handleChangeNombre = (e) =>{
        setNombre(e.target.value)
    }

    const reset = (e) =>{
        setNombre('')
        setPage(1)
        dispatch(getProveedoresTienda(page,'','',''))
        handleClose()
    }

    const search = () =>{
        dispatch(getProveedoresTienda(page,nombre))
        handleClose()
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });
    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() => {
        setLoading(true)
        dispatch(getProveedoresTienda(page, nombre))
    },[page, ok])

    return (
        <div className="notificaciones-container">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{ok.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <ModalEditarProveedor
                    show={showEditar}
                    handleClose={handleCloseEditar}
                    proveedor={proveedor}
                    handleClickAlert={handleClickAlert}
                />
            }
            {
                <ModalFiltrosProveedores
                    show={show}
                    handleClose={handleClose}
                    nombre={nombre}
                    handleChangeNombre={handleChangeNombre}
                    reset={reset}
                    search={search}
                />
            }
            <div>
                <header className="titulo-notificaciones">
                    <h3>Proveedores</h3>
                    <Button 
                        variant="outlined" 
                        sx={{height:40,width:150}}
                        onClick={handleShow}>
                        <FilterListIcon/> Filtrar
                    </Button>
                </header>
            </div>
            <div style={{display:"flex", flexDirection:"column", width:"95%", marginTop:5, marginLeft:50}}>
                <TableContainer component={Paper} sx={{margin:5,width:"100%", alignSelf:"center"}}>
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Nombre</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Tipo documento</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Nro documento</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Tel&eacute;fono</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Email</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Email facturaci&oacute;n</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Tipo condici&oacute;n IVA</TableCell> 
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Descuento</TableCell>       
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:3}} align="center">Acciones</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                    {
                        loading ? 
                        <TableRow sx={{p:20}}>
                            <TableCell colSpan={13} align='center'>
                                <ClipLoader 
                                    loading={loading}
                                    size={50}
                                />
                            </TableCell>
                        </TableRow> :
                        info.data.length > 0 ? info.data.map((row) => (
                        <TableRow
                            key={row.proveedorid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:100}} align="center">{row.nombre}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.tipodocidentidad && row.tipodocidentidad.toUpperCase()}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.cuit}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">{row.telefono}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.email}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.emailfactura}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.tipocondicioniva && row.tipocondicioniva.toUpperCase()}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">{row.descuento}</TableCell>
                            <TableCell style={{fontSize:"15px", width:"60px", height:"50px", padding: 0}} align="center">
                                <div style={{display:"flex", justifyContent:"center", cursor:"pointer"}}>
                                    <Tooltip title="Editar">
                                        <Button onClick={(e)=>handleShowEditar(e,row)} 
                                        style={{color:"black"}} disabled={permisos_acciones.modificar === "0"}>
                                            <Edit sx={{mr:1}}/>
                                        </Button>
                                    </Tooltip>
                                </div>
                            </TableCell>
                        </TableRow> 
                        )) :
                        <TableRow>
                            <TableCell colSpan={1}></TableCell>
                            <TableCell colSpan={7} align="center">
                                <h5>No hay informaci&oacute;n para mostrar</h5>
                            </TableCell>
                            <TableCell colSpan={1}></TableCell>
                        </TableRow>}
                    </TableBody> 
                    </Table>
                </TableContainer>
            </div>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}