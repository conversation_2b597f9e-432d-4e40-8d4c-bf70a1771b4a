import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getPedidosDetalle,
  getListadoClientesByName,
  generateRemito,
  editarFechaEntrega,
} from "../../../redux/actions/mitienda.actions";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  TextField,
  Checkbox,
  IconButton,
  Tooltip,
  Badge,
  FormControl,
  Select,
  MenuItem,
  ListSubheader,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Divider,
  Typography,
  CircularProgress,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import ClipLoader from "react-spinners/ClipLoader";
import { formatDate } from "../../shared/utility";
import RemoveCircleOutline from "@mui/icons-material/RemoveCircleOutline";
import AddCircleOutline from "@mui/icons-material/AddCircleOutline";
import { Clear, Search, KeyboardArrowDown } from "@mui/icons-material";
import DeleteIcon from "@mui/icons-material/Delete";
import CloseIcon from "@mui/icons-material/Close";
import { ModalFiltrosGestionRemitos } from "./modalFiltrosGestionRemitos";

export const GestionRemitos = () => {
  const dispatch = useDispatch();
  const [page, setPage] = useState(1);
  const ITEMS_PER_PAGE = 10;

  const [cart, setCart] = useState({});
  const [cartItemCount, setCartItemCount] = useState(0);
  const [showCart, setShowCart] = useState(false);

  const [showDateModal, setShowDateModal] = useState(false);
  const [newEntregaDate, setNewEntregaDate] = useState("");

  const [pedidoId, setPedidoId] = useState("");

  const [alert, setAlert] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const [selectedQuantities, setSelectedQuantities] = useState({});

  // Filter states
  const [show, setShow] = useState(false);
  const [fechaDesde, setFechaDesde] = useState(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth() - 1, 1)
      .toISOString()
      .split("T")[0];
  });
  const [fechaHasta, setFechaHasta] = useState(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth() + 1, 0)
      .toISOString()
      .split("T")[0];
  });

  const [clienteInput, setClienteInput] = useState("");
  const [clienteid, setClienteid] = useState("");
  const [selectedOption, setSelectedOption] = useState({
    nombre: "",
    apellido: "",
  });

  const [tableLoading, setTableLoading] = useState(false);
  const [generatingRemitos, setGeneratingRemitos] = useState(false);

  const [editingDateForClient, setEditingDateForClient] = useState(null);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const {
    data: pedidosDetalle,
    loading,
    paginaUltima,
  } = useSelector((state) => state.mitienda.pedidosDetalle);

  const clientes = useSelector((state) => state.mitienda.clientesByName);

  useEffect(() => {
    // only fetch data if we have at least one filter
    if (clienteid || pedidoId || fechaDesde || fechaHasta) {
      setTableLoading(true);
      dispatch(
        getPedidosDetalle(
          fechaDesde,
          fechaHasta,
          page,
          ITEMS_PER_PAGE,
          clienteid,
          pedidoId
        )
      ).finally(() => {
        setTableLoading(false);
      });
    } else {
      // clears the data when no filters are applied
      dispatch({
        type: "GET_PEDIDOS_DETALLE",
        payload: {
          data: {},
          paginaUltima: 1,
        },
      });
    }
  }, [dispatch, fechaDesde, fechaHasta, page, clienteid, pedidoId]);

  useEffect(() => {
    if (clienteInput.length > 4) {
      dispatch(getListadoClientesByName(clienteInput, ""));
    }
  }, [clienteInput]);

  const handleClose = () => setShow(false);

  const handleChangeFechaDesde = (e) => {
    setFechaDesde(e.target.value);
  };

  const handleChangeFechaHasta = (e) => {
    setFechaHasta(e.target.value);
  };

  const searchByFilters = () => {
    handleClose();
    setPage(1);
    setTableLoading(true);

    if (clienteid || pedidoId || fechaDesde || fechaHasta) {
      dispatch(
        getPedidosDetalle(
          fechaDesde,
          fechaHasta,
          1,
          ITEMS_PER_PAGE,
          clienteid,
          pedidoId
        )
      ).finally(() => {
        setTableLoading(false);
      });
    } else {
      dispatch({
        type: "GET_PEDIDOS_DETALLE",
        payload: {
          data: {},
          paginaUltima: 1,
        },
      });
      setTableLoading(false);
    }
  };

  const reset = () => {
    handleClose();
    const today = new Date();
    const initialFechaDesde = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1
    )
      .toISOString()
      .split("T")[0];
    const initialFechaHasta = new Date(
      today.getFullYear(),
      today.getMonth() + 1,
      0
    )
      .toISOString()
      .split("T")[0];

    setFechaDesde(initialFechaDesde);
    setFechaHasta(initialFechaHasta);
    setPage(1);
  };

  const clearPedidoSearch = () => {
    setPedidoId("");
    setPage(1);
    setTableLoading(true);

    if (!clienteid && !fechaDesde && !fechaHasta) {
      dispatch({
        type: "GET_PEDIDOS_DETALLE",
        payload: {
          data: {},
          paginaUltima: 1,
        },
      });
      setTableLoading(false);
    } else {
      dispatch(
        getPedidosDetalle(
          fechaDesde,
          fechaHasta,
          1,
          ITEMS_PER_PAGE,
          clienteid,
          ""
        )
      ).finally(() => {
        setTableLoading(false);
      });
    }
  };

  const handleChangePage = (event, value) => {
    setPage(value);
    setTableLoading(true);

    dispatch(
      getPedidosDetalle(
        fechaDesde,
        fechaHasta,
        value,
        ITEMS_PER_PAGE,
        clienteid,
        pedidoId
      )
    ).finally(() => {
      setTableLoading(false);
    });
  };

  const handleQuantityChange = (productId, newValue, maxQuantity) => {
    const value = Math.min(Math.max(0, Number(newValue)), Number(maxQuantity));
    setSelectedQuantities((prev) => ({
      ...prev,
      [productId]: value,
    }));
  };

  const handleSetMaxQuantity = (productId, maxQuantity) => {
    setSelectedQuantities((prev) => ({
      ...prev,
      [productId]: Number(maxQuantity),
    }));
  };

  const handleCartToggle = (item) => {
    const productId = item.pedidoplaca_productoid;
    const quantity = selectedQuantities[productId] || 0;

    if (quantity === 0) return;

    // checks if this client already exists in the cart
    const clientId = item.clienteid;
    const clientName = item.nombrecliente;

    if (cart[clientId]?.products?.[productId]) {
      // remove from cart
      setCart((prev) => {
        const newCart = { ...prev };
        delete newCart[clientId].products[productId];

        // if no more products for this client, remove the client
        if (Object.keys(newCart[clientId].products).length === 0) {
          delete newCart[clientId];
        }

        return newCart;
      });
      setCartItemCount((prev) => prev - 1);
    } else {
      // add to cart - ensure we're using the most up-to-date date from the item
      setCart((prev) => {
        const newCart = { ...prev };

        // Initialize client if not exists
        if (!newCart[clientId]) {
          newCart[clientId] = {
            clientName,
            clientId,
            fechaEntrega: item.fechaentrega,
            products: {},
          };
        } else {
          // if client exists but we're adding a new product with a different date,
          // we should update to use the latest date
          if (
            item.fechaentrega &&
            item.fechaentrega !== newCart[clientId].fechaEntrega
          ) {
            newCart[clientId].fechaEntrega = item.fechaentrega;
          }
        }

        // add product to client
        newCart[clientId].products[productId] = {
          ...item,
          selectedQuantity: quantity,
        };

        return newCart;
      });
      setCartItemCount((prev) => prev + 1);
    }
  };

  const removeFromCart = (clientId, productId) => {
    setCart((prev) => {
      const newCart = { ...prev };

      if (newCart[clientId]?.products) {
        delete newCart[clientId].products[productId];

        // if no more products for this client, remove the client
        if (Object.keys(newCart[clientId].products).length === 0) {
          delete newCart[clientId];
        }
      }

      return newCart;
    });
    setCartItemCount((prev) => prev - 1);
    setSelectedQuantities((prev) => ({
      ...prev,
      [productId]: 0,
    }));
  };

  const handleIncrement = (productId, currentPending) => {
    setSelectedQuantities((prev) => {
      const currentValue = prev[productId] || 0;
      const pendingValue = Number(currentPending || 0);

      if (currentValue < pendingValue) {
        return {
          ...prev,
          [productId]: currentValue + 1,
        };
      }
      return prev;
    });
  };

  const handleDecrement = (productId) => {
    setSelectedQuantities((prev) => {
      const currentValue = prev[productId] || 0;
      if (currentValue > 0) {
        return {
          ...prev,
          [productId]: currentValue - 1,
        };
      }
      return prev;
    });
  };

  const handleOpenDateModal = () => {
    setNewEntregaDate(new Date().toISOString().split("T")[0]);
    setShowDateModal(true);
  };

  const handleCloseDateModal = () => {
    setShowDateModal(false);
    setEditingDateForClient(null);
  };

  const handleUpdateEntregaDate = async () => {
    try {
      let selectedProducts = [];
      let selectedPedidos = [];
      let facturaIds = [];

      if (editingDateForClient && cart[editingDateForClient]) {
        // We're updating from the cart for a specific client
        const clientCart = cart[editingDateForClient];

        // gets all products for this client from the cart
        selectedProducts = Object.keys(clientCart.products);

        // collects facturaIds from the cart products
        facturaIds = [
          ...new Set(
            Object.values(clientCart.products)
              .map((item) => item.facturaid_pedido)
              .filter((id) => id) // Filter out any undefined
          ),
        ];

        selectedPedidos = Object.values(clientCart.products);
      } else {
        // We're updating from the main view - use selectedQuantities
        selectedProducts = Object.entries(selectedQuantities)
          .filter(([productId, quantity]) => quantity > 0)
          .map(([productId]) => productId);

        if (selectedProducts.length === 0) {
          throw new Error("No hay productos seleccionados para actualizar");
        }

        // Find the corresponding pedidos in pedidosDetalle
        selectedPedidos = Object.values(pedidosDetalle || {}).filter((item) =>
          selectedProducts.includes(item.pedidoplaca_productoid.toString())
        );

        if (selectedPedidos.length === 0) {
          throw new Error("No se encontraron pedidos para actualizar");
        }

        // group by facturaid_pedido to avoid duplicate API calls for the same factura
        facturaIds = [
          ...new Set(selectedPedidos.map((item) => item.facturaid_pedido)),
        ];
      }

      if (facturaIds.length === 0) {
        throw new Error("No se encontraron facturas para actualizar");
      }

      // calls the API for each unique factura
      const updatePromises = facturaIds.map(async (facturaId) => {
        return await dispatch(editarFechaEntrega(facturaId, newEntregaDate));
      });

      // Wait for all updates to complete
      await Promise.all(updatePromises);

      // Format the new date consistently for display
      const formattedNewDate = `${newEntregaDate} 00:00:00`;

      // updates the cart with the new date for all affected products
      setCart((prev) => {
        const newCart = { ...prev };

        if (editingDateForClient && newCart[editingDateForClient]) {
          const clientCart = newCart[editingDateForClient];

          clientCart.fechaEntrega = formattedNewDate;

          Object.values(clientCart.products).forEach((product) => {
            product.fechaentrega = formattedNewDate;
          });
        } else {
          // If updating from the main view, check which clients have selected products
          Object.keys(newCart).forEach((clientId) => {
            // Check if this client has any of the selected products
            const hasSelectedProduct = Object.keys(
              newCart[clientId].products
            ).some((productId) => selectedProducts.includes(productId));

            if (hasSelectedProduct) {
              // Update the client's main fecha
              newCart[clientId].fechaEntrega = formattedNewDate;

              // Also update the fechaentrega for each selected product
              Object.keys(newCart[clientId].products).forEach((productId) => {
                if (selectedProducts.includes(productId)) {
                  newCart[clientId].products[productId].fechaentrega =
                    formattedNewDate;
                }
              });
            }
          });
        }

        return newCart;
      });

      // Update selectedQuantities state to reflect these changes are applied
      if (!editingDateForClient || editingDateForClient === clienteid) {
        setSelectedQuantities((prev) => {
          // Keep only the quantities that aren't in the updated list
          const newQuantities = { ...prev };
          selectedProducts.forEach((productId) => {
            newQuantities[productId] = 0;
          });
          return newQuantities;
        });
      }

      // refreshes the data to update the view with the latest information
      setTableLoading(true);
      await dispatch(
        getPedidosDetalle(
          fechaDesde,
          fechaHasta,
          page,
          ITEMS_PER_PAGE,
          clienteid,
          pedidoId
        )
      );
      setTableLoading(false);

      setAlert({
        open: true,
        message: "Fecha de entrega actualizada correctamente",
        severity: "success",
      });
    } catch (error) {
      setAlert({
        open: true,
        message: `Error al actualizar fecha: ${error.message}`,
        severity: "error",
      });
    }

    setShowDateModal(false);
    setEditingDateForClient(null);
  };

  const formatDate = (dateString) => {
    if (!dateString || dateString === "null" || dateString === "") {
      return "Sin fecha";
    }

    try {
      const parts = dateString.split(" ")[0].split("-");
      if (parts.length !== 3) return "Formato inválido";

      return `${parts[2]}/${parts[1]}/${parts[0]}`;
    } catch (error) {
      console.error("Error formatting date:", dateString, error);
      return "Error de formato";
    }
  };

  const hasInconsistentDates = (clientId) => {
    if (!cart[clientId] || Object.keys(cart[clientId].products).length <= 1) {
      return false;
    }

    const products = Object.values(cart[clientId].products);

    // Count products with valid dates
    const productsWithValidDates = products.filter(
      (product) =>
        product.fechaentrega &&
        product.fechaentrega !== "null" &&
        product.fechaentrega !== "" &&
        !product.fechaentrega.includes("NaN")
    );

    // If no products have valid dates, there's no inconsistency
    if (productsWithValidDates.length === 0) {
      return false;
    }

    // If some products have dates and others don't, that's inconsistent
    if (productsWithValidDates.length !== products.length) {
      return true;
    }

    // Check if all valid dates are the same
    const firstDate = productsWithValidDates[0].fechaentrega;
    return productsWithValidDates.some(
      (product) => product.fechaentrega !== firstDate
    );
  };

  //w original
  // const generateRemitos = async () => {
  //   if (Object.keys(cart).length === 0) {
  //     setAlert({
  //       open: true,
  //       message: "No hay productos seleccionados para generar remito",
  //       severity: "error",
  //     });
  //     return;
  //   }

  //   // First check if any client has missing dates
  //   const clientsWithoutDate = Object.entries(cart)
  //     .filter(([_, clientData]) => {
  //       const fechaEntrega = clientData.fechaEntrega;
  //       return (
  //         !fechaEntrega ||
  //         fechaEntrega === "null" ||
  //         fechaEntrega === "" ||
  //         fechaEntrega.includes("NaN")
  //       );
  //     })
  //     .map(([_, clientData]) => clientData.clientName);

  //   if (clientsWithoutDate.length > 0) {
  //     // Open date modal for the first client without a date
  //     const clientIdWithoutDate = Object.entries(cart).find(
  //       ([_, clientData]) => {
  //         const fechaEntrega = clientData.fechaEntrega;
  //         return (
  //           !fechaEntrega ||
  //           fechaEntrega === "null" ||
  //           fechaEntrega === "" ||
  //           fechaEntrega.includes("NaN")
  //         );
  //       }
  //     )?.[0];

  //     if (clientIdWithoutDate) {
  //       setAlert({
  //         open: true,
  //         message: `Debe establecer una fecha de entrega para ${clientsWithoutDate.join(
  //           ", "
  //         )}`,
  //         severity: "warning",
  //       });

  //       // Open the date modal for this client
  //       handleOpenDateModalForClient(clientIdWithoutDate);
  //       return;
  //     }
  //   }

  //   setGeneratingRemitos(true);

  //   try {
  //     const results = [];
  //     const errors = [];

  //     for (const [clientId, clientData] of Object.entries(cart)) {
  //       try {
  //         // Convert client's products to array
  //         const pedidosArray = Object.values(clientData.products);

  //         // Extract fecha_entrega (use the same for all products of this client)
  //         const fechaEntrega = clientData.fechaEntrega.split(" ")[0];

  //         // If this client has inconsistent dates, update all pedidos with the same date
  //         if (hasInconsistentDates(clientId)) {
  //           const facturaIds = [
  //             ...new Set(
  //               pedidosArray
  //                 .map((item) => item.facturaid_pedido)
  //                 .filter((id) => id)
  //             ),
  //           ];

  //           // Update all facturas with the same date
  //           for (const facturaId of facturaIds) {
  //             await dispatch(editarFechaEntrega(facturaId, fechaEntrega));
  //           }
  //         }

  //         // Generate remito for this client
  //         const result = await dispatch(
  //           generateRemito(clientId, pedidosArray, fechaEntrega)
  //         );

  //         results.push({
  //           clientId,
  //           clientName: clientData.clientName,
  //           result,
  //         });
  //       } catch (error) {
  //         errors.push({
  //           clientId,
  //           clientName: clientData.clientName,
  //           error: error.message,
  //         });
  //       }
  //     }

  //     setCart({});
  //     setCartItemCount(0);
  //     setSelectedQuantities({});
  //     setShowCart(false);

  //     // messages
  //     if (errors.length === 0) {
  //       setAlert({
  //         open: true,
  //         message: `Remitos generados correctamente para ${results.length} cliente(s)`,
  //         severity: "success",
  //       });
  //     } else if (results.length === 0) {
  //       setAlert({
  //         open: true,
  //         message: `Error al generar remitos: ${errors[0].error}`,
  //         severity: "error",
  //       });
  //     } else {
  //       setAlert({
  //         open: true,
  //         message: `Se generaron ${results.length} remitos correctamente, pero fallaron ${errors.length}. Revise los datos e intente nuevamente.`,
  //         severity: "warning",
  //       });
  //     }

  //     // refresh
  //     setTableLoading(true);
  //     await dispatch(
  //       getPedidosDetalle(
  //         fechaDesde,
  //         fechaHasta,
  //         page,
  //         ITEMS_PER_PAGE,
  //         clienteid,
  //         pedidoId
  //       )
  //     );
  //     setTableLoading(false);
  //   } catch (error) {
  //     setAlert({
  //       open: true,
  //       message: `Error al generar remitos: ${error.message}`,
  //       severity: "error",
  //     });
  //   } finally {
  //     setGeneratingRemitos(false);
  //   }
  // };

  //f sending vendedorid
  const generateRemitos = async () => {
    if (Object.keys(cart).length === 0) {
      setAlert({
        open: true,
        message: "No hay productos seleccionados para generar remito",
        severity: "error",
      });
      return;
    }

    // First check if any client has missing dates
    const clientsWithoutDate = Object.entries(cart)
      .filter(([_, clientData]) => {
        const fechaEntrega = clientData.fechaEntrega;
        return (
          !fechaEntrega ||
          fechaEntrega === "null" ||
          fechaEntrega === "" ||
          fechaEntrega.includes("NaN")
        );
      })
      .map(([_, clientData]) => clientData.clientName);

    if (clientsWithoutDate.length > 0) {
      // Open date modal for the first client without a date
      const clientIdWithoutDate = Object.entries(cart).find(
        ([_, clientData]) => {
          const fechaEntrega = clientData.fechaEntrega;
          return (
            !fechaEntrega ||
            fechaEntrega === "null" ||
            fechaEntrega === "" ||
            fechaEntrega.includes("NaN")
          );
        }
      )?.[0];

      if (clientIdWithoutDate) {
        setAlert({
          open: true,
          message: `Debe establecer una fecha de entrega para ${clientsWithoutDate.join(
            ", "
          )}`,
          severity: "warning",
        });

        // Open the date modal for this client
        handleOpenDateModalForClient(clientIdWithoutDate);
        return;
      }
    }

    setGeneratingRemitos(true);

    try {
      const results = [];
      const errors = [];

      for (const [clientId, clientData] of Object.entries(cart)) {
        try {
          // Convert client's products to array
          const pedidosArray = Object.values(clientData.products);

          // Extract fecha_entrega (use the same for all products of this client)
          const fechaEntrega = clientData.fechaEntrega.split(" ")[0];
          
          // Get vendedorid from the first product of this client
          const vendedorid = pedidosArray[0]?.vendedorid || 0;

          // If this client has inconsistent dates, update all pedidos with the same date
          if (hasInconsistentDates(clientId)) {
            const facturaIds = [
              ...new Set(
                pedidosArray
                  .map((item) => item.facturaid_pedido)
                  .filter((id) => id)
              ),
            ];

            // Update all facturas with the same date
            for (const facturaId of facturaIds) {
              await dispatch(editarFechaEntrega(facturaId, fechaEntrega));
            }
          }

          // Generate remito for this client with vendedorid
          const result = await dispatch(
            generateRemito(clientId, pedidosArray, fechaEntrega, vendedorid)
          );

          results.push({
            clientId,
            clientName: clientData.clientName,
            result,
          });
        } catch (error) {
          errors.push({
            clientId,
            clientName: clientData.clientName,
            error: error.message,
          });
        }
      }

      setCart({});
      setCartItemCount(0);
      setSelectedQuantities({});
      setShowCart(false);

      // messages
      if (errors.length === 0) {
        setAlert({
          open: true,
          message: `Remitos generados correctamente para ${results.length} cliente(s)`,
          severity: "success",
        });
      } else if (results.length === 0) {
        setAlert({
          open: true,
          message: `Error al generar remitos: ${errors[0].error}`,
          severity: "error",
        });
      } else {
        setAlert({
          open: true,
          message: `Se generaron ${results.length} remitos correctamente, pero fallaron ${errors.length}. Revise los datos e intente nuevamente.`,
          severity: "warning",
        });
      }

      // refresh
      setTableLoading(true);
      await dispatch(
        getPedidosDetalle(
          fechaDesde,
          fechaHasta,
          page,
          ITEMS_PER_PAGE,
          clienteid,
          pedidoId
        )
      );
      setTableLoading(false);
    } catch (error) {
      setAlert({
        open: true,
        message: `Error al generar remitos: ${error.message}`,
        severity: "error",
      });
    } finally {
      setGeneratingRemitos(false);
    }
  };

  const handleCloseAlert = () => {
    setAlert({
      ...alert,
      open: false,
    });
  };

  const isProductInCart = (clientId, productId) => {
    return !!cart[clientId]?.products?.[productId];
  };

  const clearClientSearch = () => {
    setClienteid("");
    setSelectedOption({ nombre: "", apellido: "" });
    setClienteInput("");
    setPage(1);
    setTableLoading(true);

    dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });

    if (!pedidoId && !fechaDesde && !fechaHasta) {
      dispatch({
        type: "GET_PEDIDOS_DETALLE",
        payload: {
          data: {},
          paginaUltima: 1,
        },
      });
      setTableLoading(false);
    } else {
      dispatch(
        getPedidosDetalle(
          fechaDesde,
          fechaHasta,
          1,
          ITEMS_PER_PAGE,
          "",
          pedidoId
        )
      ).finally(() => {
        setTableLoading(false);
      });
    }
  };

  const handleClientChange = (e, option) => {
    setClienteid(e.target.value);
    setSelectedOption(option);
    setClienteInput("");
    setPage(1);
    setTableLoading(true);

    dispatch(
      getPedidosDetalle(
        fechaDesde,
        fechaHasta,
        1,
        ITEMS_PER_PAGE,
        e.target.value,
        pedidoId
      )
    ).finally(() => {
      setTableLoading(false);
    });
  };

  const updateCartQuantity = (clientId, productId, newValue) => {
    const product = cart[clientId]?.products[productId];
    if (!product) return;

    const maxQuantity = product.cantidadpendiente;

    const numValue = Number(newValue);

    if (numValue <= 0) {
      removeFromCart(clientId, productId);
      return;
    }

    // Ensure the new value is within bounds (max only, since we handle 0 separately)
    const value = Math.min(numValue, Number(maxQuantity));

    // Update the cart
    setCart((prev) => {
      const newCart = { ...prev };
      if (newCart[clientId]?.products[productId]) {
        newCart[clientId].products[productId] = {
          ...newCart[clientId].products[productId],
          selectedQuantity: value,
        };
      }
      return newCart;
    });

    // Also update the selectedQuantities state to keep both in sync
    setSelectedQuantities((prev) => ({
      ...prev,
      [productId]: value,
    }));
  };

  // checks if any products are selected for the current client
  const hasSelectedProductsForCurrentClient = () => {
    if (!clienteid) return false;

    // checks if any products are selected for this client
    return Object.values(pedidosDetalle || {}).some(
      (item) =>
        item.clienteid === clienteid &&
        selectedQuantities[item.pedidoplaca_productoid] > 0
    );
  };

  // Function to open date modal for the current client
  const handleOpenDateModalForCurrentClient = () => {
    setEditingDateForClient(clienteid);
    // Set default date to today if no date exists
    const clientProducts = Object.values(pedidosDetalle || {}).filter(
      (item) => item.clienteid === clienteid
    );
    const existingDate =
      clientProducts.length > 0 && clientProducts[0].fechaentrega
        ? clientProducts[0].fechaentrega.split(" ")[0]
        : new Date().toISOString().split("T")[0];

    setNewEntregaDate(existingDate);
    setShowDateModal(true);
  };

  // Function to open date modal for a specific client
  const handleOpenDateModalForClient = (clientId) => {
    const client = cart[clientId];
    if (client) {
      setEditingDateForClient(clientId);

      let latestDate = client.fechaEntrega.split(" ")[0];

      // If this is the current viewed client, check pedidosDetalle for possibly more recent data
      if (clientId === clienteid && pedidosDetalle) {
        const clientProducts = Object.values(pedidosDetalle).filter(
          (item) => item.clienteid === clientId
        );

        if (clientProducts.length > 0 && clientProducts[0].fechaentrega) {
          // Compare and use the most recent date
          const pedidosDate = clientProducts[0].fechaentrega.split(" ")[0];
          if (pedidosDate !== latestDate) {
            latestDate = pedidosDate;
          }
        }
      }

      setNewEntregaDate(latestDate);
      setShowDateModal(true);
    }
  };

  // Add this function to handle the "Cambiar Fecha" button in the cart header
  const handleOpenDateModalFromCart = () => {
    // If no clients in cart, don't do anything
    if (Object.keys(cart).length === 0) return;

    // Use the first client's date as default
    const firstClientId = Object.keys(cart)[0];
    const firstClient = cart[firstClientId];

    setEditingDateForClient(null);
    setNewEntregaDate(firstClient.fechaEntrega.split(" ")[0]);
    setShowDateModal(true);
  };

  return (
    <div className="notificaciones-container">
      <Snackbar
        open={alert.open}
        autoHideDuration={6000}
        onClose={handleCloseAlert}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert onClose={handleCloseAlert} severity={alert.severity}>
          {alert.message}
        </Alert>
      </Snackbar>

      <ModalFiltrosGestionRemitos
        show={show}
        handleClose={handleClose}
        search={searchByFilters}
        reset={reset}
        fechaDesde={fechaDesde}
        handleChangeFechaDesde={handleChangeFechaDesde}
        fechaHasta={fechaHasta}
        handleChangeFechaHasta={handleChangeFechaHasta}
      />

      <div style={{ padding: "20px" }}>
        <h3>Gestión de Entregas</h3>
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            alignItems: "center",
          }}
        >
          <Button
            variant="outlined"
            onClick={() => setShow(true)}
            startIcon={<FilterListIcon />}
          >
            Filtros
          </Button>

          {/* Client search Select component */}
          <FormControl sx={{ minWidth: 300 }}>
            <Select
              size="small"
              value={clienteid}
              onChange={(e) => {
                setClienteid(e.target.value);
                setPage(1);
              }}
              displayEmpty
              renderValue={() =>
                clienteid
                  ? `${selectedOption.nombre} ${selectedOption.apellido}`
                  : "Buscar cliente..."
              }
              onOpen={() => {
                setClienteInput("");
              }}
              IconComponent={() => (
                <div style={{ display: "flex", alignItems: "center" }}>
                  {clienteid && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearClientSearch();
                      }}
                      sx={{ marginRight: 1 }}
                    >
                      <Clear />
                    </IconButton>
                  )}
                  <KeyboardArrowDown />
                </div>
              )}
            >
              <ListSubheader>
                <TextField
                  className="client-search-input"
                  size="small"
                  autoFocus
                  placeholder="Buscar cliente..."
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  inputProps={{
                    style: {
                      height: 35,
                      fontSize: 17,
                    },
                  }}
                  onChange={(e) => setClienteInput(e.target.value)}
                  onClick={(e) => e.stopPropagation()}
                  value={clienteInput}
                />
              </ListSubheader>
              {clientes &&
                clientes.map((option) => (
                  <MenuItem
                    key={option.clienteid}
                    value={option.clienteid}
                    onClick={() =>
                      handleClientChange(
                        { target: { value: option.clienteid } },
                        option
                      )
                    }
                  >
                    {option.nombre} {option.apellido}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <TextField
            size="small"
            placeholder="Buscar por Nº Pedido..."
            value={pedidoId}
            onChange={(e) => {
              setPedidoId(e.target.value);
              setPage(1);
            }}
            onKeyPress={(e) => {
              if (e.key === "Enter") {
                setTableLoading(true);
                dispatch(
                  getPedidosDetalle(
                    fechaDesde,
                    fechaHasta,
                    1,
                    ITEMS_PER_PAGE,
                    clienteid,
                    pedidoId
                  )
                ).finally(() => {
                  setTableLoading(false);
                });
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {pedidoId && (
                    <IconButton size="small" onClick={clearPedidoSearch}>
                      <Clear />
                    </IconButton>
                  )}
                  {pedidoId && (
                    <IconButton
                      size="small"
                      onClick={() => {
                        setTableLoading(true);
                        dispatch(
                          getPedidosDetalle(
                            fechaDesde,
                            fechaHasta,
                            1,
                            ITEMS_PER_PAGE,
                            clienteid,
                            pedidoId
                          )
                        ).finally(() => {
                          setTableLoading(false);
                        });
                      }}
                    >
                      <Search />
                    </IconButton>
                  )}
                </InputAdornment>
              ),
              style: {
                height: "40px",
              },
            }}
            sx={{
              minWidth: 200,
              "& .MuiOutlinedInput-root": {
                padding: "0 8px",
              },
            }}
          />

          {/* date change button that only appears when products are selected */}
          {hasSelectedProductsForCurrentClient() && (
            <Button
              variant="outlined"
              color="primary"
              onClick={handleOpenDateModalForCurrentClient}
            >
              Cambiar Fecha de Entrega
            </Button>
          )}

          <Tooltip title="Ver remitos">
            <Badge badgeContent={cartItemCount} color="primary">
              <IconButton onClick={() => setShowCart(!showCart)}>
                <LocalShippingIcon />
              </IconButton>
            </Badge>
          </Tooltip>
        </div>
      </div>

      <TableContainer
        component={Paper}
        sx={{
          marginTop: 5,
          width: "90%",
          alignSelf: "center",
          margin: "40px auto",
          marginBottom: 5,
          padding: "0 20px",
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              {[
                "Fecha Contab.",
                "Fecha Entrega",
                "Nro Pedido",
                "Nro de Cliente",
                "Nombre SN",
                "Artículo",
                "Descripción",
                "Cant. Pedido",
                "Cant. Entregada",
                "Pendiente",
                "Almacén",
                "Precio",
                "Total(moneda)",
                "Total",
                "Cant. a Enviar",
                "Acciones",
              ].map((header) => (
                <TableCell
                  key={header}
                  style={{
                    fontWeight: "bold",
                    fontSize: "90%",
                    padding: "8px 2px",
                  }}
                  align="center"
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {tableLoading ? (
              <TableRow>
                <TableCell colSpan={16} align="center">
                  <ClipLoader loading={tableLoading} size={50} />
                </TableCell>
              </TableRow>
            ) : pedidosDetalle && Object.keys(pedidosDetalle).length > 0 ? (
              Object.values(pedidosDetalle).map((item) => (
                <TableRow key={item.pedidoplaca_productoid}>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {formatDate(item.fechapedido)}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.fechaentrega ? (
                      formatDate(item.fechaentrega)
                    ) : (
                      <span style={{ color: "#f44336" }}>Sin fecha</span>
                    )}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.pedidoplacaid}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.clienteid}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.nombrecliente}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.codigoarticulo || "--"}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.nombreproducto || "--"}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidad}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidadentregada || 0}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidadpendiente || 0}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.almacen}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {formatCurrency(item.precioventa)}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.nombremoneda}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {formatCurrency(item.totalventa)}
                  </TableCell>

                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <IconButton
                        size="small"
                        onClick={() =>
                          handleDecrement(item.pedidoplaca_productoid)
                        }
                        disabled={isProductInCart(
                          item.clienteid,
                          item.pedidoplaca_productoid
                        )}
                      >
                        <RemoveCircleOutline fontSize="small" />
                      </IconButton>
                      <TextField
                        type="number"
                        value={
                          selectedQuantities[item.pedidoplaca_productoid] || 0
                        }
                        onChange={(e) =>
                          handleQuantityChange(
                            item.pedidoplaca_productoid,
                            e.target.value,
                            item.cantidadpendiente
                          )
                        }
                        InputProps={{
                          inputProps: {
                            min: 0,
                            max: item.cantidadpendiente,
                            style: { textAlign: "center", fontSize: "80%" },
                          },
                          readOnly: isProductInCart(
                            item.clienteid,
                            item.pedidoplaca_productoid
                          ),
                        }}
                        disabled={isProductInCart(
                          item.clienteid,
                          item.pedidoplaca_productoid
                        )}
                        size="small"
                        style={{ width: "70px" }}
                      />
                      <IconButton
                        size="small"
                        onClick={() =>
                          handleIncrement(
                            item.pedidoplaca_productoid,
                            item.cantidadpendiente
                          )
                        }
                        disabled={isProductInCart(
                          item.clienteid,
                          item.pedidoplaca_productoid
                        )}
                      >
                        <AddCircleOutline fontSize="small" />
                      </IconButton>
                      {/* Add Max button */}
                      <Tooltip title="Establecer cantidad máxima">
                        <span>
                          <IconButton
                            size="small"
                            onClick={() =>
                              handleSetMaxQuantity(
                                item.pedidoplaca_productoid,
                                item.cantidadpendiente
                              )
                            }
                            disabled={
                              isProductInCart(
                                item.clienteid,
                                item.pedidoplaca_productoid
                              ) ||
                              (selectedQuantities[
                                item.pedidoplaca_productoid
                              ] || 0) === Number(item.cantidadpendiente)
                            }
                            style={{
                              marginLeft: "2px",
                              fontSize: "10px",
                              padding: "2px",
                              backgroundColor: "#f0f0f0",
                              border: "1px solid #ccc",
                              borderRadius: "2px",
                            }}
                          >
                            <span
                              style={{ fontSize: "10px", fontWeight: "bold" }}
                            >
                              MAX
                            </span>
                          </IconButton>
                        </span>
                      </Tooltip>
                    </div>
                  </TableCell>
                  {/* <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <IconButton
                        size="small"
                        onClick={() =>
                          handleDecrement(item.pedidoplaca_productoid)
                        }
                        disabled={isProductInCart(
                          item.clienteid,
                          item.pedidoplaca_productoid
                        )}
                      >
                        <RemoveCircleOutline fontSize="small" />
                      </IconButton>
                      <TextField
                        type="number"
                        value={
                          selectedQuantities[item.pedidoplaca_productoid] || 0
                        }
                        onChange={(e) =>
                          handleQuantityChange(
                            item.pedidoplaca_productoid,
                            e.target.value,
                            item.cantidadpendiente
                          )
                        }
                        InputProps={{
                          inputProps: {
                            min: 0,
                            max: item.cantidadpendiente,
                            style: { textAlign: "center", fontSize: "80%" },
                          },
                          readOnly: isProductInCart(
                            item.clienteid,
                            item.pedidoplaca_productoid
                          ),
                        }}
                        disabled={isProductInCart(
                          item.clienteid,
                          item.pedidoplaca_productoid
                        )}
                        size="small"
                        style={{ width: "70px" }}
                      />
                      <IconButton
                        size="small"
                        onClick={() =>
                          handleIncrement(
                            item.pedidoplaca_productoid,
                            item.cantidadpendiente
                          )
                        }
                        disabled={isProductInCart(
                          item.clienteid,
                          item.pedidoplaca_productoid
                        )}
                      >
                        <AddCircleOutline fontSize="small" />
                      </IconButton>
                    </div>
                  </TableCell> */}
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    <Checkbox
                      checked={isProductInCart(
                        item.clienteid,
                        item.pedidoplaca_productoid
                      )}
                      onChange={() => handleCartToggle(item)}
                      disabled={
                        !selectedQuantities[item.pedidoplaca_productoid]
                      }
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={16} align="center">
                  No hay datos para mostrar. Utilice los filtros para buscar
                  pedidos.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {paginaUltima > 1 && (
        <Pagination
          count={paginaUltima}
          page={page}
          onChange={handleChangePage}
          color="primary"
          style={{ display: "flex", justifyContent: "center", margin: "20px" }}
        />
      )}

      {/* Date change modal */}
      <Dialog open={showDateModal} onClose={handleCloseDateModal}>
        <DialogTitle>Cambiar fecha de entrega</DialogTitle>
        <DialogContent>
          <TextField
            label="Nueva fecha de entrega"
            type="date"
            value={newEntregaDate}
            onChange={(e) => setNewEntregaDate(e.target.value)}
            InputLabelProps={{ shrink: true }}
            fullWidth
            margin="dense"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDateModal}>Cancelar</Button>
          <Button
            onClick={handleUpdateEntregaDate}
            variant="contained"
            color="primary"
          >
            Actualizar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Cart sidebar */}
      {showCart && (
        <Paper
          style={{
            position: "fixed",
            right: "20px",
            top: "80px",
            padding: "20px",
            width: "400px",
            maxHeight: "80vh",
            overflow: "auto",
            zIndex: 1000,
            boxShadow: "0 4px 20px rgba(0,0,0,0.15)",
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "15px",
            }}
          >
            <h4 style={{ margin: 0 }}>Carro de Entregas</h4>
            <IconButton
              size="small"
              onClick={() => setShowCart(false)}
              style={{
                color: "#666",
                position: "absolute",
                top: "10px",
                right: "10px",
              }}
            >
              <CloseIcon />
            </IconButton>
          </div>
          {Object.keys(cart).length === 0 ? (
            <div style={{ textAlign: "center", padding: "20px 0" }}>
              No hay productos seleccionados
            </div>
          ) : (
            <>
              <Typography
                variant="body2"
                style={{ marginBottom: "15px", color: "#666" }}
              >
                {Object.keys(cart).length} cliente(s) con productos
                seleccionados
              </Typography>

              {Object.entries(cart).map(([clientId, clientData]) => (
                <div key={clientId} style={{ marginBottom: "20px" }}>
                  <Paper
                    key={clientId}
                    elevation={2}
                    style={{
                      padding: "10px",
                      backgroundColor: "#e0e0e0",
                      marginBottom: "10px",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div>
                      <Typography
                        variant="subtitle1"
                        style={{ fontWeight: "bold" }}
                      >
                        Cliente: {clientData.clientName}
                      </Typography>
                      <Typography
                        variant="body2"
                        style={{
                          color:
                            !clientData.fechaEntrega ||
                            clientData.fechaEntrega === "null" ||
                            clientData.fechaEntrega === "" ||
                            clientData.fechaEntrega.includes("NaN")
                              ? "#f44336"
                              : "#666",
                        }}
                      >
                        Fecha de entrega:{" "}
                        {formatDate(clientData.fechaEntrega) || (
                          <span style={{ color: "#f44336" }}>
                            Sin fecha (requerida)
                          </span>
                        )}
                      </Typography>
                      {hasInconsistentDates(clientId) && (
                        <Typography
                          variant="body2"
                          style={{ color: "#ff9800" }}
                        >
                          ⚠️ Hay pedidos con fechas diferentes. Todos usarán
                          esta fecha de entrega.
                        </Typography>
                      )}
                      <Typography variant="body2" style={{ color: "#666" }}>
                        Productos: {Object.keys(clientData.products).length}
                      </Typography>
                    </div>
                    <Button
                      size="small"
                      variant="outlined"
                      color={
                        !clientData.fechaEntrega ||
                        clientData.fechaEntrega === "null" ||
                        clientData.fechaEntrega === "" ||
                        clientData.fechaEntrega.includes("NaN")
                          ? "error"
                          : hasInconsistentDates(clientId)
                          ? "warning"
                          : "primary"
                      }
                      onClick={() => handleOpenDateModalForClient(clientId)}
                    >
                      {!clientData.fechaEntrega ||
                      clientData.fechaEntrega === "null" ||
                      clientData.fechaEntrega === "" ||
                      clientData.fechaEntrega.includes("NaN")
                        ? "Agregar Fecha"
                        : "Cambiar Fecha"}
                    </Button>
                  </Paper>

                  {Object.values(clientData.products).map((item) => (
                    <div
                      key={item.pedidoplaca_productoid}
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginBottom: "10px",
                        padding: "8px",
                        backgroundColor: "#f9f9f9",
                        borderRadius: "4px",
                        marginTop: "8px",
                      }}
                    >
                      <div style={{ flex: 1 }}>
                        <div
                          style={{ fontWeight: "bold", marginBottom: "5px" }}
                        >
                          {item.nombreproducto || "Producto sin nombre"}
                        </div>
                        <div style={{ fontSize: "0.9em", color: "#666" }}>
                          Pedido: {item.pedidoplacaid}
                        </div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginTop: "5px",
                          }}
                        >
                          <Typography
                            variant="body2"
                            style={{ marginRight: "10px" }}
                          >
                            Cantidad:
                          </Typography>
                          <div
                            style={{ display: "flex", alignItems: "center" }}
                          >
                            <IconButton
                              size="small"
                              onClick={() => {
                                if (item.selectedQuantity <= 1) {
                                  // If quantity is 1, directly remove the item
                                  removeFromCart(
                                    clientId,
                                    item.pedidoplaca_productoid
                                  );
                                } else {
                                  // Otherwise, decrement normally
                                  const newValue = Math.max(
                                    1,
                                    item.selectedQuantity - 1
                                  );
                                  updateCartQuantity(
                                    clientId,
                                    item.pedidoplaca_productoid,
                                    newValue
                                  );
                                }
                              }}
                              style={{ padding: "2px" }}
                            >
                              <RemoveCircleOutline fontSize="small" />
                            </IconButton>
                            <TextField
                              type="number"
                              value={item.selectedQuantity}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value === "" || Number(value) <= 0) {
                                  // If empty or zero/negative, remove the item
                                  removeFromCart(
                                    clientId,
                                    item.pedidoplaca_productoid
                                  );
                                } else {
                                  // Otherwise update normally
                                  updateCartQuantity(
                                    clientId,
                                    item.pedidoplaca_productoid,
                                    value
                                  );
                                }
                              }}
                              InputProps={{
                                inputProps: {
                                  min: 1, // Keep min as 1 since we handle 0 separately
                                  max: item.cantidadpendiente,
                                  style: {
                                    textAlign: "center",
                                    fontSize: "80%",
                                    padding: "5px",
                                  },
                                },
                              }}
                              size="small"
                              style={{ width: "50px", margin: "0 5px" }}
                            />
                            <IconButton
                              size="small"
                              onClick={() => {
                                const newValue = Math.min(
                                  item.cantidadpendiente,
                                  item.selectedQuantity + 1
                                );
                                updateCartQuantity(
                                  clientId,
                                  item.pedidoplaca_productoid,
                                  newValue
                                );
                              }}
                              style={{ padding: "2px" }}
                            >
                              <AddCircleOutline fontSize="small" />
                            </IconButton>
                          </div>
                        </div>
                      </div>
                      <IconButton
                        size="small"
                        onClick={() =>
                          removeFromCart(clientId, item.pedidoplaca_productoid)
                        }
                        style={{ color: "#f44336" }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </div>
                  ))}

                  <Divider style={{ margin: "15px 0" }} />
                </div>
              ))}

              <Button
                variant="contained"
                color="primary"
                fullWidth
                onClick={generateRemitos}
                disabled={generatingRemitos}
                style={{ marginTop: "20px" }}
              >
                {generatingRemitos ? (
                  <>
                    <CircularProgress
                      size={20}
                      color="inherit"
                      style={{ marginRight: "10px" }}
                    />
                    Generando...
                  </>
                ) : (
                  `Generar ${
                    Object.keys(cart).length > 1
                      ? Object.keys(cart).length + " Remitos"
                      : "Remito"
                  }`
                )}
              </Button>
            </>
          )}
        </Paper>
      )}
    </div>
  );
};
