import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from "@mui/material";

export const ModalFiltrosReporteCuentasCorrientes = ({
  show,
  handleClose,
  search,
  reset,
  fechaPedido,
  handleChangeFechaPedido,
}) => {
  return (
    <Dialog open={show} onClose={handleClose}>
      <DialogTitle sx={{ padding: '24px 24px 16px 24px' }}>Filtros</DialogTitle>
      <DialogContent sx={{ 
        padding: '24px', 
        minWidth: '400px',
        '& .MuiTextField-root': {
          '& .MuiOutlinedInput-root': {
            height: '45px'
          }
        }
      }}>
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          gap: '24px',
          padding: '8px 0'
        }}>
          {/* <TextField
            label="Pedido"
            type="text"
            value={codigo}
            onChange={handleChangeCodigo}
            fullWidth
            size="small"
            InputLabelProps={{
              sx: {
                display: 'flex',
                alignItems: 'center',
                transform: 'translate(14px, 14px)',
                '&.Mui-focused, &.MuiFormLabel-filled': {
                  transform: 'translate(14px, -9px) scale(0.75)'
                }
              }
            }}
          /> */}
          <TextField
            label="Fecha del pedido"
            type="date"
            value={fechaPedido}
            onChange={handleChangeFechaPedido}
            fullWidth
            size="small"
            InputLabelProps={{
              shrink: true,
              sx: {
                display: 'flex',
                alignItems: 'center',
                transform: 'translate(14px, -9px) scale(0.75)'
              }
            }}
          />
        </div>
      </DialogContent>
      <DialogActions sx={{ padding: '24px' }}>
        <Button onClick={reset}>Limpiar</Button>
        <Button onClick={search} variant="contained">
          Buscar
        </Button>
      </DialogActions>
    </Dialog>
  );
};
