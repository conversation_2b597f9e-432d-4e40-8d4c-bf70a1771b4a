// import { Paper } from "@material-ui/core"
// import React, { useEffect } from "react"
// import { useDispatch, useSelector } from "react-redux"
// import imagen from '../../../media/emprendimiento3.png'

// import "./homeMiTienda.css"
// import { ClipLoader } from "react-spinners"
// import { crearData, crearLabels, totalVentas } from "../ventas/utilsVentas"
// import moment from "moment/moment"
// import { getPedidosGrafico } from "../../../redux/actions/mitienda.actions"
// import { Line } from "react-chartjs-2"

// export const HomeMiTienda =()=>{

//     const datos = useSelector((state) => state.mitienda.datosHome)
//     const infoTienda = useSelector((state) => state.mitienda.nombreTienda)

//      const pedidos = useSelector((state) => state.mitienda.pedidosGrafico);
//   const dispatch = useDispatch();

//   const hasAdminProfile = () => {
//     const perfiles = localStorage.getItem("perfiles")
//       ? JSON.parse(localStorage.getItem("perfiles"))
//       : {};
//     return Object.values(perfiles).includes(7);
//   };

//   const options = {
//     responsive: true,
//     scales: {
//       y: {
//         ticks: {
//           callback: function(value) {
//             return '$' + value;
//           }
//         }
//       }
//     }
//   };

//   let labels = crearLabels(pedidos);
//   let data2 = crearData(pedidos, labels);
//   let info = totalVentas(pedidos);

//   const data = {
//     labels,
//     datasets: [
//       {
//         label: 'Ventas',
//         data: data2.map((p) => p),
//         borderColor: 'rgb(39, 158, 255)',
//         backgroundColor: 'rgb(120, 193, 243)',
//       },
//     ],
//   };

//   useEffect(() => {
//     const fechaActual = moment().toISOString().substring(0, 10);
//     const ultimosDias = moment().subtract(30, 'days').toISOString().substring(0, 10);

//     // Get vendedorid if not admin
//     const vendedorid = hasAdminProfile() ? '' : localStorage.getItem("idusuario");

//     dispatch(getPedidosGrafico(
//       '', '', '', '',
//       ultimosDias,
//       fechaActual,
//       '',
//       vendedorid  // Pass vendedorid to filter if needed
//     ));
//   }, []);

//     return (
//         <section className="container-home">
//             <header className="header-home">
//                 {/* <img
//                 className="img-home"
//                 src={imagen}
//                 alt=""/> */}
//                 <h2 className="h2-home">Hola, {infoTienda}! </h2>
//             </header>

// <div className="graphs-container">
//         <Paper className="graph-paper">
//           <h4>Ventas</h4>
//           {pedidos && pedidos.length > 0 ? (
//             <>
//               <Line options={options} data={data} />
//               <div className="graph-stats">
//                 <div className="stat-item">
//                   <h6>TOTAL EN VENTAS</h6>
//                   <h5>${info.total}</h5>
//                 </div>
//                 <div className="stat-item">
//                   <h6>CANTIDAD VENTAS</h6>
//                   <h5>{info.cantidad}</h5>
//                 </div>
//               </div>
//             </>
//           ) : (
//             <ClipLoader loading={true} size={30} />
//           )}
//         </Paper>
//       </div>

//             <div className="list-cards">
//                 <Paper className="paper-home">
//                     <h6 className="h6-home">VENTAS ACTIVAS</h6>
//                     {
//                         datos === '' ?
//                         <ClipLoader
//                             loading={true}
//                             size={30}
//                         /> :
//                         <h5>{datos.ventas_activas || ''}</h5>
//                     }
//                 </Paper>
//                 <Paper className="paper-home">
//                     <h6 className="h6-home">ENVIOS PENDIENTES</h6>
//                     {
//                         datos === '' ?
//                         <ClipLoader
//                             loading={true}
//                             size={30}
//                         /> :
//                         <h5>{datos.envios_pendientes  || ''}</h5>
//                     }
//                 </Paper>
//                 <Paper className="paper-home">
//                     <h6 className="h6-home">COBROS PENDIENTES</h6>
//                     {
//                         datos === '' ?
//                         <ClipLoader
//                             loading={true}
//                             size={30}
//                         /> :
//                         <h5>{datos.cobros_pendientes  || ''}</h5>
//                     }
//                 </Paper>
//                 <Paper className="paper-home">
//                     <h6 className="h6-home">CLIENTES</h6>
//                     {
//                         datos === '' ?
//                         <ClipLoader
//                             loading={true}
//                             size={30}
//                         /> :
//                         <h5>{datos.clientes  || ''}</h5>
//                     }
//                 </Paper>
//             </div>
//         </section>
//     )
// }

import { Paper } from "@material-ui/core";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import imagen from "../../../media/emprendimiento3.png";
import "./homeMiTienda.css";
import { ClipLoader } from "react-spinners";
import { crearData, crearLabels, totalVentas } from "../ventas/utilsVentas";
import moment from "moment/moment";
import {
  getPedidosGrafico,
  getLiquidacionesGrafico,
} from "../../../redux/actions/mitienda.actions";
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement } from 'chart.js';
import { Line, Bar } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement
);

export const HomeMiTienda = () => {
  const datos = useSelector((state) => state.mitienda.datosHome);
  const infoTienda = useSelector((state) => state.mitienda.nombreTienda);
//   console.log('infoTienda', infoTienda);
  const pedidos = useSelector((state) => state.mitienda.pedidosGrafico);
  const liquidaciones = useSelector(
    (state) => state.mitienda.liquidacionesGrafico
  );
  const dispatch = useDispatch();

  const hasAdminProfile = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    return Object.values(perfiles).includes(7);
  };

  const options = {
    responsive: true,
    scales: {
      y: {
        ticks: {
          callback: function (value) {
            return "$" + value;
          },
        },
      },
    },
  };

  // Ventas data
  let labelsVentas = crearLabels(pedidos);
  let dataVentas = crearData(pedidos, labelsVentas);
  let infoVentas = totalVentas(pedidos);

  // Liquidaciones data
  let labelsLiquidaciones = crearLabels(liquidaciones);
  let dataLiquidaciones = crearData(liquidaciones, labelsLiquidaciones);
  let infoLiquidaciones = totalVentas(liquidaciones);

  const ventasChartData = {
    labels: labelsVentas,
    datasets: [
      {
        label: "Ventas",
        data: dataVentas.map((p) => p),
        borderColor: "rgb(39, 158, 255)",
        backgroundColor: "rgb(120, 193, 243)",
      },
    ],
  };

  //w original with line
  // const liquidacionesChartData = {
  //     labels: labelsLiquidaciones,
  //     datasets: [
  //       {
  //         label: 'Liquidaciones',
  //         data: dataLiquidaciones.map((p) => p),
  //         borderColor: 'rgb(255, 99, 132)',
  //         backgroundColor: 'rgba(255, 99, 132, 0.5)',
  //       },
  //     ],
  // };

  //f with bars
  const liquidacionesChartData = {
    labels: labelsLiquidaciones,
    datasets: [
      {
        label: "Liquidaciones",
        data: dataLiquidaciones.map((p) => p),
        backgroundColor: "rgba(255, 99, 132, 0.7)",
        borderColor: "rgba(255, 99, 132, 1)",
        borderWidth: 1,
      },
    ],
  };

  const barOptions = {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return "$" + value;
          },
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  };

  useEffect(() => {
    const fechaActual = moment().toISOString().substring(0, 10);
    const ultimosDias = moment()
      .subtract(30, "days")
      .toISOString()
      .substring(0, 10);

    const vendedorid = hasAdminProfile()
      ? ""
      : localStorage.getItem("idusuario");

    dispatch(
      getPedidosGrafico(
        "",
        "",
        "",
        "",
        ultimosDias,
        fechaActual,
        "",
        vendedorid
      )
    );

    dispatch(getLiquidacionesGrafico(ultimosDias, fechaActual, vendedorid));
  }, []);

  return (
    <section className="container-home">
      <header className="header-home">
        <h2 className="h2-home">Hola, {infoTienda}! </h2>
      </header>

      <div className="graphs-container">
        <Paper className="graph-paper">
          <h4>Ventas</h4>
          <small className="text-muted">Últimos 30 días</small>
          {pedidos && pedidos.length > 0 ? (
            <>
              <Line options={options} data={ventasChartData} />
              <div className="graph-stats">
                <div className="stat-item">
                  <h6>TOTAL EN VENTAS</h6>
                  <h5>${infoVentas.total}</h5>
                </div>
                <div className="stat-item">
                  <h6>CANTIDAD VENTAS</h6>
                  <h5>{infoVentas.cantidad}</h5>
                </div>
              </div>
            </>
          ) : (
            <ClipLoader loading={true} size={30} />
          )}
        </Paper>
        {/* //woriginal with line graph */}
        {/* <Paper className="graph-paper">
                    <h4>Liquidaciones</h4>
                    {liquidaciones && liquidaciones.length > 0 ? (
                        <>
                            <Line options={options} data={liquidacionesChartData} />
                            <div className="graph-stats">
                                <div className="stat-item">
                                    <h6>TOTAL LIQUIDADO</h6>
                                    <h5>${infoLiquidaciones.total}</h5>
                                </div>
                                <div className="stat-item">
                                    <h6>CANTIDAD LIQUIDACIONES</h6>
                                    <h5>{infoLiquidaciones.cantidad}</h5>
                                </div>
                            </div>
                        </>
                    ) : (
                        <ClipLoader loading={true} size={30} />
                    )}
                </Paper> */} 

        <Paper className="graph-paper">
          <h4>Liquidaciones</h4>
          <small className="text-muted">Últimos 30 días</small>
          {liquidaciones && liquidaciones.length > 0 ? (
            <>
              <Bar options={barOptions} data={liquidacionesChartData} />
              <div className="graph-stats">
                <div className="stat-item">
                  <h6>TOTAL LIQUIDADO</h6>
                  <h5>${infoLiquidaciones.total}</h5>
                </div>
                <div className="stat-item">
                  <h6>CANTIDAD LIQUIDACIONES</h6>
                  <h5>{infoLiquidaciones.cantidad}</h5>
                </div>
              </div>
            </>
          ) : (
            <ClipLoader loading={true} size={30} />
          )}
        </Paper>
      </div>

      <div className="list-cards">
        <Paper className="paper-home">
          <h6 className="h6-home">VENTAS ACTIVAS</h6>
          {datos === "" ? (
            <ClipLoader loading={true} size={30} />
          ) : (
            <h5>{datos.ventas_activas || ""}</h5>
          )}
        </Paper>
        <Paper className="paper-home">
          <h6 className="h6-home">ENVIOS PENDIENTES</h6>
          {datos === "" ? (
            <ClipLoader loading={true} size={30} />
          ) : (
            <h5>{datos.envios_pendientes || ""}</h5>
          )}
        </Paper>
        <Paper className="paper-home">
          <h6 className="h6-home">COBROS PENDIENTES</h6>
          {datos === "" ? (
            <ClipLoader loading={true} size={30} />
          ) : (
            <h5>{datos.cobros_pendientes || ""}</h5>
          )}
        </Paper>
        <Paper className="paper-home">
          <h6 className="h6-home">CLIENTES</h6>
          {datos === "" ? (
            <ClipLoader loading={true} size={30} />
          ) : (
            <h5>{datos.clientes || ""}</h5>
          )}
        </Paper>
      </div>
    </section>
  );
};
