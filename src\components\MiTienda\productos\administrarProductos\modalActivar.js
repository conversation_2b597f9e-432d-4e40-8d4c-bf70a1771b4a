import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { desactivarProducto, limpiarProductoDesactivado } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalActivar = ({show, handleClose, id, activo, handleClickAlert}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const dispatch = useDispatch()

    const handleOnClick = (e) => {
        e.preventDefault()
        let aux 
        if(activo == "1"){
            aux = "0"
        }else{
            aux = "1"
        }
        dispatch(desactivarProducto(id, aux))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    }

    useEffect(() => {
        dispatch(limpiarProductoDesactivado())
    }, [])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>{activo == '1' ? 'Desactivar' : 'Activar'} producto</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>
                    {
                        activo == '1' ?
                        <div>
                            <h5>Al desactivar el producto dejar&aacute; de ser visible en la tienda hasta volver a activarlo. 
                                No se eliminar&aacute; ni borrar&aacute;n las configuraciones realizadas.
                            </h5>
                        </div> :
                        <div>
                        <h5>Al activar el producto ser&aacute; visible en la tienda. 
                        </h5>
                    </div>
                    }
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large" 
                        variant="contained" 
                        sx={{mt: 3}}
                        onClick={(e) => handleOnClick(e)}>{activo == '1' ? 'Desactivar' : 'Activar'}
                    </Button>
                </div>
        </Modal>
    )
}