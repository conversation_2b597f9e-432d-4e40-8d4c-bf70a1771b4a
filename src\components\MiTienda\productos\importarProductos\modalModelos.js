import React, { useEffect, useState } from "react"
import Modal from 'react-modal';
import Divider from '@mui/material/Divider';
import pdfProducto from './modelos/ejemploproducto.xlsx'
import pdfProveedores from './modelos/listadoproveedores.xlsx'
import pdfMarcas from './modelos/listadomarcas.xlsx'
import pdfEstilos from './modelos/listadoestilos.xlsx'
import pdfColores from './modelos/listadocolores.xlsx'
import pdfMedidas from './modelos/listadomedidas.xlsx'
import pdfCategorias from './modelos/listadocategorias.xlsx'
import pdfIva from './modelos/listadoivaproducto.xlsx'

Modal.setAppElement("#root")

export const ModalModelos = ({show, handleClose}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "850px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Importación de datos desde un archivo Excel</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{padding:10}}>
                    <p style={{fontSize:16}}>
                        El formato del archivo creado debe tener la siguiente estructura de datos:
                    </p>
                
                <div style={{padding:10}}> 

                    <p style={{fontSize:14}}>
                        <strong>Productos:</strong> Haga Click&nbsp;
                        <a
                        href={pdfProducto}
                        download="ejemploproducto.xlsx"
                        target="_blank"
                        rel="noreferrer"
                        >
                        Aquí&nbsp;
                        </a>
                        para bajar un ejemplo del archivo Excel.
                    </p>
                        <p style={{fontSize:14}}>
                        <strong>Proveedores:</strong> Haga Click&nbsp; 
                            <a
                            href={pdfProveedores}
                            download="ejemploproveedores.xlsx"
                            target="_blank"
                            rel="noreferrer"
                            >
                            Aquí&nbsp;
                            </a>
                            para bajar un ejemplo del archivo Excel.
                    </p>
                    <p style={{fontSize:14}}>
                        <strong>Marcas:</strong> Haga Click&nbsp; 
                            <a
                            href={pdfMarcas}
                            download="ejemplomarcas.xlsx"
                            target="_blank"
                            rel="noreferrer"
                            >
                            Aquí&nbsp;
                            </a>
                            para bajar un ejemplo del archivo Excel.
                    </p>
                    <p style={{fontSize:14}}>
                        <strong>Estilos:</strong> Haga Click&nbsp; 
                            <a
                            href={pdfEstilos}
                            download="ejemploestilos.xlsx"
                            target="_blank"
                            rel="noreferrer"
                            >
                            Aquí&nbsp;
                            </a>
                            para bajar un ejemplo del archivo Excel.
                    </p>
                    <p style={{fontSize:14}}>
                        <strong>Colores:</strong> Haga Click&nbsp; 
                            <a
                            href={pdfColores}
                            download="ejemplocolores.xlsx"
                            target="_blank"
                            rel="noreferrer"
                            >
                            Aquí&nbsp;
                            </a>
                            para bajar un ejemplo del archivo Excel.
                    </p>
                    <p style={{fontSize:14}}>
                        <strong>Medidas:</strong> Haga Click&nbsp; 
                            <a
                            href={pdfMedidas}
                            download="ejemplomedidas.xlsx"
                            target="_blank"
                            rel="noreferrer"
                            >
                            Aquí&nbsp;
                            </a>
                            para bajar un ejemplo del archivo Excel.
                    </p>
                    <p style={{fontSize:14}}>
                        <strong>Categor&iacute;as:</strong> Haga Click&nbsp; 
                            <a
                            href={pdfCategorias}
                            download="ejemplocategorias.xlsx"
                            target="_blank"
                            rel="noreferrer"
                            >
                            Aquí&nbsp;
                            </a>
                            para bajar un ejemplo del archivo Excel.
                    </p>
                    <p style={{fontSize:14}}>
                        <strong>IVA Producto</strong> Haga Click&nbsp; 
                            <a
                            href={pdfIva}
                            download="ejemploiva.xlsx"
                            target="_blank"
                            rel="noreferrer"
                            >
                            Aquí&nbsp;
                            </a>
                            para bajar un ejemplo del archivo Excel.
                    </p>

                    <p style={{fontSize:15, paddingTop:10}}>
                        Una vez confeccionado el archivo, cree una nueva lista de productos desde el ABM, 
                        adjunte el archivo creado, y luego haga click en el icono con la leyenda procesar. 
                        Esto actualizará los productos que contenga el archivo.
                    </p>
                    </div>
                </div>
        </Modal>
    )
}