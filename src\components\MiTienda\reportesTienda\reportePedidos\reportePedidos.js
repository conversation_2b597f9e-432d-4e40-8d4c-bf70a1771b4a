import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  FormControl,
  Select,
  MenuItem,
  ListSubheader,
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import { Search, Clear, KeyboardArrowDown, MoreHoriz } from "@mui/icons-material";
import ClipLoader from "react-spinners/ClipLoader";
import {
  getPedidosDetalle,
  getListadoClientesByName,
} from "../../../../redux/actions/mitienda.actions";
import { ModalFiltrosReportePedidos } from "./modalFiltrosReportePedidos";
import { formatDate } from "../../../shared/utility";

const TruncatedCell = ({ text }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) return <span>-</span>;

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center',
      justifyContent: 'center',
      gap: '4px'
    }}>
      <div style={{
        whiteSpace: isExpanded ? 'normal' : 'nowrap',
        overflow: isExpanded ? 'visible' : 'hidden',
        textOverflow: isExpanded ? 'unset' : 'ellipsis',
        maxWidth: '150px',
        fontSize: "80%"
      }}>
        {text}
      </div>
      {text.length > 20 && (
        <Tooltip title={isExpanded ? "Ver menos" : "Ver más"}>
          <IconButton 
            size="small" 
            onClick={() => setIsExpanded(!isExpanded)}
            style={{ padding: 0 }}
          >
            <MoreHoriz fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
    </div>
  );
};

export const ReportePedidos = () => {
  const dispatch = useDispatch();
  const [page, setPage] = useState(1);
  const ITEMS_PER_PAGE = 10;
  const [show, setShow] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const [clienteInput, setClienteInput] = useState("");
  const [clienteid, setClienteid] = useState("0");
  const [selectedOption, setSelectedOption] = useState({
    nombre: "",
    apellido: "",
  });

  const [facturaId, setFacturaId] = useState("0");

  // Filter states
  const [fechaDesde, setFechaDesde] = useState(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth() - 12, 1)
      .toISOString()
      .split("T")[0];
  });
  const [fechaHasta, setFechaHasta] = useState(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth() + 1, 0)
      .toISOString()
      .split("T")[0];
  });

  const {
    data: pedidosDetalle,
    loading,
    paginaUltima,
  } = useSelector((state) => state.mitienda.pedidosDetalle);

  const clientes = useSelector((state) => state.mitienda.clientesByName);

  useEffect(() => {
    setIsLoading(true);
    dispatch(
      getPedidosDetalle(
        fechaDesde,
        fechaHasta,
        page,
        ITEMS_PER_PAGE,
        clienteid,
        facturaId
      )
    ).finally(() => setIsLoading(false));
  }, [dispatch, page, clienteid, facturaId]);

  useEffect(() => {
    if (clienteInput.length > 1) {
      dispatch(getListadoClientesByName(clienteInput, ""));
    }
  }, [clienteInput]);

  const handleClose = () => setShow(false);

  const handleChangeFecha_desde = (e) => {
    setFechaDesde(e.target.value);
  };

  const handleChangeFecha_hasta = (e) => {
    setFechaHasta(e.target.value);
  };

  const searchByFilters = () => {
    handleClose();
    setIsLoading(true);
    setPage(1);
    dispatch(
      getPedidosDetalle(
        fechaDesde,
        fechaHasta,
        1,
        ITEMS_PER_PAGE,
        clienteid,
        facturaId
      )
    ).finally(() => {
      setIsLoading(false);
    });
  };

  const reset = () => {
    handleClose();
    setIsLoading(true);
    const today = new Date();
    const initialFechaDesde = new Date(
      today.getFullYear(),
      today.getMonth() - 12,
      1
    )
      .toISOString()
      .split("T")[0];
    const initialFechaHasta = new Date(
      today.getFullYear(),
      today.getMonth() + 1,
      0
    )
      .toISOString()
      .split("T")[0];

    setFechaDesde(initialFechaDesde);
    setFechaHasta(initialFechaHasta);
    setPage(1);
    setClienteid("");
    setClienteInput("");
    setSelectedOption({ nombre: "", apellido: "" });
    setFacturaId("");

    dispatch(
      getPedidosDetalle(
        initialFechaDesde,
        initialFechaHasta,
        1,
        ITEMS_PER_PAGE,
        "",
        ""
      )
    ).finally(() => {
      setIsLoading(false);
    });
  };

  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const handleFacturaSearch = (e) => {
    setFacturaId(e.target.value);
    setPage(1);
  };

  const clearClienteSearch = () => {
    setIsLoading(true);
    setClienteid(0);
    setSelectedOption("");
    setClienteInput("");
    dispatch(
      getPedidosDetalle(fechaDesde, fechaHasta, page, ITEMS_PER_PAGE)
    ).finally(() => setIsLoading(false));
  };

  const clearFacturaSearch = () => {
    setIsLoading(true);
    setFacturaId("");
    dispatch(
      getPedidosDetalle(fechaDesde, fechaHasta, page, ITEMS_PER_PAGE)
    ).finally(() => setIsLoading(false));
  };

  const showLoading = isLoading || loading;

  return (
    <div className="notificaciones-container">
      <div style={{ padding: "20px" }}>
        <h3>Reporte de Pedidos</h3>
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            alignItems: "center",
          }}
        >
          <Button
            variant="outlined"
            onClick={() => setShow(true)}
            startIcon={<FilterListIcon />}
          >
            Filtros
          </Button>

          <FormControl sx={{ minWidth: 300 }}>
            <Select
              size="small"
              value={clienteid}
              onChange={(e) => {
                setClienteid(e.target.value);
                setPage(1);
              }}
              displayEmpty
              renderValue={() =>
                clienteid
                  ? `${selectedOption.nombre} ${selectedOption.apellido}`
                  : "Buscar cliente..."
              }
              onOpen={() => {
                setClienteInput(""); // Clear input when opening dropdown
              }}
              IconComponent={() => (
                <div style={{ display: "flex", alignItems: "center" }}>
                  {clienteid !== 0 && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearClienteSearch();
                      }}
                      sx={{ marginRight: 1 }}
                    >
                      <Clear />
                    </IconButton>
                  )}
                  <KeyboardArrowDown />
                </div>
              )}
            >
              <ListSubheader>
                <TextField
                  size="small"
                  autoFocus
                  placeholder="Buscar cliente..."
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  inputProps={{
                    style: {
                      height: 35,
                      fontSize: 17,
                    },
                  }}
                  onChange={(e) => setClienteInput(e.target.value)}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  onKeyDown={(e) => {
                    e.stopPropagation();
                  }}
                  value={clienteInput}
                />
              </ListSubheader>
              {clientes &&
                clientes.map((option) => (
                  <MenuItem
                    key={option.clienteid}
                    value={option.clienteid}
                    onClick={() => {
                      setSelectedOption(option);
                      setClienteInput("");
                    }}
                  >
                    {option.nombre} {option.apellido}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <TextField
            size="small"
            placeholder="Buscar por Nº Pedido..."
            value={facturaId}
            onChange={handleFacturaSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {facturaId && (
                    <IconButton size="small" onClick={clearFacturaSearch}>
                      <Clear />
                    </IconButton>
                  )}
                </InputAdornment>
              ),
              style: {
                height: "40px",
              },
            }}
            sx={{
              minWidth: 200,
              "& .MuiOutlinedInput-root": {
                padding: "0 8px",
              },
            }}
          />
        </div>
      </div>

      <ModalFiltrosReportePedidos
        show={show}
        handleClose={handleClose}
        search={searchByFilters}
        reset={reset}
        fecha_desde={fechaDesde}
        handleChangeFecha_desde={handleChangeFecha_desde}
        fecha_hasta={fechaHasta}
        handleChangeFecha_hasta={handleChangeFecha_hasta}
      />

      <TableContainer
        component={Paper}
        sx={{
          marginTop: 5,
          width: "90%",
          alignSelf: "center",
          margin: "40px auto",
          marginBottom: 5,
          padding: "0 20px",
        }}
      >
        <Table aria-label="pedidos table">
          <TableHead>
            <TableRow>
              {[
                "Fecha Contab.",
                "Fecha Entrega",
                "Nro Pedido",
                // "Nro de Cliente",
                // "Nombre SN",
                "Artículo",
                "Descripción",
                "Cant. Pedido (kg)",
                "Cant. Entregada (kg)",
                "Pendiente (kg)",
                "Almacén",
                "Precio",
                "Total(moneda)",
                "Total",
              ].map((header) => (
                <TableCell
                  key={header}
                  style={{
                    fontWeight: "bold",
                    fontSize: "90%",
                    padding: "8px 2px",
                  }}
                  align="center"
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {showLoading ? (
              <TableRow>
                <TableCell colSpan={14} align="center">
                  <ClipLoader loading={true} size={50} />
                </TableCell>
              </TableRow>
            ) : pedidosDetalle && Object.keys(pedidosDetalle).length > 0 ? (
              Object.values(pedidosDetalle).map((item) => (
                <TableRow
                  key={item.pedidoplaca_productoid}
                  sx={{
                    "&:hover": { backgroundColor: "#f5f5f5" },
                    "&:last-child td, &:last-child th": { border: 0 },
                    minHeight: "45px",
                  }}
                >
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {/* {new Date(item.fechapedido).toLocaleDateString()} */}
                    {formatDate(item.fechapedido)}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 3px" }}
                    align="center"
                  >
                    {/* {item.fechaentrega
                      ? new Date(item.fechaentrega).toLocaleDateString()
                      : "--"} */}
                    {item.fechaentrega ? formatDate(item.fechaentrega) : "--"}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.pedidoplacaid}
                  </TableCell>
                  {/* <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.clienteid}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.nombrecliente}
                  </TableCell> */}
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.codigoarticulo}
                  </TableCell>
                  <TableCell
                    style={{ 
                      fontSize: "80%", 
                      padding: "6px 8px",  // Changed from "6px 2px" to "6px 8px" to add more horizontal padding
                    }}
                    align="center"
                  >
                    <TruncatedCell text={item.nombreproducto} />
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidad}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidadentregada}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidadpendiente}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.almacen}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {formatCurrency(item.precioventa)}
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "80%",
                      padding: "6px 2px",
                    }}
                    align="center"
                  >
                     {item.nombremoneda}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {formatCurrency(item.totalventa)}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={14} align="center">
                  <h5>No hay información para mostrar</h5>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {paginaUltima > 1 && (
        <Pagination
          count={paginaUltima}
          page={page}
          onChange={handleChangePage}
          size="large"
          sx={{
            alignSelf: "center",
            marginBottom: 3,
            display: "flex",
            justifyContent: "center",
          }}
        />
      )}
    </div>
  );
};
