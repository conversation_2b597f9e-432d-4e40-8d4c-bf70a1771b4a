.form-productos{
    background-color: white;
    height: auto;
    width: 95%;
    padding: 20px;
    align-self: left;
    border-radius:10px;
    align-items: left;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-left: 20px;
}

.header-producto{
    margin-left: 20px;
    margin-top: -20px;
}

.container-productos{
    width: 100%;
    min-height: 110vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.precios{
    background-color: white;
    height: auto;
    width: 95%;
    padding: 20px;
    align-self: left;
    border-radius:10px;
    align-items: left;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-left: 20px;
}

.atributos{
    background-color: white;
    height: auto;
    width: 95%;
    padding: 20px;
    align-self: left;
    border-radius:10px;
    align-items: left;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-left: 20px;
}

.dimensiones{
    background-color: white;
    height: auto;
    width: 95%;
    padding: 20px;
    align-self: left;
    border-radius:10px;
    align-items: left;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-left: 20px;
}

.otros{
    background-color: white;
    height: auto;
    width: 95%;
    padding: 20px;
    align-self: left;
    border-radius:10px;
    align-items: left;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-left: 20px;
}

.imagen{
    width: 200px;
    height: 100px;
    margin:7px;
    cursor: pointer;
    display: flex;

}

.image{
    width: 200px;
    height: 100px;
}

@media only screen and (max-width: 2000px) {
    .container-productos{
        padding: 40px;
        width: 100%;
        min-height: 110vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }
}