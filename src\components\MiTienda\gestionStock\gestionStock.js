import React, { useEffect, useState } from 'react';
import { Button, Snackbar, TextField } from '@mui/material';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { 
    aumentarStock, 
    getProductosStock, 
    restarStock, 
    restarStockReservado 
} from '../../../redux/actions/mitienda.actions'
import { ModalSubirArchivo } from './modalSubirArchivo';
import { ModalModelosStock } from './modalModelos';
import MuiAlert from '@mui/material/Alert';
import ClipLoader from "react-spinners/ClipLoader";
import { ModalFiltros } from './modalFiltros';
import { FilterList } from '@mui/icons-material';

export const GestionStock = () => {

    //Obtengo la informacion para la tabla
    const info = useSelector((state) => state.mitienda.productosStock)
    const okAumentarStock = useSelector((state) => state.mitienda.okAumentarStock)
    const okRestarStock = useSelector((state) => state.mitienda.okRestarStock)
    const okRestarStockReservado = useSelector((state) => state.mitienda.okRestarStockReservado)
    const okSubirArchivoImportaStock = useSelector((state) => state.mitienda.okSubirArchivoImportaStock)
    

    //Obtengo la ultima pagina para el componente Pagination
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaProductosStock)
    //Creo la constante que usare para cambiar las paginas y su handle change
    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    //Declaro la variable para despachar acciones
    const dispatch = useDispatch()

    const [cantidad, setCantidad] = useState(0)
    const [blockButton, setBlockButton] = useState(false)
    const [blockButtonSumar, setBlockButtonSumar] = useState(false)
    const [blockButtonReservado, setBlockButtonReservado] = useState(false)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const handleAumentarStock = (productoid) =>{
        dispatch(aumentarStock(productoid,cantidad))
        setCantidad(0)
    }

    const handleRestarStock = (productoid) =>{
        dispatch(restarStock(productoid,cantidad))
        setCantidad(0)
    }

    const handleRestarStockReservado = (productoid) =>{
        dispatch(restarStockReservado(productoid,cantidad))
        setCantidad(0)
    }

    const [codigoArticulo, setCodigoArticulo] = useState('')
    const handleCodigoArticulo = (e) =>{
        setCodigoArticulo(e.target.value)
    }

    const [nombre, setNombre] = useState('')
    const handleChangeNombre = (e) =>{
        setNombre(e.target.value)
    }

    const reset = (e) =>{
        dispatch(getProductosStock(page,'',''))
        setCodigoArticulo('')
        setNombre('')
    }

    const searchByFilters = () =>{
        setPage(1)
        dispatch(getProductosStock(page,nombre,codigoArticulo))
        handleCloseFiltros()
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openValidaNumero, setOpenValidaNumero] = React.useState(false);
    const handleClickAlertValidaNumero = () => {
      setOpenValidaNumero(true);
    };
    const handleCloseAlertValidaNumero = () => {
        setOpenValidaNumero(false);
    };

    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = (row) => {
        setShow(true);
    }

    const [showModelos, setShowModelos] = useState(false);
    const handleCloseModelos = () => setShowModelos(false);
    const handleShowModelos = () => {
        setShowModelos(true);
    }
    const [inputFields, setInputs] = useState([])

    const [showFiltros, setShowFiltros] = useState();
    const handleCloseFiltros = () => {
        setShowFiltros(false)
    };
    const handleShowFiltros = () => setShowFiltros(true);

    var tiendausuario = localStorage.getItem('tiendausuario')
    var api_key = localStorage.getItem('api_key')

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        if(info.data.length > 0){
            let inputs = info.data.map((m) => {return {suma: "0",  resta: "0", reservado: "0"}})
            setInputs(inputs)
        }
    },[info])

    useEffect(() =>{
        dispatch(getProductosStock(page,nombre,codigoArticulo))
    },[okAumentarStock, okRestarStock, okRestarStockReservado, okSubirArchivoImportaStock])

    useEffect(() =>{
        setLoading(true)
        dispatch(getProductosStock(page,nombre,codigoArticulo))
    },[page])

    return (
        <div className="notificaciones-container">
            <ModalSubirArchivo
                show={show}
                handleClose={handleClose}
                handleClickAlert={handleClickAlert}
            />
            <ModalModelosStock
                show={showModelos}
                handleClose={handleCloseModelos}
            />
            <ModalFiltros
                show={showFiltros} 
                handleClose={handleCloseFiltros}
                codigo={codigoArticulo}
                handleChangeCodigo={handleCodigoArticulo}    
                nombre={nombre}
                handleChangeNombre={handleChangeNombre}
                search={searchByFilters}
                reset={reset}
            />
            <Snackbar
                open={openValidaNumero} 
                autoHideDuration={10000} onClose={handleCloseAlertValidaNumero}
                anchorOrigin={
                    {vertical: 'bottom',
                    horizontal: 'right',}
                }>
                <Alert onClose={handleCloseAlertValidaNumero} 
                    severity="error"
                    sx={{ width: 400 }}>
                    Debe ingresar un n&uacute;mero.
                </Alert>
            </Snackbar>
            <Snackbar
                open={open} 
                autoHideDuration={10000} onClose={handleCloseAlert}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlert} 
                    severity={okSubirArchivoImportaStock.success ? "success" : "error"}
                    sx={{ width: 400 }}>
                    {okSubirArchivoImportaStock.success ? <h5>Archivo subido con exito!</h5> : 
                        <h5>Hubo un error al subir el archivo</h5>
                    }
                </Alert>
            </Snackbar>
            <div>
                <header className="titulo-notificaciones">
                    <h3>Gesti&oacute;n stock</h3>
                </header>

                <div align="right" style={{marginRight:20}}>
                    <Button 
                        variant="contained"
                        onClick={() => handleShowModelos()} 
                        style={{width:170, marginLeft:50}} 
                    >Importar datos</Button>   
                    <Button 
                        variant="contained"
                        onClick={() => handleShow()} 
                        disabled={permisos_acciones?.agregar === "0"}
                        style={{width:170, marginLeft:50}} 
                    >Subir archivo</Button>  
                    <a
                        target="_blank"
                        rel="noreferrer"
                        href={`${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGestionStock&tiendaid=${tiendausuario}&api_key=${api_key}&nombre=&codigoarticulo=&paginaResultado=&paginaActual=&exportar_excel=1`}
                    >
                    <Button 
                        variant="contained"
                        style={{width:170, marginLeft:50}} 
                    >Exportar</Button>  
                    </a>
                </div>

            </div>
            <Button 
                variant="outlined" 
                sx={{height:40,width:150, marginLeft:6}}
                onClick={handleShowFiltros}>
                <FilterList/> Filtrar
            </Button>
            <div style={{display:"flex", flexDirection:"column", width:"95%", marginTop:5, marginLeft:50}}>
                <TableContainer component={Paper} sx={{margin:5,width:"100%", alignSelf:"center"}}>
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                C&oacute;digo
                            </TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                Nombre
                            </TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                Almacen
                            </TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                Disponible
                            </TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                Reservado
                            </TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                Agregar stock
                            </TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                Quitar stock
                            </TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:0}} align="center">
                                Quitar stock reservado
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                    {
                        loading ? 
                        <TableRow sx={{p:20}}>
                            <TableCell colSpan={7} align='center'>
                                <ClipLoader 
                                    loading={loading}
                                    size={50}
                                />
                            </TableCell>
                        </TableRow> :
                        info.data.length > 0 ? info.data.map((row,index) => (
                        <TableRow
                            key={row.productoid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                            // style={{backgroundColor: row.colorfila || 'white'}}
                        >
                            <TableCell style={{fontSize:"80%", padding:0, height:40, width:100}} align="center">
                                {row.codigoarticulo}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:140}} align="center">
                                {row.nombre}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                {row.almacen}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                {row.cantidadactiva}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                {row.cantidadpasiva}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:70}} align="center">
                                <div style={{display:"flex", justifyContent:"center", alignItems:"center", width:"100%"}}>
                                    <TextField
                                        value={inputFields[index]?.suma || ''}
                                        onChange={(e) => {
                                            if(isNaN(e.target.value)) {
                                                handleClickAlertValidaNumero()
                                                setBlockButtonSumar(true)
                                            }else{
                                                setCantidad(e.target.value)
                                                inputFields[index].suma = e.target.value
                                                setInputs(inputFields)
                                            }
                                            
                                        }}
                                        style={{width:60}}
                                    />
                                    <button style={{
                                        margin:2, 
                                        width:40,
                                        height:30,
                                        backgroundColor:"#1565c0", 
                                        border:"none", 
                                        color:"white", 
                                        borderRadius:4,
                                        fontSize:20
                                    }}
                                    disabled={blockButtonSumar === true || permisos_acciones?.modificar === "0"}
                                    onClick={()=>handleAumentarStock(row.productoid)}
                                    >
                                        +
                                    </button>
                                </div>
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:60}} align="center">
                                <div style={{display:"flex", justifyContent:"center", alignItems:"center", width:"100%"}}>
                                    <TextField
                                        value={inputFields[index]?.resta || ''}
                                        onChange={(e) => {
                                            if(isNaN(e.target.value)) {
                                                handleClickAlertValidaNumero()
                                                setBlockButton(true)
                                            }else{
                                                if(parseInt(e.target.value) > parseInt(row.cantidadactiva)){
                                                    setBlockButton(true)
                                                }else{
                                                    setBlockButton(false)
                                                }
                                            }
                                            setCantidad(e.target.value)
                                            inputFields[index].resta = e.target.value
                                            setInputs(inputFields)
                                        }}
                                        style={{width:60}}
                                    />
                                    <button style={{
                                        margin:2, 
                                        width:40,
                                        height:30,
                                        backgroundColor:"red", 
                                        border:"none", 
                                        color:"white", 
                                        borderRadius:4,
                                        fontSize:20
                                    }}
                                    disabled={blockButton === true || permisos_acciones?.modificar === "0"}
                                    onClick={()=>handleRestarStock(row.productoid)}
                                    >
                                        -
                                    </button>
                                </div>
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:70}} align="center">
                                <div style={{display:"flex", justifyContent:"center", alignItems:"center"}}>
                                    <TextField
                                        value={inputFields[index]?.reservado || ''}
                                        onChange={(e) => {
                                            if(isNaN(e.target.value)) {
                                                handleClickAlertValidaNumero()
                                                setBlockButtonReservado(true)
                                            }else{
                                                if(parseInt(e.target.value) > parseInt(row.cantidadpasiva)){
                                                    setBlockButtonReservado(true)
                                                }else{
                                                    setBlockButtonReservado(false)
                                                }
                                            }
                                            setCantidad(e.target.value)
                                            inputFields[index].reservado = e.target.value
                                            setInputs(inputFields)
                                        }}
                                        style={{width:60}}
                                        disabled={row.cantidadpasiva === "0"}
                                    />
                                    <button style={{
                                        margin:2, 
                                        width:40,
                                        height:30,
                                        backgroundColor:"red", 
                                        border:"none", 
                                        color:"white", 
                                        borderRadius:4,
                                        fontSize:20
                                    }}
                                    onClick={()=>handleRestarStockReservado(row.productoid)}
                                    disabled={blockButtonReservado || permisos_acciones?.modificar === "0"}
                                    >
                                        -
                                    </button>
                                </div>
                            </TableCell>
                        </TableRow>
                        )):
                        <TableRow>
                            <TableCell colSpan={1}></TableCell>
                            <TableCell colSpan={7} align="center">
                                <h5>No hay informaci&oacute;n para mostrar</h5>
                            </TableCell>
                            <TableCell colSpan={1}></TableCell>
                        </TableRow>}
                    </TableBody>
                    </Table>
                </TableContainer>
            </div>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}
