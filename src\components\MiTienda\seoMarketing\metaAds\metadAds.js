import React from "react";
import { InputAdornment, Paper, Snackbar, TextField } from "@mui/material"
import { ContentCopy } from "@mui/icons-material";
import MuiAlert from '@mui/material/Alert';
import logo from '../../../../media/meta-logo.png'
import './metaAds.css'

export const MetaAds =()=>{

    let tiendausuario = localStorage.getItem('tiendausuario')
    let api_key = localStorage.getItem('api_key')

    const url = process.env.REACT_APP_SERVER+'php/GestionFacebook.php?type=SyncCatalogo&OI_tiendaid='+tiendausuario+'&OV_api_key='+api_key

    const copyText = () => {
        navigator.clipboard.writeText(url)
        handleClickAlert()
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);

    const handleClickAlert = () => {
      setOpen(true);
    };
  
    const handleCloseAlert = () => {
        setOpen(false);
    };

    return (
        <section className="container-meta-ads">
            {
            <Snackbar
                open={open} 
                autoHideDuration={10000} onClose={handleCloseAlert}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlert} 
                    severity="success"
                    sx={{ width: 400 }}>
                    <h5>Link copiado al portapapeles!</h5>
                </Alert>
            </Snackbar>
            }
            <div className="container-form">
                <header className="header-meta-ads">
                    <h3 style={{marginTop:20, marginBottom:20, marginLeft:-15}}><img src={logo} style={{width:"60px"}}/>Meta Ads</h3>
                    <div>
                        <h4>Meta Ads te permite configurar pagos en Instagram y Facebook desde el administrador
                            de anuncios de Facebook.
                        </h4>
                        <h4>
                            Con la creaci&oacute;n de un cat&aacute;logo de productos en Meta Ads, podr&aacute;s configurar anuncios din&aacute;micos que aumentan las
                            ventas y conversiones de tu negocio.
                        </h4>
                        <h4>
                            Podr&aacute;s crear f&aacute;cilmente tu cat&aacute;logo de productos en Facebook y vincularlo con los productos de tu tienda utilizando
                            el link proporcionado.
                        </h4>
                    </div>
                </header>
                <Paper className="form-meta-ads">
                    <TextField 
                        label="Link para tu catalogo de productos"  
                        fullWidth margin="normal" 
                        value={url}
                        InputProps={{
                            endAdornment:<InputAdornment position="start">
                            <div onClick={copyText} style={{cursor:"pointer"}}><ContentCopy/></div>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 70,
                            fontSize:17
                            },
                        }}
                    />
                </Paper>
            </div>
        </section>
    )
}