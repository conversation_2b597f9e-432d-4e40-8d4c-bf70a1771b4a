import React from "react";
import { InputAdornment, Paper, Snackbar, TextField } from "@mui/material"
import { ContentCopy } from "@mui/icons-material";
import MuiAlert from '@mui/material/Alert';

export const GoogleShopping =()=>{

    let tiendausuario = localStorage.getItem('tiendausuario')
    let api_key = localStorage.getItem('api_key')

    const url = process.env.REACT_APP_SERVER+'php/GestionGoogle.php?type=SyncCatalogo&OI_tiendaid='+tiendausuario+'&OV_api_key='+api_key

    const copyText = () => {
        navigator.clipboard.writeText(url)
        handleClickAlert()
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);

    const handleClickAlert = () => {
      setOpen(true);
    };
  
    const handleCloseAlert = () => {
        setOpen(false);
    };

    return (
        <section className="container-meta-ads">
            {
            <Snackbar
                open={open} 
                autoHideDuration={10000} onClose={handleCloseAlert}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlert} 
                    severity="success"
                    sx={{ width: 400 }}>
                    <h5>Link copiado al portapapeles!</h5>
                </Alert>
            </Snackbar>
            }
            <div className="container-form">
                <header className="header-meta-ads">
                    <h3 style={{marginTop:20, marginBottom:20, display:"flex"}}>
                        <h3 style={{color:"blue"}}>G</h3><h3 style={{color:"red"}}>o</h3><h3 style={{color:"yellow"}}>o</h3><h3 style={{color:"blue"}}>g</h3><h3 style={{color:"green"}}>l</h3><h3 style={{color:"red"}}>e</h3>&nbsp;Shopping
                    </h3>
                    <div>
                        <h4>Es una secci&oacute;n de Google que agrupa los productos de las marcas que utilizan esta funci&oacute;n.
                        </h4>
                        <h4>
                            Al buscar un producto en Google aparecen los resultados de Google Shopping permitiendo que
                            los usuarios encuentren tu producto e ingresen a tu web para comprarlo.
                        </h4>
                    </div>
                </header>
                <Paper className="form-meta-ads">
                    <TextField 
                        label="Link para tu catalogo de productos"  
                        fullWidth margin="normal" 
                        value={url}
                        InputProps={{
                            endAdornment:<InputAdornment position="start">
                            <div onClick={copyText} style={{cursor:"pointer"}}><ContentCopy/></div>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 70,
                            fontSize:17
                            },
                        }}
                    />
                </Paper>
            </div>
        </section>
    )
}