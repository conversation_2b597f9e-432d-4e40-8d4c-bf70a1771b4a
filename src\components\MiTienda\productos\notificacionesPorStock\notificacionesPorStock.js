import React from 'react';
import { Button } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import './notificacionesPorStock.css'
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import Pagination from '@mui/material/Pagination';
import { useState } from 'react';
import {getStock} from '../../../../redux/actions/mitienda.actions'
import { ModalNotificaciones } from './modalConfiguracion';
import { ClipLoader } from 'react-spinners';

export const Notificaciones = () => {

    const stock = useSelector((state) => state.mitienda.stock)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaStock)

    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    const [marcaid, setMarcaid] = useState(0)
    const handleChangeMarcaid = (e) =>{
        setMarcaid(e.target.value)
    }
    
    const [categoriaid, setCategoriaid] = useState(0)
    const handleChangeCategoriaid = (e) =>{
        setCategoriaid(e.target.value)
    }

    const [tallaid, setTallaid] = useState(0)
    const handleChangeTallaid = (e) =>{
        setTallaid(e.target.value)
    }

    const [colorid, setColorid] = useState(0)
    const handleChangeColorid = (e) =>{
        setColorid(e.target.value)
    }

    const [cantidadDisponible, setCantidadDisponible] = useState("0")
    const handleChangeCantidadDisponible = (e) =>{
        if(e.target.checked === true){
            setCantidadDisponible("1")
        }else{
            setCantidadDisponible("0")
        }
    }

    const [cantidadReservada, setCantidadReservada] = useState("0")
    const handleChangeCantidadReservada = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setCantidadReservada("1")
        }else{
            setCantidadReservada("0")
        }
    }

    const [codigoArticulo, setCodigoArticulo] = useState('')
    const handleCodigoArticulo = (e) =>{
        setCodigoArticulo(e.target.value)
    }

    const [nombre, setNombre] = useState('')
    const handleChangeNombre = (e) =>{
        setNombre(e.target.value)
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const reset = (e) =>{
        dispatch(getStock('','','','','','','','',1))
        setCategoriaid(0)
        setMarcaid(0)
        setCodigoArticulo(0)
        setTallaid(0)
        setCodigoArticulo('')
        setNombre('')
        setCantidadDisponible("0")
        setCantidadReservada("0")
    }

    const searchByFilters = () =>{
        setPage(1)
        dispatch(getStock(marcaid,categoriaid,tallaid,colorid,nombre,codigoArticulo,cantidadDisponible,cantidadReservada,page))
        handleClose()
    }

    const dispatch = useDispatch()

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[stock])

    useEffect(() =>{
        setLoading(true)
        dispatch(getStock(marcaid,categoriaid,tallaid,colorid,nombre,codigoArticulo,cantidadDisponible,cantidadReservada,page))
    },[page]) 

    return (
        <div className="notificaciones-container">
            {
                <ModalNotificaciones 
                    show={show} 
                    handleClose={handleClose}
                    setMarca={setMarcaid}
                    setCategoria={setCategoriaid}
                    setColor={setColorid}
                    setTalla={setTallaid}
                    codigo={codigoArticulo}
                    handleChangeCodigo={handleCodigoArticulo}    
                    nombre={nombre}
                    handleChangeNombre={handleChangeNombre}
                    reservada={cantidadReservada}
                    handleCantidadReservada={handleChangeCantidadReservada}
                    disponible={cantidadDisponible}
                    handleCantidadDisponible={handleChangeCantidadDisponible}
                    search={searchByFilters}
                    reset={reset}
                />
            }
            <div>
                <header className="titulo-notificaciones">
                    <h3>Notificaciones por stock</h3>
                    <Button 
                        variant="outlined" 
                        sx={{height:40,width:150}}
                        onClick={handleShow}>
                        <FilterListIcon/> Filtrar
                    </Button>
                </header>
            </div>
            <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">C&oacute;digo producto</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Cantidad disponible</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Cantidad reservada</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Marca</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Categor&iacute;a</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Nombre art&iacute;culo</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Talla</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Color</TableCell>
        
                        </TableRow>
                    </TableHead>
                    <TableBody>
                    {
                        loading ? 
                        <TableRow sx={{p:20}}>
                            <TableCell colSpan={13} align='center'>
                                <ClipLoader 
                                    loading={loading}
                                    size={50}
                                />
                            </TableCell>
                        </TableRow> :
                        stock.length > 0 ? stock.map((row) => (
                        <TableRow
                            key={row.facturaid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                            <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                                {row.codigoarticulo}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:100}} align="center">{row.cantidadactiva}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.cantidadpasiva}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombremarca}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">{row.nombrecategoria}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:300, overflow:"hidden"}} align="center">{row.nombreproducto}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombremedida}</TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nombrecolor}</TableCell>
                        </TableRow> 
                        )):
                        <TableRow>
                            <TableCell colSpan={2}></TableCell>
                            <TableCell colSpan={5}>
                                <h5>No hay informaci&oacute;n para mostrar</h5>
                            </TableCell>
                            <TableCell colSpan={2}></TableCell>
                        </TableRow>}
                    </TableBody>
                    </Table>
                </TableContainer>
            </div>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}