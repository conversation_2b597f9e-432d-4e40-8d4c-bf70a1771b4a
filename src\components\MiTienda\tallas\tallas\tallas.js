import React, { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux";

import Button from "@mui/material/Button";
import { Paper, Snackbar, Tooltip } from "@mui/material";
import AppsIcon from '@mui/icons-material/Apps';
import EditIcon from '@mui/icons-material/Edit';
import Pagination from '@mui/material/Pagination';

import { getTallasPaginado, limpiarAgregarMarcaTienda, limpiarEditarMarcaTienda } from "../../../../redux/actions/mitienda.actions";
import { Check, Clear, Delete } from "@mui/icons-material";
import { ModalEliminarTalla } from "./modalEliminarTalla";
import MuiAlert from '@mui/material/Alert';
import { AgregarTalla } from "./agregarTalla";
import { EditarTalla } from "./editarTalla";
import { ClipLoader } from "react-spinners";

export const Tallas = () =>{

    const info = useSelector((state) => state.mitienda.tallas_paginado)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaTallas)
    const okAgregar = useSelector((state) => state.mitienda.okConfigTalla)
    const okEliminar = useSelector((state) => state.mitienda.eliminarTalla)
    const okEditar = useSelector((state) => state.mitienda.editarTalla)

    const [datos, setDatos] = useState('');

    const dispatch = useDispatch()

    const [show, setShow] = useState();
    const handleClose = () => {
        dispatch(limpiarAgregarMarcaTienda())
        setShow(false)
    };
    const handleShow = () => setShow(true);

    const [showEditar, setShowEditar] = useState();
    const handleCloseEditar = () => {
        setShowEditar(false);
        dispatch(limpiarEditarMarcaTienda())
    }
    const handleShowEditar = (e,a) => {
        setDatos(a)
        setShowEditar(true);
    }

    const [showEliminar, setShowEliminar] = useState();
    const handleCloseEliminar = () => setShowEliminar(false);
    const handleShowEliminar = (e,a) => {
        setDatos(a)
        setShowEliminar(true);
    };

    const [page, setPage] = useState(1);
    const handleChangePage = (e,value) => {
        e.preventDefault()
        setPage(value);
    };

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [openAgregar, setOpenAgregar] = React.useState(false);
    const handleClickAlertAgregar = () => {
      setOpenAgregar(true);
    };
    const handleCloseAlertAgregar = () => {
        setOpenAgregar(false);
    };

    const [openEditar, setOpenEditar] = React.useState(false);
    const handleClickAlertEditar = () => {
      setOpenEditar(true);
    };
    const handleCloseAlertEditar = () => {
        setOpenEditar(false);
        limpiarEditarMarcaTienda()
    };

    const [openEliminar, setOpenEliminar] = React.useState(false);
    const handleClickAlertEliminar = () => {
      setOpenEliminar(true);
    };
    const handleCloseAlertEliminar = () => {
        setOpenEliminar(false);
    };

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        setLoading(true)
        dispatch(getTallasPaginado(page))
    }, [page, okAgregar, okEliminar, okEditar])

    return (
        <section className="container-categorias">
            {
                <AgregarTalla 
                    show={show} 
                    handleClose={handleClose} 
                    handleClickAlert={handleClickAlertAgregar}
                />
            }
            {
                <EditarTalla 
                    show={showEditar} 
                    handleClose={handleCloseEditar} 
                    datos={datos} 
                    handleClickAlert={handleClickAlertEditar}
                />
            }
            {
                <ModalEliminarTalla 
                    show={showEliminar} 
                    handleClose={handleCloseEliminar} 
                    datos={datos}
                    handleClickAlert={handleClickAlertEliminar}
                />
            }
            {
                <Snackbar
                    open={openAgregar} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregar} 
                        severity={okAgregar.success === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okAgregar.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEditar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditar} 
                        severity={okEditar.success === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEditar.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEliminar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEliminar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEliminar} 
                        severity={okEliminar.success === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEliminar.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <div  style={{marginBottom:30}}>
                <header className="titulo-categorias">
                    <h3>Medidas</h3>
                    <Button 
                        size="large" 
                        variant="contained"
                        className="btn-miplan"
                        onClick={handleShow}
                    >AGREGAR</Button>
                </header>
                <div className="text-categorias">
                    <h4>En esta secci&oacute;n podr&aacute;s agregar las medidas para luego
                        subir los productos en tu tienda.
                    </h4>
                </div>
            </div>
            {
                loading ? 
                <div style={{display:"flex", justifyContent:"center"}}>
                    <ClipLoader 
                        loading={loading}
                        size={50}
                    />
                </div> :
                info.length > 0 ? <ul className="lista">
                {
                    info && info.map ((a) => 
                        <li key={a.tallaid} style={{marginTop:"20px"}}>
                            <Paper className="paper">
                                <div style={{display:"flex"}}>
                                    <div className="paper-primer-div"><AppsIcon/></div>
                                    <div className="paper-primer-div">{a.nombretalla}</div>
                                </div>
                                <div className="icons">
                                    <Tooltip placement="top" title={a.activo === "1" ? "Activada" : "Desactivada"} className="icon">
                                        {
                                            a.activo === "1" ? <Check color="success"/> : <Clear color="error"/>
                                        }
                                    </Tooltip>
                                    <div onClick={(e) => handleShowEditar(e, a)} className="icon">
                                        <Tooltip title="Editar">
                                            <EditIcon style={{color:"black"}}/>
                                        </Tooltip>
                                    </div>
                                </div>
                            </Paper>
                        </li>
                    )
                }
                </ul> :
                <div style={{display:"flex", justifyContent:"center"}}>
                    <h3>No hay informaci&oacute;n para mostrar</h3>
                </div>
            }
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </section>
    )
}