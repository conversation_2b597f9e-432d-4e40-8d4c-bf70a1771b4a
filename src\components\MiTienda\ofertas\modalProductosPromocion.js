import React, { useEffect, useState } from "react"
import Modal from 'react-modal';
import { useDispatch, useSelector } from "react-redux";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { agregarProductoPromocion, eliminarProductoPromocion, getProductosByNameSkuNuevaVenta, getProductosPromocion } from "../../../redux/actions/mitienda.actions";
import { Button, MenuItem, Pagination, Select, TextField } from "@mui/material";
import { Delete } from "@mui/icons-material";
import { SelectSearchInput } from "./SelectSearchInput";

Modal.setAppElement("#root")

export const ModalProductoPromocion = ({show, handleClose, promo}) =>{

    const productosPromocion = useSelector((state) => state.mitienda.productosPromocion)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaProductosPromocion)
    const productsByNameSku = useSelector((state) => state.mitienda.productosNuevaVenta)
    const okAgregar = useSelector((state) => state.mitienda.okAgregarProductoPromocion)
    const okEliminar = useSelector((state) => state.mitienda.okEliminarProductoPromocion)
    
  const [cantidad, setCantidad] = useState("1")
  const [product, setProduct] = useState("")
  const [open, setOpen] = useState(true)

  const [input, setInput] = useState('')
  const handleChange = (e) => {
    e.preventDefault()
    setInput(e.target.value)
  }

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "90%",
          height: "90vh",
          borderRadius: "8px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const handleAddProduct =()=>{
        dispatch(agregarProductoPromocion(promo.promocion_prodid,product,cantidad))
        dispatch(getProductosByNameSkuNuevaVenta('')) 
        setTimeout(function(){
            setOpen(true)
        }, 5000);
        setInput('')
        setProduct('')
        setCantidad('1')
    }

    const eliminarProducto = (promoid,produpromoid)=>{
        dispatch(eliminarProductoPromocion(promoid,produpromoid))
    }

    useEffect(() => {
        if(promo){
            dispatch(getProductosPromocion(promo.promocion_prodid,page))
        }
    },[promo,page,okAgregar,okEliminar])

    useEffect(() =>{
        if(input.length > 4){
            dispatch(getProductosByNameSkuNuevaVenta(input)) 
        }
      }, [input])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Agregar productos - {promo.nombre}</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <div style={{display:"flex", justifyContent:"center", alignItems:"center", margin:40}}>
                <div style={{width:"80%", marginRight:20, marginTop:-20}}>
                    <h6><strong>Producto</strong></h6>
                    <SelectSearchInput
                        info={productsByNameSku}
                        value={input}
                        setValue={setInput}
                        setProductoId={setProduct}
                        open={open}
                        setOpen={setOpen}
                    />
                </div>
                <div style={{width:"10%", marginRight:20}}>
                    <h6><strong>Cantidad</strong></h6>
                    <TextField
                        style={{width:"100%"}}
                        type='number'
                        name='cantidad'
                        value={cantidad}
                        onChange={(e)=>setCantidad(e.target.value)}
                    />
                </div>
                <Button 
                    style={{marginTop:25}} 
                    variant='contained'  
                    onClick={(e)=>handleAddProduct()}
                >Agregar</Button>
            </div>

            <div style={{display:"flex", flexDirection:"column", justifyContent:"center"}}>
                <TableContainer sx={{width:"100%", alignSelf:"center", marginLeft:3}}>
                    {
                        productosPromocion.length === 0 ?
                        <h5 style={{display:"flex", justifyContent:"center"}}>No hay datos</h5> :
                        <Table aria-label="simple table">
                            <TableHead>
                                <TableRow>
                                    <TableCell style={{fontWeight:"bold", 
                                        fontSize:"110%"
                                    }} align="center">C&oacute;digo</TableCell>
                                    <TableCell style={{fontWeight:"bold", 
                                        fontSize:"110%"
                                    }} align="center">Nombre</TableCell>
                                    <TableCell style={{fontWeight:"bold", 
                                        fontSize:"110%",
                                        width:200
                                    }} align="center">Cantidad</TableCell>
                                    <TableCell style={{fontWeight:"bold", 
                                        fontSize:"110%",
                                        width:200
                                    }} align="center">Precio antes</TableCell>
                                    <TableCell style={{fontWeight:"bold", 
                                        fontSize:"110%",
                                        width:200
                                    }} align="center">Precio actual</TableCell>
                                    <TableCell style={{fontWeight:"bold", 
                                        fontSize:"110%",
                                        width:200
                                    }} align="center">Eliminar</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {productosPromocion && productosPromocion.map((row) => (
                                <TableRow
                                    key={row.promocion_prod_productoid}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                >
                                    <TableCell 
                                        style={{fontSize:"16px", width:"200px"}} align="center">
                                        {row.codigoarticulo || ''}
                                    </TableCell>
                                    <TableCell 
                                        style={{fontSize:"16px"}} align="center">
                                        {row.producto || ''}
                                    </TableCell>
                                    <TableCell 
                                        style={{fontSize:"16px", width:"50px"}} align="center">
                                        {row.cantidad || ''}
                                    </TableCell>
                                    <TableCell 
                                        style={{fontSize:"16px", width:"50px"}} align="center">
                                        ${row.precioVenta || ''}
                                    </TableCell>
                                    <TableCell 
                                        style={{fontSize:"16px", width:"50px"}} align="center">
                                        ${row.precioPromo || ''}
                                    </TableCell>
                                    <TableCell style={{fontSize:"100%", width:"50px"}} align="center" 
                                    onClick={(e)=>eliminarProducto(row.promocion_prodid,row.promocion_prod_productoid)}><div><Delete/></div></TableCell>
                                </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    }
                </TableContainer>
                <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
            </div>
        </Modal>
    )
}