import React from "react";
import { Button, Checkbox, FormControlLabel, InputAdornment, Paper, Snackbar, TextField } from "@mui/material"
import { ContentCopy, Done } from "@mui/icons-material";
import MuiAlert from '@mui/material/Alert';
import logo from '../../../../media/google-analytics-logo.png'
import { useState } from "react";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { configSEO, getEtiquetasHtml } from "../../../../redux/actions/mitienda.actions";

export const GoogleAnalytics =()=>{

    const infoSeo = useSelector((state) => state.mitienda.etiquetas)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const [input, setInput] = useState('')

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const dispatch = useDispatch()

    const [open, setOpen] = React.useState(false);

    const handleClickAlert = () => {
      setOpen(true);
    };
  
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const handleOnClick = () => {
        dispatch(configSEO(input))
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    };

    useEffect(() => {
        dispatch(getEtiquetasHtml())
    }, [])

    useEffect(() => {
        if(infoSeo){
            setInput(infoSeo)
        }
    }, [infoSeo])

    return (
        <section className="container-meta-ads">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity="success"
                        sx={{ width: 400 }}>
                        <h5>Informacion editada con exito!</h5>
                    </Alert>
                </Snackbar>
            }
            <div className="container-form">
                <header className="header-meta-ads">
                    <h3 style={{marginTop:10, marginBottom:20, display:"flex"}}>
                    <img src={logo} style={{width:"250px"}}/>
                    </h3>
                    <div>
                        <h4>Es una herramienta que te permite medir las ventas, las conversiones y obtener
                            informaci&oacute;n detallada sobre c&oacute;mo los visitantes utilizan tu tienda, c&oacute;mo llegan a ella y
                            qu&eacute; acciones realizan.
                        </h4>
                    </div>
                </header>
                <Paper className="form-google-analytics">
                    <TextField 
                        label="ID de Seguimiento (opcional)"  
                        fullWidth margin="normal" 
                        value={input.ecommerce_analytics || ''}
                        name="ecommerce_analytics"
                        onChange={handleChange}
                        InputProps={{
                            endAdornment:<InputAdornment position="end">
                            <Done/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 50,
                            fontSize:17
                            },
                        }}
                    />
                    {/* <FormControlLabel
                        style={{marginTop:30, marginBottom:-30}}
                        control={
                            <Checkbox/>
                        }
                        label="Activar Google Analytics"
                    /> */}
                </Paper>
            <div style={{alignSelf:"start"}}>
                <Button 
                    size="large" 
                    variant="contained" 
                    onClick={handleOnClick}
                    disabled={permisos_acciones?.modificar === "0"}
                    sx={{mt: 3, mb:3}}>Guardar</Button>
            </div>
            </div>
        </section>
    )
}