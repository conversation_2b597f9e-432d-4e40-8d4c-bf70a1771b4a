import React, { useEffect } from "react"
import Mo<PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { useDispatch, useSelector } from "react-redux";
import { Button, FormControl, InputLabel, MenuItem, Select, TextField } from "@mui/material";
import {
    getCategoriasSinPag,
    getMarcasTienda
} from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalFiltrosRentabilidadProductos = ({
  show,
  handleClose,
  handleChangeCodigo,
  codigo,
  handleChangeNombre,
  nombre,
  handleChangeMarca,
  marca,
  handleChangeCategoria,
  categoria,
  search,
  reset
}) =>{

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };

  const categorias = useSelector((state) => state.mitienda.categoriasSinPag)
  const marcas = useSelector((state) => state.mitienda.marcas)

  const dispatch = useDispatch()

  //esta funcion se ejecuta una sola vez al montar el componente
  //aca obtenemos la informacion para los selects de los filtros
  useEffect(() =>{
    dispatch(getCategoriasSinPag())
    dispatch(getMarcasTienda())
}, [])

  return (
      show &&
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
              <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                  <h4>FILTRAR</h4>
                  <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
              </div>
              <Divider/>
              <div style={{display:"flex", flexDirection:"column"}}>
              <FormControl sx={{mt:4}}>
                      <InputLabel id="marcalabel">Marca</InputLabel>
                      <Select
                      labelId="marcalabel"
                      id="marca"
                      label="Marca"  
                      size="small"
                      name="marcaId"
                      value={marca}
                      onChange={handleChangeMarca}
                      MenuProps={{ keepMounted: true, disablePortal: true }}
                      >
                          <MenuItem value={0}>Seleccione una opcion</MenuItem>
                      {
                          marcas && marcas.map((m) => 
                              <MenuItem value={m.marcaid}>{m.nombremarca}</MenuItem>
                          )
                      }
                      </Select>
                  </FormControl>
                  <FormControl sx={{mt:4}}>
                      <InputLabel id="categoria-select-label">Categorias</InputLabel>
                      <Select
                      labelId="categoria-select-label"
                      id="categoria-select"
                      label="Categorias"
                      size="small"
                      name="categoria"
                      value={categoria}
                      onChange={handleChangeCategoria}
                      MenuProps={{ keepMounted: true, disablePortal: true }}
                      >
                      <MenuItem value={0}>Seleccione una opcion</MenuItem>
                      {
                          categorias && categorias.map((c) =>
                              <MenuItem value={c.categoriaid} key={c.categoriaid}>{c.nombrecategoria}</MenuItem>
                          )
                      }
                      </Select>
                  </FormControl>

                  <TextField 
                    sx={{mt:4}}
                    label="Codigo"  
                    variant="outlined" 
                    margin="codigo" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nombre"
                    onChange={(e) => handleChangeCodigo(e)}
                    value={codigo}
                  />
                  <TextField 
                    sx={{mt:4}}
                    label="Nombre"  
                    variant="outlined" 
                    margin="normal" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nombre"
                    onChange={(e) => handleChangeNombre(e)}
                    value={nombre}
                  />
              </div>
              <div align="center">
              <Button
                  size="large"
                  variant="contained"
                  sx={{mt: 3}} fullWidth
                  onClick={search}
              >Aplicar filtros</Button>
              <Button
                  size="large"
                  variant="contained"
                  sx={{mt: 3}} fullWidth
                  onClick={reset}
              >Limpiar filtros</Button>
              </div>
      </Modal>
  )
}
