import React, { useEffect, useState } from 'react';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import { getListadoClientesByName } from '../../../redux/actions/mitienda.actions';
import { Button, FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material';

// const customStyles = {
//     content: {
//         top: '50%',
//         left: '50%',
//         right: 'auto',
//         bottom: 'auto',
//         marginRight: '-50%',
//         transform: 'translate(-50%, -50%)',
//         width: '50%',
//         height: '70%',
//     },
// };

const customStyles = {
    content: {
        top: '50%',
        left: '50%',
        right: 'auto',
        bottom: 'auto',
        marginRight: '-50%',
        transform: 'translate(-50%, -50%)',
        width: '30%', // Reduced width
        height: 'auto', // Auto height for tighter fit
        padding: '20px 30px',
        borderRadius: '12px',
    },
};

// export const ModalFiltrosRemito = ({
//     show,
//     handleClose,
//     handleChangeClienteid,
//     clienteid,
//     handleChangeNrocomprobante,
//     nrocomprobante,
//     setClienteId,
//     handleChangeFechaDesde,
//     fechaDesde,
//     handleChangeFechaHasta,
//     fechaHasta,
//     reset,
//     search
// }) => {
//     const dispatch = useDispatch();
//     const clientes = useSelector((state) => state.mitienda.clientesByName);
//     const [clienteInput, setClienteInput] = useState('');

//     useEffect(() => {
//         if (clienteInput.length > 2) {
//             dispatch(getListadoClientesByName(clienteInput, ''));
//         }
//     }, [clienteInput, dispatch]);

//     return (
//         show && 
//         <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
//             <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
//                 <h4>Filtros</h4>
//                 <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
//             </div>
//             <div style={{display:"flex", flexDirection:"column", justifyContent:"space-between", height:"90%"}}>
//                 <div>
//                     {/* <FormControl fullWidth sx={{marginTop:1}} size="small">
//                         <InputLabel id="search-select-label">Cliente</InputLabel>
//                         <Select
//                             style={{height:40}}
//                             MenuProps={{ autoFocus: false }}
//                             labelId="search-select-label"
//                             id="search-select"
//                             value={clienteid}
//                             label="Cliente"
//                             onClose={() => setClienteInput('')}
//                             onOpen={() => setClienteInput('')}
//                             onChange={handleChangeClienteid}
//                             onKeyDown={(e) => {
//                                 setClienteInput(e.target.value);
//                             }}
//                         >
//                             <MenuItem value={0}>
//                                 <em>Todos</em>
//                             </MenuItem>
//                             {clientes.length > 0 && clientes.map((c) => (
//                                 <MenuItem key={c.clienteid} value={c.clienteid}>
//                                     {c.nombre} {c.apellido}
//                                 </MenuItem>
//                             ))}
//                         </Select>
//                     </FormControl>
//                     <TextField
//                         label="Número de comprobante"  
//                         variant="outlined" 
//                         margin="normal" 
//                         fullWidth
//                         InputLabelProps={{
//                             style: {
//                                 marginBottom:10,
//                                 marginTop: -7
//                             },
//                         }}
//                         inputProps={{
//                             style: {
//                                 height: 40,
//                                 fontSize:17,
//                             },
//                         }}
//                         name="nrocomprobante"
//                         onChange={handleChangeNrocomprobante}
//                         value={nrocomprobante}
//                     /> */}
//                     <div style={{display:"flex", justifyContent:"space-between", marginTop:10}}>
//                         <div style={{width:"48%"}}>
//                             <p style={{margin:0}}>Fecha desde</p>
//                             <TextField
//                                 type="date"
//                                 variant="outlined" 
//                                 margin="normal" 
//                                 fullWidth
//                                 InputLabelProps={{
//                                     style: {
//                                         marginBottom:10,
//                                         marginTop: -7
//                                     },
//                                 }}
//                                 inputProps={{
//                                     style: {
//                                         height: 40,
//                                         fontSize:17,
//                                     },
//                                 }}
//                                 name="fechaDesde"
//                                 onChange={handleChangeFechaDesde}
//                                 value={fechaDesde}
//                             />
//                         </div>
//                         <div style={{width:"48%"}}>
//                             <p style={{margin:0}}>Fecha hasta</p>
//                             <TextField
//                                 type="date"
//                                 variant="outlined" 
//                                 margin="normal" 
//                                 fullWidth
//                                 InputLabelProps={{
//                                     style: {
//                                         marginBottom:10,
//                                         marginTop: -7
//                                     },
//                                 }}
//                                 inputProps={{
//                                     style: {
//                                         height: 40,
//                                         fontSize:17,
//                                     },
//                                 }}
//                                 name="fechaHasta"
//                                 onChange={handleChangeFechaHasta}
//                                 value={fechaHasta}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div style={{display:"flex", justifyContent:"space-between", marginTop:20}}>
//                     <Button 
//                         variant="outlined" 
//                         sx={{height:40, width:150}}
//                         onClick={reset}
//                     >
//                         Limpiar
//                     </Button>
//                     <Button 
//                         variant="contained" 
//                         sx={{height:40, width:150}}
//                         onClick={search}
//                     >
//                         Buscar
//                     </Button>
//                 </div>
//             </div>
//         </Modal>
//     );
// };

export const ModalFiltrosRemito = ({
    show,
    handleClose,
    handleChangeClienteid,
    clienteid,
    handleChangeNrocomprobante,
    nrocomprobante,
    setClienteId,
    handleChangeFechaDesde,
    fechaDesde,
    handleChangeFechaHasta,
    fechaHasta,
    reset,
    search
}) => {
    const dispatch = useDispatch();
    const clientes = useSelector((state) => state.mitienda.clientesByName);
    const [clienteInput, setClienteInput] = useState('');

    useEffect(() => {
        if (clienteInput.length > 2) {
            dispatch(getListadoClientesByName(clienteInput, ''));
        }
    }, [clienteInput, dispatch]);

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: 15 }}>
                <h4 style={{ margin: 0 }}>Filtros</h4>
                <button
                    onClick={handleClose}
                    style={{ all: "unset", cursor: "pointer", fontSize: 18, lineHeight: 1 }}
                    aria-label="Cerrar"
                >
                    ✕
                </button>
            </div>

            <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
                <div style={{ display: "flex", justifyContent: "space-between", gap: 10 }}>
                    <div style={{ flex: 1 }}>
                        <label style={{ fontSize: 14, marginBottom: 4, display: "block" }}>Fecha desde</label>
                        <TextField
                            type="date"
                            variant="outlined"
                            fullWidth
                            name="fechaDesde"
                            onChange={handleChangeFechaDesde}
                            value={fechaDesde}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize: 16,
                                },
                            }}
                        />
                    </div>
                    <div style={{ flex: 1 }}>
                        <label style={{ fontSize: 14, marginBottom: 4, display: "block" }}>Fecha hasta</label>
                        <TextField
                            type="date"
                            variant="outlined"
                            fullWidth
                            name="fechaHasta"
                            onChange={handleChangeFechaHasta}
                            value={fechaHasta}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize: 16,
                                },
                            }}
                        />
                    </div>
                </div>

                <div style={{ display: "flex", justifyContent: "space-between", marginTop: 10 }}>
                    <Button
                        variant="outlined"
                        sx={{ height: 40, width: '48%' }}
                        onClick={reset}
                    >
                        Limpiar
                    </Button>
                    <Button
                        variant="contained"
                        sx={{ height: 40, width: '48%' }}
                        onClick={search}
                    >
                        Buscar
                    </Button>
                </div>
            </div>
        </Modal>
    );
};