import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { editarColorTienda } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const EditarColorTienda = ({show, handleClose, datos, handleClickAlert}) =>{

  let pathname = window.location.pathname 
  let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
  .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

  const [input, setInput] = useState()

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value})
  }

  const dispatch = useDispatch()

  const handleClick = (e) => {
    e.preventDefault()
    dispatch(editarColorTienda(input))
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  const handleCheck = (e) =>{
    e.preventDefault()
    if(e.target.checked === true){
        setInput({...input, [e.target.name] : "1"})
    }else{
        setInput({...input, [e.target.name] : "0"})
    }
  }

  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      maxHeight: "90vh",
      borderRadius: "8px",
      maxWidth: "650px",
    //   backgroundColor: "#D1D1D1"
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  useEffect(() =>{
    setInput(datos)
  }, [datos])

  return (
    show ?
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
            <h4>Editar color</h4>
            <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
        </div>
        <Divider/>
        <div style={{marginTop:15, marginBottom:15}}>
          <TextField 
            name="nombre"
            label="Nombre"  
            variant="outlined" 
            fullWidth margin="normal" 
            value={input.nombre || ''}
            onChange={(e)=> handleChange(e)}
            InputLabelProps={{
              style: {
                marginBottom:10,
                marginTop: -7
              },
            }}
            inputProps={{
              style: {
                height: 40,
                fontSize:17
              },
            }}
          />
          <TextField 
            name="color"
            label="Color"  
            variant="outlined" 
            fullWidth margin="normal" 
            value={input.color || ''}
            onChange={(e)=> handleChange(e)}
            type="color"
            InputLabelProps={{
              style: {
                marginBottom:10,
                marginTop: -7
              },
            }}
            inputProps={{
              style: {
                height: 70,
                fontSize:17
              },
            }}
          />
          <div style={{marginTop:15, marginBottom:5}}>
            <FormControlLabel
              control={
              <Checkbox/>
              }
              style={{color:"black"}}
              onChange={(e)=> handleCheck(e)}
              disabled={permisos_acciones?.activar === "0"}
              checked={input.activo == "1" ? true : false}
              label="Activo"
              name="activo"
            />
          </div>
        </div>
        <Divider/>
        <div align="right">
          <Button 
            size="large" 
            variant="contained" 
            sx={{mt: 3}}
            onClick={handleClick}
            disabled={permisos_acciones?.modificar === "0"}
            >Editar</Button>
        </div>
      </Modal> : null
  )
}