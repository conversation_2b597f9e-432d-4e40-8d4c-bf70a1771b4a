import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield } from "@mui/material";
import Modal from "react-modal";
import {
  enviarEmail,
  getClienteById,
} from "../../../redux/actions/mitienda.actions";
import { useDispatch, useSelector } from "react-redux";

Modal.setAppElement("#root");

export const ModalPdf = ({ show, handleClose, info }) => {
  const cliente = useSelector((state) => state.mitienda.clienteById);
  const okEnviarEmail = useSelector((state) => state.mitienda.enviarEmail);

  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      maxHeight: "90vh",
      borderRadius: "8px",
      maxWidth: "650px",
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      //   backgroundColor: "#D1D1D1"
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  const dispatch = useDispatch();

  const [input, setInput] = useState("");
  const handleChange = (e) => {
    e.preventDefault();
    setInput(e.target.value);
  };

  const handleChangeEmail = (e) => {
    e.preventDefault();
    setInput(e.target.value);
  };

  useEffect(() => {
    dispatch(getClienteById(info.clienteid));
  }, [info]);

  useEffect(() => {
    setInput(cliente.email);
  }, [cliente]);

  return (
    show && (
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <h4>Descargar comprobante</h4>
          <button
            onClick={handleClose}
            style={{
              all: "unset",
              color: "black",
              paddingBottom: 10,
              cursor: "pointer",
            }}
          >
            X
          </button>
        </div>
        <Divider />
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            padding: 20,
          }}
        >
          <h5>
            Factura #{info.facturacompraid} - {info.nombreproveedor}
          </h5>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <div
              style={{
                display: "flex",
                width: "100%",
                justifyContent: "center",
              }}
            >
              <Button
                variant="contained"
                style={{ width: 200, margin: 10 }}
                onClick={(e) =>
                  dispatch(enviarEmail(info.facturacompraid, input))
                }
              >
                Enviar al email
              </Button>
              {/* <Button variant="contained" style={{ width: 200, margin: 10 }}>
                <a 
                                href={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=127&codigocomprobante=${info.numero_comprob}&tipoaccionpdf=1`}
                                target="_blank"
                                style={{color:"white"}}
                            > 
                
                  Descargar
                </a>
              </Button> */}
              <Button variant="contained" style={{ width: 200, margin: 10 }}>
                <a
                  href={`${
                    process.env.REACT_APP_SERVER
                  }dompdf/generarcomprobante_pdf.php?tipocomprobante=127&codigocomprobante=${
                    info.numero_comprob
                  }&tipoaccionpdf=1&tiendaid=${localStorage.getItem(
                    "tiendausuario"
                  )}`}
                  target="_blank"
                  style={{ color: "white" }}
                >
                  Descargar
                </a>
              </Button>
            </div>
            <TextField
              value={input || ""}
              onChange={handleChange}
              style={{ width: "80%" }}
            />
          </div>
        </div>
        <Divider />
        {okEnviarEmail !== "" && (
          <Alert severity="info" style={{ width: "90%", margin: 10 }}>
            {okEnviarEmail}
          </Alert>
        )}
      </Modal>
    )
  );
};
