import React, { useEffect, useState } from "react";
import Modal from "react-modal";
import {
  Button,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  ListSubheader,
  MenuItem,
  Paper,
  Select,
  Snackbar,
  TextField,
  Tooltip,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import {
  getListadoClientes,
  getListadoClientesByName,
} from "../../../../redux/actions/mitienda.actions";
import Pagination from "@mui/material/Pagination";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import "./administrarClientes.css";
import { ModalFiltrosClientes } from "./modalFiltros";
import {
  Add,
  Article,
  Clear,
  Download,
  Edit,
  FilterList,
  KeyboardArrowDown,
  RemoveRedEye,
  Search,
} from "@mui/icons-material";
import { ModalEditarCliente } from "./modalEditarCliente";
import * as XLSX from "xlsx";
import { ModalActivarDesactivarCliente } from "./modalActivarDesactivarCliente";
import MuiAlert from "@mui/material/Alert";
import { ModalAgregarCliente } from "../agregarCliente";
import { ModalVerDirecciones } from "./modalVerDirecciones";
import ClipLoader from "react-spinners/ClipLoader";

Modal.setAppElement("#root");

export const AdministrarClientes = () => {
  const dispatch = useDispatch();
  const info = useSelector((state) => state.mitienda.listadoDeClientes);
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaClientes
  );
  const okActivar = useSelector(
    (state) => state.mitienda.activarDesactivarCliente
  );
  const okEditarCliente = useSelector((state) => state.mitienda.editarCliente);
  const okAgregarCliente = useSelector(
    (state) => state.mitienda.okAgregarCliente
  );

  let pathname = window.location.pathname;
  let permisos_acciones = Object.values(
    JSON.parse(localStorage.getItem("permisos_acciones"))
  ).filter((p) =>
    p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase())
  )[0];

  const [cliente, setCliente] = useState("");

  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => {
    setShow(true);
  };

  const [showEditar, setShowEditar] = useState(false);
  const handleCloseEditar = () => setShowEditar(false);
  const handleShowEditar = (e, row) => {
    e.preventDefault();
    setShowEditar(true);
    setCliente(row);
  };

  const [showAgregar, setShowAgregar] = useState(false);
  const handleCloseAgregar = () => setShowAgregar(false);
  const handleShowAgregar = (e, row) => {
    e.preventDefault();
    setShowAgregar(true);
  };

  const [showActivarDesactivar, setShowActivarDesactivar] = useState(false);
  const handleCloseActivarDesactivar = () => setShowActivarDesactivar(false);
  const handleShowActivarDesactivar = (e, row) => {
    e.preventDefault();
    setShowActivarDesactivar(true);
    setCliente(row);
  };

  const [direcciones, setDirecciones] = useState([]);
  const [showDirecciones, setShowDirecciones] = useState(false);
  const handleCloseDirecciones = () => setShowDirecciones(false);
  const handleShowDirecciones = (e, row) => {
    e.preventDefault();
    setDirecciones(row);
    setShowDirecciones(true);
  };

  const [numeroCuit, setNumeroCuit] = useState("");
  const handleChangeNumeroCuit = (e) => {
    setNumeroCuit(e.target.value);
  };
  const [email, setEmail] = useState("");
  const handleChangeEmail = (e) => {
    setEmail(e.target.value);
  };

  const [nombre, setNombre] = useState("");
  const handleChangeNombre = (e) => {
    setNombre(e.target.value);
  };

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const [dropdownOpen, setDropdownOpen] = useState(false);

  const reset = (e) => {
    setEmail("");
    setNombre("");
    setNumeroCuit("");
    setPage(1);
    dispatch(getListadoClientes(page, "", "", ""));
    handleClose();
  };

  const search = () => {
    dispatch(getListadoClientes(page, nombre, numeroCuit, email));
    handleClose();
  };

  const match = (string) => {
    if (string !== null && string.includes("/")) {
      let aux = string.split("/");
      let aux2 = "";
      for (let i = 0; i < aux.length; i++) {
        aux2 += aux[i] + " ";
      }
      return aux2;
    } else {
      return string;
    }
  };

  const downloadExcel = () => {
    const workSheet = XLSX.utils.json_to_sheet(info);
    const workBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workBook, workSheet, "info");
    let buffer = XLSX.write(workBook, { bookType: "xlsx", type: "buffer" });
    XLSX.write(workBook, { bookType: "xlsx", type: "binary" });

    XLSX.writeFile(workBook, "Clientes.xlsx");
  };

  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });
  const [open, setOpen] = React.useState(false);
  const handleClickAlert = () => {
    setOpen(true);
  };
  const handleCloseAlert = () => {
    setOpen(false);
  };

  const [openEditar, setOpenEditar] = React.useState(false);
  const handleClickAlertEditar = () => {
    setOpenEditar(true);
  };
  const handleCloseAlertEditar = () => {
    setOpenEditar(false);
  };

  const [openAgregar, setOpenAgregar] = React.useState(false);
  //w
  // const handleClickAlertAgregar = () => {
  //   setOpenAgregar(true);
  // };

//   const handleClickAlertAgregar = () => {
//   // Only open if there's an actual message to display
//   if (okAgregarCliente && okAgregarCliente.mensaje) {
//     setOpenAgregar(true);
//   }
// };

const handleClickAlertAgregar = () => {
  if (okAgregarCliente && (okAgregarCliente.mensaje || okAgregarCliente.message)) {
    setOpenAgregar(true);
  }
};

  const handleCloseAlertAgregar = () => {
    setOpenAgregar(false);
  };

  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(false);
  }, [info]);

  useEffect(() => {
    setLoading(true);
    // If a client is selected, maintain the filter when changing pages
    if (selectedClient) {
      dispatch(getListadoClientes(page, "", "", "", selectedClient.clienteid));
    } else {
      dispatch(getListadoClientes(page, "", "", ""));
    }
  }, [page, okAgregarCliente, okEditarCliente]);

  useEffect(() => {
  if (okAgregarCliente && okAgregarCliente.mensaje) {
    handleClickAlertAgregar();
  }
}, [okAgregarCliente]);

  // Add state for autocomplete search
  const [searchNombre, setSearchNombre] = useState("");
  const [selectedClient, setSelectedClient] = useState(null);
  const clientesByName = useSelector((state) => state.mitienda.clientesByName);

  // Function to handle search input change
  const handleSearchInputChange = (e) => {
    setSearchNombre(e.target.value);
    if (e.target.value.length > 2) {
      dispatch(getListadoClientesByName(e.target.value, ""));
    }
  };

  // Function to handle client selection
  const handleClientSelection = (client) => {
    setSelectedClient(client);
    setSearchNombre("");
    setPage(1); // Always reset to page 1 when selecting a client
    // Use client.clienteid instead of client.nombre for more precise filtering
    dispatch(getListadoClientes(1, "", "", "", client.clienteid));
  };

  // Function to clear search
  const clearSearch = () => {
    setSelectedClient(null);
    setSearchNombre("");
    dispatch(getListadoClientes(page, "", "", ""));
  };

  return (
    <section className="container-adm-clientes">
      <ModalVerDirecciones
        show={showDirecciones}
        info={direcciones}
        handleClose={handleCloseDirecciones}
      />
      {
        <Snackbar
          open={open}
          autoHideDuration={10000}
          onClose={handleCloseAlert}
          anchorOrigin={{ vertical: "top", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseAlert}
            severity={okActivar.success ? "success" : "error"}
            sx={{ width: 400 }}
          >
            <h5>{okActivar.mensaje}</h5>
          </Alert>
        </Snackbar>
      }
      {
        <Snackbar
          open={openEditar}
          autoHideDuration={10000}
          onClose={handleCloseAlertEditar}
          anchorOrigin={{ vertical: "top", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseAlertEditar}
            severity={okEditarCliente.success ? "success" : "error"}
            sx={{ width: 400 }}
          >
            <h5>{okEditarCliente.mensaje}</h5>
          </Alert>
        </Snackbar>
      }
      {
        <Snackbar
          open={openAgregar}
          autoHideDuration={10000}
          onClose={handleCloseAlertAgregar}
          anchorOrigin={{ vertical: "top", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseAlertAgregar}
            severity={okAgregarCliente.success ? "success" : "error"}
            sx={{ width: 400 }}
          >
            <h5>{okAgregarCliente.mensaje}</h5>
          </Alert>
        </Snackbar>
      }


      {
        <ModalFiltrosClientes
          show={show}
          handleClose={handleClose}
          handleChangeNombre={handleChangeNombre}
          nombre={nombre}
          handleChangeEmail={handleChangeEmail}
          email={email}
          handleChangeCuit={handleChangeNumeroCuit}
          cuit={numeroCuit}
          reset={reset}
          search={search}
        />
      }
      {
        <ModalEditarCliente
          show={showEditar}
          handleClose={handleCloseEditar}
          cliente={cliente}
          handleClickAlert={handleClickAlertEditar}
        />
      }
      {
        <ModalAgregarCliente
          show={showAgregar}
          handleClose={handleCloseAgregar}
          handleClickAlert={handleClickAlertAgregar}
        />
      }
      {
        <ModalActivarDesactivarCliente
          show={showActivarDesactivar}
          handleClose={handleCloseActivarDesactivar}
          cliente={cliente}
          handleClickAlert={handleClickAlert}
        />
      }
      <header className="titulo-clientes">
        <h3>Clientes</h3>
      </header>
      <div
        style={{
          display: "flex",
          marginLeft: "50px",
          marginBottom: 20,
          alignItems: "center",
        }}
      >
        <Button
          variant="outlined"
          sx={{ height: 40, width: 150, marginTop: 2 }}
          onClick={handleShow}
        >
          <FilterList /> Filtrar
        </Button>

        {/* autocomplete search field */}

        <FormControl sx={{ width: 300, marginLeft: 2, marginTop: 2 }}>
          <InputLabel id="client-search-label">Buscar cliente</InputLabel>
          {/* <Select
            labelId="client-search-label"
            id="client-search"
            value={selectedClient ? selectedClient.clienteid : ""}
            label="Buscar cliente"
            sx={{ height: 40 }}
            renderValue={() =>
              selectedClient
                ? `${selectedClient.nombre} ${selectedClient.apellido}`
                : ""
            }
            onOpen={() => {
              setSearchNombre("");
            }}
            onClose={() => {
              if (!selectedClient) {
                setSearchNombre("");
                dispatch(getListadoClientesByName("", ""));
              }
            }}
            IconComponent={() => (
              <div style={{ display: "flex", alignItems: "center" }}>
                {selectedClient && (
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      clearSearch();
                    }}
                    sx={{ marginRight: 1 }}
                  >
                    <Clear />
                  </IconButton>
                )}
                <KeyboardArrowDown />
              </div>
            )}
          > */}
        <Select
  labelId="client-search-label"
  id="client-search"
  value={selectedClient ? selectedClient.clienteid : ""}
  label="Buscar cliente"
  sx={{ height: 40 }}
  renderValue={() =>
    selectedClient
      ? `${selectedClient.nombre} ${selectedClient.apellido}`
      : ""
  }
  open={dropdownOpen}
  onOpen={() => {
    setDropdownOpen(true);
  }}
  onClose={() => {
    setDropdownOpen(false);
    if (!selectedClient) {
      setSearchNombre("");
      dispatch(getListadoClientesByName("", ""));
    }
  }}
  MenuProps={{
    disableAutoFocusItem: true, // This prevents focus from jumping to menu items
    autoFocus: false, // Prevents menu from taking focus
    disableAutoFocus: true, // Prevents auto-focusing on menu items
    onClose: () => setDropdownOpen(false),
  }}
  IconComponent={() => (
    <div style={{ display: "flex", alignItems: "center" }}>
      {selectedClient && (
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            clearSearch();
          }}
          sx={{ marginRight: 1 }}
        >
          <Clear />
        </IconButton>
      )}
      <KeyboardArrowDown />
    </div>
  )}
>
            {/* <ListSubheader>
              <TextField
                size="small"
                autoFocus
                placeholder="Buscar cliente..."
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                inputProps={{
                  style: {
                    height: 35,
                    fontSize: 17,
                  },
                }}
                onChange={handleSearchInputChange}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
                onKeyDown={(e) => {
                  e.stopPropagation();
                }}
                value={searchNombre}
              />
            </ListSubheader> */}
           <ListSubheader>
    <TextField
      size="small"
      autoFocus // This will now work properly
      placeholder="Buscar cliente..."
      fullWidth
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <Search />
          </InputAdornment>
        ),
      }}
      inputProps={{
        style: {
          height: 35,
          fontSize: 17,
        },
      }}
      onChange={(e) => {
        setSearchNombre(e.target.value);
        if (e.target.value.length > 2) {
          dispatch(getListadoClientesByName(e.target.value, ""));
          // Don't automatically open dropdown here, let user interaction control it
        } else if (e.target.value.length === 0) {
          setDropdownOpen(false);
          dispatch(getListadoClientesByName("", ""));
        }
      }}
      onClick={(e) => {
        e.stopPropagation();
        // Don't automatically open dropdown on click
      }}
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
        }
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          // Prevent arrow keys from moving focus to menu items while typing
          e.preventDefault();
        }
        // Allow other keys to propagate normally for typing
        e.stopPropagation();
      }}
      onFocus={(e) => {
        // Keep focus on the input field
        e.target.focus();
        // Open dropdown when user focuses on the input and there are results
        if (searchNombre.length > 2 && clientesByName && clientesByName.length > 0) {
          setDropdownOpen(true);
        }
      }}
      value={searchNombre}
    />
  </ListSubheader>
            {clientesByName &&
              clientesByName.map((client, i) => (
                <MenuItem
                  key={i}
                  value={client.clienteid}
                  onClick={() => handleClientSelection(client)}
                >
                  {client.nombre} {client.apellido}
                </MenuItem>
              ))}
          </Select>
        </FormControl>

        <a
          target="_blank"
          rel="noreferrer"
          href={`${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=0&paginaActual=&paginaResultado=&OV_nombre=&NV_cuit=&OV_email=&exportar_excel=1`}
        >
          <Button
            variant="outlined"
            sx={{ height: 40, width: 150, marginTop: 2, marginLeft: "50px" }}
          >
            <Download /> Exportar
          </Button>
        </a>
        <Button
          variant="outlined"
          sx={{
            height: 40,
            width: 170,
            marginTop: 2,
            marginLeft: "50px",
            padding: 1,
          }}
          onClick={handleShowAgregar}
        >
          Agregar cliente
        </Button>
      </div>
      {permisos_acciones?.listar !== "0" && (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyItems: "center",
          }}
          className="paper-clientes"
        >
          <TableContainer component={Paper} sx={{ width: "100%" }}>
            <Table aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    Nombre
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    CUIT
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    Email
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    Celular
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    Direcciones
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    Cond. IVA
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    Cond. pago
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    Estado
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "110%",
                      paddingLeft: 0,
                      paddingRight: 0,
                    }}
                    align="center"
                  >
                    Acciones
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow sx={{ p: 20 }}>
                    <TableCell colSpan={13} align="center">
                      <ClipLoader loading={loading} size={50} />
                    </TableCell>
                  </TableRow>
                ) : info.data.length > 0 ? (
                  info.data.map((row) => (
                    <TableRow
                      key={row.clienteid}
                      sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                    >
                      <TableCell
                        style={{
                          fontSize: "15px",
                          width: "230px",
                          height: "50px",
                          padding: 0,
                          paddingLeft: 5,
                          overflow: "hidden",
                          textAlign: "left",
                        }}
                        align="center"
                      >
                        {row.apellido !== ""
                          ? row.nombre + " " + row.apellido
                          : row.nombre !== "" && row.nombre.slice(0, 30)}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "15px",
                          width: "80px",
                          height: "50px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.cuit && row.cuit.slice(0, 10)}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "15px",
                          width: "100px",
                          height: "50px",
                          padding: 0,
                          overflow: "hidden",
                        }}
                        align="center"
                      >
                        {row.email && match(row.email)}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "15px",
                          width: "100px",
                          height: "50px",
                          padding: 0,
                          overflow: "hidden",
                        }}
                        align="center"
                      >
                        {row.celular && row.celular.slice(0, 9)}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "15px",
                          width: "100px",
                          height: "50px",
                          padding: 0,
                          overflow: "hidden",
                        }}
                        align="center"
                      >
                        <div
                          onClick={(e) =>
                            handleShowDirecciones(
                              e,
                              Object.values(row.direccioncliente)
                            )
                          }
                        >
                          <Tooltip title="Ver direcciones">
                            <Article sx={{ mr: 1 }} />
                          </Tooltip>
                        </div>
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "50px",
                          padding: 1,
                        }}
                        align="center"
                      >
                        {row.tipocondicioniva &&
                          row.tipocondicioniva.toUpperCase()}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "100px",
                          height: "50px",
                          padding: 1,
                        }}
                        align="center"
                      >
                        {row.tipocondicionpago &&
                          row.tipocondicionpago.toUpperCase()}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "50px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.activo == "1" ? (
                          <h6 style={{ color: "green", fontSize: "14px" }}>
                            {" "}
                            ACTIVO
                          </h6>
                        ) : (
                          <h6 style={{ color: "red", fontSize: "14px" }}>
                            NO ACTIVO
                          </h6>
                        )}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "15px",
                          width: "150px",
                          height: "50px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        <div
                          style={{ display: "flex", justifyContent: "center" }}
                        >
                          <Tooltip title="Editar">
                            <Button
                              style={{ color: "black" }}
                              disabled={permisos_acciones?.modificar === "0"}
                              onClick={(e) => handleShowEditar(e, row)}
                            >
                              <Edit sx={{ mr: 1 }} />
                            </Button>
                          </Tooltip>
                          <Tooltip title="Activar/Desactivar">
                            <Button
                              onClick={(e) =>
                                handleShowActivarDesactivar(e, row)
                              }
                              style={{ color: "black" }}
                              disabled={permisos_acciones?.activar === "0"}
                            >
                              <RemoveRedEye />
                            </Button>
                          </Tooltip>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} align="center">
                      <h5>No hay informaci&oacute;n para mostrar</h5>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Pagination
            count={paginaUltima}
            page={page}
            onChange={handleChangePage}
            size="large"
            sx={{ alignSelf: "center", marginTop: 5 }}
          />
        </div>
      )}
    </section>
  );
};
