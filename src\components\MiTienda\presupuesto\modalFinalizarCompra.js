import React from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { useDispatch } from "react-redux";
import { facturarPedido } from "../../../redux/actions/mitienda.actions";
import Alert from '@mui/material/Alert';
import { CheckCircleOutline, Clear } from "@mui/icons-material";

Modal.setAppElement("#root")

export const ModalFinalizarCompra = ({show, handleClose, pedido, handleClickAlert, okFinalizarPedido}) =>{

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const handleFacturar = () => {
        dispatch(facturarPedido(okFinalizarPedido.pedidoid))
        setTimeout(function(){
            handleClickAlert()
        }, 3000);
    }

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                {
                    okFinalizarPedido !== '' &&
                    <Alert icon={okFinalizarPedido.success ? <CheckCircleOutline fontSize="inherit" /> : <Clear/>} 
                    severity={okFinalizarPedido.success ? "success" : "error"}
                    style={{width:"90%"}}
                    >
                        {okFinalizarPedido.mensaje}
                    </Alert>
                }
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{margin:15, display:"flex", flexWrap:"wrap", justifyContent:"center"}}>  
                <Button variant="contained" style={{width:150, justifyContent:"center", margin:5}}>
                    <a 
                        href={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=124&codigocomprobante=${okFinalizarPedido.pedidoid}&tipoaccionpdf=1`}
                        target="_blank"
                        style={{color:"white"}}
                    > 
                        Comprobante
                    </a>
                </Button>
                <Button style={{margin:5}} variant="contained" onClick={handleFacturar}>
                    Generar factura
                </Button> 
                {/* <Button style={{margin:10}} variant="contained" disabled={input[0].facturado === 1 ? false : true}>
                    <a style={{color:"white"}} href={input[0].url_comprobante} target="_blank" >
                        Descargar factura
                    </a>
                </Button> 
                <div style={{display:"flex", alignContent:"center", margin:10, flexDirection:"column"}}>
                    <Button variant="contained" disabled={input[0].facturado === 1 ? false : true}>
                        Enviar factura al email
                    </Button> 
                </div> */}
            </div>
        </Modal>
    )
}