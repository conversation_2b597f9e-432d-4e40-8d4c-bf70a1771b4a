import { TextField } from '@material-ui/core';
import { Description, Download, RemoveRedEye, Search } from '@mui/icons-material';
import { Button, Checkbox, InputAdornment, Snackbar, Tooltip } from '@mui/material';
import React from 'react';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import './ventas.css'
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { 
    facturarVariosPedidos, 
    getPedidos, 
    limpiarPedido 
} from '../../../redux/actions/mitienda.actions';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import Pagination from '@mui/material/Pagination';
import { useState } from 'react';
import { ModalFiltrosVenta } from './modalFiltros';
import MuiAlert from '@mui/material/Alert';
import moment from 'moment';
import * as XLSX from "xlsx";
import { ModalVerPdf } from './verPdf';
import ClipLoader from "react-spinners/ClipLoader";

export const Ventas = () => {

    let fechaActual = moment()
    let ultimosDias = moment().day(-60)
    
    const pedidos = useSelector((state) => state.mitienda.pedidos)
    const okFacturarMasivamente = useSelector((state) => state.mitienda.okFacturarMasivamente)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaPedidos)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const [pedidosFacturar, setPedidosFacturar] = useState([])

    const [show, setShow] = useState('');

    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [clienteid, setClienteid] = useState(0)
    const handleChangeClienteid = (e) =>{
        setClienteid(e.target.value)
    }

    const [numerofactura, setNumeroFactura] = useState('')
    const handleChangeNumeroFactura = (e) =>{
        setNumeroFactura(e.target.value)
    }

    const [nrocomprobante, setNrocomprobante] = useState('')
    const handleChangeNrocomprobante = (e) =>{
        setNrocomprobante(e.target.value)
    }

    const [nombrecliente, setNombrecliente] = useState('')
    const handleChangeNombrecliente = (e) =>{
        setNombrecliente(e.target.value)
    }

    const [fechaDesde, setFechaDesde] = useState(ultimosDias.toISOString().substring(0,10))
    const handleChangeFechaDesde = (e) =>{
        setFechaDesde(e.target.value)
    }

    const [fechaHasta, setFechaHasta] = useState(fechaActual.toISOString().substring(0,10))
    const handleChangeFechaHasta = (e) =>{
        setFechaHasta(e.target.value)
    }

    const [pedido, setPedido] = useState('')
    const [showPreviewPdf, setShowPreviewPdf] = useState('');
    const handleClosePreviewPdf = () => setShowPreviewPdf(false);
    const handleShowPreviewPdf = (p) => {
        setPedido(p)
        setShowPreviewPdf(true);
    }

    const [searchOk, setSearchOk] = useState(false)

    const convertirANumero = (n) =>{
        let split = n.split(/\.|\,/)
        if(split.length > 2){
            let aux = split[0]+split[1]
            let aux2 = parseFloat(aux+'.'+split[2])
            return aux2
        }else{
            let nuevo = parseFloat(split[0]+'.'+split[1])
            return nuevo
        }
    }

    const [isVendedor, setIsVendedor] = useState(false);

    useEffect(() => {
  const perfiles = localStorage.getItem("perfiles") 
    ? JSON.parse(localStorage.getItem("perfiles")) 
    : {};
  const profileValues = Object.values(perfiles);
  setIsVendedor(profileValues.includes(18) && !profileValues.includes(7));
}, []);

    const dispatch = useDispatch()

    const reset = (e) =>{
        dispatch(getPedidos('','','','','',''))
        setClienteid(0)
        setNumeroFactura('')
        setNrocomprobante('')
        setFechaDesde(ultimosDias.toISOString().substring(0,10))
        setFechaHasta(fechaActual.toISOString().substring(0,10))
        setNombrecliente('')
        handleClose()
    }

    const search = () =>{
        setSearchOk(true)
        setPage(1)
        dispatch(getPedidos(clienteid,numerofactura,nrocomprobante,nombrecliente,fechaDesde,fechaHasta,1))
        handleClose()
    }

    const downloadExcel = () => {
        const workSheet = XLSX.utils.json_to_sheet(pedidos)
        const workBook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workBook, workSheet, "info")
        let buffer = XLSX.write(workBook,{bookType:"xlsx", type:"buffer"})
        XLSX.write(workBook,{bookType:"xlsx", type:"binary"})
    
        XLSX.writeFile(workBook, "Ventas.xlsx")
    }

    const formatoFecha = (fecha) =>{
        let aux = fecha.split(' ')
        let aux2 = aux[0].split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]+' '+aux[1].slice(0,5)
    }

    const handleCheck = (e, facturaid) =>{
        e.preventDefault()
        if(e.target.checked){
            setPedidosFacturar(pedidosFacturar.concat(facturaid))
        }
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [openAlert, setOpenAlert] = React.useState(false);
    const handleClickAlert = () => {
      setOpenAlert(true);
    };
    const handleCloseAlert = () => {
        setOpenAlert(false);
    };

    const handleFacturarPedidos =()=> {
        dispatch(facturarVariosPedidos(pedidosFacturar))
        setTimeout(function(){
            handleClickAlert()
        }, 4000);
    }    

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[pedidos])

    useEffect(() =>{
        setLoading(true)
        dispatch(getPedidos(clienteid,numerofactura,nrocomprobante,nombrecliente,fechaDesde,fechaHasta,page))
        dispatch(limpiarPedido())
    },[page,okFacturarMasivamente])


    useEffect(() =>{
        if(searchOk === true && numerofactura === ''){
            dispatch(getPedidos(clienteid,numerofactura,nrocomprobante,nombrecliente,fechaDesde,fechaHasta,page))
            setSearchOk(false)
        }
    }, [search,numerofactura])

    return (
        <div className="ventas-container">
            <ModalVerPdf
                show={showPreviewPdf}
                handleClose={handleClosePreviewPdf}
                id={pedido}
            />
            {
                <ModalFiltrosVenta
                    show={show} 
                    handleClose={handleClose}
                    handleChangeClienteid={handleChangeClienteid}
                    clienteid={clienteid}
                    handleChangeNrocomprobante={handleChangeNrocomprobante} 
                    nrocomprobante={nrocomprobante}
                    setClienteId={setClienteid}
                    handleChangeFechaDesde={handleChangeFechaDesde}
                    fechaDesde={fechaDesde}
                    handleChangeFechaHasta={handleChangeFechaHasta}
                    fechaHasta={fechaHasta}
                    reset={reset}
                    search={search}
                />
            }
            <Snackbar
                open={openAlert} 
                autoHideDuration={6000} onClose={handleCloseAlert}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlert} 
                    severity={okFacturarMasivamente.success}
                    sx={{ width: 500 }}>
                    <h5>{okFacturarMasivamente.mensaje}</h5>
                </Alert>
            </Snackbar>
            <div className="titulo-ventas">
                    <h3  className="text-ventas">Pedidos</h3>
                <div className="text-ventas">
                    <h4>Los pedidos que se muestran se comprenden entre la fecha actual y 60 d&iacute;as para atr&aacute;s. 
                    </h4>
                </div>
                <div className='list-ventas' style={{display:"flex", width:"100%"}}>
                    <TextField
                        label="Buscar por numero de factura"  
                        variant="outlined" 
                        margin="normal" 
                        style={{width:600,marginLeft:40,marginRight:4}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17,
                            },
                        }}
                        name="numerofactura"
                        onChange={(e) => handleChangeNumeroFactura(e)}
                        value={numerofactura}
                    />
                    <Tooltip title="Buscar por n&uacute;mero de factura">
                        <Button 
                            variant="outlined" 
                            sx={{height:40,marginTop:2, marginRight:1}} 
                            onClick={search}
                        >
                            <Search/>
                        </Button>
                    </Tooltip>
                    <div style={{display:"flex", marginLeft:40}}>
                        <Button 
                            variant="outlined" 
                            sx={{height:40,width:150, marginTop:2}}
                            onClick={handleShow}>
                            <FilterListIcon/> Filtrar
                        </Button>
                        <Button variant="outlined" 
                        sx={{height:40,width:150, marginTop:2, marginLeft:5}} 
                        onClick={downloadExcel}>
                            <Download/> Exportar
                        </Button>
                        {/* <Button variant="outlined" 
                        disabled={pedidosFacturar.length === 0}
                        sx={{height:40,width:220, marginTop:2, marginLeft:5}} 
                        onClick={handleFacturarPedidos}>
                            <Description/> Facturar pedidos
                        </Button> */}
                        {!isVendedor && (
    <Button variant="outlined" 
    disabled={pedidosFacturar.length === 0}
    sx={{height:40,width:220, marginTop:2, marginLeft:5}} 
    onClick={handleFacturarPedidos}>
      <Description/> Facturar pedidos
    </Button>
  )}
                    </div>
                </div>
            </div>
           
            {
                permisos_acciones?.listar !== "0" && <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                <Table aria-label="simple table">
                <TableHead>
                    <TableRow>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}}>#</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%", padding:0}} align="center">Cliente</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Estado</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Fecha</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Total</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Pagos</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center" >Saldo</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Tipo pago</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Ver</TableCell>
                        {/* <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Facturar</TableCell> */}
                        {!isVendedor && (
      <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Facturar</TableCell>
    )}
                    </TableRow>
                </TableHead>
                <TableBody>
                {
                    loading ? 
                    <TableRow sx={{p:20}}>
                        <TableCell colSpan={13} align='center'>
                            <ClipLoader 
                                loading={loading}
                                size={50}
                            />
                        </TableCell>
                    </TableRow> :
                    pedidos.data.length > 0 ? pedidos.data.map((row) => (
                    <TableRow
                        key={row.pedidoplacaid}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 }}}
                    >
                        <TableCell style={{fontSize:"16px", width:"50px", height:"40px", padding: 3}} component="th" scope="row">
                            <Link to={`/Mitienda/Pedidos/${row.numerofactura}/${row.facturaid}`} >
                                {row.numerofactura}
                            </Link>
                        </TableCell>
                        <TableCell style={{fontSize:"14px", width:"90px", height:"40px", padding: 0}} align="center">
                            {row.nombrecliente}
                        </TableCell>
                        <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center">
                            {row.descripcionestado}
                        </TableCell>
                        <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center">
                            {formatoFecha(row.fechaingreso)}
                        </TableCell>
                        <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center">
                            ${convertirANumero(row.total).toFixed(2)}
                        </TableCell>
                        <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center">
                            ${convertirANumero(row.total_pago).toFixed(2)}
                        </TableCell>
                        <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center">
                            ${(convertirANumero(row.total)-convertirANumero(row.total_pago)).toFixed(2)}
                        </TableCell>
                        <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center">
                            {row.descripcionpago}
                        </TableCell>
                        <TableCell style={{fontSize:"14px", width:"50px", height:"40px", padding: 0}} align="center"
                        onClick={(e)=>handleShowPreviewPdf(row.numerofactura)}
                        > 
                            <RemoveRedEye/>
                        </TableCell>

                        {/* <TableCell style={{fontSize:"14px", width:"50px", height:"40px", padding: 0}} align="center">{row.facturado === 1 ? 
                        <a target="_blank" href={row.url_comprobante}>Factura</a> : 
                        <Checkbox onChange={(e)=> handleCheck(e, row.numerofactura)}
                        /> 
                        }</TableCell> */}
                        {!isVendedor && (
        <TableCell style={{fontSize:"14px", width:"50px", height:"40px", padding: 0}} align="center">{row.facturado === 1 ? 
        <a target="_blank" href={row.url_comprobante}>Factura</a> : 
        <Checkbox onChange={(e)=> handleCheck(e, row.numerofactura)}
        /> 
        }</TableCell>
      )}
                    </TableRow>
                    )) :
                    <TableRow>
                        <TableCell colSpan={10} align='center'>
                            <h5>No hay informaci&oacute;n para mostrar</h5>
                        </TableCell>
                    </TableRow>}
                </TableBody>
                </Table>
            </TableContainer>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
                </div>
            }
        </div>
    )
}