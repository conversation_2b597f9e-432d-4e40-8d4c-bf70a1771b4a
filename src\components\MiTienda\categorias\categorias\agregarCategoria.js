import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel, TextField } from "@mui/material";
import { configCategoria } from "../../../../redux/actions/mitienda.actions";
import { useDispatch } from "react-redux";
import { useEffect } from "react";

Modal.setAppElement("#root")

export const AgregarCategoria = ({show, handleClose, handleClickAlert}) =>{

  const [input, setInput] = useState({
    id: 0,
    nombrecategoria: "",
    descripcion: "",
    activo: "0",
    ecommerce: "0"
  })

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value})
  }

  const dispatch = useDispatch()

  const handleClick = () => {
    dispatch(configCategoria(input, 0))
    setInput({
      id: 0,
      nombrecategoria: "",
      descripcion: "",
      activo: "0",
      ecommerce: "0"
    })
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  const handleCheck = (e) =>{
    e.preventDefault()
    if(e.target.checked === true){
        setInput({...input, [e.target.name] : "1"})
    }else{
        setInput({...input, [e.target.name] : "0"})
    }
  }

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    useEffect(() => {
      setInput({
        id: 0,
        nombrecategoria: "",
        descripcion: "",
        activo: "0",
        ecommerce: "0"
      })
    },[])

    return (
        show ? 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
          <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
              <h4>Agregar categoria</h4>
              <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
          </div>
          <Divider/>
          <div style={{marginTop:15, marginBottom:15}}>
            <TextField 
                name="nombrecategoria"
                label="Nombre"  
                variant="outlined" 
                fullWidth margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                  }}
                inputProps={{
                    style: {
                      height: 40,
                      fontSize:17
                    },
                }}
                value={input.nombrecategoria}
                onChange={(e)=> handleChange(e)}
            />
            <TextField 
                name="descripcion"
                label="Descripcion (opcional)"  
                variant="outlined" 
                fullWidth margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                  }}
                inputProps={{
                    style: {
                      height: 40,
                      fontSize:17
                    },
                }}
                value={input.descripcion}
                onChange={(e)=> handleChange(e)}
            />
          </div>
          <div style={{marginTop:15, marginBottom:5}}>
            <FormControlLabel
                control={
                <Checkbox/>
                }
                style={{color:"black"}}
                onChange={(e)=> handleCheck(e)}
                checked={input.activo == "1" ? true : false}
                label="Activo"
                name="activo"
            />
          </div>
          <div style={{marginTop:5, marginBottom:5}}>
            <FormControlLabel
                control={
                <Checkbox/>
                }
                style={{color:"black"}}
                onChange={(e)=> handleCheck(e)}
                checked={input.ecommerce == "1" ? true : false}
                label="Mostrar categor&iacute;a"
                name="ecommerce"
            />
          </div>
          <Divider/>
          <div align="right">
          <Button 
            size="large" 
            variant="contained" 
            sx={{mt: 3}}
            onClick={handleClick}
            >Agregar</Button>
          </div>
        </Modal> : null 
    )
}