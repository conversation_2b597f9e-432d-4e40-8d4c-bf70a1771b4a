import {
  aumentarStock,
  cancelarFactura,
  grabarMovimiento,
} from "../actions/mitienda.actions";

const defaultState = {
  loading: false,
  infoTienda: {},
  okInfoTienda: false,
  abonos: [],
  generalOk: false,
  metodosDePago: false,
  okEnvioDomicilio: false,
  infoMetodosDepago: [],
  seo: false,
  okDefaultRetiroLocal: "",
  okConfigPickit: false,
  configPickit: "",

  tasas: [],
  tipoVenta: [],
  datosHome: "",
  locales: [],
  menuDinamico: [],
  nombreTienda: "",
  etiquetas: {},
  configChat: {},
  okChat: false,
  metodos: {},
  redesSociales: {},
  okProductoDesactivado: false,
  okRedesSociales: false,
  configMP: {},
  pedidos: {
    data: [],
    loading: true,
  },
  pedidosDetalle: {
    data: {},
    loading: false,
    error: null,
    paginaUltima: 1,
  },
  //   pedidosPendientesLiquidacion: {
  //   data: {},
  //   loading: false,
  //   error: null,
  //   paginaUltima: 1,
  // },
  remitosPendientesLiquidacion: {
    data: {},
    loading: false,
    paginaUltima: 1,
  },
  notificationRemitosPendientes: {
  data: [],
  loading: false,
  allLoaded: false
},
   liquidaciones: {
    loading: false,
    error: null,
    data: null,
    paginaUltima: 1,
    paginaSiguiente: null,
    paginaPrevia: null,
  },
  liquidacion: {
    loading: false,
    data: [],
    rawData: null,
  },
  lista_estado_liquidacion: [],
  cambio_estado_liquidacion: {
    loading: false,
    success: false,
    mensaje: ""
  },
  comprobantesUpload: {
    loading: false,
    error: null,
    success: false
  },
  comprobantes: {
    loading: false,
    error: null,
    data: null
  },

  pedidosGrafico: [],
  liquidacionesGrafico: [],
  pedido: {
    data: "",
    loading: true,
  },
  pedidoDetails: {},
  lista_estado_pedido: [],
  paginaUltimaPedidos: "",

  entregas: {
    data: {},
    loading: true,
  },
  paginaUltimaEntregas: 1,

  remitos: {
    remitos: {},
    loading: true,
  },
  paginaUltimaRemitos: 1,

  remito: {
    loading: true,
    data: [],
  },

  cambio_estado_remito: null,
  editar_fecha_entrega_remito: null,

  okFechaEntrega: {
    success: false,
    mensaje: "",
  },

  stock: [],
  paginaUltimaStock: "",
  pedidoCancelado: "",
  defaultMp: {},
  metodosModificados: "",
  datostienda: {},
  paginaUltimaMiPlan: "",
  paginaUltimaMarcasTienda: "",
  okCambiarEstadoPedido: "",
  pagosRealizados: "",
  registrarPago: "",

  productos: {
    data: [],
    loading: true,
  },
  productosByName: [],
  paginaUltimaProductos: "",
  producto: {},
  productoById: "",
  productoBySku: [],
  productosFiltrados: [],
  productosByTalla: [],
  okProducto: "",
  okAgregarProducto: false,
  okProductoEditado: false,
  eliminarImagenProducto: "",
  okAsociarProducto: false,
  okAumentoMasivo: { success: false, mensaje: "" },
  descuentos: [],
  descuentosSinPaginado: [],
  okDescuento: { success: false, mensaje: "" },
  okEditarDescuento: { success: false, mensaje: "" },
  paginaUltimaDescuentos: "",
  rentabilidad: [],
  okRentabilidad: "",
  okEditarRentabilidad: "",
  okEliminarRentabilidad: "",
  paginaUltimaRentabilidad: "",

  categorias: [],
  agregarImagenCategoria: false,
  paginaUltimaCategoria: "",
  okConfigCategoria: false,
  okConfigSubCategoria: false,
  categoria: {},
  okCategoria: false,
  editarCategoria: false,
  categoriasSinPag: [],
  subcategoria: {},
  okSubCategoria: false,
  editarSubCategoria: false,
  desactivarCategoria: false,
  desactivarSubCategoria: false,

  clientes: [],
  listadoDeClientes: {
    data: [],
    loading: true,
  },
  paginaUltimaClientes: "",
  editarCliente: "",
  activarDesactivarCliente: "",
  condicionesIva: [],
  condicionesPago: [],
  tiposDocumentos: [],
  // okAgregarCliente: "",
  okAgregarCliente: {
    success: false,
    mensaje: "",
  },

  // liquidaciones: {
  //     data: {},
  //     loading: false,
  //     error: null
  //   },

  marcas: [],
  marcas_tienda_paginado: [],
  okConfigMarca: false,
  eliminarMarcaTienda: false,
  editarMarcaTienda: false,
  agregarImagenMarca: false,
  eliminarImagenMarca: "",
  eliminarImagenCategoria: "",

  configParametros: "",
  okConfigParametros: "",
  agregarImagenesParametros1: true,
  agregarImagenesParametros2: true,

  logo: "",
  okLogo: true,

  tallas: [],
  tallas_paginado: [],
  paginaUltimaTallas: "",
  okConfigTalla: false,
  eliminarTalla: "",
  editarTalla: "",

  colores: [],
  colores_paginado: [],
  paginaUltimaColoresPaginado: "",
  okConfigColorTienda: "",
  eliminarColorTienda: "",
  editarColorTienda: "",

  monedas: [],
  monedas_paginado: [],
  paginaUltimaMonedasPaginado: "",
  okConfigMoneda: "",
  eliminarMoneda: "",
  editarMoneda: "",

  subcategorias: [],
  info_pedido_pickit: {
    data: "",
    loading: true,
  },
  url_pickit: {
    data: "",
    loading: true,
  },
  provincias: [],
  provincia: "",
  ciudad: "",

  proveedores: {
    data: [],
    loading: true,
  },
  paginaUltimaProveedores: "",
  okConfigProveedor: "",
  editarProveedor: "",

  cotizaciones: [],
  paginaUltimaCotizaciones: "",
  okCotizacion: { success: false, mensaje: "" },
  okEditarCotizacion: { success: false, mensaje: "" },
  productosNuevaVenta: [],

  //------ REPORTES --------------------
  reporteListaDePrecios: {
    data: [],
    loading: true,
  },
  subdiarioCompras: {
    data: [],
    loading: true,
    totales: {
      total_gravado: "0,00",
      total_iva: "0,00",
      total_nogravado: "0,00",
      total_otros: "0,00",
      total_gral: "0,00",
    },
  },
  subdiarioVentas: {
    data: [],
    loading: true,
    totales: {
      total_gravado: "0,00",
      total_iva: "0,00",
      total_nogravado: "0,00",
      total_otros: "0,00",
      total_gral: "0,00",
    },
  },
  stockVendido: {
    data: [],
    loading: true,
  },
  facturasElectronicas: {
    data: [],
    loading: true,
  },
  paginaUltimaFacturasElectronicasPaginado: 0,
  paginaUltimaEstilos: "",
  estilos: [],
  punto_pickit: "",

  //-------------- FACTURA -------------------
  okFacturar: "",
  okFacturarMasivamente: "",
  okConfigAfip: "",
  configAfip: "",
  okConfigCertificadosAfip: "",

  //-------------- NUEVA VENTA ---------------
  okAgregarArticulo: "",
  detalleCarrito: [],
  calcularCarrito: "",
  paginaUltimaDetalleCarrito: "",
  tiposDePago: [],
  tiposDeTarjetas: [],
  cuotasTarjetas: [],
  okFinalizarPedido: "",
  okEliminarArticulo: "",
  clientesByName: [],
  clienteById: "",
  cotizacionEnvioADomicilio: "",
  transaccionEnvioADomicilio: "",
  modificarDescuentoArticulo: "",
  modificarCantidadArticulo: "",
  modificarProductoEditable: "",
  puntosDeRetiroPickit: [],
  transaccionEnvioAPunto: "",
  cotizacionEnvioAPunto: "",
  presupuesto: "",
  presupuestos: {
    data: [],
    loading: true,
  },
  paginaUltimaPresupuestos: " ",
  okCrearPresupuesto: "",
  okCrearDetallePresupuesto: "",
  okEditarDetallePresupuesto: "",
  okClonarPresupuesto: "",
  okPedidoPresupuesto: "",
  enviarEmail: "",
  detallePresupuesto: "",

  promociones: [],
  paginaUltimaPromocion: 0,
  okAgregarPromocion: "",
  okEditarPromocion: "",
  okEliminarPromocion: "",
  productosPromocion: [],
  paginaUltimaProductosPromocion: "",
  okAgregarProductoPromocion: "",
  okEditarProductoPromocion: "",
  okEliminarProductoPromocion: "",

  productosHome: [],
  paginaUltimaProductosHome: 1,
  okAgregarProductoHome: "",
  okEliminarProductoHome: "",

  productsByNameSkuPromocion: [],
  pedidoPuntoVenta: "",
  okSendMessage: "",

  datosFooter: [],
  okAgregarDatosFooter: "",
  okAgregarSubtitulo: "",
  okEditarDatosFooter: "",
  okEliminarDatosFooter: "",
  messagesByPedido: "",

  urlMercadoPago: "",

  reportePorVendedor: {
    reporte: [],
    totales: {
      comisionvta: 0,
      totalfactura: 0,
      totalcomision: 0,
      total_mas_comision: 0,
    },
  },
  vendedores: [],
  itemsFactura: [],
  paginaUltimaItemsFacturaProveedor: 0,
  reporteRentabilidadProductos: {
    data: [],
    loading: true,
  },
  paginaUltimaReporteRentabilidadProductos: 0,
  okAgregarFacturaProveedor: "",
  okEditarFacturaProveedor: "",
  okRegistrarPagoFacturaProveedor: "",
  okEliminarFacturaProveedor: "",
  okEditarItemFacturaProveedor: "",
  cuentasBanco: [],
  chequesBanco: [],
  tiposChequesBanco: [],
  tarjetas: [],
  tiposDeTarjetas: [],
  pagosRealizadosFacturaProveedor: [],
  paginaUltimapagosRealizadosFacturaProveedor: 0,
  conceptosProveedor: [],

  reporteCuentaCorrienteDeudas: {
    data: [],
    loading: true,
  },
  paginaUltimaReporteCuentaCorrienteDeudas: 0,

  listadoArchivos: [],
  okCrearEncabezado: "",
  okSubirArchivo: "",
  okProcesarListado: "",
  proveedoresSinPaginado: [],
  okEditarEncabezado: "",

  listadoArchivosListaPrecio: [],
  ultimaPaginalistadoArchivosListaPrecio: 0,
  okCrearEncabezadoListaPrecio: "",
  okSubirArchivoListaPrecio: "",
  okProcesarListadoListaPrecio: "",
  proveedoresSinPaginadoListaPrecio: [],
  okEditarEncabezadoListaPrecio: "",

  okAgregarItemFacturaProveedor: "",

  estadoCaja: "",
  totalCaja: 0,
  okGrabarMovimiento: "",

  cuentasCorrientes: {
    data: [],
    loading: true,
  },
  paginaUltimaCuentasCorrientes: 0,
  detalleCuentaCorriente: [],
  detalleCuentaCorrienteCliente: "",
  paginaUltimaDetalleCuentaCorriente: 0,
  okPagoCuentaCorriente: "",
  okPagoCuentaCorrienteCheque: "",
  okPagoCuentaCorrienteTarjeta: "",
  pagosCuentaCorriente: [],
  paginaUltimaPagosCuentaCorriente: 0,
  okEliminarPagoCuentaCorriente: "",
  okAfectarPagoCuentaCorriente: "",

  proveedoresByName: [],
  cuentasCorrientesProveedor: {
    data: [],
    loading: true,
  },

  cuentasCorrientes: {
    data: [],
    loading: true,
    total: 0,
  },
  paginaUltimaCuentasCorrientes: 1,

  paginaUltimaCuentasCorrientesProveedor: 0,
  detalleCuentaCorrienteProveedor: [],
  detalleCuentaCorrienteClienteProveedor: "",
  paginaUltimaDetalleCuentaCorrienteProveedor: 0,
  okPagoCuentaCorrienteProveedor: "",
  okPagoCuentaCorrienteChequeProveedor: "",
  okPagoCuentaCorrienteTarjetaProveedor: "",
  pagosCuentaCorrienteProveedor: [],
  paginaUltimaPagosCuentaCorrienteProveedor: 0,
  okEliminarPagoCuentaCorrienteProveedor: "",
  okAfectarPagoCuentaCorrienteProveedor: "",
  okCancelarFactura: "",
  okTraerFactura: "",
  datosTiendaById: "",
  okFacturarPuntoVenta: "",
  categoriasByName: [],
  marcasByName: [],
  coloresByName: [],
  tallasByName: [],
  productosStock: {
    data: [],
    loading: true,
  },
  okAumentarStock: "",
  okRestarStock: "",
  okRestarStockReservado: "",
  paginaUltimaProductosStock: 0,
  okSubirArchivoImportaStock: "",
  facturasProveedor: {
    data: [],
    loading: true,
  },
  messagesByProductoID: [],
  okSendMessageProducto: "",

  rentabilidadCategorias: [],
  paginaUltimaCategoriaRentabilidad: "",
  okEliminarCategoriaRentabilidad: "",
  okCategoriaRentabilidad: "",
};

const mitiendaReducer = (state = defaultState, { type, payload }) => {
  switch (type) {
    case "GET_LOGO":
      return { ...state, logo: payload };
    case "AGREGAR_IMAGEN_LOGO":
      return { ...state, okLogo: payload };
    case "CONFIG_TIENDA":
      return { ...state, generalOk: payload };
    case "GET_CONFIG_TIENDA":
      return {
        ...state,
        okInfoTienda: payload.success,
        infoTienda: payload.confingtienda,
      };
    case "GET_ABONOS":
      let paginaAuxAbonos = payload.pop();
      return { ...state, abonos: payload, paginaUltimaMiPlan: paginaAuxAbonos };
    case "CONFIG_PAGOS":
      return { ...state, metodosDePago: payload };
    case "GET_METODOS_PAGO":
      return { ...state, infoMetodosDepago: payload };
    case "GET_METODOS_ENVIO":
      return { ...state, metodos: payload };
    case "CONFIG_ENVIO_DOMICILIO":
      return { ...state, okEnvioDomicilio: payload };
    case "CONFIG_RETIRO_LOCAL":
      return { ...state, okDefaultRetiroLocal: payload };
    case "CONFIG_PICKIT":
      return { ...state, okConfigPickit: payload };
    case "GET_CONFIG_PICKIT":
      return { ...state, okConfigPickit: payload };
    case "CONFIG_CHAT":
      return { ...state, okChat: payload.success };
    case "LIMPIAR_CHAT":
      return { ...state, okChat: payload };
    case "CONFIG_REDES":
      return { ...state, okRedesSociales: payload.success };
    case "LIMPIAR_REDES_SOCIALES":
      return { ...state, okRedesSociales: payload };
    case "LIMPIAR_ETIQUETAS":
      return { ...state, seo: payload };
    case "GET_REDES_SOCIALES":
      return { ...state, redesSociales: payload };
    case "GET_CATEGORIAS":
      let pagina = payload.pop();
      return { ...state, categorias: payload, paginaUltimaCategoria: pagina };
    case "GET_SUB_CATEGORIAS":
      return { ...state, subcategoria: payload, okSubCategoria: true };
    case "GET_SUB_CATEGORIAS_BY_CATEGORIA":
      return { ...state, subcategorias: payload };
    case "CONFIG_CATEGORIA":
      return { ...state, okConfigCategoria: payload };
    case "EDITAR_CATEGORIA":
      return { ...state, editarCategoria: payload };
    case "LIMPIAR_EDITAR_CATEGORIA":
      return { ...state, editarCategoria: payload };
    case "GET_CATEGORIA":
      return { ...state, categoria: payload, okCategoria: true };
    case "GET_CATEGORIAS_SIN_PAG":
      return { ...state, categoriasSinPag: payload };
    case "DESACTIVAR_CATEGORIA":
      return { ...state, desactivarCategoria: payload };
    case "LIMPIAR_DESACTIVADO":
      return { ...state, okProductoDesactivado: payload };
    case "LIMPIAR_GENERAL":
      return { ...state, generalOk: payload };
    case "LIMPIAR_OK":
      return { ...state, desactivarCategoria: payload };
    case "LIMPIAR_SUBCATEGORIA_OK":
      return { ...state, desactivarSubCategoria: payload };
    case "DESACTIVAR_SUBCATEGORIA":
      return { ...state, desactivarSubCategoria: payload };
    case "CONFIG_SUB_CATEGORIA":
      return { ...state, okConfigSubCategoria: payload };
    case "EDITAR_SUB_CATEGORIA":
      return { ...state, editarSubCategoria: payload };
    case "LIMPIAR_EDITAR_SUB_CATEGORIA":
      return { ...state, editarSubCategoria: payload };
    case "LIMPIAR_SUBCATEGORIA_CREADA":
      return { ...state, okConfigSubCategoria: false };
    case "AGREGAR_IMAGEN_CATEGORIA":
      return { ...state, agregarImagenCategoria: payload };
    case "LIMPIAR_AGREGAR_IMAGEN_CATEGORIA":
      return { ...state, agregarImagenCategoria: payload };
    case "CONFIG_SEO":
      return { ...state, seo: payload };
    case "AGREGAR_PRODUCTO":
      return { ...state, okAgregarProducto: payload };
    case "CLONAR_PRODUCTO":
      return { ...state, okClonarProducto: payload };
    case "LIMPIAR_CLONAR_PRODUCTO":
      return { ...state, okClonarProducto: payload };
    case "ASOCIAR_PRODUCTO":
      return { ...state, okAsociarProducto: payload };
    case "LIMPIAR_ASOCIAR_PRODUCTO":
      return { ...state, okAsociarProducto: payload };
    case "GET_PRODUCTOS":
      let paginaProd = payload.pop();
      return {
        ...state,
        productos: {
          data: payload,
          loading: false,
        },
        paginaUltimaProductos: paginaProd,
      };
    case "GET_PRODUCTOS_FILTRADOS":
      let paginaFiltrados = payload.pop();
      return {
        ...state,
        productosFiltrados: payload,
        paginaUltimaProductos: paginaFiltrados,
      };
    case "GET_PRODUCTOS_BY_NAME":
      let paginaAux = payload.pop();
      return {
        ...state,
        productosByName: payload,
        paginaUltimaProductos: paginaAux,
      };
    case "GET_PRODUCTOS_TALLAS":
      let paginaAuxTallas = payload.pop();
      return {
        ...state,
        productosByTalla: payload,
        paginaUltimaProductos: paginaAuxTallas,
      };
    case "GET_PRODUCTO_BY_SKU":
      return { ...state, productoBySku: payload, paginaUltimaProductos: 1 };
    case "GET_PRODUCTO_BY_ID":
      return { ...state, productoById: payload, okProducto: true };
    case "DESACTIVAR_PRODUCTO":
      return { ...state, productoById: payload, okProductoDesactivado: true };
    case "AUMENTO_MASIVO":
      return { ...state, okAumentoMasivo: payload };
    case "LIMPIAR_AUMENTO_MASIVO":
      return { ...state, okAumentoMasivo: payload };
    case "LIMPIAR_PRODUCTO_EDITADO":
      return { ...state, okProductoEditado: false };
    case "LIMPIAR_PRODUCTOS":
      return { ...state, productoBySku: payload, productosByName: payload };
    case "LIMPIAR_PRODUCTO_BY_SKU":
      return { ...state, productoBySku: payload };
    case "LIMPIAR_PRODUCTOS_BY_NAME":
      return { ...state, productosByName: payload };
    case "LIMPIAR_ENVIO_CLIENTE":
      return { ...state, okEnvioDomicilio: payload };
    case "EDITAR_PRODUCTO":
      return { ...state, producto: payload, okProductoEditado: true };
    case "LIMPIAR_PRODUCTO_EDITADO":
      return { ...state, okProductoEditado: payload };
    case "LIMPIAR_AGREGADO":
      return { ...state, okAgregarProducto: payload };
    case "GET_TASAS_IVA":
      return { ...state, tasas: payload };
    case "GET_TIPO_VENTA":
      return { ...state, tipoVenta: payload };
    case "GET_DATOS_HOME":
      return { ...state, datosHome: payload };
    case "GET_LOCALES":
      return { ...state, locales: payload };
    case "GET_MENU_DINAMICO":
      return { ...state, menuDinamico: payload };
    case "GET_NOMBRE_TIENDA":
      return { ...state, nombreTienda: payload };
    case "GET_ETIQUETAS":
      return { ...state, etiquetas: payload };
    case "GET_CONFIG_CHAT":
      return { ...state, configChat: payload };
    case "GET_CONFIG_MP":
      return { ...state, configMP: payload };
    case "GET_PEDIDOS":
      let paginaUltimaPedidosAux = payload.pop();
      return {
        ...state,
        pedidos: {
          data: payload,
          loading: false,
        },
        paginaUltimaPedidos: paginaUltimaPedidosAux,
      };
    case "GET_PEDIDOS_DETALLE":
      return {
        ...state,
        pedidosDetalle: {
          ...state.pedidosDetalle,
          data: payload.detalle,
          paginaUltima: payload.paginaUltima,
          loading: false,
          error: null,
        },
      };
    // case "GET_PEDIDOS_PENDIENTES_LIQUIDACION":
    //   return {
    //     ...state,
    //     pedidosPendientesLiquidacion: {
    //       ...state.pedidosPendientesLiquidacion,
    //       data: payload.detalle,
    //       paginaUltima: payload.paginaUltima,
    //       loading: false,
    //       error: null,
    //     },
    //   };
    case "GET_REMITOS_PENDIENTES_LIQUIDACION":
  return {
    ...state,
    remitosPendientesLiquidacion: {
      ...state.remitosPendientesLiquidacion,
      data: payload.data,
      paginaUltima: payload.paginaUltima,
      loading: false,
    },
  };

case "GET_REMITOS_PENDIENTES_LIQUIDACION_REQUEST":
  return {
    ...state,
    remitosPendientesLiquidacion: {
      ...state.remitosPendientesLiquidacion,
      loading: true,
    },
  };

case "GET_REMITOS_PENDIENTES_LIQUIDACION_FAILURE":
  return {
    ...state,
    remitosPendientesLiquidacion: {
      ...state.remitosPendientesLiquidacion,
      loading: false,
    },
  };
  case "GET_LIQUIDACIONES_REQUEST":
  return {
    ...state,
    liquidaciones: {
      ...state.liquidaciones,
      loading: true,
      error: null,
    },
  };

case "GET_LIQUIDACIONES_SUCCESS":
  return {
    ...state,
    liquidaciones: {
      ...state.liquidaciones,
      loading: false,
      data: payload.data,
      paginaUltima: payload.paginaUltima,
      paginaSiguiente: payload.paginaSiguiente,
      paginaPrevia: payload.paginaPrevia,
    },
  };

case "GET_LIQUIDACIONES_ERROR":
  return {
    ...state,
    liquidaciones: {
      ...state.liquidaciones,
      loading: false,
      error: payload,
      data: null,
    },
  };
    case "GET_PEDIDOS_GRAFICO":
      return { ...state, pedidosGrafico: payload };
      case "GET_LIQUIDACIONES_GRAFICO":
  return { ...state, liquidacionesGrafico: payload };
    case "CAMBIAR_ESTADO_PEDIDO":
      return { ...state, okCambiarEstadoPedido: payload };
    case "GET_PEDIDO":
      return { ...state, pedido: payload };
    case "GET_PEDIDO_DETAILS":
      return { ...state, pedidoDetails: payload };
    case "GET_ESTADOS_PEDIDO":
      return { ...state, 
        lista_estado_pedido: payload,
      lista_estado_liquidacion: payload
      };
    case "LIMPIAR_OK":
      return { ...state, pedido: payload };
    // -------------------  REMITOS
    case "GET_REMITOS":
      return {
        ...state,
        remitos: payload,
        paginaUltimaRemitos: payload.paginaUltima || 1,
      };
    case "GET_REMITO":
      return {
        ...state,
        remito: payload,
      };
    case "CAMBIAR_ESTADO_REMITO":
      return {
        ...state,
        cambio_estado_remito: payload,
      };
    case "EDITAR_FECHA_ENTREGA_REMITO":
      return {
        ...state,
        editar_fecha_entrega_remito: payload,
      };
    case "GENERATE_LIQUIDACION_SUCCESS":
      return {
        ...state,
        loading: false,
        error: null,
      };

    case "GENERATE_LIQUIDACION_ERROR":
      return {
        ...state,
        loading: false,
        error: payload.error,
      };
    case "GET_ENTREGAS":
      return {
        ...state,
        entregas: {
          data: payload.detalle,
          loading: payload.loading,
        },
        paginaUltimaEntregas: payload.paginaUltima,
      };
    case "GET_ENTREGAS_REQUEST":
      return {
        ...state,
        entregas: {
          ...state.entregas,
          loading: true,
        },
      };

    case "GET_ENTREGAS_SUCCESS":
      return {
        ...state,
        entregas: {
          data: payload.detalle,
          loading: false,
        },
        paginaUltimaEntregas: payload.paginaUltima,
      };

    case "GET_ENTREGAS_FAILURE":
      return {
        ...state,
        entregas: {
          ...state.entregas,
          loading: false,
          error: payload,
        },
      };
    case "EDITAR_FECHA_ENTREGA":
      return {
        ...state,
        okFechaEntrega: payload,
      };
    case "GET_STOCK":
      let paginaUltimaStockAux = payload.pop();
      return {
        ...state,
        stock: payload,
        paginaUltimaStock: paginaUltimaStockAux,
      };
    case "GET_TALLAS":
      return { ...state, tallas: payload };
    case "CANCELAR_PEDIDO":
      return { ...state, pedidoCancelado: payload };
    case "LIMPIAR_PEDIDO_CANCELADO":
      return { ...state, pedidoCancelado: payload };
    case "LIMPIAR_CATEGORIA":
      return { ...state, okConfigCategoria: payload };
    case "GET_DEFAULT":
      return { ...state, defaultMp: payload };
    case "GET_MODIFICAR":
      return { ...state, metodosModificados: payload };
    case "LIMPIAR_CONFIG_METODOS":
      return { ...state, metodosModificados: payload };
    case "GET_CONDICIONES_PAGO":
      return { ...state, condicionesPago: payload };
    case "GET_CONDICIONES_IVA":
      return { ...state, condicionesIva: payload };
    case "GET_PAGOS_REALIZADOS":
      return { ...state, pagosRealizados: payload };
    case "GET_TIPOS_DOCUMENTO_TIENDA":
      return { ...state, tiposDocumentos: payload };
    case "GET_DATOS_TIENDA":
      return { ...state, datostienda: payload };
    case "LIMPIAR_RETIRO":
      return { ...state, okDefaultRetiroLocal: payload };
    case "ELIMINAR_IMAGEN_PRODUCTO":
      return { ...state, eliminarImagenProducto: payload };
    case "LIMPIAR_ELIMINAR_IMAGEN_PRODUCTO":
      return { ...state, eliminarImagenProducto: payload };
    case "REGISTRAR_PAGO":
      return { ...state, registrarPago: payload };
    case "LIMPIAR_REGISTRAR_PEDIDO":
      return { ...state, registrarPago: payload };
    case "ELIMINAR_IMAGEN_CATEGORIA":
      return { ...state, eliminarImagenCategoria: payload };
    case "LIMPIAR_ELIMINAR_IMAGEN_CATEGORIA":
      return { ...state, eliminarImagenCategoria: payload };
    case "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA":
      return { ...state, productosNuevaVenta: payload };
    //------------------- MARCAS ----------------------------------
    case "GET_MARCAS":
      return { ...state, marcas: payload };
    case "CONFIG_MARCA_TIENDA":
      return { ...state, okConfigMarca: payload };
    case "LIMPIAR_AGREGAR_MARCA_TIENDA":
      return { ...state, okConfigMarca: payload };
    case "EDITAR_MARCA_TIENDA":
      return { ...state, editarMarcaTienda: payload };
    case "LIMPIAR_EDITAR_MARCA_TIENDA":
      return { ...state, editarMarcaTienda: payload };
    case "ELIMINAR_MARCA_TIENDA":
      return { ...state, eliminarMarcaTienda: payload };
    case "AGREGAR_IMAGEN_MARCA":
      return { ...state, agregarImagenMarca: payload };
    case "LIMPIAR_AGREGAR_IMAGEN_MARCA":
      return { ...state, agregarImagenMarca: payload };
    case "ELIMINAR_IMAGEN_MARCA":
      return { ...state, eliminarImagenMarca: payload };
    case "LIMPIAR_ELIMINAR_MARCA_TIENDA":
      return { ...state, eliminarMarcaTienda: payload };
    case "LIMPIAR_ELIMINAR_IMAGEN_MARCA":
      return { ...state, eliminarImagenMarca: payload };
    case "GET_MARCAS_TIENDA_PAGINADO":
      let paginaUltimaMarcasAux = payload.pop();
      return {
        ...state,
        marcas_tienda_paginado: payload,
        paginaUltimaMarcasTienda: paginaUltimaMarcasAux,
      };
    //------------------- CLIENTES --------------------------------
    // case "AGREGAR_CLIENTE":
    //   return { ...state, okAgregarCliente: payload };
    case "AGREGAR_CLIENTE":
      return {
        ...state,
        okAgregarCliente: {
          success: true,
          mensaje: payload.mensaje,
        },
      };
    case "AGREGAR_CLIENTE_ERROR":
      return {
        ...state,
        okAgregarCliente: {
          success: false,
          mensaje: payload.mensaje,
        },
      };
    case "EDITAR_CLIENTE":
      return { ...state, editarCliente: payload };
    case "ACTIVAR_DESACTIVAR_CLIENTE":
      return { ...state, activarDesactivarCliente: payload };
    case "GET_CLIENTES":
      return { ...state, clientes: payload };
    case "GET_LISTADO_CLIENTES":
      let paginaUltimaClientesAux = payload.pop();
      return {
        ...state,
        listadoDeClientes: {
          data: payload,
          loading: false,
        },
        paginaUltimaClientes: paginaUltimaClientesAux,
      };
    //------------------- PARAMETROS ------------------------------
    case "GET_CONFIG_PARAMETROS_TIENDA":
      return { ...state, configParametros: payload };
    case "CONFIG_PARAMETROS_TIENDA":
      return { ...state, okConfigParametros: payload };
    case "AGREGAR_IMAGEN_PARAMETROS1":
      return { ...state, agregarImagenesParametros1: payload };
    case "AGREGAR_IMAGEN_PARAMETROS2":
      return { ...state, agregarImagenesParametros2: payload };
    //------------------- DESCUENTOS -------------------------------
    case "GET_DESCUENTOS":
      let paginaUltimaDescuentosAux = payload.pop();
      return {
        ...state,
        descuentos: payload,
        paginaUltimaDescuentos: paginaUltimaDescuentosAux,
      };
    case "GET_DESCUENTOS_SIN_PAGINADO":
      return { ...state, descuentosSinPaginado: payload };
    case "AGREGAR_DESCUENTOS":
      return { ...state, okDescuento: payload };
    case "EDITAR_DESCUENTO":
      return { ...state, okEditarDescuento: payload };
    //--------------------  RENTABILIDAD ---------------------------
    case "GET_RENTABILIDAD":
      let paginaUltimaRentabilidadAux = payload.pop();
      return {
        ...state,
        rentabilidad: payload,
        paginaUltimaRentabilidad: paginaUltimaRentabilidadAux,
      };
    case "AGREGAR_RENTABILIDAD":
      return { ...state, okRentabilidad: payload };
    case "EDITAR_RENTABILIDAD":
      return { ...state, okEditarRentabilidad: payload };
    case "ELIMINAR_RENTABILIDAD":
      return { ...state, okEliminarRentabilidad: payload };
    case "ELIMINAR_CATEGORIA_RENTABILIDAD":
      return { ...state, okEliminarCategoriaRentabilidad: payload };
    case "GET_CATEGORIAS_RENTABILIDAD":
      return {
        ...state,
        rentabilidadCategorias: Object.values(payload.categorias_rentabilidad),
        paginaUltimaCategoriaRentabilidad: payload.paginaUltima,
      };
    case "AGREGAR_CATEGORIA_RENTABILIDAD":
      return { ...state, okCategoriaRentabilidad: payload };
    //--------------------- TALLA ------------------------------------
    case "CONFIG_TALLA":
      return { ...state, okConfigTalla: payload };
    case "LIMPIAR_AGREGAR_TALLA":
      return { ...state, okConfigTalla: payload };
    case "EDITAR_TALLA":
      return { ...state, editarTalla: payload };
    case "LIMPIAR_EDITAR_TALLA":
      return { ...state, editarTalla: payload };
    case "ELIMINAR_TALLA":
      return { ...state, eliminarTalla: payload };
    case "LIMPIAR_ELIMINAR_TALLA":
      return { ...state, eliminarMarcaTienda: payload };
    case "GET_TALLAS_PAGINADO":
      let paginaUltimaTallasAux = payload.pop();
      return {
        ...state,
        tallas_paginado: payload,
        paginaUltimaTallas: paginaUltimaTallasAux,
      };
    //--------------------- COLORES -------------------------------------
    case "CONFIG_COLOR_TIENDA":
      return { ...state, okConfigColorTienda: payload };
    case "LIMPIAR_AGREGAR_COLOR_TIENDA":
      return { ...state, okConfigColorTienda: payload };
    case "EDITAR_COLOR_TIENDA":
      return { ...state, editarColorTienda: payload };
    case "LIMPIAR_EDITAR_COLOR_TIENDA":
      return { ...state, editarColorTienda: payload };
    case "ELIMINAR_COLOR_TIENDA":
      return { ...state, eliminarColorTienda: payload };
    case "LIMPIAR_ELIMINAR_COLOR_TIENDA":
      return { ...state, eliminarColorTienda: payload };
    case "GET_COLORES_TIENDA":
      return { ...state, colores: payload };
    case "GET_COLORES_TIENDA_PAGINADO":
      let paginaUltimaColoresAux = payload.pop();
      return {
        ...state,
        colores_paginado: payload,
        paginaUltimaColoresPaginado: paginaUltimaColoresAux,
      };
    //----------------------- MONEDAS -----------------------------------------
    case "CONFIG_MONEDA":
      return { ...state, okConfigMoneda: payload };
    case "LIMPIAR_AGREGAR_MONEDA":
      return { ...state, okConfigMoneda: payload };
    case "EDITAR_MONEDA":
      return { ...state, editarMoneda: payload };
    case "LIMPIAR_EDITAR_MONEDA":
      return { ...state, editarMoneda: payload };
    case "ELIMINAR_MONEDA":
      return { ...state, eliminarMoneda: payload };
    case "LIMPIAR_ELIMINAR_MONEDA":
      return { ...state, eliminarMoneda: payload };
    case "GET_MONEDAS":
      return { ...state, monedas: payload };
    case "GET_MONEDAS_PAGINADO":
      let paginaUltimaMonedasAux = payload.pop();
      return {
        ...state,
        monedas_paginado: payload,
        paginaUltimaMonedasPaginado: paginaUltimaMonedasAux,
      };
    //--------------------- PROVEEDORES ------------------------------------
    case "AGREGAR_PROVEEDOR_TIENDA":
      return { ...state, okConfigProveedor: payload };
    case "EDITAR_PROVEEDOR_TIENDA":
      return { ...state, editarProveedor: payload };
    case "GET_PROVEEDORES_TIENDA":
      let paginaUltimaProveedoresAux = payload.pop();
      return {
        ...state,
        proveedores: {
          data: payload,
          loading: false,
        },
        paginaUltimaProveedores: paginaUltimaProveedoresAux,
      };
    //------------------- COTIZACIONES  -------------------------------
    case "GET_COTIZACIONES":
      let paginaUltimaCotizacionesAux = payload.pop();
      return {
        ...state,
        cotizaciones: payload,
        paginaUltimaCotizaciones: paginaUltimaCotizacionesAux,
      };
    case "AGREGAR_COTIZACION":
      return { ...state, okCotizacion: payload };
    case "EDITAR_COTIZACION":
      return { ...state, okEditarCotizacion: payload };
    //--------------------------------- REPORTES ---------------------------------
    case "GET_REPORTE_LISTA_PRECIOS":
      //aca obtengo la pagina ultima para el paginado que se guardo al obtener la info del endpoint
      let paginaUltimaReporteListaDePreciosAux = payload.pop();
      //aca asocio las variables con sus valores
      return {
        ...state,
        reporteListaDePrecios: {
          data: payload,
          loading: false,
        },
        paginaUltimaListaDePreciosPaginado:
          paginaUltimaReporteListaDePreciosAux,
      };

    case "GET_SUBDIARIO_COMPRAS":
      //aca obtengo la pagina ultima para el paginado que se guardo al obtener la info del endpoint
      //aca asocio las variables con sus valores
      return {
        ...state,
        subdiarioCompras: {
          data: Object.values(payload.subdiariocompra),
          totales: payload.totales,
          loading: false,
        },
        paginaUltimaSubdiarioComprasPaginado: payload.paginaUltima,
      };

    case "GET_SUBDIARIO_VENTAS":
      return {
        ...state,
        subdiarioVentas: {
          data: Object.values(payload.subdiarioventa),
          totales: payload.totales,
          loading: false,
        },
        paginaUltimaSubdiarioVentasPaginado: payload.paginaUltima,
      };

    case "GET_STOCK_VENDIDO":
      //aca obtengo la pagina ultima para el paginado que se guardo al obtener la info del endpoint
      //aca asocio las variables con sus valores
      return {
        ...state,
        stockVendido: {
          data: payload,
          loading: false,
        },
      };

    case "GET_FACTURAS_ELECTRONICAS":
      //aca obtengo la pagina ultima para el paginado que se guardo al obtener la info del endpoint
      let paginaUltimaFacturasElectronicasAux = payload.pop();
      //aca asocio las variables con sus valores
      return {
        ...state,
        facturasElectronicas: {
          data: payload,
          loading: false,
        },
        paginaUltimaFacturasElectronicasPaginado:
          paginaUltimaFacturasElectronicasAux,
      };

    case "GET_ESTILOS_TIENDA":
      //en este caso lo asocio la info con el campo porque no necesito hacer mas nada
      return { ...state, estilos: payload };
    //----------------------------------------------------------------------------
    case "GET_INFO_PICKIT":
      return {
        ...state,
        info_pedido_pickit: { data: payload, loading: false },
      };
    case "GET_PUNTO_PICKIT":
      return { ...state, punto_pickit: payload };
    case "GET_TICKET_PICKIT":
      return { ...state, url_pickit: { data: payload, loading: false } };
    case "GET_LISTADO_PROVINCIAS":
      return { ...state, provincias: payload };
    case "GET_PROVINCIA":
      return { ...state, provincia: payload };
    case "GET_CIUDAD":
      return { ...state, ciudad: payload };
    //---------------------------------- FACTURA --------------------------------------
    case "FACTURAR_PEDIDO":
      return { ...state, okFacturar: payload };
    case "FACTURAR_MASIVAMENTE":
      return { ...state, okFacturarMasivamente: payload };
    case "GET_CONFIG_AFIP":
      return { ...state, configAfip: payload };
    case "CONFIG_AFIP":
      return { ...state, okConfigAfip: payload };
    case "AGREGAR_CERTIFICADOS_AFIP":
      return { ...state, okConfigCertificadosAfip: payload };
    //--------------------------------- NUEVA VENTA -----------------------------------
    case "GET_LISTADO_CLIENTES_BY_NAME":
      return { ...state, clientesByName: payload };
    case "GET_CLIENTE_BY_ID":
      return { ...state, clienteById: payload };
    // case "AGREGAR_ARTICULO":
    //   return { ...state, okAgregarArticulo: payload };
    case "AGREGAR_ARTICULO":
      return {
        ...state,
        okAgregarArticulo: payload,
        currentFacturaId: payload.facturaid,
      };
    case "GET_CARRITO_DETALLE":
      return {
        ...state,
        detalleCarrito: Object.values(payload.articulodetalle).filter(
          (a) => a.productoid !== "0"
        ),
        paginaUltimaDetalleCarrito: payload.paginaUltima,
      };

    case "CALCULAR_CARRITO":
      return { ...state, calcularCarrito: payload };
    case "RESET_CARRITO":
      return {
        ...state,
        detalleCarrito: [], // Empty array to clear all products
        calcularCarrito: {
          subtotal: "0,00", // Match the format used in the app
          intereses: "0", // Match the format used in the app
          total: "0,00", // Match the format used in the app
          simbolo: "$", // Keep the currency symbol
        },
        okAgregarArticulo: "", // Reset the add article status
        currentFacturaId: null, // Reset the current factura ID if it exists
      };
    case "MODIFICAR_DESCUENTO_ARTICULO":
      return { ...state, modificarDescuentoArticulo: payload };
    case "MODIFICAR_CANTIDAD_ARTICULO":
      return { ...state, modificarCantidadArticulo: payload };
    case "MODIFICAR_PRODUCTO_EDITABLE":
      return { ...state, modificarProductoEditable: payload };
    case "LIMPIAR_MODIFICAR_PRODUCTO_EDITABLE":
      return { ...state, modificarProductoEditable: payload };
    case "FINALIZAR_PEDIDO":
      return { ...state, okFinalizarPedido: payload };
    case "GET_TIPOS_DE_PAGO":
      return { ...state, tiposDePago: payload };
    case "GET_TIPOS_DE_TARJETAS":
      return { ...state, tiposDeTarjetas: payload };
    case "GET_CUOTAS_TARJETAS":
      return { ...state, cuotasTarjetas: payload };
    case "ELIMINAR_ARTICULO":
      return { ...state, okEliminarArticulo: payload };
    case "COTIZAR_ENVIO_A_DOMICILIO":
      return { ...state, cotizacionEnvioADomicilio: payload };
    case "TRANSACCION_ENVIO_A_DOMICILIO":
      return { ...state, transaccionEnvioADomicilio: payload };
    case "GET_PUNTOS_DE_RETIRO_PICKIT":
      return { ...state, puntosDeRetiroPickit: payload };
    case "COTIZAR_ENVIO_A_PUNTO":
      return { ...state, cotizacionEnvioAPunto: payload };
    case "TRANSACCION_ENVIO_A_PUNTO":
      return { ...state, transaccionEnvioAPunto: payload };
    //-------------------------- PRESUPUESTO -----------------------
    case "GET_PRESUPUESTOS":
      return {
        ...state,
        presupuestos: {
          data: Object.values(payload.presupuestos),
          loading: false,
        },
        paginaUltimaPresupuestos: payload.paginaUltima,
      };
    case "GET_PRESUPUESTO":
      return { ...state, presupuesto: payload };
    case "GET_DETALLE_PRESUPUESTO":
      let aux = payload.pop();
      return { ...state, detallePresupuesto: payload };
    case "CREAR_PRESUPUESTO":
      return { ...state, okCrearPresupuesto: payload };
    case "CREAR_DETALLE_PRESUPUESTO":
      return { ...state, okCrearDetallePresupuesto: payload };
    case "EDITAR_DETALLE_PRESUPUESTO":
      return { ...state, okEditarDetallePresupuesto: payload };
    case "CLONAR_PRESUPUESTO":
      return { ...state, okClonarPresupuesto: payload };
    case "GENERAR_PEDIDO_PRESUPUESTO":
      return { ...state, okPedidoPresupuesto: payload };
    case "ENVIAR_EMAIL":
      return { ...state, enviarEmail: payload };
    case "LIMPIAR_ENVIAR_EMAIL":
      return { ...state, enviarEmail: payload };
    //-------------------------- PROMOCIONES -----------------------
    case "GET_PROMOCIONES":
      return {
        ...state,
        promociones: Object.values(payload.promociones),
        paginaUltimaPromociones: payload.paginaUltima,
      };
    case "AGREGAR_PROMOCION":
      return { ...state, okAgregarPromocion: payload };
    case "EDITAR_PROMOCION":
      return { ...state, okEditarPromocion: payload };
    case "ELIMINAR_PROMOCION":
      return { ...state, okEliminarPromocion: payload };
    case "GET_PRODUCTOS_PROMOCION":
      return {
        ...state,
        productosPromocion: Object.values(payload.productos_promocion),
        paginaUltimaProductosPromocion: payload.paginaUltima,
      };
    case "AGREGAR_PRODUCTO_PROMOCION":
      return { ...state, okAgregarProductoPromocion: payload };
    case "EDITAR_PRODUCTO_PROMOCION":
      return { ...state, okEditarProductoPromocion: payload };
    case "ELIMINAR_PRODUCTO_PROMOCION":
      return { ...state, okEliminarProductoPromocion: payload };
    case "GET_PRODUCTOS_BY_NAME_PROMOCION":
      return {
        ...state,
        productsByNameSkuPromocion: payload,
      };
    case "GET_PEDIDO_PUNTO_VENTA":
      return { ...state, pedidoPuntoVenta: payload };
    //-------------------------- PRODUCTOS HOME -----------------------
    case "GET_PRODUCTOS_HOME":
      return {
        ...state,
        productosHome: Object.values(payload.productoshome),
        paginaUltimaProductosHome: payload.paginaUltima,
      };
    case "AGREGAR_PRODUCTO_HOME":
      return { ...state, okAgregarProductoHome: payload };
    case "ELIMINAR_PRODUCTO_HOME":
      return { ...state, okEliminarProductoHome: payload };
    //-------------------------- VENDEDORES -------------------------
    case "GET_REPORTE_POR_VENDEDOR":
      return {
        ...state,
        reportePorVendedor: payload,
      };
    case "GET_DETALLE_FACTURA_VENDEDOR":
      return {
        ...state,
        detalleFacturaVendedor: Object.values(payload.detalle),
      };
    case "GET_VENDEDORES":
      return {
        ...state,
        vendedores: payload,
      };
    //-------------------------- FACTURA PROVEEDOR -----------------------
    case "GET_PROVEEDORES_SIN_PAGINADO_TIENDA":
      return { ...state, proveedoresSinPaginado: payload };
    case "GET_FACTURAS_PROVEEDOR":
      return {
        ...state,
        facturasProveedor: {
          data: payload.facturas
            ? Object.values(payload.facturas).reverse()
            : [],
          loading: false,
        },
        paginaUltimaFacturasProveedor: payload.paginaUltima,
      };
    case "AGREGAR_FACTURA_PROVEEDOR":
      return { ...state, okAgregarFacturaProveedor: payload };
    case "EDITAR_FACTURA_PROVEEDOR":
      return { ...state, okEditarFacturaProveedor: payload };
    case "GET_ITEMS_FACTURA_PROVEEDOR":
      return {
        ...state,
        itemsFactura: payload.hasOwnProperty("detallefactura")
          ? Object.values(payload.detallefactura)
          : [],
        paginaUltimaItemsFacturaProveedor: payload.paginaUltima,
      };
    case "AGREGAR_DETALLE_FACTURA_PROVEEDOR":
      return {
        ...state,
        okAgregarItemFacturaProveedor: payload,
      };
    case "ELIMINAR_ITEM_FACTURA_PROVEEDOR":
      return {
        ...state,
        okEliminarItemFacturaProveedor: payload,
      };
    case "ELIMINAR_FACTURA_PROVEEDOR":
      return {
        ...state,
        okEliminarFacturaProveedor: payload,
      };
    case "EDITAR_ITEM_FACTURA_PROVEEDOR":
      return {
        ...state,
        okEditarItemFacturaProveedor: payload,
      };
    case "GET_TIPOS_COMPROBANTES":
      return {
        ...state,
        tiposComprobantes: Object.values(payload.tipocomprobantes),
      };
    case "GET_CONCEPTOS_PROVEEDOR":
      return {
        ...state,
        conceptosProveedor: Object.values(payload.conceptos),
      };
    case "REGISTRAR_PAGO_FACTURA_PROVEEDOR":
      return {
        ...state,
        okRegistrarPagoFacturaProveedor: payload,
      };
    case "GET_CUENTAS_BANCO":
      return {
        ...state,
        cuentasBanco: Object.values(payload.cuentasbanco),
      };
    case "GET_CHEQUES_BANCO":
      return {
        ...state,
        chequesBanco: Object.values(payload.chequesrecibido),
      };
    case "GET_TIPOS_CHEQUES_BANCO":
      return {
        ...state,
        tiposChequesBanco: Object.values(payload.tiposcheque),
      };
    case "GET_TARJETAS":
      return {
        ...state,
        tarjetas: Object.values(payload.tarjetas),
      };
    case "GET_TIPOS_TARJETAS":
      return {
        ...state,
        tiposDeTarjetas: Object.values(payload.tarjetas),
      };
    case "GET_PAGOS_REALIZADOS_FACTURA_PROVEEDOR":
      return {
        ...state,
        pagosRealizadosFacturaProveedor: payload.hasOwnProperty("detallepago")
          ? Object.values(payload.detallepago)
          : [],
        paginaUltimapagosRealizadosFacturaProveedor: payload.paginaUltima,
      };
    //--------------------------- FOOTER ---------------------------------
    case "GET_DATOS_FOOTER":
      return {
        ...state,
        datosFooter: Object.values(payload),
      };
    case "AGREGAR_DATOS_FOOTER":
      return { ...state, okAgregarDatosFooter: payload };
    case "AGREGAR_SUBTITULO_FOOTER":
      return { ...state, okAgregarSubtitulo: payload };
    case "EDITAR_DATOS_FOOTER":
      return { ...state, okEditarDatosFooter: payload };
    case "ELIMINAR_DATOS_FOOTER":
      return { ...state, okEliminarDatosFooter: payload };
    //--------------------------- MENSAJES -------------------------------
    case "SEND_MESSAGE_USER":
      return { ...state, okSendMessage: payload };
    case "SEND_MESSAGE_PRODUCTO":
      return { ...state, okSendMessageProducto: payload };
    case "GET_MESSAGES_BY_PEDIDO":
      return { ...state, messagesByPedido: payload };
    case "GET_MESSAGES_BY_PRODUCTOID":
      return { ...state, messagesByProductoID: payload };
    //---------------------------- URL MERCADOPAGO --------------------------------
    case "GET_URL_MERCADOPAGO":
      return { ...state, urlMercadoPago: payload };
    //---------------------------- REPORTE RENTABILIDAD PRODUCTOS ----------------------------
    case "GET_REPORTE_RENTABILIDAD_PRODUCTOS":
      return {
        ...state,
        reporteRentabilidadProductos: {
          data: Object.values(payload.rentabilidad),
          loading: false,
        },
        paginaUltimaReporteRentabilidadProductos: payload.paginaUltima,
      };
    //---------------------------- REPORTE RENTABILIDAD PRODUCTOS ----------------------------
    case "GET_REPORTE_CUENTA_CORRIENTE_DEUDAS":
      return {
        ...state,
        reporteCuentaCorrienteDeudas: {
          data: Object.values(payload.ctactedeuda),
          loading: false,
        },
        paginaUltimaReporteCuentaCorrienteDeudas: payload.paginaUltima,
      };
    case "GET_CUENTAS_CORRIENTES_2":
      return {
        ...state,
        cuentasCorrientes: {
          data: Object.values(payload.ctacte),
          loading: false,
          total: payload.sumatotalComprobante,
        },
        paginaUltimaCuentasCorrientes: payload.paginaUltima,
      };
    //---------------------------- IMPORTAR PRODUCTOS --------------------------------
    case "GET_ARCHIVOS":
      return {
        ...state,
        listadoArchivos: Object.values(payload.productosimporta),
      };
    case "CREAR_ENCABEZADO_IMPORTACION":
      return {
        ...state,
        okCrearEncabezado: payload,
      };
    case "EDITAR_ENCABEZADO_IMPORTACION":
      return {
        ...state,
        okEditarEncabezado: payload,
      };
    case "SUBIR_ARCHIVO_IMPORTA":
      return {
        ...state,
        okSubirArchivo: payload,
      };
    case "PROCESAR_LISTADO":
      return {
        ...state,
        okProcesarListado: payload,
      };
    case "GET_PROVEEDORES_SIN_PAGINADO_TIENDA":
      return { ...state, proveedoresSinPaginado: payload };
    //---------------------------- IMPORTAR LISTA PRECIOS  --------------------------------
    case "GET_ARCHIVOS_LISTA_PRECIOS":
      return {
        ...state,
        listadoArchivosListaPrecio: Object.values(
          payload.listasimporta
        ).reverse(),
        ultimaPaginalistadoArchivosListaPrecio: payload.paginaUltima,
      };
    case "CREAR_ENCABEZADO_IMPORTACION_LISTA_PRECIOS":
      return {
        ...state,
        okCrearEncabezadoListaPrecio: payload,
      };
    case "EDITAR_ENCABEZADO_IMPORTACION_LISTA_PRECIOS":
      return {
        ...state,
        okEditarEncabezadoListaPrecio: payload,
      };
    case "SUBIR_ARCHIVO_IMPORTA_LISTA_PRECIOS":
      return {
        ...state,
        okSubirArchivoListaPrecio: payload,
      };
    case "PROCESAR_LISTADO_LISTA_PRECIOS":
      return {
        ...state,
        okProcesarListadoListaPrecio: payload,
      };
    //---------------------------- CUENTAS CORRIENTES ----------------------------
    case "GET_CUENTAS_CORRIENTES":
      return {
        ...state,
        cuentasCorrientes: {
          data: Object.values(payload.ctacte),
          loading: false,
        },
        paginaUltimaCuentasCorrientes: payload.paginaUltima,
      };
    case "GET_DETALLE_CUENTA_CORRIENTE":
      let infoAux = Object.values(payload.ctacte);
      let paginaSiguiente = infoAux.pop();
      let paginaPrevia = infoAux.pop();
      let paginaUltima = infoAux.pop();

      return {
        ...state,
        detalleCuentaCorriente: infoAux,
        detalleCuentaCorrienteCliente: payload.cliente,
        paginaUltimaDetalleCuentaCorriente: paginaUltima,
      };
    case "PAGAR_CUENTA_CORRIENTE_GENERAL":
      return {
        ...state,
        okPagoCuentaCorriente: payload,
      };
    case "PAGAR_CUENTA_CORRIENTE_CHEQUE":
      return {
        ...state,
        okPagoCuentaCorrienteCheque: payload,
      };
    case "PAGAR_CUENTA_CORRIENTE_TARJETA":
      return {
        ...state,
        okPagoCuentaCorrienteTarjeta: payload,
      };
    case "GET_PAGOS_CUENTA_CORRIENTE":
      return {
        ...state,
        pagosCuentaCorriente: Object.values(payload.ctacte_pagos),
        paginaUltimaPagosCuentaCorriente: payload.paginaUltima,
      };
    case "ELIMINAR_PAGO_CUENTA_CORRIENTE":
      return {
        ...state,
        okEliminarPagoCuentaCorriente: payload,
      };
    case "AFECTAR_PAGO_CUENTA_CORRIENTE":
      return {
        ...state,
        okAfectarPagoCuentaCorriente: payload,
      };
    //---------------------------- CUENTAS CORRIENTES PROVEEDOR----------------------------
    case "GET_CUENTAS_CORRIENTES_PROVEEDOR":
      return {
        ...state,
        cuentasCorrientesProveedor: {
          data: Object.values(payload.ctacte),
          loading: false,
        },
        paginaUltimaCuentasCorrientes: payload.paginaUltima,
      };
    case "GET_LISTADO_PROVEEDORES_BY_NAME":
      return {
        ...state,
        proveedoresByName: payload,
      };
    case "GET_DETALLE_CUENTA_CORRIENTE_PROVEEDOR":
      let infoAuxProveedor = Object.values(payload.ctacte);
      let paginaSiguienteProveedor = infoAuxProveedor.pop();
      let paginaPreviaProveedor = infoAuxProveedor.pop();
      let paginaUltimaProveedor = infoAuxProveedor.pop();

      return {
        ...state,
        detalleCuentaCorrienteProveedor: infoAuxProveedor,
        detalleCuentaCorrienteClienteProveedor: payload.proveedor,
        paginaUltimaDetalleCuentaCorrienteProveedor: paginaUltimaProveedor,
      };
    case "PAGAR_CUENTA_CORRIENTE_PROVEEDOR":
      return {
        ...state,
        okPagoCuentaCorrienteProveedor: payload,
      };
    case "GET_PAGOS_CUENTA_CORRIENTE_PROVEEDOR":
      return {
        ...state,
        pagosCuentaCorrienteProveedor: Object.values(payload.pagos),
        paginaUltimaPagosCuentaCorrienteProveedor: payload.paginaUltima,
      };
    case "ELIMINAR_PAGO_CUENTA_CORRIENTE_PROVEEDOR":
      return {
        ...state,
        okEliminarPagoCuentaCorrienteProveedor: payload,
      };
    case "AFECTAR_PAGO_CUENTA_CORRIENTE_PROVEEDOR":
      return {
        ...state,
        okAfectarPagoCuentaCorrienteProveedor: payload,
      };
    //--------------------------------- MANEJO CAJA --------------------------------------
    case "GET_ESTADO_CAJA":
      return {
        ...state,
        estadoCaja: payload.estado_caja,
      };
    case "GET_TOTAL_CAJA":
      return {
        ...state,
        totalCaja: payload.totalcaja,
      };
    case "GRABAR_MOVIMIENTO":
      return {
        ...state,
        okGrabarMovimiento: payload,
      };
    case "CANCELAR_FACTURA":
      return {
        ...state,
        okCancelarFactura: payload,
      };
    case "TRAER_FACTURA_TRUE":
      return {
        ...state,
        detalleCarrito: Object.values(payload.articulodetalle).filter(
          (a) => a.productoid !== "0"
        ),
        paginaUltimaDetalleCarrito: payload.paginaUltima,
        okTraerFactura: true,
      };
    case "TRAER_FACTURA_FALSE":
      return {
        ...state,
        detalleCarrito: [],
        paginaUltimaDetalleCarrito: 0,
        okTraerFactura: false,
      };
    case "GET_DATOS_BY_TIENDA_ID":
      return {
        ...state,
        datosTiendaById: payload,
      };
    case "FACTURAR_PUNTO_VENTA":
      return {
        ...state,
        okFacturarPuntoVenta: payload,
      };
    case "GET_CATEGORIAS_BY_NAME":
      return {
        ...state,
        categoriasByName: Object.values(payload.listacategorias),
      };
    case "GET_MARCAS_BY_NAME":
      return {
        ...state,
        marcasByName: Object.values(payload.listamarcas),
      };
    case "GET_TALLAS_BY_NAME":
      return {
        ...state,
        tallasByName: Object.values(payload.listatallas),
      };
    case "GET_COLORES_BY_NAME":
      return {
        ...state,
        coloresByName: Object.values(payload.colores),
      };
    case "GET_DATOS_TIENDA_ID":
      return {
        ...state,
        datosTiendaById: payload,
      };
    case "GET_PRODUCTOS_STOCK":
      return {
        ...state,
        productosStock: {
          data: Object.values(payload.gestionstock),
          loading: false,
        },
        paginaUltimaProductosStock: payload.paginaUltima,
      };
    case "AUMENTAR_STOCK":
      return {
        ...state,
        okAumentarStock: payload,
      };
    case "RESTAR_STOCK":
      return {
        ...state,
        okRestarStock: payload,
      };
    case "RESTAR_STOCK_RESERVADO":
      return {
        ...state,
        okRestarStockReservado: payload,
      };
    case "SUBIR_ARCHIVO_IMPORTA_STOCK":
      return {
        ...state,
        okSubirArchivoImportaStock: payload,
      };
    case "GET_LIQUIDACIONES_SUCCESS":
      return {
        ...state,
        liquidaciones: {
          ...state.liquidaciones,
          data: payload.data,
          loading: false,
        },
      };
      case "UPLOAD_COMPROBANTES_START":
      return {
        ...state,
        comprobantesUpload: {
          loading: true,
          error: null,
          success: false
        }
      };
    case "UPLOAD_COMPROBANTES_SUCCESS":
      return {
        ...state,
        comprobantesUpload: {
          loading: false,
          error: null,
          success: true
        }
      };
    case "UPLOAD_COMPROBANTES_ERROR":
      return {
        ...state,
        comprobantesUpload: {
          loading: false,
          error: payload,
          success: false
        }
      };
      case "GET_LIQUIDACION":
  return {
    ...state,
    liquidacion: payload,
  };
    case "CAMBIAR_ESTADO_LIQUIDACION":
  return {
    ...state,
    cambio_estado_liquidacion: {
      loading: false,
      success: payload.success,
      mensaje: payload.mensaje
    }
  };
case "CAMBIAR_ESTADO_LIQUIDACION_ERROR":
  return {
    ...state,
    cambio_estado_liquidacion: {
      loading: false,
      success: false,
      mensaje: payload.mensaje
    }
  };
  case 'TRIGGER_NOTIFICATION_REFRESH':
  return {
    ...state,
    notificationRefreshTrigger: Date.now()
  };
  case 'FETCH_COMPROBANTES_START':
  return {
    ...state,
    comprobantes: {
      ...state.comprobantes,
      loading: true,
      error: null
    }
  };
case 'FETCH_COMPROBANTES_SUCCESS':
  return {
    ...state,
    comprobantes: {
      ...state.comprobantes,
      loading: false,
      data: payload,
      error: null
    }
  };
case 'FETCH_COMPROBANTES_FAILURE':
  return {
    ...state,
    comprobantes: {
      ...state.comprobantes,
      loading: false,
       data: null,
      error: payload
    }
  };
  case "GET_NOTIFICATION_REMITOS_PENDIENTES":
  return {
    ...state,
    notificationRemitosPendientes: {
      ...state.notificationRemitosPendientes,
      data: payload.data,
      loading: false,
      allLoaded: payload.paginaUltima === 1,
    },
  };
    default:
      return state;
  }
};

export default mitiendaReducer;
