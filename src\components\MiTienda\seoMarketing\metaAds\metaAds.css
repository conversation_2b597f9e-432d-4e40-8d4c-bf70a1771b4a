.container-meta-ads {
    width: 100%;
    min-height: 100vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.form-meta-ads{
    background-color: white;
    height: 50%;
    width: 100%;
    padding: 20px;
    padding-top: 15px;
    align-self: center;
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-google-analytics{
    background-color: white;
    height: 40%;
    width: 100%;
    padding: 20px;
    padding-top: 15px;
    display: flex;
    flex-direction: column;
}

.header-meta-ads{
    width: 95%;
}

.container-form{
    width: 90%;
}

.container-facebook-pixel {
    width: 100%;
    min-height: 100vh;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.texto-facebook-pixel{
    padding: 20px;
    margin-top: 50px;
    margin-bottom: 50px;
    height: auto;
}

@media only screen and (max-width: 1200px) {
    .container-meta-ads{
        padding: 20px;
        width: 100%;
        min-height: 110vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }
}