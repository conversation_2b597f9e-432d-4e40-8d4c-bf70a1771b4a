//f con filtro por cliente
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getRemitos,
  getListadoClientesByName,
} from "../../../redux/actions/mitienda.actions";
import { Link } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  TextField,
  Tooltip,
  IconButton,
  InputAdornment,
  Popper,
  ClickAwayListener,
  Grow,
  Paper as MuiPaper,
  MenuList,
  MenuItem,
  ListItemText,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import { RemoveRedEye, Search, Download, Clear } from "@mui/icons-material";
import MuiAlert from "@mui/material/Alert";
import moment from "moment";
import * as XLSX from "xlsx";
import { ModalVerPdf } from "../ventas/verPdf";
import ClipLoader from "react-spinners/ClipLoader";
import { ModalFiltrosRemito } from "./modalFiltrosRemito";

export const Remitos = () => {
  let fechaActual = moment();
  let ultimosDias = moment().day(-60);

  const remitos = useSelector(
    (state) => state.mitienda.remitos || { remitos: {} }
  );
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaRemitos || 1
  );
  const clientesByName = useSelector(
    (state) => state.mitienda.clientesByName || []
  );

  let pathname = window.location.pathname;
  let permisos_acciones =
    Object.values(
      JSON.parse(localStorage.getItem("permisos_acciones") || "{}")
    ).filter((p) =>
      p?.archivo?.toLowerCase().includes(pathname.toLocaleLowerCase())
    )[0] || {};

  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const [clienteid, setClienteid] = useState(0);
  const handleChangeClienteid = (e) => {
    setClienteid(e.target.value);
  };

  const [facturaid, setFacturaid] = useState(0);
  const handleChangeFacturaid = (e) => {
    setFacturaid(e.target.value);
  };

  const [nrocomprobante, setNrocomprobante] = useState("");
  const handleChangeNrocomprobante = (e) => {
    setNrocomprobante(e.target.value);
  };

  const [fechaDesde, setFechaDesde] = useState(
    ultimosDias.toISOString().substring(0, 10)
  );
  const handleChangeFechaDesde = (e) => {
    setFechaDesde(e.target.value);
  };

  const [fechaHasta, setFechaHasta] = useState(
    fechaActual.toISOString().substring(0, 10)
  );
  const handleChangeFechaHasta = (e) => {
    setFechaHasta(e.target.value);
  };

  // Client search states
  const [clienteNombre, setClienteNombre] = useState("");
  const [clienteSeleccionado, setClienteSeleccionado] = useState(null);
  const [clienteDropdownOpen, setClienteDropdownOpen] = useState(false);
  const clienteInputRef = React.useRef(null);

  const [remito, setRemito] = useState("");
  const [showPreviewPdf, setShowPreviewPdf] = useState(false);
  const handleClosePreviewPdf = () => setShowPreviewPdf(false);
  const handleShowPreviewPdf = (p) => {
    setRemito(p);
    setShowPreviewPdf(true);
  };

  const [searchOk, setSearchOk] = useState(false);

  const convertirANumero = (n) => {
    if (!n) return 0;
    let split = n.split(/\.|\,/);
    if (split.length > 2) {
      let aux = split[0] + split[1];
      let aux2 = parseFloat(aux + "." + split[2]);
      return aux2;
    } else if (split.length === 2) {
      let nuevo = parseFloat(split[0] + "." + split[1]);
      return nuevo;
    }
    return parseFloat(n);
  };

  const dispatch = useDispatch();

  // const reset = () => {
  //     dispatch(getRemitos(0, 0, '', '', '', 1));
  //     setClienteid(0);
  //     setFacturaid(0);
  //     setNrocomprobante('');
  //     setFechaDesde(ultimosDias.toISOString().substring(0,10));
  //     setFechaHasta(fechaActual.toISOString().substring(0,10));
  //     setClienteNombre('');
  //     setClienteSeleccionado(null);
  //     handleClose();
  // };

  const reset = () => {
    const defaultDesde = ultimosDias.toISOString().substring(0, 10);
    const defaultHasta = fechaActual.toISOString().substring(0, 10);

    dispatch(getRemitos(0, 0, "", defaultDesde, defaultHasta, 1));
    setClienteid(0);
    setFacturaid(0);
    setNrocomprobante("");
    setFechaDesde(defaultDesde);
    setFechaHasta(defaultHasta);
    setClienteNombre("");
    setClienteSeleccionado(null);
    handleClose();
  };

  const clearClienteSearch = () => {
    setClienteNombre("");
    setClienteSeleccionado(null);
    setClienteid(0);
    setPage(1);
    dispatch(
      getRemitos(0, facturaid, nrocomprobante, fechaDesde, fechaHasta, 1)
    );
  };

  const clearComprobanteSearch = () => {
    setNrocomprobante("");
    setPage(1);
    dispatch(getRemitos(clienteid, facturaid, "", fechaDesde, fechaHasta, 1));
  };

  //w
  // const search = () => {
  //     setSearchOk(true);
  //     setPage(1);
  //     dispatch(getRemitos(clienteid, facturaid, nrocomprobante, fechaDesde, fechaHasta, 1));
  //     handleClose();
  // };

  const search = () => {
    setPage(1);
    dispatch(
      getRemitos(
        clienteid,
        facturaid,
        nrocomprobante,
        fechaDesde,
        fechaHasta,
        1
      )
    );
    handleClose();
  };

  const downloadExcel = () => {
    if (!remitos || !remitos.remitos) return;

    const dataForExcel = Object.values(remitos.remitos).map((item) => ({
      "Número Remito": item.facturaid,
      Cliente: item.nombrecliente,
      Estado: item.descripcionestado || "Sin estado",
      Fecha: formatoFecha(item.fechaingreso),
      Total: convertirANumero(item.total).toFixed(2),
    }));

    const workSheet = XLSX.utils.json_to_sheet(dataForExcel);
    const workBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workBook, workSheet, "Remitos");
    XLSX.writeFile(workBook, "Remitos.xlsx");
  };

  const formatoFecha = (fecha) => {
    if (!fecha) return "";
    let aux = fecha.split(" ");
    let aux2 = aux[0].split("-");

    return (
      aux2[2] +
      "-" +
      aux2[1] +
      "-" +
      aux2[0] +
      " " +
      (aux[1] ? aux[1].slice(0, 5) : "")
    );
  };

  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(false);
  }, [remitos]);

  useEffect(() => {
    setLoading(true);
    dispatch(
      getRemitos(
        clienteid,
        facturaid,
        nrocomprobante,
        fechaDesde,
        fechaHasta,
        page
      )
    ).finally(() => {
      setLoading(false);
    });
  }, [page, dispatch, clienteid, facturaid, nrocomprobante]);

  //w (removed to prevent duplicate search)
  // useEffect(() => {
  //     if (searchOk === true && nrocomprobante === '') {
  //         dispatch(getRemitos(clienteid, facturaid, nrocomprobante, fechaDesde, fechaHasta, page));
  //         setSearchOk(false);
  //     }
  // }, [search, nrocomprobante]);

  // New effect for client search
  useEffect(() => {
    if (clienteNombre.length > 2) {
      dispatch(getListadoClientesByName(clienteNombre, ""));
      setClienteDropdownOpen(true);
    } else {
      setClienteDropdownOpen(false);
    }
  }, [clienteNombre, dispatch]);

  const handleClienteSelect = (cliente) => {
    setClienteSeleccionado(cliente);
    setClienteNombre(cliente.nombre + " " + cliente.apellido);
    setClienteid(cliente.clienteid);
    setClienteDropdownOpen(false);

    // Trigger search with selected client
    setPage(1);
    dispatch(
      getRemitos(
        cliente.clienteid,
        facturaid,
        nrocomprobante,
        fechaDesde,
        fechaHasta,
        1
      )
    );
  };

  const handleClickAway = () => {
    setClienteDropdownOpen(false);
  };

  return (
    <div className="ventas-container">
      <ModalVerPdf
        show={showPreviewPdf}
        handleClose={handleClosePreviewPdf}
        id={remito}
        tipoDocumento="Remito"
        tipoComprobante="130" // Add this parameter for remitos
      />

      <ModalFiltrosRemito
        show={show}
        handleClose={handleClose}
        handleChangeClienteid={handleChangeClienteid}
        clienteid={clienteid}
        handleChangeNrocomprobante={handleChangeNrocomprobante}
        nrocomprobante={nrocomprobante}
        setClienteId={setClienteid}
        handleChangeFechaDesde={handleChangeFechaDesde}
        fechaDesde={fechaDesde}
        handleChangeFechaHasta={handleChangeFechaHasta}
        fechaHasta={fechaHasta}
        reset={reset}
        search={search}
      />

      <div className="titulo-ventas">
        <h3 className="text-ventas">Remitos</h3>
        <div className="text-ventas">
          <h4>
            Los remitos que se muestran se comprenden entre la fecha actual y 60
            días para atrás.
          </h4>
        </div>
        <div
          className="list-ventas"
          style={{ display: "flex", width: "100%", flexWrap: "wrap" }}
        >
          {/* <TextField
            label="Buscar por número de comprobante"
            variant="outlined"
            margin="normal"
            style={{ width: 400, marginLeft: 40, marginRight: 10 }}
            InputLabelProps={{
              style: {
                marginBottom: 10,
                marginTop: -7,
              },
            }}
            inputProps={{
              style: {
                height: 40,
                fontSize: 17,
                paddingRight: 40,
              },
            }}
            name="nrocomprobante"
            onChange={(e) => handleChangeNrocomprobante(e)}
            value={nrocomprobante}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={search} edge="end">
                    <Search />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          /> */}

          <TextField
    label="Buscar por número de comprobante"
    variant="outlined"
    margin="normal"
    style={{ width: 400, marginLeft: 40, marginRight: 10 }}
    InputLabelProps={{
        style: {
            marginBottom: 10,
            marginTop: -7,
        },
    }}
    inputProps={{
        style: {
            height: 40,
            fontSize: 17,
            paddingRight: 40 // Ensure space for both buttons
        },
    }}
    name="nrocomprobante"
    onChange={(e) => handleChangeNrocomprobante(e)}
    value={nrocomprobante}
    InputProps={{
        endAdornment: (
            <InputAdornment position="end">
                {/* Clear button (shows only when text exists) */}
                {nrocomprobante && (
                    <IconButton
                        onClick={clearComprobanteSearch}
                        edge="end"
                    >
                        <Clear />
                    </IconButton>
                )}
                {/* Search button (always visible) */}
                <IconButton
                    onClick={search}
                    edge="end"
                >
                    <Search />
                </IconButton>
            </InputAdornment>
        ),
    }}
/>

          {/* Cliente Autocomplete */}
          <div style={{ position: "relative", marginRight: 10 }}>
            <TextField
              label="Buscar por cliente"
              variant="outlined"
              margin="normal"
              style={{ width: 300 }}
              ref={clienteInputRef}
              InputLabelProps={{
                style: {
                  marginBottom: 10,
                  marginTop: -7,
                },
              }}
              inputProps={{
                style: {
                  height: 40,
                  fontSize: 17,
                },
              }}
              name="clienteNombre"
              onChange={(e) => setClienteNombre(e.target.value)}
              value={clienteNombre}
              onClick={() => {
                if (clienteNombre.length > 2) {
                  setClienteDropdownOpen(true);
                }
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {clienteNombre && (
                      <IconButton onClick={clearClienteSearch} edge="end">
                        <Clear />
                      </IconButton>
                    )}
                  </InputAdornment>
                ),
              }}
            />
            <Popper
              open={clienteDropdownOpen && clientesByName.length > 0}
              anchorEl={clienteInputRef.current}
              placement="bottom-start"
              transition
              style={{ zIndex: 1300, width: 300 }}
            >
              {({ TransitionProps }) => (
                <Grow {...TransitionProps} style={{ transformOrigin: "top" }}>
                  <MuiPaper>
                    <ClickAwayListener onClickAway={handleClickAway}>
                      <MenuList
                        // autoFocusItem={clienteDropdownOpen}
                        autoFocus={false}
                        style={{ maxHeight: 300, overflow: "auto" }}
                      >
                        {clientesByName.map((cliente, i) => (
                          <MenuItem
                            key={i}
                            onClick={() => handleClienteSelect(cliente)}
                          >
                            <ListItemText
                              primary={cliente.nombre + " " + cliente.apellido}
                            />
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </MuiPaper>
                </Grow>
              )}
            </Popper>
          </div>

          <div style={{ display: "flex", marginLeft: 10 }}>
            <Button
              variant="outlined"
              sx={{ height: 40, width: 150, marginTop: 2 }}
              onClick={handleShow}
            >
              <FilterListIcon /> Filtrar
            </Button>
            <Button
              variant="outlined"
              sx={{ height: 40, width: 150, marginTop: 2, marginLeft: 5 }}
              onClick={downloadExcel}
            >
              <Download /> Exportar
            </Button>
          </div>
        </div>
      </div>

      {permisos_acciones?.listar !== "0" && (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyItems: "center",
          }}
        >
          <TableContainer
            component={Paper}
            sx={{
              marginTop: 5,
              width: "95%",
              alignSelf: "center",
              marginLeft: 3,
              marginBottom: 5,
            }}
          >
            <Table aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell style={{ fontWeight: "bold", fontSize: "110%" }}>
                    #
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%", padding: 0 }}
                    align="center"
                  >
                    Cliente
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Estado
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Fecha Ingreso
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Fecha de entrega
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    Ver
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow sx={{ p: 20 }}>
                    <TableCell colSpan={5} align="center">
                      <ClipLoader loading={loading} size={50} />
                    </TableCell>
                  </TableRow>
                ) : remitos &&
                  remitos.remitos &&
                  Object.keys(remitos.remitos).length > 0 ? (
                  Object.values(remitos.remitos).map((row) => (
                    <TableRow
                      key={row.facturaid}
                      sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                    >
                      <TableCell
                        style={{
                          fontSize: "16px",
                          width: "50px",
                          height: "40px",
                          padding: 3,
                        }}
                        component="th"
                        scope="row"
                      >
                        <Link
                          to={`/Mitienda/Remitos/${row.facturaid}/${row.clienteid}`}
                        >
                          {row.facturaid}
                        </Link>
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "90px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.nombrecliente}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {row.descripcionestado || "Sin estado"}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {formatoFecha(row.fechaingreso)}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "80px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        {formatoFecha(row.fechaentrega) ||
                          "Sin fecha de entrega"}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "50px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        <Tooltip title="Ver remito">
                          <IconButton
                            onClick={() => handleShowPreviewPdf(row.facturaid)}
                          >
                            <RemoveRedEye />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <h5>No hay información para mostrar</h5>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Pagination
            count={paginaUltima}
            page={page}
            onChange={handleChangePage}
            size="large"
            sx={{ alignSelf: "center" }}
          />
        </div>
      )}
    </div>
  );
};

//w original
// import React, { useEffect, useState } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import {
//     getRemitos
// } from '../../../redux/actions/mitienda.actions';
// import { Link } from 'react-router-dom';
// import {
//     Table,
//     TableBody,
//     TableCell,
//     TableContainer,
//     TableHead,
//     TableRow,
//     Paper,
//     Pagination,
//     Button,
//     TextField,
//     Tooltip,
//     Checkbox,
//     Snackbar,
//     IconButton,
//     InputAdornment
// } from '@mui/material';
// import FilterListIcon from '@mui/icons-material/FilterList';
// import { RemoveRedEye, Search, Download } from '@mui/icons-material';
// import MuiAlert from '@mui/material/Alert';
// import moment from 'moment';
// import * as XLSX from "xlsx";
// import { ModalVerPdf } from '../ventas/verPdf';
// import ClipLoader from "react-spinners/ClipLoader";
// import { ModalFiltrosRemito } from './modalFiltrosRemito';

// export const Remitos = () => {
//     let fechaActual = moment();
//     let ultimosDias = moment().day(-60);

//     const remitos = useSelector((state) => state.mitienda.remitos || { remitos: {} });
//     const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaRemitos || 1);

//     let pathname = window.location.pathname;
//     let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones') || '{}'))
//         .filter((p) => p?.archivo?.toLowerCase().includes(pathname.toLocaleLowerCase()))[0] || {};

//     const [show, setShow] = useState(false);
//     const handleClose = () => setShow(false);
//     const handleShow = () => setShow(true);

//     const [page, setPage] = useState(1);
//     const handleChangePage = (event, value) => {
//         // console.log("Changing to page:", value);
//         setPage(value);

//     };

//     const [clienteid, setClienteid] = useState(0);
//     const handleChangeClienteid = (e) => {
//         setClienteid(e.target.value);
//     };

//     const [facturaid, setFacturaid] = useState(0);
//     const handleChangeFacturaid = (e) => {
//         setFacturaid(e.target.value);
//     };

//     const [nrocomprobante, setNrocomprobante] = useState('');
//     const handleChangeNrocomprobante = (e) => {
//         setNrocomprobante(e.target.value);
//     };

//     const [fechaDesde, setFechaDesde] = useState(ultimosDias.toISOString().substring(0,10));
//     const handleChangeFechaDesde = (e) => {
//         setFechaDesde(e.target.value);
//     };

//     const [fechaHasta, setFechaHasta] = useState(fechaActual.toISOString().substring(0,10));
//     const handleChangeFechaHasta = (e) => {
//         setFechaHasta(e.target.value);
//     };

//     const [remito, setRemito] = useState('');
//     const [showPreviewPdf, setShowPreviewPdf] = useState(false);
//     const handleClosePreviewPdf = () => setShowPreviewPdf(false);
//     const handleShowPreviewPdf = (p) => {
//         setRemito(p);
//         setShowPreviewPdf(true);
//     };

//     const [searchOk, setSearchOk] = useState(false);

//     const convertirANumero = (n) => {
//         if (!n) return 0;
//         let split = n.split(/\.|\,/);
//         if (split.length > 2) {
//             let aux = split[0] + split[1];
//             let aux2 = parseFloat(aux + '.' + split[2]);
//             return aux2;
//         } else if (split.length === 2) {
//             let nuevo = parseFloat(split[0] + '.' + split[1]);
//             return nuevo;
//         }
//         return parseFloat(n);
//     };

//     const dispatch = useDispatch();

//     const reset = () => {
//         dispatch(getRemitos(0, 0, '', '', '', 1));
//         setClienteid(0);
//         setFacturaid(0);
//         setNrocomprobante('');
//         setFechaDesde(ultimosDias.toISOString().substring(0,10));
//         setFechaHasta(fechaActual.toISOString().substring(0,10));
//         handleClose();
//     };

//     const search = () => {
//         setSearchOk(true);
//         setPage(1);
//         dispatch(getRemitos(clienteid, facturaid, nrocomprobante, fechaDesde, fechaHasta, 1));
//         handleClose();
//     };

//     const downloadExcel = () => {
//         if (!remitos || !remitos.remitos) return;

//         const dataForExcel = Object.values(remitos.remitos).map(item => ({
//             'Número Remito': item.facturaid,
//             'Cliente': item.nombrecliente,
//             'Estado': item.descripcionestado || 'Sin estado',
//             'Fecha': formatoFecha(item.fechaingreso),
//             'Total': convertirANumero(item.total).toFixed(2)
//         }));

//         const workSheet = XLSX.utils.json_to_sheet(dataForExcel);
//         const workBook = XLSX.utils.book_new();
//         XLSX.utils.book_append_sheet(workBook, workSheet, "Remitos");
//         XLSX.writeFile(workBook, "Remitos.xlsx");
//     };

//     const formatoFecha = (fecha) => {
//         if (!fecha) return '';
//         let aux = fecha.split(' ');
//         let aux2 = aux[0].split('-');

//         return aux2[2] + '-' + aux2[1] + '-' + aux2[0] + ' ' + (aux[1] ? aux[1].slice(0,5) : '');
//     };

//     const Alert = React.forwardRef(function Alert(props, ref) {
//         return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
//     });

//     const [loading, setLoading] = useState(true);

//     useEffect(() => {
//         setLoading(false);
//     }, [remitos]);

//     useEffect(() => {
//         // console.log("Page changed to:", page);
//         setLoading(true);
//         dispatch(getRemitos(clienteid, facturaid, nrocomprobante, fechaDesde, fechaHasta, page))
//             .finally(() => {
//                 setLoading(false);
//             });
//     }, [page, dispatch, clienteid, facturaid, nrocomprobante, fechaDesde, fechaHasta]);

//     useEffect(() => {
//         if (searchOk === true && nrocomprobante === '') {
//             dispatch(getRemitos(clienteid, facturaid, nrocomprobante, fechaDesde, fechaHasta, page));
//             setSearchOk(false);
//         }
//     }, [search, nrocomprobante]);

//     return (
//         <div className="ventas-container">
//             <ModalVerPdf
//                 show={showPreviewPdf}
//                 handleClose={handleClosePreviewPdf}
//                 id={remito}
//                 tipoDocumento="Remito"
//                 tipoComprobante="130"  // Add this parameter for remitos
//             />

//             <ModalFiltrosRemito
//                 show={show}
//                 handleClose={handleClose}
//                 handleChangeClienteid={handleChangeClienteid}
//                 clienteid={clienteid}
//                 handleChangeNrocomprobante={handleChangeNrocomprobante}
//                 nrocomprobante={nrocomprobante}
//                 setClienteId={setClienteid}
//                 handleChangeFechaDesde={handleChangeFechaDesde}
//                 fechaDesde={fechaDesde}
//                 handleChangeFechaHasta={handleChangeFechaHasta}
//                 fechaHasta={fechaHasta}
//                 reset={reset}
//                 search={search}
//             />

//             <div className="titulo-ventas">
//                 <h3 className="text-ventas">Remitos</h3>
//                 <div className="text-ventas">
//                     <h4>Los remitos que se muestran se comprenden entre la fecha actual y 60 días para atrás.</h4>
//                 </div>
//                 <div className='list-ventas' style={{display:"flex", width:"100%"}}>
//                     <TextField
//                         label="Buscar por número de comprobante"
//                         variant="outlined"
//                         margin="normal"
//                         style={{width:600, marginLeft:40, marginRight:4}}
//                         InputLabelProps={{
//                             style: {
//                                 marginBottom:10,
//                                 marginTop: -7
//                             },
//                         }}
//                         inputProps={{
//                             style: {
//                                 height: 40,
//                                 fontSize:17,
//                             },
//                         }}
//                         name="nrocomprobante"
//                         onChange={(e) => handleChangeNrocomprobante(e)}
//                         value={nrocomprobante}
//                         InputProps={{
//                             endAdornment: (
//                                 <InputAdornment position="end">
//                                     <IconButton
//                                         onClick={search}
//                                         edge="end"
//                                     >
//                                         <Search />
//                                     </IconButton>
//                                 </InputAdornment>
//                             ),
//                         }}
//                     />
//                     <div style={{display:"flex", marginLeft:40}}>
//                         <Button
//                             variant="outlined"
//                             sx={{height:40, width:150, marginTop:2}}
//                             onClick={handleShow}>
//                             <FilterListIcon/> Filtrar
//                         </Button>
//                         <Button
//                             variant="outlined"
//                             sx={{height:40, width:150, marginTop:2, marginLeft:5}}
//                             onClick={downloadExcel}>
//                             <Download/> Exportar
//                         </Button>
//                     </div>
//                 </div>
//             </div>

//             {permisos_acciones?.listar !== "0" &&
//                 <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
//                     <TableContainer component={Paper} sx={{marginTop:5, width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
//                         <Table aria-label="simple table">
//                             <TableHead>
//                                 <TableRow>
//                                     <TableCell style={{fontWeight:"bold", fontSize:"110%"}}>#</TableCell>
//                                     <TableCell style={{fontWeight:"bold", fontSize:"110%", padding:0}} align="center">Cliente</TableCell>
//                                     <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Estado</TableCell>
//                                     <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Fecha</TableCell>
//                                     <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Ver</TableCell>
//                                 </TableRow>
//                             </TableHead>
//                             <TableBody>
//                             {loading ?
//                                 <TableRow sx={{p:20}}>
//                                     <TableCell colSpan={5} align='center'>
//                                         <ClipLoader
//                                             loading={loading}
//                                             size={50}
//                                         />
//                                     </TableCell>
//                                 </TableRow> :
//                                 remitos && remitos.remitos && Object.keys(remitos.remitos).length > 0 ?
//                                     Object.values(remitos.remitos).map((row) => (
//                                         <TableRow
//                                             key={row.facturaid}
//                                             sx={{ '&:last-child td, &:last-child th': { border: 0 }}}
//                                         >
//                                             <TableCell style={{fontSize:"16px", width:"50px", height:"40px", padding: 3}} component="th" scope="row">
//                                                 <Link to={`/Mitienda/Remitos/${row.facturaid}/${row.clienteid}`}>
//                                                     {row.facturaid}
//                                                 </Link>
//                                             </TableCell>
//                                             <TableCell style={{fontSize:"14px", width:"90px", height:"40px", padding: 0}} align="center">
//                                                 {row.nombrecliente}
//                                             </TableCell>
//                                             <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center">
//                                                 {row.descripcionestado || 'Sin estado'}
//                                             </TableCell>
//                                             <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center">
//                                                 {formatoFecha(row.fechaingreso)}
//                                             </TableCell>
//                                             <TableCell style={{fontSize:"14px", width:"50px", height:"40px", padding: 0}} align="center">
//                                                 <Tooltip title="Ver remito">
//                                                     <IconButton onClick={() => handleShowPreviewPdf(row.facturaid)}>
//                                                         <RemoveRedEye/>
//                                                     </IconButton>
//                                                 </Tooltip>
//                                             </TableCell>
//                                         </TableRow>
//                                     )) :
//                                     <TableRow>
//                                         <TableCell colSpan={5} align='center'>
//                                             <h5>No hay información para mostrar</h5>
//                                         </TableCell>
//                                     </TableRow>
//                             }
//                             </TableBody>
//                         </Table>
//                     </TableContainer>
//                     <Pagination
//                         count={paginaUltima}
//                         page={page}
//                         onChange={handleChangePage}
//                         size="large"
//                         sx={{alignSelf:"center"}}
//                     />
//                 </div>
//             }
//         </div>
//     );
// };
