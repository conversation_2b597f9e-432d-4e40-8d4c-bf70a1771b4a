function calcularTotales (array){

    let totales = {
        gravado: 0,
        iva: 0,
        nogravado: 0,
        otros: 0,
        totalFC: 0,
    }

    let num

    if(array){
        for (let i = 0; i < array.length; i++){ 
            if(array[i].baseimp21.includes(".") && array[i].baseimp21.includes(",")){
                num = array[i].baseimp21.split(".")
                num = parseFloat(num[0]+num[1])
                totales.gravado += num
            }else{
                totales.gravado += parseFloat(array[i].baseimp21.replace(",","."))
            }
            totales.iva += parseFloat(array[i].importeiva21)
            totales.nogravado += parseFloat(array[i].nogravado)
            totales.otros += parseFloat(array[i].otrosimpuestos)
            totales.totalFC += parseFloat(array[i].totalFC)
        }
    }

    return totales
}

module.exports = {
    calcularTotales
}