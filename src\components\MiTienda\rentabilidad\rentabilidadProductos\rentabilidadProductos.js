import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button, Pagination, Paper, Snackbar, TextField } from "@mui/material";

import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import MuiAlert from '@mui/material/Alert';
import { Delete, Edit } from "@mui/icons-material";
import { ModalEditarRentabilidad } from "./editarRentabilidad";
import { ClipLoader } from "react-spinners";
import { agregarRentabilidad, getCategoriasByName, getRentabilidad } from "../../../../redux/actions/mitienda.actions";
import { ModalEliminarRentabilidad } from "./eliminarRentabilidad";
import Autocomplete from '@mui/material/Autocomplete';

export const RentabilidadProductos = () =>{

    const info = useSelector((state) => state.mitienda.rentabilidad)
    const ok = useSelector((state) => state.mitienda.okRentabilidad)
    const okEditar = useSelector((state) => state.mitienda.okEditarRentabilidad)
    const okEliminar = useSelector((state) => state.mitienda.okEliminarRentabilidad)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaRentabilidad)
    const categorias = useSelector((state) => state.mitienda.categoriasByName)

    const dispatch = useDispatch()
    const [value, setValue] = useState([]);

    const [input, setInput] = useState({
        porcentaje: '',
        categoriaids: []
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }
    
    const handleClick = () => {
        input.categoriaids = input.categoriaids.map((c) => c.categoriaid).join()
        dispatch(agregarRentabilidad(input))
        setTimeout(function(){
            handleClickAlert()
          }, 3000);
        setInput({porcentaje: '', categoriaids: []})
    }

    const [rentabilidad, setRentabilidad] = useState('')

    const [showEditar, setShowEditar] = useState(false);
    const handleCloseEditar = () => setShowEditar(false);
    const handleShowEditar = (e,row) => {
        e.preventDefault()
        setShowEditar(true);
        setRentabilidad(row)
    }

    const [eliminar, setEliminar] = useState('')
    const [showEliminar, setShowEliminar] = useState(false);
    const handleCloseEliminar = () => setShowEliminar(false);
    const handleShowEliminar = (row) => {
        setEliminar(row)
        setShowEliminar(true);
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openEditar, setOpenEditar] = React.useState(false);
    const handleClickAlertEditar = () => {
      setOpenEditar(true);
    };
    const handleCloseAlertEditar = () => {
        setOpenEditar(false);
    };

    const [openEliminar, setOpenEliminar] = useState(false)
    const handleClickAlertEliminar = () => {
      setOpenEliminar(true);
    };
    const handleCloseAlertEliminar = () => {
        setOpenEliminar(false);
    };

    const [nombreCategoria, setNombreCategoria] = useState('')

    useEffect(() =>{
        if(nombreCategoria.length > 2){
            dispatch(getCategoriasByName(nombreCategoria))
        }
    }, [nombreCategoria])

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() => {
        setLoading(true)
        dispatch(getRentabilidad(page))
    },[ok, okEditar, okEliminar, page])

    return (
        <div className="container-default">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{ok.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEditar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditar} 
                        severity={okEditar.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEditar.mensaje || okEditar.mensajeError }</h5>
                    </Alert>
                </Snackbar>
            }
            <Snackbar
                open={openEliminar} 
                autoHideDuration={10000} onClose={handleCloseAlertEliminar}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertEliminar} 
                    severity={okEliminar.success ? "success" : "error"}
                    sx={{ width: 400 }}>
                    <h5>{okEliminar.mensaje}</h5>
                </Alert>
            </Snackbar>
            {
                <ModalEditarRentabilidad
                    show={showEditar}   
                    handleClose={handleCloseEditar}   
                    rentabilidad={rentabilidad}
                    handleClickAlert={handleClickAlertEditar}
                />
            }
            <ModalEliminarRentabilidad
                show={showEliminar}
                handleClose={handleCloseEliminar}
                id={eliminar}
                handleClickAlert={handleClickAlertEliminar}
            />
            <header className="header-default">
                <h3>Rentabilidad por Productos</h3>
            </header>
            <Paper className="paper-form">
                <TextField 
                    name="porcentaje"
                    label="Porcentaje"  
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    value={input.porcentaje || ''}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <Autocomplete
                    multiple
                    freeSolo={true}
                    options={categorias}
                    value={value}
                    getOptionLabel={(option) => option.categoriaid+'-'+option.nombrecategoria}
                    onChange={(event, newValue) => {
                        setInput({
                            ...input,
                            categoriaids: newValue
                        })
                        setValue(newValue)
                    }}
                    onInputChange={(event, newInputValue) => {
                        setNombreCategoria(newInputValue);
                    }}
                    renderInput={(params) => (
                    <TextField
                        {...params}
                        margin="normal"
                        variant="outlined"
                        label="Categor&iacute;as"
                        placeholder="Buscar categor&iacute;as"
                    />
                    )}
                />
            </Paper>
            <div align="right">
                <Button 
                    disabled={
                        input.porcentaje === '' ||
                        input.categoriaids.length === 0
                    }
                    size="large" 
                    variant="contained" 
                    onClick={(e)=>{
                        handleClick(e)
                        dispatch(getCategoriasByName(""))
                        setValue([])
                    }}
                    sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>

            <TableContainer component={Paper} style={{width:"95%", alignSelf:"center", marginTop:40, marginLeft:40}}>
                <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Nombre de la tienda</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Porcentaje</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Categor&iacute;as</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Acciones</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                    {
                        loading ? 
                        <TableRow sx={{p:20}}>
                            <TableCell colSpan={4} align='center'>
                                <ClipLoader 
                                    loading={loading}
                                    size={50}
                                />
                            </TableCell>
                        </TableRow> :
                        info.length > 0 ? info.map((row) => (
                        <TableRow
                            key={row.descuentovtaid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                            <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                                {row.nombre}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                                {row.rentabilidad}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                                {row.categorias}
                            </TableCell>
                            <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                                <div style={{display:"flex", justifyContent:"center"}}>
                                    <div style={{marginRight:10}} onClick={(e)=>handleShowEditar(e,row)}><Edit/></div>
                                    <div onClick={()=>handleShowEliminar(row)}><Delete/></div>
                                </div>
                            </TableCell>
                        </TableRow> 
                        )):
                        <TableRow>
                            <TableCell colSpan={4} align="center">
                                <h5>No hay informaci&oacute;n para mostrar</h5>
                            </TableCell>
                        </TableRow>}
                    </TableBody>
                    </Table>
                </TableContainer>
                <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", marginTop:5}} />
        </div>
    )
}