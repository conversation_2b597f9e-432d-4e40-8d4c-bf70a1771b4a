import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, FormControl, InputLabel, MenuItem, Select,} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { cambiarEstadoPedido, getEstadosPedido } from "../../../redux/actions/mitienda.actions";
import { useEffect } from "react";

Modal.setAppElement("#root")

export const ModalCambiarEstado = ({show, handleClose,pedido, handleClickAlert}) =>{

    // console.log('pedido en moda', pedido)

    const estados = useSelector((state) => state.mitienda.lista_estado_pedido)

    const [estado, setEstado] = useState('')

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    //w
    const handleChange = () => {
        dispatch(cambiarEstadoPedido(pedido[0].pedidoplacaid,estado))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    }
    
    useEffect(() => {
        dispatch(getEstadosPedido())
    },[])

    useEffect(() => {
        if(pedido !== null){
            setEstado(pedido[0].pedidoplacaestadoid)
        }
    },[pedido])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Cambiar estado del pedido</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>   
                <FormControl style={{marginTop:20}} fullWidth>
                      <InputLabel id="estado">Estado</InputLabel>
                      <Select
                      labelId="estadolabel"
                      id="estado"
                      label="clienteid"  
                      size="small"
                      name="clienteid"
                      value={estado}
                      onChange={e => setEstado(e.target.value)}
                      MenuProps={{ keepMounted: true, disablePortal: true }}
                      >
                          <MenuItem value={0}>Seleccione una opcion</MenuItem>
                      {
                          estados && estados.map((m) => 
                              <MenuItem value={m.pedidoplacaestadoid} key={m.orden}>{m.nombre.toUpperCase()}</MenuItem>
                          )
                      }
                      </Select>
                  </FormControl>
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="contained"
                    onClick={handleChange}
                    sx={{mt: 3, mr:1}}>Cambiar</Button>
                </div>
        </Modal>
    )
}