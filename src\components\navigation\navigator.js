import React from 'react';

import Menu from './dropdown';

import {FiUser} from 'react-icons/fi'

import logo from '../../media/logo_develone.png'
import './navigator.css'
import {useTranslation} from "react-i18next"
import { useDispatch } from 'react-redux';
import { useState } from 'react';

const Navigator = () => {

    const [t, i18n] = useTranslation("global")

    const cerrar = () => {
        localStorage.clear();
        window.location.href = '/';
    }

    return (
        <nav className='nav'>
            <ul>
                <li>
                  <a href='/Home'><img className='logo' src={logo}/></a>
                </li>
                <li className='menu' >
                    <a href='#'><Menu/></a>
                </li>
                <li className='user'>
                    <a href='#'><FiUser size={30}/></a>
                    <div className="dropdown-content">
                        <a href="#" onClick={cerrar}>{t('menu.log-out')}</a>
                    </div>
                </li>
            </ul>
        </nav>
    );
};

export default Navigator;
