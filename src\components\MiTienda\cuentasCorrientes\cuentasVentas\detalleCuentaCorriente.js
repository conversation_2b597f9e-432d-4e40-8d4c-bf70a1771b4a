import React, { useEffect, useState } from 'react';
import { 
    <PERSON><PERSON>, 
    TextField 
} from '@mui/material';

import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { 
    getDetalleCuentaCorriente, 
} from '../../../../redux/actions/mitienda.actions'
import { ArrowBack, AttachMoney } from '@mui/icons-material';
import { ModalAfectar } from './modalAfectarPagoCuentaCorriente';

export const DetalleCuentaCorriente = (props) => {

    const clienteid = props.match.params.clienteid

    const info = useSelector((state) => state.mitienda.detalleCuentaCorriente)
    const cliente = useSelector((state) => state.mitienda.detalleCuentaCorrienteCliente)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaCuentasCorrientes)
    const okAfectarPagoCuentaCorriente = useSelector((state) => state.mitienda.okAfectarPagoCuentaCorriente)

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };
    
    let fechaActual = new Date().toJSON().slice(0,10)

    const [nrocomprobante, setNrocomprobante] = useState('')
    const [fechacomprobante, setFechaComprobante] = useState(fechaActual)

    const dispatch = useDispatch()

    const [factura, setFactura] = useState('')

    const [showPagos, setShowPagos] = useState('');
    const handleClosePagos = () => setShowPagos(false);
    const handleShowPagos = (p) => {
        setFactura(p)
        setShowPagos(true);
    }

    const onSearch = () => {
        dispatch(getDetalleCuentaCorriente(page,clienteid,nrocomprobante,fechacomprobante))
    }

    //page,clienteid
    useEffect(() =>{
        dispatch(getDetalleCuentaCorriente(page,clienteid,nrocomprobante,fechacomprobante))
    },[page, clienteid, okAfectarPagoCuentaCorriente])

    return (
        <div className="notificaciones-container">
            <ModalAfectar
                show={showPagos}
                handleClose={handleClosePagos}
                info={factura}
                cliente={cliente}
            />
            <div>
            <a href="/Mitienda/CuentasCorrientes" style={{color:'black'}}>
                <div style={{display:"flex", fontSize:"130%", marginBottom:20, marginLeft:40}}>
                    <ArrowBack style={{fontSize:"130%"}}/>
                    Volver a Cuentas Corrientes
                </div>
            </a>
                <header className="titulo-notificaciones" style={{display:"flex", flexDirection:"column"}}>
                    <h3>Cuenta corriente: {cliente.nombre}</h3>
                    <div style={{display:"flex"}}>
                    <TextField 
                        label="N&uacute;mero de comprobante"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{width:300, marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        value={nrocomprobante}
                        onChange={(e) => setNrocomprobante(e.target.value)}
                    />
                    <TextField 
                        label="Fecha del comprobante"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{width:300, marginRight:5}}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        type='date'
                        value={fechacomprobante}
                        onChange={(e) => setFechaComprobante(e.target.value)}
                    />
                    <Button onClick={onSearch} variant='contained' style={{height:40, marginTop:15}} >Buscar</Button>
                    </div>
                </header>
            
            </div>
            <div className='container-tabla'>
                <TableContainer component={Paper} className='tabla'>
                    <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell colSpan={2} style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Total ventas con CC
                                </TableCell>
                                <TableCell colSpan={2} style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Total pagos CC
                                </TableCell>
                                <TableCell colSpan={1} style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Total deuda CC
                                </TableCell>
                                <TableCell colSpan={3} style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Total pendiente afectar
                                </TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                                {
                                    cliente !== '' &&
                                    <TableRow
                                        key={cliente.clienteid}
                                        sx={{ '&:last-child td, &:last-child th': { borderBottom: 1 } }}
                                    >
                                    <TableCell colSpan={2} style={{fontSize:"120%", fontWeight:"bold", padding:0, height:70, width:200}} align="center">
                                        ${cliente.total.ventas_cc}
                                    </TableCell>
                                    <TableCell colSpan={2} style={{fontSize:"100%", padding:0, height:70, width:80}} align="center">
                                        ${cliente.total.en_contra}
                                    </TableCell>
                                    <TableCell colSpan={1} style={{fontSize:"100%", padding:0, height:70, width:80}} align="center">
                                        ${cliente.total.deuda_cc}
                                    </TableCell>
                                    <TableCell colSpan={3} style={{fontSize:"120%", fontWeight:"bold", padding:0, height:70, width:50}} align="center">
                                        ${cliente.total.sin_asignar}
                                    </TableCell>
                                </TableRow>
                                }
                        </TableBody>
                        <TableHead>
                            <TableRow>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Origen
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    N&uacute;mero
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Nombre
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Fecha
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Monto operaci&oacute;n
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Monto afectado
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Monto deudor
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">
                                    Afectar
                                </TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {
                            info.length > 0 && info.map((row) => 
                            <TableRow
                                key={row.clienteid}
                                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                            >
                                <TableCell style={{fontSize:"80%", padding:0, height:40, width:100}} align="center">
                                    {row.origen}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                    {row.nro}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:250}} align="center">
                                    {row.nombre}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                    {row.fechaComprobante}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">
                                    ${row.totalcomprobante}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">
                                    ${row.totalafectado}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">
                                    ${row.totalrestanteafectar}
                                </TableCell>
                                <TableCell 
                                    style={{fontSize:"80%", padding:0, height:45, width:90}} align="center"
                                    onClick={()=>handleShowPagos(row)}
                                ><AttachMoney/>
                                </TableCell>
                            </TableRow>
                        )}
                        </TableBody>
                    </Table>
                </TableContainer>
            </div>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}
