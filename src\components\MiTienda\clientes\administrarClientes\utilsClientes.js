function matchCondicionPago (cliente,condiciones){
    let condicionid = condiciones.filter((c) => c.nombre == cliente.tipocondicionpago )

    return condicionid[0].tipocondicionpagoid
}

function matchCondicionIva (cliente,condiciones){
    let condicionid = condiciones.filter((c) => c.nombre == cliente.tipocondicioniva )

    return condicionid[0].tipocondicionivaid
}

function matchTipoDocumento (cliente,condiciones){
    let condicionid = condiciones.filter((c) => c.nombre == cliente.tipodocidentidad )

    return condicionid[0].tipodocidentidadid
}

module.exports = {
    matchCondicionPago,
    matchCondicionIva,
    matchTipoDocumento
}