import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, InputAdornment, TextField, Select, FormControl, InputLabel, MenuItem } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { AttachMoney } from "@mui/icons-material";
import { afectarPagoCuentaCorriente, afectarPagoCuentaCorrienteProveedor, getPagosCuentaCorriente, getPagosCuentaCorrienteProveedor } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalAfectarPagoCuentaCorrienteProveedor = ({show, handleClose, info, cliente, handleClickAlert}) =>{

    const pagosCuentaCorriente = useSelector((state) => state.mitienda.pagosCuentaCorrienteProveedor)

    const dispatch = useDispatch()

    const [monto, setMonto] = useState('')
    const [pagosctaxcobrarid, setPagosctaxcobrarid] = useState('')

    const handleChange =(e)=> {
        setPagosctaxcobrarid(e.target.value)
    }

    //clienteid, pedidoid, pagosctaxcobrarid, montoabono
    const handleAfectar =()=> {
        dispatch(afectarPagoCuentaCorrienteProveedor(cliente.proveedorid, info.nro, pagosctaxcobrarid, monto ))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        handleClose()
    }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    useEffect(() => {
        dispatch(getPagosCuentaCorrienteProveedor(1,cliente.proveedorid))
    },[])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <div style={{display:"flex", flexDirection:"column"}}>
                        <h4>Cuentas Corrientes - Afectar pago</h4>
                        <p style={{fontSize:18}}>Cliente: {cliente.nombre}</p>
                    </div>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>   
                    <p style={{fontSize:18}}>Pedido #{info.nro} Monto: ${info.totalcomprobante}</p>
                    <p style={{fontSize:16}}>Pendiente Afectar: {info.totalrestanteafectar}</p>
                </div>
                <div style={{margin:15}}>
                    <FormControl fullWidth
                        margin="normal">
                        <InputLabel id="cuentabanco-select-label">Pagos cuenta corriente</InputLabel>
                        <Select
                        style={{height:40}}
                        labelId="cuentabanco-select-label"
                        label="Pagos cuenta corriente"
                        size="small"
                        name="cuentabancoid"
                        value={pagosctaxcobrarid || ''}
                        onChange={handleChange}
                        MenuProps={{ keepMounted: true, disablePortal: true }}
                        >
                        <MenuItem value="">Seleccione una opcion</MenuItem>
                        {               
                            pagosCuentaCorriente && pagosCuentaCorriente.map((t) =>
                                <MenuItem value={t.pagosctaxpagarid} key={t.pagosctaxpagarid}>
                                    {t.fechapago+' - '+t.tipopago+' | Monto afectar: $'+t.montoabono}
                                </MenuItem>
                            )
                        }
                        </Select>
                    </FormControl>
                    <TextField 
                        label="Monto afectar"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{width:300, marginRight:5}}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <AttachMoney/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        value={monto}
                        onChange={(e) => setMonto(e.target.value)}
                    />
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="contained" 
                    onClick={handleAfectar}
                    sx={{mt: 3}}>Afectar</Button>
                </div>
        </Modal>
    )
}