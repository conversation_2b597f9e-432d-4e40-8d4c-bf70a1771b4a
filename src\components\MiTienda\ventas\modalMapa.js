import React, { useState } from "react"
import Modal from 'react-modal';
import {
    APIProvider, 
    AdvancedMarker, 
    InfoWindow, 
    Map, 
    Pin, 
    useAdvancedMarkerRef
} from '@vis.gl/react-google-maps';
import { Button, Divider } from "@mui/material";
import './ventas.css'

Modal.setAppElement("#root")

export const ModalMapaPuntos = ({
    show, 
    handleClose, 
    points, 
    point, 
    setPoint, 
    setPuntoId, 
    handleChange
}) =>{
    
    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "90%",
          height: "90vh",
          borderRadius: "8px",
          display:"flex",
          flexDirection:"column"
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const position = {lat: point.lat, lng: point.lng};
    const [markerRef, marker] = useAdvancedMarkerRef();

    return (
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <div className="container-listado-puntos-mapa">
            <div className="container-listado-puntos">
                <ul style={{listStyle:"none"}}>
                    {
                        points.length > 0 && points.map((p) =>
                            <li onClick={()=>setPoint(p)} className="info-punto">
                                <h5>{p.name.toUpperCase()}</h5>
                                <h6>{p.address}</h6>
                                <ul style={{padding:0}}>
                                {
                                    p.schedule.map((s) =>
                                        <li style={{listStyle:"none", paddingBottom:5}}>
                                            <p style={{display:"flex", justifyContent:"space-between"}}>{s.dia}{
                                                s.horarios.length === 2 ?
                                                <p>{s.horarios[0].open.slice(0,5)} a {s.horarios[0].close.slice(0,5)} - {s.horarios[1].open.slice(0,5)} a {s.horarios[1].close.slice(0,5)}               
                                                </p> :
                                                s.horarios.map((h) => <p>{h.open.slice(0,5)} a {h.close.slice(0,5)}&nbsp;</p>)
                                            }</p>
                                        </li>
                                    )
                                }
                                <Divider/>
                                </ul>
                            </li>
                        )
                    }
                </ul>
            </div>
            <APIProvider apiKey={process.env.REACT_APP_GOOGLE_MAPS_KEY}>
            <Map center={position} zoom={12} mapId={process.env.REACT_APP_GOOGLE_MAPS_MAP_ID}>
                {
                    points.length > 0 && points.map ((p) => 
                    <AdvancedMarker 
                        position={{lat: p.lat, lng: p.lng}} 
                        onClick={()=>setPoint(p)}
                        key={p.id}
                    >
                        <Pin background={'#FFF56D'} glyphColor={'#F55353'} borderColor={'#F55353'} />
                    </AdvancedMarker>
                    )
                }
                {
                    point !== undefined && point !== '' ?
                    <AdvancedMarker ref={markerRef} position={{lat: point.lat, lng: point.lng}}>
                        <Pin 
                            background={'#AA2EE6'} 
                            glyphColor={'#72147E'} 
                            borderColor={'#72147E'} 
                            scale={1.5}
                        />
                        <InfoWindow anchor={marker}>
                            <div>
                                <p>
                                {point.name}
                                </p>
                                <Button
                                    variant="outlined"
                                    onClick={()=>{
                                        setPuntoId(point.id)
                                        handleChange(parseInt(point.id))
                                        handleClose()
                                    }}
                                    >
                                        Seleccionar
                                </Button>
                            </div>
                        </InfoWindow>
                    </AdvancedMarker> : null
                }
            </Map>
            </APIProvider>
            </div>
        </Modal>
    )
}