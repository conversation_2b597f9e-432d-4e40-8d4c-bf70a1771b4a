import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { <PERSON><PERSON>, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { agregarDatosFooter } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const AgregarTituloFooter = ({show, handleClose, handleClickAlert}) =>{

  const [input, setInput] = useState({
    titulo: '',
    subtitulo: '',
    descripcion: '',
    orden:'',
    url: ''
  })

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value})
  }

  const dispatch = useDispatch()

  const handleClick = () => {
    dispatch(agregarDatosFooter(input))
    setInput({
      titulo: '',
      subtitulo: '',
      descripcion: '',
      orden:'',
      url: ''
    })
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "850px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    return (
        show ? 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
          <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
              <h4>Agregar t&iacute;tulo</h4>
              <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
          </div>
          <Divider/>
          <div style={{marginTop:15, marginBottom:15}}>
            <TextField 
              name="titulo"
              label="T&iacute;tulo"  
              margin="normal" 
              onChange={(e)=> handleChange(e)}
              value={input && input.titulo || ''}
              style={{width:"95%"}}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
              inputProps={{
                  style: {
                  height: 40,
                  fontSize:17
                },
              }}
            />
            <TextField 
              name="subtitulo"
              label="Subtitulo"  
              margin="normal" 
              onChange={(e)=> handleChange(e)}
              value={input && input.subtitulo || ''}
              style={{width:"95%"}}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
              inputProps={{
                  style: {
                  height: 40,
                  fontSize:17
                },
              }}
            />
            <TextField 
              name="url"
              label="URL"  
              margin="normal" 
              onChange={(e)=> handleChange(e)}
              value={input && input.url || ''}
              style={{width:"95%"}}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
            />
            <TextField 
              name="orden"
              label="Orden"  
              margin="normal" 
              onChange={(e)=> handleChange(e)}
              value={input && input.orden || ''}
              style={{width:"95%"}}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
            />
            <TextField 
              name="descripcion"
              label="Descripcion"  
              margin="normal" 
              onChange={(e)=> handleChange(e)}
              value={input && input.descripcion || ''}
              style={{width:"95%"}}
              InputLabelProps={{
                style: {
                  marginBottom:10,
                  marginTop: -7
                },
              }}
              minRows={8}
              multiline
            />
          </div>
          <Divider/>
          <div align="right">
            <Button 
              size="large" 
              variant="contained" 
              sx={{mt: 3}}
              onClick={handleClick}
              >Agregar</Button>
          </div>
        </Modal> : null 
    )
}