import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import Select from '@mui/material/Select';
import { Button, Checkbox, FormControl, FormControlLabel, InputAdornment, InputLabel, ListSubheader, MenuItem, TextField } from "@mui/material";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { getCategoriasByName, getColoresByName, getMarcasByName, getTallasByName } from "../../../../redux/actions/mitienda.actions";
import { useSelector } from "react-redux";
import { Search } from "@mui/icons-material";

Modal.setAppElement("#root")

export const ModalNotificaciones = ({
  show, 
  handleClose, 
  setMarca, 
  setCategoria, 
  setColor, 
  setTalla, 
  handleChangeNombre, 
  nombre,
  handleChangeCodigo, 
  codigo,
  handleCantidadReservada,
  reservada,
  handleCantidadDisponible,
  disponible,
  search, 
  reset
}) =>{

  const customStyles = {
    content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
    },
  };

  const categorias = useSelector((state) => state.mitienda.categoriasByName)
  const marcas = useSelector((state) => state.mitienda.marcasByName)
  const tallas = useSelector((state) => state.mitienda.tallasByName)
  const colores = useSelector((state) => state.mitienda.coloresByName)

  const [nombreCategoria, setNombreCategoria] = useState('')
  const [nombreMarca, setNombreMarca] = useState('')
  const [nombreTalla, setNombreTalla] = useState('')
  const [nombreColor, setNombreColor] = useState('')
  const [selectedOption, setSelectedOption] = useState('');
  const [selectedOptionMarca, setSelectedOptionMarca] = useState('');
  const [selectedOptionTalla, setSelectedOptionTalla] = useState('');
  const [selectedOptionColor, setSelectedOptionColor] = useState('');

  const dispatch = useDispatch()

  useEffect(() =>{
    if(nombreCategoria.length > 2){
    dispatch(getCategoriasByName(nombreCategoria))
    }
  }, [nombreCategoria])

  useEffect(() =>{
    if(nombreMarca.length > 2){
    dispatch(getMarcasByName(nombreMarca))
    }
  }, [nombreMarca])

  useEffect(() =>{
    if(nombreTalla.length > 1){
    dispatch(getTallasByName(nombreTalla))
    }
  }, [nombreTalla])

  useEffect(() =>{
    if(nombreColor.length > 2){
        dispatch(getColoresByName(nombreColor))
    }
  }, [nombreColor])

  return (
      show && 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
            <h4>FILTRAR</h4>
            <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
        </div>
        <Divider/>
        <div style={{display:"flex", flexDirection:"column"}}>

            <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                <InputLabel id="search-select-label">Marcas</InputLabel>
                <Select
                MenuProps={{ autoFocus: false,  disablePortal: true }}
                labelId="search-select-label"
                id="search-select"
                value={selectedOptionMarca.marcaid || ''}
                label="Marcas"
                onClose={() => {
                    dispatch(getMarcasByName(""))
                }}
                size="small"
                renderValue={() => selectedOptionMarca.nombremarca}
                >
                <ListSubheader>
                <TextField
                    size="small"
                    autoFocus
                    placeholder="Buscar marca..."
                    fullWidth
                    InputProps={{
                        startAdornment: (
                        <InputAdornment position="start">
                            <Search />
                        </InputAdornment>
                        )
                    }}
                    inputProps={{
                        style: {
                            height: 35,
                            fontSize:17
                        },
                    }}
                    onChange={(e) => setNombreMarca(e.target.value)}
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                />
                </ListSubheader>
                {marcas.map((option, i) => (
                    <MenuItem key={i} value={option.marcaid} onClick={() => {
                        setSelectedOptionMarca(option)
                        setNombreMarca(option.nombremarca)
                        setMarca(option.marcaid)
                    }}>
                    {option.nombremarca}
                    </MenuItem>
                ))}
                </Select>
            </FormControl>

            <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                <InputLabel id="search-select-label">Categor&iacute;as</InputLabel>
                <Select
                MenuProps={{ autoFocus: false,  disablePortal: true }}
                labelId="search-select-label"
                id="search-select"
                value={selectedOption.categoriaid || ''}
                label="Categor&iacute;as"
                onClose={() => {
                    dispatch(getCategoriasByName(""))
                }}
                size="small"
                renderValue={() => selectedOption.nombrecategoria}
                >
                <ListSubheader>
                <TextField
                    size="small"
                    autoFocus
                    placeholder="Buscar categor&iacute;a..."
                    fullWidth
                    InputProps={{
                        startAdornment: (
                        <InputAdornment position="start">
                            <Search />
                        </InputAdornment>
                        )
                    }}
                    inputProps={{
                        style: {
                            height: 35,
                            fontSize:17
                        },
                    }}
                    onChange={(e) => setNombreCategoria(e.target.value)}
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                />
                </ListSubheader>
                {categorias.map((option, i) => (
                    <MenuItem key={i} value={option.categoriaid} onClick={() => {
                        setSelectedOption(option)
                        setNombreCategoria(option.nombrecategoria)
                        setCategoria(option.categoriaid)
                    }}>
                    {option.nombrecategoria}
                    </MenuItem>
                ))}
                </Select>
            </FormControl>

            <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                <InputLabel id="search-select-label">Medidas</InputLabel>
                <Select
                MenuProps={{ autoFocus: false,  disablePortal: true }}
                labelId="search-select-label"
                id="search-select"
                value={selectedOptionTalla.tallaid || ''}
                label="Medidas"
                onClose={() => {
                    dispatch(getTallasByName(""))
                }}
                size="small"
                renderValue={() => selectedOptionTalla.nombretalla}
                >
                <ListSubheader>
                <TextField
                    size="small"
                    // Autofocus on textfield
                    autoFocus
                    placeholder="Buscar medida..."
                    fullWidth
                    InputProps={{
                        startAdornment: (
                        <InputAdornment position="start">
                            <Search />
                        </InputAdornment>
                        )
                    }}
                    inputProps={{
                        style: {
                            height: 35,
                            fontSize:17
                        },
                    }}
                    onChange={(e) => setNombreTalla(e.target.value)}
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                />
                </ListSubheader>
                {tallas.map((option, i) => (
                    <MenuItem key={i} value={option.tallaid} onClick={() => {
                        setSelectedOptionTalla(option)
                        setNombreTalla(option.nombretalla)
                        setTalla(option.tallaid)
                    }}>
                    {option.nombretalla}
                    </MenuItem>
                ))}
                </Select>
            </FormControl>

            <FormControl fullWidth sx={{marginTop:2, marginBottom:1}}>
                <InputLabel id="search-select-label">Colores</InputLabel>
                <Select
                MenuProps={{ autoFocus: false,  disablePortal: true }}
                labelId="search-select-label"
                id="search-select"
                value={selectedOptionColor.colorid || ''}
                label="Colores"
                onClose={() => {
                    dispatch(getColoresByName(""))
                }}
                size="small"
                renderValue={() => selectedOptionColor.nombre}
                >
                <ListSubheader>
                <TextField
                    size="small"
                    autoFocus
                    placeholder="Buscar color..."
                    fullWidth
                    InputProps={{
                        startAdornment: (
                        <InputAdornment position="start">
                            <Search />
                        </InputAdornment>
                        )
                    }}
                    inputProps={{
                        style: {
                            height: 35,
                            fontSize:17
                        },
                    }}
                    onChange={(e) => setNombreColor(e.target.value)}
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                />
                </ListSubheader>
                {colores.map((option, i) => (
                    <MenuItem key={i} value={option.colorid} onClick={() => {
                        setSelectedOptionColor(option)
                        setNombreColor(option.nombre)
                        setColor(option.colorid)
                    }}>
                    {option.nombre}
                    </MenuItem>
                ))}
                </Select>
            </FormControl>
                  
            <TextField 
                sx={{mt:3}}
                label="Codigo articulo"  
                variant="outlined" 
                margin="codigo" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                        height: 40,
                        fontSize:17
                    },
                }}
                name="nombre"
                onChange={(e) => handleChangeCodigo(e)}
                value={codigo}
            />
            <TextField 
                sx={{mt:4}}
                label="Nombre articulo"  
                variant="outlined" 
                margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                        height: 40,
                        fontSize:17
                    },
                }}
                name="nombre"
                onChange={(e) => handleChangeNombre(e)}
                value={nombre}
            />
            <div style={{display:"flex"}}>
            <FormControlLabel
                sx={{mr:5, ml:5}}
                control={
                <Checkbox/>
                }
                name="cantidadDisponible"
                style={{color:"black"}}
                onChange={(e)=> handleCantidadDisponible(e)}
                checked={disponible == "1" ? true : false}
                label="Cantidad disponible"
            />
            <FormControlLabel
                sx={{ml:5}}
                control={
                <Checkbox/>
                }
                name="cantidadReservada"
                style={{color:"black"}}
                onChange={(e)=> handleCantidadReservada(e)}
                checked={reservada == "1" ? true : false}
                label="Cantidad reservada"
            />
            </div>
        </div>
        <div align="center">
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={search}
            >Aplicar filtros</Button>
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={() => {
                    reset()
                    setNombreColor('')
                    setNombreCategoria('')
                    setNombreMarca('')
                    setNombreTalla('')
                    setSelectedOptionColor('')
                    setSelectedOption('')
                    setSelectedOptionMarca('')
                    setSelectedOptionTalla('')
                    dispatch(getColoresByName(""))
                    dispatch(getTallasByName(""))
                    dispatch(getMarcasByName(""))
                    dispatch(getTallasByName(""))
                }}
            >Limpiar filtros</Button>
        </div>
      </Modal>
  )
}