import React, { useState } from "react"
import Modal from 'react-modal';

import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Divider from '@mui/material/Divider';
import ArticleIcon from '@mui/icons-material/Article';
import { Button, ListItemIcon, ListSubheader, Paper, TextField } from "@mui/material";

Modal.setAppElement("#root")

export const ModalEliminar = ({show, handleClose}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Eliminar producto</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>
                    <h5>Esta seguro de eliminar el producto?</h5>
                </div>
                <Divider/>
                <div align="right">
                <Button size="large" variant="contained" sx={{mt: 3}}>Eliminar</Button>
                </div>
        </Modal>
    )
}