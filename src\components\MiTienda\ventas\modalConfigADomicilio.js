import React from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { useDispatch } from "react-redux";
import { agregarConceptoEnvio, cotizarEnvioADomicilio, getClienteById} from "../../../redux/actions/mitienda.actions";
import { useEffect } from "react";

Modal.setAppElement("#root")

export const ModalConfigADomicilio= ({
    show, 
    handleClose, 
    clienteid, 
    cliente, 
    infoTienda, 
    cotizacion, 
    productos, 
    facturaid, 
    getCalcularCarrito
}) =>{

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "700px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const handleCotizarEnvio = () =>{
        dispatch(cotizarEnvioADomicilio(productos, infoTienda, cliente))
    }

    const handleGuardarEnvio = () =>{
        dispatch(agregarConceptoEnvio(clienteid,facturaid,cotizacion.price,cotizacion.tax,cotizacion.totalPrice))
        dispatch(getCalcularCarrito(clienteid))
        handleClose()
    }

    useEffect(() => {
        if(clienteid !== undefined && clienteid !== ''){
            dispatch(getClienteById(clienteid))
        }
    },[clienteid])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Configurar Env&iacute;o a domicilio</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15, display:"flex", flexDirection:"column", justifyContent:"center", alignItems:"center" }}>   
                    <Button onClick={handleCotizarEnvio} variant="outlined" style={{width:"50%"}}>Cotizar env&iacute;o a domicilio</Button>
                    <div style={{display:"flex", flexDirection:"column", marginTop:10}}>
                        <p style={{fontSize:18}}><strong>Precio:</strong> ${cotizacion.price+".00" || ''}</p>
                        <p style={{fontSize:18}}><strong>Impuestos:</strong> {cotizacion.tax || ''}%</p>
                        <p style={{fontSize:18}}><strong>Total:</strong> ${cotizacion.totalPrice+".00" || ''}</p>
                    </div>
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="contained"
                    onClick={handleGuardarEnvio}
                    sx={{mt: 3, mr:1}}>Guardar</Button>
                </div>
        </Modal>
    )
}