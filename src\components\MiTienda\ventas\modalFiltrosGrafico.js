import React from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";

Modal.setAppElement("#root")

export const ModalFiltrosGraficoVentas = ({
  show, 
  handleClose, 
//   handleChangeClienteid, 
//   clienteid, 
//   handleChangeNrocomprobante, 
//   nrocomprobante, 
//   handleChangeNombrecliente, 
//   nombrecliente,
  handleChangeFechaDesde, 
  fechaDesde,
  handleChangeFechaHasta, 
  fechaHasta,
  search, 
  reset}) =>{

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };

  return (
      show && 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
              <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                  <h4>FILTRAR</h4>
                  <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
              </div>
              <Divider/>
              <div style={{display:"flex", flexDirection:"column"}}>
                  {/* <FormControl style={{marginTop:20}}>
                      <InputLabel id="clienteid">Cliente</InputLabel>
                      <Select
                      labelId="clienteidlabel"
                      id="clienteid"
                      label="clienteid"  
                      size="small"
                      name="clienteid"
                      value={clienteid}
                      onChange={handleChangeClienteid}
                      MenuProps={{ keepMounted: true, disablePortal: true }}
                      >
                          <MenuItem value={0}>Seleccione una opcion</MenuItem>
                      {
                          clientes && clientes.map((m) => 
                              <MenuItem value={m.clienteid}>{m.nombre}</MenuItem>
                          )
                      }
                      </Select>
                  </FormControl> */}
                {/* <TextField 
                    style={{marginTop:20,marginBottom:20}}
                    label="Numero comprobante"  
                    variant="outlined"  
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nrocomprobante"
                    onChange={(e) => handleChangeNrocomprobante(e)}
                    value={nrocomprobante}
                  />
                  <TextField 
                    style={{marginBottom:20}}
                    label="Nombre cliente"  
                    variant="outlined"
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nombrecliente"
                    onChange={(e) => handleChangeNombrecliente(e)}
                    value={nombrecliente}
                  /> */}
                <div style={{display:"flex", flexDirection:"column", marginTop:10}}>
                    <h6>Fecha desde</h6>
                  <TextField 
                    style={{marginBottom:20}}
                    variant="outlined" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="fechaDesde"
                    onChange={(e) => handleChangeFechaDesde(e)}
                    value={fechaDesde}
                    type="date"
                  />
                </div>  
                <div style={{display:"flex", flexDirection:"column"}}>
                    <h6>Fecha hasta</h6>
                    <TextField 
                        style={{marginBottom:20}}
                        variant="outlined" 
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        name="fechaHasta"
                        onChange={(e) => handleChangeFechaHasta(e)}
                        value={fechaHasta}
                        type="date"
                    /> 
                </div>   
              </div>
              <div align="center">
              <Button 
                  size="large" 
                  variant="contained" 
                  sx={{mt: 3}} fullWidth 
                  onClick={search}
              >Aplicar filtros</Button>
              <Button 
                  size="large" 
                  variant="contained" 
                  sx={{mt: 3}} fullWidth 
                  onClick={reset}
              >Limpiar filtros</Button>
              </div>
      </Modal>
  )
}