import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { RentabilidadCategorias } from './rentabilidadCategorias/rentabilidadCategorias';

export const Rentabilidad = () => {
  const [value, setValue] = React.useState('categorias');

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <div className='container-punto-de-venta'>
    <Tabs
      value={value}
      onChange={handleChange}
      textColor="primary"
      indicatorColor="primary"
      aria-label="primary tabs example"
    >
      <Tab 
        value="categorias" 
        label="Categor&iacute;as" 
        disabled={value !== "categorias"}
      />
      <Tab 
        value="marcas" 
        label="Marcas" 
        disabled={value !== "marcas"}
      />
        <Tab 
        value="productos" 
        label="Productos" 
        disabled={value !== "productos"}
      />
    </Tabs>
    {
        value === "categorias" ?
        <RentabilidadCategorias/> : null
    }
    </div>
  );
}