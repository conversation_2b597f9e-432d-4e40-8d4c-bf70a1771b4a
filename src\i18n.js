import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from 'i18next-browser-languagedetector';

import global_es from "./translations/es/global.json"
import global_en from "./translations/en/global.json"

const resources = {
    es:{
        global: global_es,
    },
    en:{
        global: global_en
    }
};


const DETECTION_OPTIONS = {
    order: ['localStorage', 'navigator'],
    caches: ['localStorage']
};

i18next
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
        resources,
        detection: DETECTION_OPTIONS,
        fallbackLng: 'es',
        interpolation: {
            escapeValue: false,
        },
    });
export default i18next;