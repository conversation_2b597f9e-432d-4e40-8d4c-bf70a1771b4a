import React, { useState } from "react"
import Modal from 'react-modal';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';

Modal.setAppElement("#root")

export const ModalShareOptions = ({show, handleClose, info}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "50%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "400px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    let url = localStorage.getItem('direccionweb')

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Compartir producto</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <div>
                    <a style={{display:"flex", color:"black"}} target="_blank" href={`${url}producto/${info.nombre}/${info.id}`}>
                        <OpenInNewIcon style={{fontSize:30, marginRight:5}}/> <div style={{alignSelf:"center", fontSize:20}}>Ver producto en la tienda</div>
                    </a>
                </div>
        </Modal>
    )
}