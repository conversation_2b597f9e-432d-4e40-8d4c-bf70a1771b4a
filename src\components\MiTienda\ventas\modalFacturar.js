import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { useDispatch } from "react-redux";
import { facturarPedido } from "../../../redux/actions/mitienda.actions";
import { useEffect } from "react";

Modal.setAppElement("#root")

export const ModalFacturar = ({show, handleClose, pedido, handleClickAlert}) =>{

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [input, setInput] = useState('')

    const handleFacturar = () => {
        dispatch(facturarPedido(pedido[0].pedidoplacaid))
        setTimeout(function(){
            handleClickAlert()
        }, 3000);
    }
    
    useEffect(() => {
        setInput(pedido)
    },[pedido])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Facturar</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15, display:"flex", flexWrap:"wrap", justifyContent:"center"}}>  
                    <Button style={{margin:10}} variant="contained" onClick={handleFacturar}>
                        Generar factura
                    </Button> 
                    <Button style={{margin:10}} variant="contained" disabled={input[0].facturado === 1 ? false : true}>
                        <a style={{color:"white"}} href={input[0].url_comprobante} target="_blank" >
                            Descargar factura
                        </a>
                    </Button> 
                    <div style={{display:"flex", alignContent:"center", margin:10, flexDirection:"column"}}>
                        <Button variant="contained" disabled={input[0].facturado === 1 ? false : true}>
                            Enviar factura al email
                        </Button> 
                    </div>
                </div>
        </Modal>
    )
}