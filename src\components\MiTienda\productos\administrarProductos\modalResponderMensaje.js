import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { enviarMensajeProducto, marcarMensajeLeido } from "../../../../redux/actions/mitienda.actions";


Modal.setAppElement("#root")

export const ModalResponderMensaje = ({show, handleClose, mensaje}) =>{

    const okMensaje = useSelector((state) => state.mitienda.okSendMessage)
    
    var idusuario = localStorage.getItem('idusuario')

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "95vh",
          overflow:"hidden",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const formatoFecha = (fecha) =>{
        let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
    }

    const [input, setInput] = useState({
        titulo: '',
        mensaje: '',
        usuariodestinoid: '',
        pedidoid: ''
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }
    
    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(enviarMensajeProducto(input,mensaje.usuarioid,))
        dispatch(marcarMensajeLeido(mensaje))
        setInput({
            titulo: mensaje.usuarioid,
            mensaje: '',
            usuariodestinoid: mensaje.usuarioid,
            productoid: mensaje.productoid
        })
        handleClose()
    }

    useEffect(() =>{
        if(mensaje){
            setInput({
                titulo: mensaje.usuarioid,
                mensaje: '',
                usuariodestinoid: mensaje.usuarioid,
                productoid: mensaje.productoid
            })
        }
    },[show,mensaje])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Responder</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>

                <TextField
                    fullWidth
                    style={{marginTop:20,marginBottom:20}}
                    label="Mensaje"  
                    variant="outlined"  
                    name="mensaje"
                    value={input.mensaje}
                    onChange={(e)=>handleChange(e)}
                    multiline
                    minRows={3}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                />
                
                <Divider/>

                <div align="right">
                    <Button 
                    disabled={input.mensaje === ""}
                    size="large"
                    variant="contained"
                    onClick={handleOnClick}
                    sx={{mt: 3, mr:1}}>Enviar</Button>
                </div>
        </Modal>
    )
}