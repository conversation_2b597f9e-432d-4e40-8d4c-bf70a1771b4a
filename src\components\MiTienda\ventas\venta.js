//f

import React from "react";
import {
  <PERSON>ton,
  CircularProgress,
  Divider,
  IconButton,
  Snackbar,
  Tooltip,
  Typography,
  TextField,
} from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "./ventas.css";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  editarFechaEntrega,
  finalizarPedido,
  getCiudad,
  getDatosTienda,
  getInfoPickit,
  getPagosRealizados,
  getPedido,
  getProvincia,
  getPuntoPickit,
  getTicketPickit,
} from "../../../redux/actions/mitienda.actions";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import { useState } from "react";
import { ModalCancelar } from "./modalCancelarVenta";
import { Edit, Forum, WhatsApp } from "@mui/icons-material";
import MuiAlert from "@mui/material/Alert";
import { ModalRegistrarPago } from "./modalRegistrarPago";
import { ModalVerPagos } from "./modalVerPagos";
import { ModalCambiarEstado } from "./modalCambiarEstado";
import { ModalFacturar } from "./modalFacturar";
import { ModalMensaje } from "./modalMensaje";
import { ModalMensajesPedido } from "./modalMensajesPedido";

export const Venta = (props) => {
  const id = props.match.params.id;
  const factura = props.match.params.factura;

  const data = useSelector((state) => state.mitienda.pedido);
  const direcciontienda = useSelector((state) => state.mitienda.datostienda);
  const ok = useSelector((state) => state.mitienda.pedidoCancelado);
  const okCambiarEstadoPedido = useSelector(
    (state) => state.mitienda.okCambiarEstadoPedido
  );
  const registrarPago = useSelector((state) => state.mitienda.registrarPago);
  const dataPedidoPickit = useSelector(
    (state) => state.mitienda.info_pedido_pickit
  );
  const dataUrlticket = useSelector((state) => state.mitienda.url_pickit);
  const provincia = useSelector((state) => state.mitienda.provincia);
  const ciudad = useSelector((state) => state.mitienda.ciudad);
  const punto_pickit = useSelector((state) => state.mitienda.punto_pickit);
  const okFacturar = useSelector((state) => state.mitienda.okFacturar);
  const okMensaje = useSelector((state) => state.mitienda.okSendMessage);
  const pagosRealizados = useSelector(
    (state) => state.mitienda.pagosRealizados
  );
  const okFechaEntrega = useSelector((state) => state.mitienda.okFechaEntrega);

  const dispatch = useDispatch();

  const [pedidoid, setPedidoId] = useState("");
  const [pedido, setPedido] = useState("");
  // console.log("pedido:", pedido);

  const [infoPedidoPickit, setInfoPedidoPickit] = useState("");
  const [ticketUrl, setTicketUrl] = useState("");

  const [isLoading, setIsLoading] = useState(true);

  const [show, setShow] = useState("");
  const handleClose = () => setShow(false);

  const [show2, setShow2] = useState("");
  const handleClose2 = () => setShow2(false);
  const handleShow2 = () => setShow2(true);

  const [showRegistraPago, setShowRegistraPago] = useState("");
  const handleCloseRegistraPago = () => setShowRegistraPago(false);
  const handleShowRegistraPago = () => setShowRegistraPago(true);

  const [showVerPagos, setShowVerPagos] = useState("");
  const handleCloseVerPagos = () => setShowVerPagos(false);
  const handleShowVerPagos = () => setShowVerPagos(true);

  const [showFacturar, setShowFacturar] = useState(false);
  const handleCloseFacturar = () => setShowFacturar(false);
  const handleShowFacturar = () => setShowFacturar(true);

  const [showMensaje, setShowMensaje] = useState("");
  const handleCloseMensaje = () => setShowMensaje(false);

  const [showMensajesPedido, setShowMensajesPedido] = useState("");
  const handleCloseMensajesPedido = () => setShowMensajesPedido(false);
  const handleShowMensajesPedido = () => setShowMensajesPedido(true);

  const [isVendedor, setIsVendedor] = useState(false);

  useEffect(() => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    setIsVendedor(profileValues.includes(18) && !profileValues.includes(7));
  }, []);

  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });

  const [openAlert, setOpenAlert] = React.useState(false);
  const handleClickAlert = () => {
    setOpenAlert(true);
  };
  const handleCloseAlert = () => {
    setOpenAlert(false);
  };

  const [openAlertRegistarPago, setOpenAlertRegistarPago] =
    React.useState(false);
  const handleClickAlertRegistarPago = () => {
    setOpenAlertRegistarPago(true);
  };
  const handleCloseAlertRegistarPago = () => {
    setOpenAlertRegistarPago(false);
  };

  const [openAlertFactura, setOpenAlertFactura] = React.useState(false);
  const handleClickAlertFactura = () => {
    setOpenAlertFactura(true);
  };
  const handleCloseAlertFactura = () => {
    setOpenAlertFactura(false);
  };

  const [openAlertMensaje, setOpenAlertMensaje] = React.useState(false);
  const handleClickAlertMensaje = () => {
    setOpenAlertMensaje(true);
  };
  const handleCloseAlertMensaje = () => {
    setOpenAlertMensaje(false);
  };

  const [openAlertFechaEntrega, setOpenAlertFechaEntrega] =
    React.useState(false);
  const handleClickAlertFechaEntrega = () => {
    setOpenAlertFechaEntrega(true);
  };
  const handleCloseAlertFechaEntrega = () => {
    setOpenAlertFechaEntrega(false);
  };

  const formatoFecha = (fecha) => {
    if (fecha === null || fecha === "") {
      return null;
    } else {
      let aux = fecha.slice(0, 10);
      let aux2 = aux.split("-");

      return aux2[2] + "-" + aux2[1] + "-" + aux2[0];
    }
  };

  useEffect(() => {
    dispatch(getPedido(factura));
    dispatch(getDatosTienda());
  }, [okCambiarEstadoPedido, ok, okFacturar]);

  //w
  // useEffect(() => {
  //   if (!data.loading) {
  //     setPedido(data.data);
  //     dispatch(getPagosRealizados(data.data[0].pedidoplacaid));
  //     setPedidoId(data.data[0].pedidoplacaid);
  //     if (data.data[0].tipo_envio === "Pickit") {
  //       dispatch(
  //         getInfoPickit(data.data[0].clientepickitid, data.data[0].clienteid)
  //       );
  //     }
  //   }
  // }, [data]);

  //f
  useEffect(() => {
    if (!data.loading) {
      if (data.data && data.data.length > 0) {
        setPedido(data.data);
        dispatch(getPagosRealizados(data.data[0].pedidoplacaid));
        setPedidoId(data.data[0].pedidoplacaid);
        if (data.data[0].tipo_envio === "Pickit") {
          dispatch(
            getInfoPickit(data.data[0].clientepickitid, data.data[0].clienteid)
          );
        }
      }
      setIsLoading(false);
    }
  }, [data]);

  useEffect(() => {
    if (!dataPedidoPickit.loading) {
      setInfoPedidoPickit(dataPedidoPickit.data);
      if (dataPedidoPickit.data.origen_envio === "2") {
        dispatch(getProvincia(dataPedidoPickit.data.provinciaid));
        dispatch(
          getCiudad(
            dataPedidoPickit.data.provinciaid,
            dataPedidoPickit.data.localidadid
          )
        );
      } else {
        dispatch(getPuntoPickit(dataPedidoPickit.data.punto_envio));
      }
      dispatch(getTicketPickit(dataPedidoPickit.data));
    }
  }, [dataPedidoPickit]);

  useEffect(() => {
    if (!dataUrlticket.loading && dataUrlticket.data !== undefined) {
      setTicketUrl(dataUrlticket.data);
    }
  }, [dataUrlticket]);

  const [fechaCompromiso, setFechaCompromiso] = React.useState("");

  useEffect(() => {
    if (pedido && pedido[0] && pedido[0].fechaentrega) {
      setFechaCompromiso(pedido[0].fechaentrega);
    }
  }, [pedido]);

  const handleFechaCompromisoChange = (date) => {
    setFechaCompromiso(date);
  };

  // const saveFechaCompromiso = async () => {
  //   try {
  //     await dispatch(
  //       editarFechaEntrega(pedido[0].pedidoplacaid, fechaCompromiso)
  //     );

  //     // Show the alert instead of using window.alert
  //     handleClickAlertFechaEntrega();
  //   } catch (error) {
  //     console.error("Error al guardar fecha de compromiso:", error);
  //     // You could set an error state here if needed
  //     handleClickAlertFechaEntrega();
  //   }
  // };

  const saveFechaCompromiso = async () => {
    try {
      // Check if we have the pedido data in the expected array format
      if (!pedido || !Array.isArray(pedido) || !pedido[0]) {
        throw new Error("Datos del pedido no disponibles");
      }

      const facturaId = pedido[0].facturaid_pedido;

      await dispatch(editarFechaEntrega(facturaId, fechaCompromiso));
    } catch (error) {
      // console.error("Error al guardar fecha de compromiso:", error);

      dispatch({
        type: "EDITAR_FECHA_ENTREGA",
        payload: {
          success: false,
          mensaje: error.message || "Error al actualizar la fecha de entrega",
        },
      });
    }
  };

  useEffect(() => {
    if (okFechaEntrega && okFechaEntrega.mensaje) {
      handleClickAlertFechaEntrega();
    }
  }, [okFechaEntrega]);

  return (
    <div className="ventas-container">
      {isLoading ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh",
          }}
        >
          <CircularProgress />
        </div>
      ) : pedido === "" ? (
        <div style={{ textAlign: "center", marginTop: "2rem" }}>
          <Typography variant="h6">
            No se encontró información del pedido
          </Typography>
          <Button
            component={Link}
            to="/MiTienda/Pedidos"
            variant="contained"
            startIcon={<ArrowBackIcon />}
            style={{ marginTop: "1rem" }}
          >
            Volver a Pedidos
          </Button>
        </div>
      ) : (
        <div>
          <ModalCancelar
            show={show}
            handleClose={handleClose}
            pedido={pedido && pedido[0].pedidoplacaid}
          />

          <ModalRegistrarPago
            show={showRegistraPago}
            handleClose={handleCloseRegistraPago}
            pedido={pedido}
            handleClickAlert={handleClickAlertRegistarPago}
          />

          <ModalVerPagos
            show={showVerPagos}
            handleClose={handleCloseVerPagos}
            pedido={pedidoid}
          />
          <ModalCambiarEstado
            show={show2}
            handleClose={handleClose2}
            pedido={pedido}
            handleClickAlert={handleClickAlert}
          />
          <ModalFacturar
            show={showFacturar}
            handleClose={handleCloseFacturar}
            pedido={pedido}
            handleClickAlert={handleClickAlertFactura}
          />
          <ModalMensaje
            show={showMensaje}
            handleClose={handleCloseMensaje}
            pedido={pedido}
            handleClickAlert={handleClickAlertMensaje}
          />
          <ModalMensajesPedido
            show={showMensajesPedido}
            handleClose={handleCloseMensajesPedido}
            pedido={pedido}
          />
          <Snackbar
            open={openAlert}
            autoHideDuration={10000}
            onClose={handleCloseAlert}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert
              onClose={handleCloseAlert}
              severity={okCambiarEstadoPedido.success ? "success" : "error"}
              sx={{ width: 400 }}
            >
              <h5>{okCambiarEstadoPedido.mensaje}</h5>
            </Alert>
          </Snackbar>
          <Snackbar
            open={openAlertRegistarPago}
            autoHideDuration={6000}
            onClose={handleCloseAlertRegistarPago}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert
              onClose={handleCloseAlertRegistarPago}
              severity={registrarPago.success ? "success" : "error"}
              sx={{ width: 400 }}
            >
              <h5>{registrarPago.mensaje}</h5>
            </Alert>
          </Snackbar>
          <Snackbar
            open={openAlertRegistarPago}
            autoHideDuration={6000}
            onClose={handleCloseAlertRegistarPago}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert
              onClose={handleCloseAlertRegistarPago}
              severity={registrarPago.success ? "success" : "error"}
              sx={{ width: 400 }}
            >
              <h5>{registrarPago.mensaje}</h5>
            </Alert>
          </Snackbar>
          <Snackbar
            open={openAlertMensaje}
            autoHideDuration={6000}
            onClose={handleCloseAlertMensaje}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert
              onClose={handleCloseAlertMensaje}
              severity={okMensaje.success ? "success" : "error"}
              sx={{ width: 400 }}
            >
              <h5>{okMensaje.mensaje}</h5>
            </Alert>
          </Snackbar>
          <Snackbar
            open={openAlertFechaEntrega}
            autoHideDuration={6000}
            onClose={handleCloseAlertFechaEntrega}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert
              onClose={handleCloseAlertFechaEntrega}
              severity={
                okFechaEntrega && okFechaEntrega.success ? "success" : "error"
              }
              sx={{ width: 400 }}
            >
              <h5>
                {okFechaEntrega && okFechaEntrega.mensaje
                  ? okFechaEntrega.mensaje
                  : "Error al actualizar la fecha"}
              </h5>
            </Alert>
          </Snackbar>
          <div style={{ margin: 20 }}>
            <Link to="/Mitienda/Pedidos" style={{ color: "black" }}>
              <div
                style={{ display: "flex", fontSize: "130%", marginBottom: 20 }}
              >
                <ArrowBackIcon style={{ fontSize: "130%" }} />
                Volver a Pedidos
              </div>
            </Link>
            <div className="div-titulo">
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  width: "25%",
                }}
              >
                <h3
                  style={{
                    color:
                      pedido &&
                      pedido[0].descripcionestado === "Cancelado" &&
                      "red",
                  }}
                >
                  Orden #{id}
                </h3>
                <h4
                  style={{
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div
                    style={{
                      width: "100%",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <Button
                      style={{ cursor: "default" }}
                      variant="contained"
                      color={
                        pedido && pedido[0].descripcionestado === "Cancelado"
                          ? "error"
                          : "info"
                      }
                    >
                      {pedido && pedido[0].descripcionestado}
                    </Button>
                    {/* <div onClick={handleShow2} style={{ cursor: "pointer" }}>
                      <Tooltip title="Editar estado">
                        <IconButton size="large" color="primary">
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    </div> */}
                    <div
                      onClick={!isVendedor ? handleShow2 : undefined}
                      style={{ cursor: isVendedor ? "not-allowed" : "pointer" }}
                    >
                      <Tooltip
                        title={
                          isVendedor
                            ? "No tiene permisos para editar estado"
                            : "Editar estado"
                        }
                      >
                        <IconButton
                          size="large"
                          color="primary"
                          disabled={isVendedor}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    </div>
                  </div>
                </h4>
              </div>
              <div>
                <Button
                  variant="contained"
                  style={{ width: 150, justifyContent: "center", margin: 5 }}
                >
                  <a
                    href={`${
                      process.env.REACT_APP_SERVER
                    }dompdf/generarcomprobante_pdf.php?tipocomprobante=124&codigocomprobante=${
                      pedido[0].pedidoplacaid
                    }&tipoaccionpdf=1&tiendaid=${localStorage.getItem(
                      "tiendausuario"
                    )}`}
                    target="_blank"
                    style={{ color: "white" }}
                  >
                    Imprimir
                  </a>
                </Button>
                <Button
                  variant="contained"
                  onClick={handleShowFacturar}
                  style={{ width: 150, justifyContent: "center", margin: 5 }}
                >
                  Facturar
                </Button>
              </div>
            </div>
          </div>
          <div className="columnas">
            <div className="columna1">
              <div style={{ display: "flex", flexDirection: "column" }}>
                <div className="columna1-fila1" style={{ display: "flex" }}>
                  <Paper className="paper-metodos-pago">
                    <div style={{ display: "flex", flexDirection: "column" }}>
                      <div style={{ display: "flex" }}>
                        <AccountBalanceIcon />
                        <h5 style={{ marginLeft: 10 }}>
                          <strong>Método de Pago:</strong>{" "}
                          {pedido[0].descripcionpago || ""}
                        </h5>
                      </div>
                      {pedido[0].descripcionpago ===
                      "Acordar con el Vendedor" ? (
                        <h6>
                          <strong>Estado del pago:</strong>{" "}
                          {pagosRealizados !== "" &&
                          pagosRealizados.pagos.length > 0
                            ? "Pagado"
                            : "Pendiente"}
                        </h6>
                      ) : null}
                      {pedido[0].descripcionpago ===
                      "Pago Electronico. Mercado Pago" ? (
                        <h6>
                          <strong>Estado del pago:</strong>{" "}
                          {pagosRealizados !== "" &&
                          pagosRealizados.pagos.length > 0
                            ? pagosRealizados.pagos[0].status
                            : "Pendiente"}
                        </h6>
                      ) : null}
                      <div style={{ width: "100%" }}>
                        <Button
                          style={{ marginTop: 10, width: "100%" }}
                          onClick={handleShowRegistraPago}
                          variant="outlined"
                        >
                          Registrar pago
                        </Button>
                        <Button
                          style={{ marginTop: 10, width: "100%" }}
                          onClick={handleShowVerPagos}
                          variant="outlined"
                        >
                          Pagos realizados
                        </Button>
                      </div>
                    </div>
                  </Paper>
                  <Paper className="paper-metodos-envio">
                    <div style={{ display: "flex" }}>
                      <LocalShippingIcon />
                      <h5 style={{ marginLeft: 10 }}>
                        <strong>Envío:</strong> {pedido[0].tipo_envio || ""}
                      </h5>
                    </div>
                    <Divider />
                    {pedido[0].tipo_envio === "Pickit" &&
                    infoPedidoPickit !== "" ? (
                      <div style={{ width: "100%" }}>
                        <h6>
                          <strong>Código:</strong>{" "}
                          {infoPedidoPickit.pickit_code}
                        </h6>
                        <a
                          href={!dataUrlticket.loading && ticketUrl}
                          target="_blank"
                          download
                        >
                          <Button
                            style={{ marginTop: 10, width: "100%" }}
                            variant="outlined"
                          >
                            Imprimir ticket
                          </Button>
                        </a>
                        <a href={infoPedidoPickit.url_tracking} target="_blank">
                          <Button
                            style={{ marginTop: 10, width: "100%" }}
                            variant="outlined"
                          >
                            Seguimiento envío
                          </Button>
                        </a>
                      </div>
                    ) : null}
                    {/* Fecha de compromiso de entrega */}
                    {/* <div style={{ padding: 10 }}>
                      <h6>
                        <strong>Fecha de compromiso de entrega:</strong>
                      </h6>
                      <TextField
                        name="fechaCompromiso"
                        type="date"
                        fullWidth
                        margin="normal"
                        onChange={(e) => {
                          handleFechaCompromisoChange(e.target.value);
                        }}
                        value={fechaCompromiso}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        inputProps={{
                          style: {
                            height: 40,
                            fontSize: 17,
                          },
                        }}
                      />
                      <Button
                        style={{ marginTop: 10, width: "100%" }}
                        variant="contained"
                        color="primary"
                        onClick={saveFechaCompromiso}
                      >
                        Guardar fecha de compromiso
                      </Button>
                    </div> */}
                    <div style={{ padding: 10 }}>
                      <h6>
                        <strong>Fecha de compromiso de entrega:</strong>
                      </h6>
                      <TextField
                        name="fechaCompromiso"
                        type="date"
                        fullWidth
                        margin="normal"
                        onChange={(e) => {
                          if (!isVendedor) {
                            handleFechaCompromisoChange(e.target.value);
                          }
                        }}
                        value={fechaCompromiso}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        inputProps={{
                          style: {
                            height: 40,
                            fontSize: 17,
                          },
                          disabled: isVendedor,
                        }}
                      />
                      <Button
                        style={{ marginTop: 10, width: "100%" }}
                        variant="contained"
                        color="primary"
                        onClick={!isVendedor ? saveFechaCompromiso : undefined}
                        disabled={isVendedor}
                      >
                        Guardar fecha de compromiso
                      </Button>
                    </div>
                    {/* {pedido[0].tipo_envio === "Envio a Domicilio" && (
                      <div style={{ padding: 10 }}>
                        <h6>
                          Fecha entrega: {formatoFecha(pedido[6].fecha_entrega)}
                        </h6>
                        <h6>
                          Horario: De {pedido[6].hora_desde}hs hasta{" "}
                          {pedido[6].hora_hasta}hs
                        </h6>
                        <h6>Días disponibles: {pedido[6].franja}</h6>
                      </div>
                    )} */}
                    {pedido[0].tipo_envio === "Envio a Domicilio" && (
                      <div style={{ padding: 10 }}>
                        {/* <h6>
                          Fecha entrega:{" "}
                          {pedido[6]?.fecha_entrega
                            ? formatoFecha(pedido[6].fecha_entrega)
                            : "Pendiente"}
                        </h6> */}
                        <h6>
                          Horario:{" "}
                          {pedido[6]?.hora_desde
                            ? `De ${pedido[6].hora_desde}hs hasta ${pedido[6].hora_hasta}hs`
                            : "Pendiente"}
                        </h6>
                        <h6>
                          Días disponibles: {pedido[6]?.franja || "Pendiente"}
                        </h6>
                      </div>
                    )}
                  </Paper>
                </div>
                <div className="columna1-tabla">
                  <TableContainer component={Paper} style={{ width: "100%" }}>
                    <Table aria-label="simple table">
                      <TableHead>
                        <TableRow>
                          <TableCell
                            style={{
                              fontWeight: "bold",
                              fontSize: "110%",
                              display: "flex",
                              justifyContent: "space-between",
                            }}
                          >
                            <h4>
                              <strong>Detalle de la venta</strong>
                            </h4>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {pedido &&
                          pedido[1].map((row) => (
                            <TableRow
                              key={row.codigoarticulo}
                              sx={{
                                "&:last-child td, &:last-child th": {
                                  border: 0,
                                },
                              }}
                            >
                              <TableCell
                                style={{ fontSize: "100%" }}
                                align="left"
                              >
                                <div
                                  style={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <div>
                                    <h6>
                                      <strong>
                                        {row.nombreproducto} {row.color}{" "}
                                        {row.talle}
                                      </strong>
                                    </h6>
                                    <h6>Codigo: {row.codigoarticulo}</h6>
                                    <h6>Cantidad: {row.cantidad}</h6>
                                  </div>
                                  <h6>
                                    <strong>
                                      ${row.totalventa.toFixed(2)}
                                    </strong>
                                  </h6>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        {pedido[0].tipo_envio === "Pickit" && (
                          <TableRow
                            key="124"
                            sx={{
                              "&:last-child td, &:last-child th": { border: 0 },
                            }}
                          >
                            <TableCell
                              style={{ fontSize: "100%" }}
                              align="left"
                            >
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                }}
                              >
                                <div style={{ marginRight: 20 }}>
                                  <h6>
                                    <strong>Envío</strong>
                                  </h6>
                                </div>
                                <div>
                                  <h6>
                                    <strong>
                                      ${pedido[5].totalventa.toFixed(2)}
                                    </strong>
                                  </h6>
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                        <TableRow
                          key="123"
                          sx={{
                            "&:last-child td, &:last-child th": { border: 0 },
                          }}
                        >
                          <TableCell style={{ fontSize: "100%" }} align="left">
                            <div
                              style={{ display: "flex", justifyContent: "end" }}
                            >
                              <div style={{ marginRight: 20 }}>
                                <h6>Precio compra</h6>
                                <h6>Total IVA</h6>
                                <h6>
                                  <strong>Total</strong>
                                </h6>
                              </div>
                              <div>
                                <h6>
                                  ${pedido[3].precioTotalCompra.toFixed(2)}
                                </h6>
                                <h6>${pedido[3].precioTotalIva.toFixed(2)}</h6>
                                <h5>
                                  <strong>
                                    ${pedido[3].precioTotal.toFixed(2)}
                                  </strong>
                                </h5>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </div>
              </div>
            </div>
            <div className="columna2">
              <Paper className="columna2-papper-info-cliente">
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <h4>
                    <strong>Información del usuario</strong>
                  </h4>
                </div>
                <Divider />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    overflow: "hidden",
                    marginTop: 10,
                  }}
                >
                  <h6>
                    <strong>Nombre:</strong> {pedido[0].nombre || ""}
                  </h6>
                  <h6>
                    <strong>Teléfono:</strong> {pedido[0].telefono || ""}
                  </h6>
                  <h6>
                    <strong>Email:</strong>
                    {pedido[0].email}
                  </h6>
                  <div style={{ display: "flex", marginTop: 5 }}>
                    <a
                      href={`https://wa.me/${pedido[0].telefono}`}
                      target="_blank"
                    >
                      <Button
                        color="success"
                        variant="contained"
                        style={{ width: 250, margin: 10 }}
                      >
                        <WhatsApp />
                        WhatsApp
                      </Button>
                    </a>
                    <Button
                      onClick={handleShowMensajesPedido}
                      variant="contained"
                      color="info"
                      style={{ width: 250, margin: 10 }}
                    >
                      <Forum fontSize="small" style={{ marginRight: 5 }} />{" "}
                      Enviar mensaje
                    </Button>
                  </div>
                </div>
              </Paper>
              {pedido[0].tipo_envio === "Pickit" &&
              infoPedidoPickit.origen_envio === "2" ? (
                <Paper className="columna2-papper-info-envio">
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      overflow: "hidden",
                    }}
                  >
                    <h4>
                      <strong>Información del envío</strong>
                    </h4>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h6>
                        Metodo de envío:{" "}
                        {pedido[0].tipo_envio + " - " + "Envio a Domicilio" ||
                          ""}
                      </h6>
                    </div>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h6>
                        Dirección: {infoPedidoPickit.calle}{" "}
                        {infoPedidoPickit.altura}, {ciudad || ""},{" "}
                        {provincia || ""}
                      </h6>
                    </div>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h6>Precio: ${infoPedidoPickit.costo_envio + ".00"}</h6>
                      <h6>Impuesto: {infoPedidoPickit.impuesto_envio}%</h6>
                      <h6>Total: ${infoPedidoPickit.total_envio + ".00"}</h6>
                    </div>
                  </div>
                </Paper>
              ) : pedido[0].tipo_envio === "Pickit" &&
                infoPedidoPickit.origen_envio === "1" ? (
                <Paper className="columna2-papper-info-envio">
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      overflow: "hidden",
                    }}
                  >
                    <h4>
                      <strong>Información del envío</strong>
                    </h4>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h6>
                        <strong>Metodo de envío:</strong>{" "}
                        {pedido[0].tipo_envio + " - " + "Envio a Punto" || ""}
                      </h6>
                    </div>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h6>
                        <strong>Nombre punto:</strong>{" "}
                        {punto_pickit !== undefined
                          ? punto_pickit.name
                          : "No definido"}
                      </h6>
                      <h6>
                        <strong>Dirección:</strong>{" "}
                        {punto_pickit !== undefined
                          ? punto_pickit.address
                          : "No definido"}
                      </h6>
                    </div>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h6>
                        <strong>Precio:</strong> $
                        {infoPedidoPickit.costo_envio + ".00"}
                      </h6>
                      <h6>
                        <strong>Impuesto:</strong>{" "}
                        {infoPedidoPickit.impuesto_envio}%
                      </h6>
                      <h6>
                        <strong>Total:</strong> $
                        {infoPedidoPickit.total_envio + ".00"}
                      </h6>
                    </div>
                  </div>
                </Paper>
              ) : pedido[0].tipo_envio === "Retiro por Sucursal" ? (
                <Paper className="columna2-papper-info-envio">
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      overflow: "hidden",
                    }}
                  >
                    <h4>
                      <strong>Información del retiro</strong>
                    </h4>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h6>
                        <strong>Método de entrega:</strong>{" "}
                        {pedido[0].tipo_envio || ""}
                      </h6>
                      <h6>
                        <strong>Dirección de retiro:</strong>&nbsp;
                        {direcciontienda.direccion || ""}&nbsp; Nro{" "}
                        {direcciontienda.altura || ""}
                      </h6>
                    </div>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h5>
                        <strong>Retira</strong>
                      </h5>
                      <h6>
                        <strong>Nombre completo:</strong>{" "}
                        {pedido[0].nombre || ""}
                      </h6>
                      <h6>
                        <strong>Teléfono:</strong> {pedido[0].telefono || ""}
                      </h6>
                      <h6>
                        <strong>Email:</strong> {pedido[0].email || ""}
                      </h6>
                    </div>
                  </div>
                </Paper>
              ) : (
                <Paper className="columna2-papper-info-envio">
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      overflow: "hidden",
                    }}
                  >
                    <h4>
                      <strong>Información del envío</strong>
                    </h4>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h6>Metodo de envío: {pedido[0].tipo_envio || ""}</h6>
                    </div>
                    <Divider />
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h5>Dirección de envío</h5>
                      <h6>
                        Dirección: {pedido[0].direccion} {pedido[0].altura}
                      </h6>
                    </div>
                    {/* <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h5>Información de entrega</h5>
                      <h6>
                      {console.log("pedido:", pedido)}
                        {console.log("pedido[6]:", pedido?.[6])}
                        {console.log("fecha_entrega:", pedido?.[6]?.fecha_entrega)}
                        Fecha entrega: {formatoFecha(pedido[6].fecha_entrega)}
                      </h6>
                      <h6>
                        Horario: De {pedido[6].hora_desde}hs hasta{" "}
                        {pedido[6].hora_hasta}hs
                      </h6>
                      <h6>Días disponibles: {pedido[6].franja}</h6>
                    </div> */}
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h5>Información de entrega</h5>
                      <h6>
                        {/* {console.log("pedido:", pedido)} */}
                        {/* {console.log("pedido[6]:", pedido?.[6])} */}
                        {/* {console.log("fecha_entrega:", pedido?.[6]?.fecha_entrega)} */}
                        Fecha entrega:{" "}
                        {pedido[6]?.fecha_entrega
                          ? formatoFecha(pedido[6].fecha_entrega)
                          : "Pendiente"}
                      </h6>
                      <h6>
                        Horario:{" "}
                        {pedido[6]?.hora_desde
                          ? `De ${pedido[6].hora_desde}hs hasta ${pedido[6].hora_hasta}hs`
                          : "Pendiente"}
                      </h6>
                      <h6>
                        Días disponibles: {pedido[6]?.franja || "Pendiente"}
                      </h6>
                    </div>
                  </div>
                </Paper>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// import React from "react";
// import { Button, Divider, IconButton, Snackbar, Tooltip } from "@mui/material";
// import Table from "@mui/material/Table";
// import TableBody from "@mui/material/TableBody";
// import TableCell from "@mui/material/TableCell";
// import TableContainer from "@mui/material/TableContainer";
// import TableHead from "@mui/material/TableHead";
// import TableRow from "@mui/material/TableRow";
// import Paper from "@mui/material/Paper";
// import "./ventas.css";
// import { useEffect } from "react";
// import { useDispatch } from "react-redux";
// import {
//   getCiudad,
//   getDatosTienda,
//   getInfoPickit,
//   getPagosRealizados,
//   getPedido,
//   getProvincia,
//   getPuntoPickit,
//   getTicketPickit,
// } from "../../../redux/actions/mitienda.actions";
// import { useSelector } from "react-redux";
// import { Link } from "react-router-dom";
// import ArrowBackIcon from "@mui/icons-material/ArrowBack";
// import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
// import LocalShippingIcon from "@mui/icons-material/LocalShipping";
// import { useState } from "react";
// import { ModalCancelar } from "./modalCancelarVenta";
// import { Edit, Forum, WhatsApp } from "@mui/icons-material";
// import MuiAlert from "@mui/material/Alert";
// import { ModalRegistrarPago } from "./modalRegistrarPago";
// import { ModalVerPagos } from "./modalVerPagos";
// import { ModalCambiarEstado } from "./modalCambiarEstado";
// import { ModalFacturar } from "./modalFacturar";
// import { ModalMensaje } from "./modalMensaje";
// import { ModalMensajesPedido } from "./modalMensajesPedido";

// export const Venta = (props) => {
//   const id = props.match.params.id;
//   const factura = props.match.params.factura;

//   const data = useSelector((state) => state.mitienda.pedido);
//   const direcciontienda = useSelector((state) => state.mitienda.datostienda);
//   const ok = useSelector((state) => state.mitienda.pedidoCancelado);
//   const okCambiarEstadoPedido = useSelector(
//     (state) => state.mitienda.okCambiarEstadoPedido
//   );
//   const registrarPago = useSelector((state) => state.mitienda.registrarPago);
//   const dataPedidoPickit = useSelector(
//     (state) => state.mitienda.info_pedido_pickit
//   );
//   const dataUrlticket = useSelector((state) => state.mitienda.url_pickit);
//   const provincia = useSelector((state) => state.mitienda.provincia);
//   const ciudad = useSelector((state) => state.mitienda.ciudad);
//   const punto_pickit = useSelector((state) => state.mitienda.punto_pickit);
//   const okFacturar = useSelector((state) => state.mitienda.okFacturar);
//   const okMensaje = useSelector((state) => state.mitienda.okSendMessage);
//   const pagosRealizados = useSelector(
//     (state) => state.mitienda.pagosRealizados
//   );

//   const dispatch = useDispatch();

//   const [pedidoid, setPedidoId] = useState("");
//   const [pedido, setPedido] = useState("");
//   const [infoPedidoPickit, setInfoPedidoPickit] = useState("");
//   const [ticketUrl, setTicketUrl] = useState("");

//   const [isLoading, setIsLoading] = useState(true);

//   const [show, setShow] = useState("");
//   const handleClose = () => setShow(false);

//   const [show2, setShow2] = useState("");
//   const handleClose2 = () => setShow2(false);
//   const handleShow2 = () => setShow2(true);

//   const [showRegistraPago, setShowRegistraPago] = useState("");
//   const handleCloseRegistraPago = () => setShowRegistraPago(false);
//   const handleShowRegistraPago = () => setShowRegistraPago(true);

//   const [showVerPagos, setShowVerPagos] = useState("");
//   const handleCloseVerPagos = () => setShowVerPagos(false);
//   const handleShowVerPagos = () => setShowVerPagos(true);

//   const [showFacturar, setShowFacturar] = useState(false);
//   const handleCloseFacturar = () => setShowFacturar(false);
//   const handleShowFacturar = () => setShowFacturar(true);

//   const [showMensaje, setShowMensaje] = useState("");
//   const handleCloseMensaje = () => setShowMensaje(false);

//   const [showMensajesPedido, setShowMensajesPedido] = useState("");
//   const handleCloseMensajesPedido = () => setShowMensajesPedido(false);
//   const handleShowMensajesPedido = () => setShowMensajesPedido(true);

//   const Alert = React.forwardRef(function Alert(props, ref) {
//     return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
//   });

//   const [openAlert, setOpenAlert] = React.useState(false);
//   const handleClickAlert = () => {
//     setOpenAlert(true);
//   };
//   const handleCloseAlert = () => {
//     setOpenAlert(false);
//   };

//   const [openAlertRegistarPago, setOpenAlertRegistarPago] =
//     React.useState(false);
//   const handleClickAlertRegistarPago = () => {
//     setOpenAlertRegistarPago(true);
//   };
//   const handleCloseAlertRegistarPago = () => {
//     setOpenAlertRegistarPago(false);
//   };

//   const [openAlertFactura, setOpenAlertFactura] = React.useState(false);
//   const handleClickAlertFactura = () => {
//     setOpenAlertFactura(true);
//   };
//   const handleCloseAlertFactura = () => {
//     setOpenAlertFactura(false);
//   };

//   const [openAlertMensaje, setOpenAlertMensaje] = React.useState(false);
//   const handleClickAlertMensaje = () => {
//     setOpenAlertMensaje(true);
//   };
//   const handleCloseAlertMensaje = () => {
//     setOpenAlertMensaje(false);
//   };

//   const formatoFecha = (fecha) => {
//     if (fecha === null || fecha === "") {
//       return null;
//     } else {
//       let aux = fecha.slice(0, 10);
//       let aux2 = aux.split("-");

//       return aux2[2] + "-" + aux2[1] + "-" + aux2[0];
//     }
//   };

//   useEffect(() => {
//     dispatch(getPedido(factura));
//     dispatch(getDatosTienda());
//   }, [okCambiarEstadoPedido, ok, okFacturar]);

//   //w
//   // useEffect(() => {
//   //   if (!data.loading) {
//   //     setPedido(data.data);
//   //     dispatch(getPagosRealizados(data.data[0].pedidoplacaid));
//   //     setPedidoId(data.data[0].pedidoplacaid);
//   //     if (data.data[0].tipo_envio === "Pickit") {
//   //       dispatch(
//   //         getInfoPickit(data.data[0].clientepickitid, data.data[0].clienteid)
//   //       );
//   //     }
//   //   }
//   // }, [data]);

//   //f
//   useEffect(() => {
//     if (!data.loading) {
//       if (data.data && data.data.length > 0) {
//         setPedido(data.data);
//         dispatch(getPagosRealizados(data.data[0].pedidoplacaid));
//         setPedidoId(data.data[0].pedidoplacaid);
//         if (data.data[0].tipo_envio === "Pickit") {
//           dispatch(
//             getInfoPickit(data.data[0].clientepickitid, data.data[0].clienteid)
//           );
//         }
//       }
//       setIsLoading(false);
//     }
//   }, [data]);

//   useEffect(() => {
//     if (!dataPedidoPickit.loading) {
//       setInfoPedidoPickit(dataPedidoPickit.data);
//       if (dataPedidoPickit.data.origen_envio === "2") {
//         dispatch(getProvincia(dataPedidoPickit.data.provinciaid));
//         dispatch(
//           getCiudad(
//             dataPedidoPickit.data.provinciaid,
//             dataPedidoPickit.data.localidadid
//           )
//         );
//       } else {
//         dispatch(getPuntoPickit(dataPedidoPickit.data.punto_envio));
//       }
//       dispatch(getTicketPickit(dataPedidoPickit.data));
//     }
//   }, [dataPedidoPickit]);

//   useEffect(() => {
//     if (!dataUrlticket.loading && dataUrlticket.data !== undefined) {
//       setTicketUrl(dataUrlticket.data);
//     }
//   }, [dataUrlticket]);

//   return (
//     <div className="ventas-container">
//       {pedido !== "" ? (
//         <div>
//           <ModalCancelar
//             show={show}
//             handleClose={handleClose}
//             pedido={pedido && pedido[0].pedidoplacaid}
//           />

//           <ModalRegistrarPago
//             show={showRegistraPago}
//             handleClose={handleCloseRegistraPago}
//             pedido={pedido}
//             handleClickAlert={handleClickAlertRegistarPago}
//           />

//           <ModalVerPagos
//             show={showVerPagos}
//             handleClose={handleCloseVerPagos}
//             pedido={pedidoid}
//           />
//           <ModalCambiarEstado
//             show={show2}
//             handleClose={handleClose2}
//             pedido={pedido}
//             handleClickAlert={handleClickAlert}
//           />
//           <ModalFacturar
//             show={showFacturar}
//             handleClose={handleCloseFacturar}
//             pedido={pedido}
//             handleClickAlert={handleClickAlertFactura}
//           />
//           <ModalMensaje
//             show={showMensaje}
//             handleClose={handleCloseMensaje}
//             pedido={pedido}
//             handleClickAlert={handleClickAlertMensaje}
//           />
//           <ModalMensajesPedido
//             show={showMensajesPedido}
//             handleClose={handleCloseMensajesPedido}
//             pedido={pedido}
//           />
//           <Snackbar
//             open={openAlert}
//             autoHideDuration={10000}
//             onClose={handleCloseAlert}
//             anchorOrigin={{ vertical: "top", horizontal: "center" }}
//           >
//             <Alert
//               onClose={handleCloseAlert}
//               severity={okCambiarEstadoPedido.success ? "success" : "error"}
//               sx={{ width: 400 }}
//             >
//               <h5>{okCambiarEstadoPedido.mensaje}</h5>
//             </Alert>
//           </Snackbar>
//           <Snackbar
//             open={openAlertRegistarPago}
//             autoHideDuration={6000}
//             onClose={handleCloseAlertRegistarPago}
//             anchorOrigin={{ vertical: "top", horizontal: "center" }}
//           >
//             <Alert
//               onClose={handleCloseAlertRegistarPago}
//               severity={registrarPago.success ? "success" : "error"}
//               sx={{ width: 400 }}
//             >
//               <h5>{registrarPago.mensaje}</h5>
//             </Alert>
//           </Snackbar>
//           <Snackbar
//             open={openAlertRegistarPago}
//             autoHideDuration={6000}
//             onClose={handleCloseAlertRegistarPago}
//             anchorOrigin={{ vertical: "top", horizontal: "center" }}
//           >
//             <Alert
//               onClose={handleCloseAlertRegistarPago}
//               severity={registrarPago.success ? "success" : "error"}
//               sx={{ width: 400 }}
//             >
//               <h5>{registrarPago.mensaje}</h5>
//             </Alert>
//           </Snackbar>
//           <Snackbar
//             open={openAlertMensaje}
//             autoHideDuration={6000}
//             onClose={handleCloseAlertMensaje}
//             anchorOrigin={{ vertical: "top", horizontal: "center" }}
//           >
//             <Alert
//               onClose={handleCloseAlertMensaje}
//               severity={okMensaje.success ? "success" : "error"}
//               sx={{ width: 400 }}
//             >
//               <h5>{okMensaje.mensaje}</h5>
//             </Alert>
//           </Snackbar>
//           <div style={{ margin: 20 }}>
//             <Link to="/Mitienda/Pedidos" style={{ color: "black" }}>
//               <div
//                 style={{ display: "flex", fontSize: "130%", marginBottom: 20 }}
//               >
//                 <ArrowBackIcon style={{ fontSize: "130%" }} />
//                 Volver a Pedidos
//               </div>
//             </Link>
//             <div className="div-titulo">
//               <div
//                 style={{
//                   display: "flex",
//                   flexDirection: "column",
//                   width: "25%",
//                 }}
//               >
//                 <h3
//                   style={{
//                     color:
//                       pedido &&
//                       pedido[0].descripcionestado === "Cancelado" &&
//                       "red",
//                   }}
//                 >
//                   Orden #{id}
//                 </h3>
//                 <h4
//                   style={{
//                     width: "100%",
//                     display: "flex",
//                     alignItems: "center",
//                     justifyContent: "space-between",
//                   }}
//                 >
//                   <div
//                     style={{
//                       width: "100%",
//                       display: "flex",
//                       alignItems: "center",
//                     }}
//                   >
//                     <Button
//                       style={{ cursor: "default" }}
//                       variant="contained"
//                       color={
//                         pedido && pedido[0].descripcionestado === "Cancelado"
//                           ? "error"
//                           : "info"
//                       }
//                     >
//                       {pedido && pedido[0].descripcionestado}
//                     </Button>
//                     <div onClick={handleShow2} style={{ cursor: "pointer" }}>
//                       <Tooltip title="Editar estado">
//                         <IconButton size="large" color="primary">
//                           <Edit />
//                         </IconButton>
//                       </Tooltip>
//                     </div>
//                   </div>
//                 </h4>
//               </div>
//               <div>
//                 <Button
//                   variant="contained"
//                   style={{ width: 150, justifyContent: "center", margin: 5 }}
//                 >
//                   {/* <a
//                                     href={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=124&codigocomprobante=${pedido[0].pedidoplacaid}&tipoaccionpdf=1`}
//                                     target="_blank"
//                                     style={{color:"white"}}
//                                 >  */}
//                   <a
//                     href={`${
//                       process.env.REACT_APP_SERVER
//                     }dompdf/generarcomprobante_pdf.php?tipocomprobante=124&codigocomprobante=${
//                       pedido[0].pedidoplacaid
//                     }&tipoaccionpdf=1&tiendaid=${localStorage.getItem(
//                       "tiendausuario"
//                     )}`}
//                     target="_blank"
//                     style={{ color: "white" }}
//                   >
//                     Imprimir
//                   </a>
//                 </Button>
//                 <Button
//                   variant="contained"
//                   onClick={handleShowFacturar}
//                   style={{ width: 150, justifyContent: "center", margin: 5 }}
//                 >
//                   Facturar
//                 </Button>
//               </div>
//             </div>
//           </div>
//           <div className="columnas">
//             <div className="columna1">
//               <div style={{ display: "flex", flexDirection: "column" }}>
//                 <div className="columna1-fila1" style={{ display: "flex" }}>
//                   <Paper className="paper-metodos-pago">
//                     <div style={{ display: "flex", flexDirection: "column" }}>
//                       <div style={{ display: "flex" }}>
//                         <AccountBalanceIcon />
//                         <h5 style={{ marginLeft: 10 }}>
//                           <strong>M&eacute;todo de Pago:</strong>{" "}
//                           {pedido[0].descripcionpago || ""}
//                         </h5>
//                       </div>
//                       {pedido[0].descripcionpago ===
//                       "Acordar con el Vendedor" ? (
//                         <h6>
//                           <strong>Estado del pago:</strong>{" "}
//                           {pagosRealizados !== "" &&
//                           pagosRealizados.pagos.length > 0
//                             ? "Pagado"
//                             : "Pendiente"}
//                         </h6>
//                       ) : null}
//                       {pedido[0].descripcionpago ===
//                       "Pago Electronico. Mercado Pago" ? (
//                         <h6>
//                           <strong>Estado del pago:</strong>{" "}
//                           {pagosRealizados !== "" &&
//                           pagosRealizados.pagos.length > 0
//                             ? pagosRealizados.pagos[0].status
//                             : "Pendiente"}
//                         </h6>
//                       ) : null}
//                       <div style={{ width: "100%" }}>
//                         <Button
//                           style={{ marginTop: 10, width: "100%" }}
//                           onClick={handleShowRegistraPago}
//                           variant="outlined"
//                         >
//                           Registrar pago
//                         </Button>
//                         <Button
//                           style={{ marginTop: 10, width: "100%" }}
//                           onClick={handleShowVerPagos}
//                           variant="outlined"
//                         >
//                           Pagos realizados
//                         </Button>
//                       </div>
//                     </div>
//                   </Paper>
//                   <Paper className="paper-metodos-envio">
//                     <div style={{ display: "flex" }}>
//                       <LocalShippingIcon />
//                       <h5 style={{ marginLeft: 10 }}>
//                         <strong>Env&iacute;o:</strong>{" "}
//                         {pedido[0].tipo_envio || ""}
//                       </h5>
//                     </div>
//                     <Divider />
//                     {pedido[0].tipo_envio === "Pickit" &&
//                     infoPedidoPickit !== "" ? (
//                       <div style={{ width: "100%" }}>
//                         <h6>
//                           <strong>C&oacute;digo:</strong>{" "}
//                           {infoPedidoPickit.pickit_code}
//                         </h6>
//                         <a
//                           href={!dataUrlticket.loading && ticketUrl}
//                           target="_blank"
//                           download
//                         >
//                           <Button
//                             style={{ marginTop: 10, width: "100%" }}
//                             variant="outlined"
//                           >
//                             Imprimir ticket
//                           </Button>
//                         </a>
//                         <a href={infoPedidoPickit.url_tracking} target="_blank">
//                           <Button
//                             style={{ marginTop: 10, width: "100%" }}
//                             variant="outlined"
//                           >
//                             Seguimiento env&iacute;o
//                           </Button>
//                         </a>
//                       </div>
//                     ) : null}
//                     {pedido[0].tipo_envio === "Envio a Domicilio" && (
//                       <div style={{ padding: 10 }}>
//                         <h6>
//                           Fecha entrega: {formatoFecha(pedido[6].fecha_entrega)}
//                         </h6>
//                         <h6>
//                           Horario: De {pedido[6].hora_desde}hs hasta{" "}
//                           {pedido[6].hora_hasta}hs
//                         </h6>
//                         <h6>D&iacute;as disponibles: {pedido[6].franja}</h6>
//                       </div>
//                     )}
//                   </Paper>
//                 </div>
//                 <div className="columna1-tabla">
//                   <TableContainer component={Paper} style={{ width: "100%" }}>
//                     <Table aria-label="simple table">
//                       <TableHead>
//                         <TableRow>
//                           <TableCell
//                             style={{
//                               fontWeight: "bold",
//                               fontSize: "110%",
//                               display: "flex",
//                               justifyContent: "space-between",
//                             }}
//                           >
//                             <h4>
//                               <strong>Detalle de la venta</strong>
//                             </h4>
//                           </TableCell>
//                         </TableRow>
//                       </TableHead>
//                       <TableBody>
//                         {pedido &&
//                           pedido[1].map((row) => (
//                             <TableRow
//                               key={row.codigoarticulo}
//                               sx={{
//                                 "&:last-child td, &:last-child th": {
//                                   border: 0,
//                                 },
//                               }}
//                             >
//                               <TableCell
//                                 style={{ fontSize: "100%" }}
//                                 align="left"
//                               >
//                                 <div
//                                   style={{
//                                     display: "flex",
//                                     justifyContent: "space-between",
//                                   }}
//                                 >
//                                   <div>
//                                     <h6>
//                                       <strong>
//                                         {row.nombreproducto} {row.color}{" "}
//                                         {row.talle}
//                                       </strong>
//                                     </h6>
//                                     <h6>Codigo: {row.codigoarticulo}</h6>
//                                     <h6>Cantidad: {row.cantidad}</h6>
//                                   </div>
//                                   <h6>
//                                     <strong>
//                                       ${row.totalventa.toFixed(2)}
//                                     </strong>
//                                   </h6>
//                                 </div>
//                               </TableCell>
//                             </TableRow>
//                           ))}
//                         {pedido[0].tipo_envio === "Pickit" && (
//                           <TableRow
//                             key="124"
//                             sx={{
//                               "&:last-child td, &:last-child th": { border: 0 },
//                             }}
//                           >
//                             <TableCell
//                               style={{ fontSize: "100%" }}
//                               align="left"
//                             >
//                               <div
//                                 style={{
//                                   display: "flex",
//                                   justifyContent: "space-between",
//                                 }}
//                               >
//                                 <div style={{ marginRight: 20 }}>
//                                   <h6>
//                                     <strong>Env&iacute;o</strong>
//                                   </h6>
//                                 </div>
//                                 <div>
//                                   <h6>
//                                     <strong>
//                                       ${pedido[5].totalventa.toFixed(2)}
//                                     </strong>
//                                   </h6>
//                                 </div>
//                               </div>
//                             </TableCell>
//                           </TableRow>
//                         )}
//                         <TableRow
//                           key="123"
//                           sx={{
//                             "&:last-child td, &:last-child th": { border: 0 },
//                           }}
//                         >
//                           <TableCell style={{ fontSize: "100%" }} align="left">
//                             <div
//                               style={{ display: "flex", justifyContent: "end" }}
//                             >
//                               <div style={{ marginRight: 20 }}>
//                                 <h6>Precio compra</h6>
//                                 <h6>Total IVA</h6>
//                                 <h6>
//                                   <strong>Total</strong>
//                                 </h6>
//                               </div>
//                               <div>
//                                 <h6>
//                                   ${pedido[3].precioTotalCompra.toFixed(2)}
//                                 </h6>
//                                 <h6>${pedido[3].precioTotalIva.toFixed(2)}</h6>
//                                 <h5>
//                                   <strong>
//                                     ${pedido[3].precioTotal.toFixed(2)}
//                                   </strong>
//                                 </h5>
//                               </div>
//                             </div>
//                           </TableCell>
//                         </TableRow>
//                       </TableBody>
//                     </Table>
//                   </TableContainer>
//                 </div>
//               </div>
//             </div>
//             <div className="columna2">
//               <Paper className="columna2-papper-info-cliente">
//                 <div
//                   style={{ display: "flex", justifyContent: "space-between" }}
//                 >
//                   <h4>
//                     <strong>Informaci&oacute;n del usuario</strong>
//                   </h4>
//                 </div>
//                 <Divider />
//                 <div
//                   style={{
//                     display: "flex",
//                     flexDirection: "column",
//                     overflow: "hidden",
//                     marginTop: 10,
//                   }}
//                 >
//                   <h6>
//                     <strong>Nombre:</strong> {pedido[0].nombre || ""}
//                   </h6>
//                   <h6>
//                     <strong>Tel&eacute;fono:</strong> {pedido[0].telefono || ""}
//                   </h6>
//                   <h6>
//                     <strong>Email:</strong>
//                     {pedido[0].email}
//                   </h6>
//                   <div style={{ display: "flex", marginTop: 5 }}>
//                     <a
//                       href={`https://wa.me/${pedido[0].telefono}`}
//                       target="_blank"
//                     >
//                       <Button
//                         color="success"
//                         variant="contained"
//                         style={{ width: 250, margin: 10 }}
//                       >
//                         <WhatsApp />
//                         WhatsApp
//                       </Button>
//                     </a>
//                     <Button
//                       onClick={handleShowMensajesPedido}
//                       variant="contained"
//                       color="info"
//                       style={{ width: 250, margin: 10 }}
//                     >
//                       <Forum fontSize="small" style={{ marginRight: 5 }} />{" "}
//                       Enviar mensaje
//                     </Button>
//                   </div>
//                 </div>
//               </Paper>
//               {pedido[0].tipo_envio === "Pickit" &&
//               infoPedidoPickit.origen_envio === "2" ? (
//                 <Paper className="columna2-papper-info-envio">
//                   <div
//                     style={{
//                       display: "flex",
//                       flexDirection: "column",
//                       overflow: "hidden",
//                     }}
//                   >
//                     <h4>
//                       <strong>Informaci&oacute;n del env&iacute;o</strong>
//                     </h4>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h6>
//                         Metodo de env&iacute;o:{" "}
//                         {pedido[0].tipo_envio + " - " + "Envio a Domicilio" ||
//                           ""}
//                       </h6>
//                     </div>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h6>
//                         Direcci&oacute;n: {infoPedidoPickit.calle}{" "}
//                         {infoPedidoPickit.altura}, {ciudad || ""},{" "}
//                         {provincia || ""}
//                       </h6>
//                     </div>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h6>Precio: ${infoPedidoPickit.costo_envio + ".00"}</h6>
//                       <h6>Impuesto: {infoPedidoPickit.impuesto_envio}%</h6>
//                       <h6>Total: ${infoPedidoPickit.total_envio + ".00"}</h6>
//                     </div>
//                   </div>
//                 </Paper>
//               ) : pedido[0].tipo_envio === "Pickit" &&
//                 infoPedidoPickit.origen_envio === "1" ? (
//                 <Paper className="columna2-papper-info-envio">
//                   <div
//                     style={{
//                       display: "flex",
//                       flexDirection: "column",
//                       overflow: "hidden",
//                     }}
//                   >
//                     <h4>
//                       <strong>Informaci&oacute;n del env&iacute;o</strong>
//                     </h4>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h6>
//                         <strong>Metodo de env&iacute;o:</strong>{" "}
//                         {pedido[0].tipo_envio + " - " + "Envio a Punto" || ""}
//                       </h6>
//                     </div>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h6>
//                         <strong>Nombre punto:</strong>{" "}
//                         {punto_pickit !== undefined
//                           ? punto_pickit.name
//                           : "No definido"}
//                       </h6>
//                       <h6>
//                         <strong>Direcci&oacute;n:</strong>{" "}
//                         {punto_pickit !== undefined
//                           ? punto_pickit.address
//                           : "No definido"}
//                       </h6>
//                     </div>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h6>
//                         <strong>Precio:</strong> $
//                         {infoPedidoPickit.costo_envio + ".00"}
//                       </h6>
//                       <h6>
//                         <strong>Impuesto:</strong>{" "}
//                         {infoPedidoPickit.impuesto_envio}%
//                       </h6>
//                       <h6>
//                         <strong>Total:</strong> $
//                         {infoPedidoPickit.total_envio + ".00"}
//                       </h6>
//                     </div>
//                   </div>
//                 </Paper>
//               ) : pedido[0].tipo_envio === "Retiro por Sucursal" ? (
//                 <Paper className="columna2-papper-info-envio">
//                   <div
//                     style={{
//                       display: "flex",
//                       flexDirection: "column",
//                       overflow: "hidden",
//                     }}
//                   >
//                     <h4>
//                       <strong>Informaci&oacute;n del retiro</strong>
//                     </h4>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h6>
//                         <strong>M&eacute;todo de entrega:</strong>{" "}
//                         {pedido[0].tipo_envio || ""}
//                       </h6>
//                       <h6>
//                         <strong>Direcci&oacute;n de retiro:</strong>&nbsp;
//                         {direcciontienda.direccion || ""}&nbsp; Nro{" "}
//                         {direcciontienda.altura || ""}
//                       </h6>
//                     </div>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h5>
//                         <strong>Retira</strong>
//                       </h5>
//                       <h6>
//                         <strong>Nombre completo:</strong>{" "}
//                         {pedido[0].nombre || ""}
//                       </h6>
//                       <h6>
//                         <strong>Tel&eacute;fono:</strong>{" "}
//                         {pedido[0].telefono || ""}
//                       </h6>
//                       <h6>
//                         <strong>Email:</strong> {pedido[0].email || ""}
//                       </h6>
//                     </div>
//                   </div>
//                 </Paper>
//               ) : (
//                 <Paper className="columna2-papper-info-envio">
//                   <div
//                     style={{
//                       display: "flex",
//                       flexDirection: "column",
//                       overflow: "hidden",
//                     }}
//                   >
//                     <h4>
//                       <strong>Informaci&oacute;n del env&iacute;o</strong>
//                     </h4>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h6>
//                         Metodo de env&iacute;o: {pedido[0].tipo_envio || ""}
//                       </h6>
//                     </div>
//                     <Divider />
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h5>Direcci&oacute;n de env&iacute;o</h5>
//                       <h6>
//                         Direcci&oacute;n: {pedido[0].direccion}{" "}
//                         {pedido[0].altura}
//                       </h6>
//                     </div>
//                     <div style={{ paddingTop: 10, paddingBottom: 10 }}>
//                       <h5>Informaci&oacute;n de entrega</h5>
//                       <h6>
//                         Fecha entrega: {formatoFecha(pedido[6].fecha_entrega)}
//                       </h6>
//                       <h6>
//                         Horario: De {pedido[6].hora_desde}hs hasta{" "}
//                         {pedido[6].hora_hasta}hs
//                       </h6>
//                       <h6>D&iacute;as disponibles: {pedido[6].franja}</h6>
//                     </div>
//                   </div>
//                 </Paper>
//               )}
//             </div>
//           </div>
//         </div>
//       ) : (
//         <h3>Cargando ...</h3>
//       )}
//     </div>
//   );
// };
