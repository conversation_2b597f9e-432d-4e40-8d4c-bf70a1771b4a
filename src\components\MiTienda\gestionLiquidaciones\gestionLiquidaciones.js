import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  TextField,
  Checkbox,
  IconButton,
  Tooltip,
  Badge,
  FormControl,
  Select,
  MenuItem,
  ListSubheader,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Divider,
  Typography,
  CircularProgress,
  Chip,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import ClipLoader from "react-spinners/ClipLoader";
import { formatDate } from "../../shared/utility";
import RemoveCircleOutline from "@mui/icons-material/RemoveCircleOutline";
import AddCircleOutline from "@mui/icons-material/AddCircleOutline";
import { Clear, Search, KeyboardArrowDown } from "@mui/icons-material";
import DeleteIcon from "@mui/icons-material/Delete";
import CloseIcon from "@mui/icons-material/Close";
import PaidIcon from "@mui/icons-material/Paid";
import { ModalFiltrosGestionRemitos } from "../gestionRemitos/modalFiltrosGestionRemitos";
import {
  getListadoClientesByName,
  getRemitosPendientesLiquidacion,
  generarLiquidacion,
  triggerNotificationRefresh,
} from "../../../redux/actions/mitienda.actions";

export const GestionLiquidaciones = () => {
  const dispatch = useDispatch();
  const [page, setPage] = useState(1);
  const ITEMS_PER_PAGE = 10;

  const [cart, setCart] = useState({});
  const [cartItemCount, setCartItemCount] = useState(0);
  const [showCart, setShowCart] = useState(false);

  const [showDateModal, setShowDateModal] = useState(false);
  const [newLiquidacionDate, setNewLiquidacionDate] = useState("");

  const [remitoId, setRemitoId] = useState("");
  const [numeroRemito, setNumeroRemito] = useState("");

  const [alert, setAlert] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const [selectedQuantities, setSelectedQuantities] = useState({});
  // const [files, setFiles] = useState([]);
  const [filesByClient, setFilesByClient] = useState({});

  // Filter states
  const [show, setShow] = useState(false);
  // const [fechaDesde, setFechaDesde] = useState(() => {
  //   const today = new Date();
  //   return new Date(today.getFullYear(), today.getMonth() - 1, 1)
  //     .toISOString()
  //     .split("T")[0];
  // });
  const [fechaDesde, setFechaDesde] = useState(() => {
  const today = new Date();
  return new Date(today.getFullYear(), 0, 1) // January 1st of current year
    .toISOString()
    .split("T")[0];
});
  // const [fechaHasta, setFechaHasta] = useState(() => {
  //   const today = new Date();
  //   return new Date(today.getFullYear(), today.getMonth() + 1, 0)
  //     .toISOString()
  //     .split("T")[0];
  // });
  const [fechaHasta, setFechaHasta] = useState(() => {
  const today = new Date();
  return new Date(today.getFullYear(), 11, 31) // December 31st of current year
    .toISOString()
    .split("T")[0];
}); 

  const [clienteInput, setClienteInput] = useState("");
  const [clienteid, setClienteid] = useState("");
  const [selectedOption, setSelectedOption] = useState({
    nombre: "",
    apellido: "",
  });

  const [tableLoading, setTableLoading] = useState(false);
  const [generatingLiquidacion, setGeneratingLiquidacion] = useState(false);

  const [arcaNumbers, setArcaNumbers] = useState({});

  const formatCurrency = (value) => {
    return new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const {
    data: remitosPendientes,
    loading,
    paginaUltima,
  } = useSelector((state) => state.mitienda.remitosPendientesLiquidacion);

  const clientes = useSelector((state) => state.mitienda.clientesByName);

  //w
  // useEffect(() => {
  //   if (clienteid || remitoId || fechaDesde || fechaHasta) {
  //     setTableLoading(true);
  //     dispatch(
  //       getRemitosPendientesLiquidacion(
  //         fechaDesde,
  //         fechaHasta,
  //         page,
  //         ITEMS_PER_PAGE,
  //         clienteid,
  //         remitoId
  //       )
  //     ).finally(() => {
  //       setTableLoading(false);
  //     });
  //   } else {
  //     dispatch({
  //       type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
  //       payload: {
  //         data: {},
  //         paginaUltima: 1,
  //       },
  //     });
  //   }
  // }, [dispatch, fechaDesde, fechaHasta, page, clienteid, remitoId]);

  //f with pagination
//   useEffect(() => {
//   //   console.log('Fetching data with params:', {
//   //   fechaDesde, fechaHasta, page, clienteid, remitoId
//   // });
//   setTableLoading(true);
//   dispatch(
//     getRemitosPendientesLiquidacion(
//       fechaDesde,
//       fechaHasta,
//       page,
//       ITEMS_PER_PAGE,
//       clienteid,
//       remitoId
//     )
//   ).finally(() => {
//     setTableLoading(false);
//   });
// }, [dispatch, fechaDesde, fechaHasta, page, clienteid, remitoId]);

  //f  with numeroRemito
  useEffect(() => {
  if (clienteid || numeroRemito || fechaDesde || fechaHasta) {
    setTableLoading(true);
    dispatch(
      getRemitosPendientesLiquidacion(
        fechaDesde,
        fechaHasta,
        page,
        ITEMS_PER_PAGE,
        clienteid,
        numeroRemito  // Changed from remitoId
      )
    ).finally(() => {
      setTableLoading(false);
    });
  } else {
    dispatch({
      type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
      payload: {
        data: {},
        paginaUltima: 1,
      },
    });
  }
}, [dispatch, fechaDesde, fechaHasta, page, clienteid, numeroRemito]);

  useEffect(() => {
    if (clienteInput.length > 1) {
      dispatch(getListadoClientesByName(clienteInput, ""));
    }
  }, [clienteInput]);

  const handleClose = () => setShow(false);

  const handleChangeFechaDesde = (e) => {
    setFechaDesde(e.target.value);
  };

  const handleChangeFechaHasta = (e) => {
    setFechaHasta(e.target.value);
  };

  //w
  // const searchByFilters = () => {
  //   handleClose();
  //   setPage(1);
  //   setTableLoading(true);

  //   if (clienteid || remitoId || fechaDesde || fechaHasta) {
  //     dispatch(
  //       getRemitosPendientesLiquidacion(
  //         fechaDesde,
  //         fechaHasta,
  //         1,
  //         ITEMS_PER_PAGE,
  //         clienteid,
  //         remitoId
  //       )
  //     ).finally(() => {
  //       setTableLoading(false);
  //     });
  //   } else {
  //     dispatch({
  //       type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
  //       payload: {
  //         data: {},
  //         paginaUltima: 1,
  //       },
  //     });
  //     setTableLoading(false);
  //   }
  // };

  //f with numeroRemito
  const searchByFilters = () => {
  handleClose();
  setPage(1);
  setTableLoading(true);

  if (clienteid || numeroRemito || fechaDesde || fechaHasta) {  // Changed from remitoId
    dispatch(
      getRemitosPendientesLiquidacion(
        fechaDesde,
        fechaHasta,
        1,
        ITEMS_PER_PAGE,
        clienteid,
        numeroRemito  // Changed from remitoId
      )
    ).finally(() => {
      setTableLoading(false);
    });
  } else {
    dispatch({
      type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
      payload: {
        data: {},
        paginaUltima: 1,
      },
    });
    setTableLoading(false);
  }
};

  const reset = () => {
    handleClose();
    const today = new Date();
    const initialFechaDesde = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1
    )
      .toISOString()
      .split("T")[0];
    const initialFechaHasta = new Date(
      today.getFullYear(),
      today.getMonth() + 1,
      0
    )
      .toISOString()
      .split("T")[0];

    setFechaDesde(initialFechaDesde);
    setFechaHasta(initialFechaHasta);
    setPage(1);
  };

  //w
  // const clearRemitoSearch = () => {
  //   setRemitoId("");
  //   setPage(1);
  //   setTableLoading(true);

  //   if (!clienteid && !fechaDesde && !fechaHasta) {
  //     dispatch({
  //       type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
  //       payload: {
  //         data: {},
  //         paginaUltima: 1,
  //       },
  //     });
  //     setTableLoading(false);
  //   } else {
  //     dispatch(
  //       getRemitosPendientesLiquidacion(
  //         fechaDesde,
  //         fechaHasta,
  //         1,
  //         ITEMS_PER_PAGE,
  //         clienteid,
  //         ""
  //       )
  //     ).finally(() => {
  //       setTableLoading(false);
  //     });
  //   }
  // };


  //f with numeroRemito
  const clearRemitoSearch = () => {
  setNumeroRemito("");  // Changed from setRemitoId
  setPage(1);
  setTableLoading(true);

  if (!clienteid && !fechaDesde && !fechaHasta) {
    dispatch({
      type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
      payload: {
        data: {},
        paginaUltima: 1,
      },
    });
    setTableLoading(false);
  } else {
    dispatch(
      getRemitosPendientesLiquidacion(
        fechaDesde,
        fechaHasta,
        1,
        ITEMS_PER_PAGE,
        clienteid,
        ""  // Empty string instead of remitoId
      )
    ).finally(() => {
      setTableLoading(false);
    });
  }
};

//w
  // const handleChangePage = (event, value) => {
  //   setPage(value);
  //   setTableLoading(true);

  //   dispatch(
  //     getRemitosPendientesLiquidacion(
  //       fechaDesde,
  //       fechaHasta,
  //       value,
  //       ITEMS_PER_PAGE,
  //       clienteid,
  //       remitoId
  //     )
  //   ).finally(() => {
  //     setTableLoading(false);
  //   });
  // };

  //f con numeroRemito
  const handleChangePage = (event, value) => {
  setPage(value);
  setTableLoading(true);

  dispatch(
    getRemitosPendientesLiquidacion(
      fechaDesde,
      fechaHasta,
      value,
      ITEMS_PER_PAGE,
      clienteid,
      numeroRemito  // Changed from remitoId
    )
  ).finally(() => {
    setTableLoading(false);
  });
};

  const handleQuantityChange = (productId, newValue, maxQuantity) => {
    const value = Math.min(Math.max(0, Number(newValue)), Number(maxQuantity));
    setSelectedQuantities((prev) => ({
      ...prev,
      [productId]: value,
    }));
  };

  const handleSetMaxQuantity = (productId, maxQuantity) => {
    setSelectedQuantities((prev) => ({
      ...prev,
      [productId]: Number(maxQuantity),
    }));
  };

  const handleCartToggle = (item) => {
    const productId = item.remitodetalleid;
    const quantity = selectedQuantities[productId] || 0;

    if (quantity === 0) return;

    const clientId = item.clienteid;
    const clientName = item.nombrecliente;

    if (cart[clientId]?.products?.[productId]) {
      // remove from cart
      setCart((prev) => {
        const newCart = { ...prev };
        delete newCart[clientId].products[productId];

        if (Object.keys(newCart[clientId].products).length === 0) {
          delete newCart[clientId];
        }

        return newCart;
      });
      setCartItemCount((prev) => prev - 1);
    } else {
      // add to cart
      setCart((prev) => {
        const newCart = { ...prev };

        if (!newCart[clientId]) {
          newCart[clientId] = {
            clientName,
            clientId,
            fechaLiquidacion: new Date().toISOString().split("T")[0],
            products: {},
          };
        }

        newCart[clientId].products[productId] = {
          ...item,
          selectedQuantity: quantity,
        };

        return newCart;
      });
      setCartItemCount((prev) => prev + 1);
    }
  };

  const removeFromCart = (clientId, productId) => {
    setCart((prev) => {
      const newCart = { ...prev };

      if (newCart[clientId]?.products) {
        delete newCart[clientId].products[productId];

        if (Object.keys(newCart[clientId].products).length === 0) {
          delete newCart[clientId];
        }
      }

      return newCart;
    });
    setCartItemCount((prev) => prev - 1);
    setSelectedQuantities((prev) => ({
      ...prev,
      [productId]: 0,
    }));
  };

  const handleIncrement = (productId, currentPending) => {
    setSelectedQuantities((prev) => {
      const currentValue = prev[productId] || 0;
      const pendingValue = Number(currentPending || 0);

      if (currentValue < pendingValue) {
        return {
          ...prev,
          [productId]: currentValue + 1,
        };
      }
      return prev;
    });
  };

  const handleDecrement = (productId) => {
    setSelectedQuantities((prev) => {
      const currentValue = prev[productId] || 0;
      if (currentValue > 0) {
        return {
          ...prev,
          [productId]: currentValue - 1,
        };
      }
      return prev;
    });
  };

  //w
  // const handleOpenDateModal = () => {
  //   setNewLiquidacionDate(new Date().toISOString().split("T")[0]);
  //   setShowDateModal(true);
  // };

  const handleOpenDateModal = () => {
    // Get current date in local timezone without time conversion
    const today = new Date();
    const localDateString = `${today.getFullYear()}-${String(
      today.getMonth() + 1
    ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;
    setNewLiquidacionDate(localDateString);
    setShowDateModal(true);
  };

  const handleCloseDateModal = () => {
    setShowDateModal(false);
  };

  //w
  // const handleUpdateLiquidacionDate = () => {
  //   setCart((prev) => {
  //     const newCart = { ...prev };
  //     Object.keys(newCart).forEach((clientId) => {
  //       newCart[clientId].fechaLiquidacion = newLiquidacionDate;
  //     });
  //     return newCart;
  //   });

  //   setAlert({
  //     open: true,
  //     message: "Fecha de liquidación actualizada correctamente",
  //     severity: "success",
  //   });
  //   setShowDateModal(false);
  // };

  const handleUpdateLiquidacionDate = () => {
    // Use the date string directly without creating a new Date object
    const formattedDate = newLiquidacionDate;

    setCart((prev) => {
      const newCart = { ...prev };
      Object.keys(newCart).forEach((clientId) => {
        newCart[clientId].fechaLiquidacion = formattedDate;
      });
      return newCart;
    });

    setAlert({
      open: true,
      message: "Fecha de liquidación actualizada correctamente",
      severity: "success",
    });
    setShowDateModal(false);
  };

  //w
  // const handleFileUpload = (e) => {
  //   const newFiles = Array.from(e.target.files);
  //   setFiles((prev) => [...prev, ...newFiles]);
  // };

  const handleFileUpload = (e, clientId) => {
    const newFiles = Array.from(e.target.files);
    setFilesByClient((prev) => ({
      ...prev,
      [clientId]: [...(prev[clientId] || []), ...newFiles],
    }));
  };

  //w
  // const removeFile = (index) => {
  //   setFiles((prev) => prev.filter((_, i) => i !== index));
  // };

  //f removes file for specific client
  const removeFile = (clientId, index) => {
    setFilesByClient((prev) => {
      const newFiles = [...prev[clientId]];
      newFiles.splice(index, 1);
      return {
        ...prev,
        [clientId]: newFiles,
      };
    });
  };

  //w original (no file upload)
  // const generateLiquidacion = async () => {
  //   if (Object.keys(cart).length === 0) {
  //     setAlert({
  //       open: true,
  //       message: "No hay productos seleccionados para liquidar",
  //       severity: "error",
  //     });
  //     return;
  //   }

  //   const clientsWithoutDate = Object.entries(cart)
  //     .filter(([_, clientData]) => {
  //       const fechaLiquidacion = clientData.fechaLiquidacion;
  //       return (
  //         !fechaLiquidacion ||
  //         fechaLiquidacion === "null" ||
  //         fechaLiquidacion === "" ||
  //         fechaLiquidacion.includes("NaN")
  //       );
  //     })
  //     .map(([_, clientData]) => clientData.clientName);

  //   if (clientsWithoutDate.length > 0) {
  //     setAlert({
  //       open: true,
  //       message: `Debe establecer una fecha de liquidación para ${clientsWithoutDate.join(
  //         ", "
  //       )}`,
  //       severity: "warning",
  //     });
  //     return;
  //   }

  //   setGeneratingLiquidacion(true);

  //   try {
  //     const results = [];
  //     const errors = [];

  //     for (const [clientId, clientData] of Object.entries(cart)) {
  //       try {
  //         const productosArray = Object.values(clientData.products);

  //         // Ensure we're passing the correct properties
  //         const formattedProducts = productosArray.map((item) => ({
  //           ...item,
  //           remitoid: item.remitoid,
  //           remitodetalleid: item.remitodetalleid,
  //         }));

  //         const result = await dispatch(
  //           generarLiquidacion(
  //             clientId,
  //             formattedProducts,
  //             clientData.fechaLiquidacion
  //           )
  //         );

  //         results.push({
  //           clientId,
  //           clientName: clientData.clientName,
  //           result,
  //         });
  //       } catch (error) {
  //         errors.push({
  //           clientId,
  //           clientName: clientData.clientName,
  //           error: error.message,
  //         });
  //       }
  //     }

  //     // clears cart and show success
  //     setCart({});
  //     setCartItemCount(0);
  //     setSelectedQuantities({});
  //     setFiles([]);
  //     setShowCart(false);

  //     // show appropriate message
  //     if (errors.length === 0) {
  //       setAlert({
  //         open: true,
  //         message: `Liquidaciones generadas correctamente para ${results.length} cliente(s)`,
  //         severity: "success",
  //       });
  //     } else if (results.length === 0) {
  //       setAlert({
  //         open: true,
  //         message: `Error al generar liquidaciones: ${errors[0].error}`,
  //         severity: "error",
  //       });
  //     } else {
  //       setAlert({
  //         open: true,
  //         message: `Se generaron ${results.length} liquidaciones correctamente, pero fallaron ${errors.length}. Revise los datos e intente nuevamente.`,
  //         severity: "warning",
  //       });
  //     }

  //     // refreshes the data
  //     setTableLoading(true);
  //     await dispatch(
  //       getRemitosPendientesLiquidacion(
  //         fechaDesde,
  //         fechaHasta,
  //         page,
  //         ITEMS_PER_PAGE,
  //         clienteid,
  //         remitoId
  //       )
  //     );
  //     setTableLoading(false);
  //   } catch (error) {
  //     setAlert({
  //       open: true,
  //       message: `Error al generar liquidaciones: ${error.message}`,
  //       severity: "error",
  //     });
  //   } finally {
  //     setGeneratingLiquidacion(false);
  //   }
  // };

  //f with file upload
  // const generateLiquidacion = async () => {
  //   if (Object.keys(cart).length === 0) {
  //     setAlert({
  //       open: true,
  //       message: "No hay productos seleccionados para liquidar",
  //       severity: "error",
  //     });
  //     return;
  //   }

  //   const clientsWithoutDate = Object.entries(cart)
  //     .filter(([_, clientData]) => {
  //       const fechaLiquidacion = clientData.fechaLiquidacion;
  //       return (
  //         !fechaLiquidacion ||
  //         fechaLiquidacion === "null" ||
  //         fechaLiquidacion === "" ||
  //         fechaLiquidacion.includes("NaN")
  //       );
  //     })
  //     .map(([_, clientData]) => clientData.clientName);

  //   if (clientsWithoutDate.length > 0) {
  //     setAlert({
  //       open: true,
  //       message: `Debe establecer una fecha de liquidación para ${clientsWithoutDate.join(
  //         ", "
  //       )}`,
  //       severity: "warning",
  //     });
  //     return;
  //   }

  //   setGeneratingLiquidacion(true);

  //   try {
  //     const results = [];
  //     const errors = [];

  //     for (const [clientId, clientData] of Object.entries(cart)) {
  //       try {
  //         const productosArray = Object.values(clientData.products);
  //         const clientFiles = filesByClient[clientId] || [];

  //         // Convert files to base64
  //         const filePromises = clientFiles.map((file) => {
  //           return new Promise((resolve) => {
  //             const reader = new FileReader();
  //             reader.onload = (event) => {
  //               resolve({
  //                 remitoid: productosArray[0].remitoid, // Assuming all products in cart are from same remito
  //                 nombre: file.name,
  //                 imagen64: event.target.result.split(",")[1], // Remove data URL prefix
  //               });
  //             };
  //             reader.readAsDataURL(file);
  //           });
  //         });

  //         const imagenes = await Promise.all(filePromises);

  //         const formattedProducts = productosArray.map((item) => ({
  //           ...item,
  //           remitoid: item.remitoid,
  //           remitodetalleid: item.remitodetalleid,
  //         }));

  //         const result = await dispatch(
  //           generarLiquidacion(
  //             clientId,
  //             formattedProducts,
  //             clientData.fechaLiquidacion,
  //             imagenes
  //           )
  //         );

  //         results.push({
  //           clientId,
  //           clientName: clientData.clientName,
  //           result,
  //         });
  //       } catch (error) {
  //         errors.push({
  //           clientId,
  //           clientName: clientData.clientName,
  //           error: error.message,
  //         });
  //       }
  //     }

  //     // clears cart and show success
  //     setCart({});
  //     setFilesByClient({});
  //     setCartItemCount(0);
  //     setSelectedQuantities({});
  //     setShowCart(false);

  //     // show appropriate message
  //     if (errors.length === 0) {
  //       setAlert({
  //         open: true,
  //         message: `Liquidaciones generadas correctamente para ${results.length} cliente(s)`,
  //         severity: "success",
  //       });
  //     } else if (results.length === 0) {
  //       setAlert({
  //         open: true,
  //         message: `Error al generar liquidaciones: ${errors[0].error}`,
  //         severity: "error",
  //       });
  //     } else {
  //       setAlert({
  //         open: true,
  //         message: `Se generaron ${results.length} liquidaciones correctamente, pero fallaron ${errors.length}. Revise los datos e intente nuevamente.`,
  //         severity: "warning",
  //       });
  //     }

  //     // refreshes the data
  //     setTableLoading(true);
  //     await dispatch(
  //       getRemitosPendientesLiquidacion(
  //         fechaDesde,
  //         fechaHasta,
  //         page,
  //         ITEMS_PER_PAGE,
  //         clienteid,
  //         remitoId
  //       )
  //     );
  //     setTableLoading(false);
  //   } catch (error) {
  //     setAlert({
  //       open: true,
  //       message: `Error al generar liquidaciones: ${error.message}`,
  //       severity: "error",
  //     });
  //   } finally {
  //     setGeneratingLiquidacion(false);
  //   }
  // };

  //f with arca number
  // const generateLiquidacion = async () => {
  //   if (Object.keys(cart).length === 0) {
  //     setAlert({
  //       open: true,
  //       message: "No hay productos seleccionados para liquidar",
  //       severity: "error",
  //     });
  //     return;
  //   }

  //   const clientsWithoutDate = Object.entries(cart)
  //     .filter(([_, clientData]) => {
  //       const fechaLiquidacion = clientData.fechaLiquidacion;
  //       return (
  //         !fechaLiquidacion ||
  //         fechaLiquidacion === "null" ||
  //         fechaLiquidacion === "" ||
  //         fechaLiquidacion.includes("NaN")
  //       );
  //     })
  //     .map(([_, clientData]) => clientData.clientName);

  //   if (clientsWithoutDate.length > 0) {
  //     setAlert({
  //       open: true,
  //       message: `Debe establecer una fecha de liquidación para ${clientsWithoutDate.join(
  //         ", "
  //       )}`,
  //       severity: "warning",
  //     });
  //     return;
  //   }

  //   setGeneratingLiquidacion(true);

  //   try {
  //     const results = [];
  //     const errors = [];

  //     for (const [clientId, clientData] of Object.entries(cart)) {
  //       try {
  //         const productosArray = Object.values(clientData.products);
  //         const clientFiles = filesByClient[clientId] || [];
  //         const arcaNumber = arcaNumbers[clientId] || "";

  //         // Convert files to base64
  //         const filePromises = clientFiles.map((file) => {
  //           return new Promise((resolve) => {
  //             const reader = new FileReader();
  //             reader.onload = (event) => {
  //               resolve({
  //                 remitoid: productosArray[0].remitoid,
  //                 nombre: file.name,
  //                 imagen64: event.target.result.split(",")[1],
  //               });
  //             };
  //             reader.readAsDataURL(file);
  //           });
  //         });

  //         const imagenes = await Promise.all(filePromises);

  //         const formattedProducts = productosArray.map((item) => ({
  //           ...item,
  //           remitoid: item.remitoid,
  //           remitodetalleid: item.remitodetalleid,
  //         }));

  //         const result = await dispatch(
  //           generarLiquidacion(
  //             clientId,
  //             formattedProducts,
  //             clientData.fechaLiquidacion,
  //             imagenes,
  //             arcaNumber
  //           )
  //         );

  //         results.push({
  //           clientId,
  //           clientName: clientData.clientName,
  //           result,
  //         });
  //       } catch (error) {
  //         errors.push({
  //           clientId,
  //           clientName: clientData.clientName,
  //           error: error.message,
  //         });
  //       }
  //     }

  //     // Clear cart and show success
  //     setCart({});
  //     setFilesByClient({});
  //     setArcaNumbers({});
  //     setCartItemCount(0);
  //     setSelectedQuantities({});
  //     setShowCart(false);

  //     // Show appropriate message
  //     if (errors.length === 0) {
  //       setAlert({
  //         open: true,
  //         message: `Liquidaciones generadas correctamente para ${results.length} cliente(s)`,
  //         severity: "success",
  //       });
  //     } else if (results.length === 0) {
  //       setAlert({
  //         open: true,
  //         message: `Error al generar liquidaciones: ${errors[0].error}`,
  //         severity: "error",
  //       });
  //     } else {
  //       setAlert({
  //         open: true,
  //         message: `Se generaron ${results.length} liquidaciones correctamente, pero fallaron ${errors.length}. Revise los datos e intente nuevamente.`,
  //         severity: "warning",
  //       });
  //     }

  //     // Refresh the data
  //     setTableLoading(true);
  //     await dispatch(
  //       getRemitosPendientesLiquidacion(
  //         fechaDesde,
  //         fechaHasta,
  //         page,
  //         ITEMS_PER_PAGE,
  //         clienteid,
  //         remitoId
  //       )
  //     );
  //     setTableLoading(false);
  //   } catch (error) {
  //     setAlert({
  //       open: true,
  //       message: `Error al generar liquidaciones: ${error.message}`,
  //       severity: "error",
  //     });
  //   } finally {
  //     setGeneratingLiquidacion(false);
  //   }
  // };

  //w
//   const generateLiquidacion = async () => {
//   if (Object.keys(cart).length === 0) {
//     setAlert({
//       open: true,
//       message: "No hay productos seleccionados para liquidar",
//       severity: "error",
//     });
//     return;
//   }

//   const clientsWithoutDate = Object.entries(cart)
//     .filter(([_, clientData]) => {
//       const fechaLiquidacion = clientData.fechaLiquidacion;
//       return (
//         !fechaLiquidacion ||
//         fechaLiquidacion === "null" ||
//         fechaLiquidacion === "" ||
//         fechaLiquidacion.includes("NaN")
//       );
//     })
//     .map(([_, clientData]) => clientData.clientName);

//   if (clientsWithoutDate.length > 0) {
//     setAlert({
//       open: true,
//       message: `Debe establecer una fecha de liquidación para ${clientsWithoutDate.join(
//         ", "
//       )}`,
//       severity: "warning",
//     });
//     return;
//   }

//   setGeneratingLiquidacion(true);

//   try {
//     const results = [];
//     const errors = [];

//     for (const [clientId, clientData] of Object.entries(cart)) {
//       try {
//         const productosArray = Object.values(clientData.products);
//         const clientFiles = filesByClient[clientId] || [];
//         const arcaNumber = arcaNumbers[clientId] || "";

//         // Convert files to base64
//         const filePromises = clientFiles.map((file) => {
//           return new Promise((resolve) => {
//             const reader = new FileReader();
//             reader.onload = (event) => {
//               resolve({
//                 remitoid: productosArray[0].remitoid,
//                 nombre: file.name,
//                 imagen64: event.target.result.split(",")[1],
//               });
//             };
//             reader.readAsDataURL(file);
//           });
//         });

//         const imagenes = await Promise.all(filePromises);

//         const formattedProducts = productosArray.map((item) => ({
//           ...item,
//           remitoid: item.remitoid,
//           remitodetalleid: item.remitodetalleid,
//         }));

//         const result = await dispatch(
//           generarLiquidacion(
//             clientId,
//             formattedProducts,
//             clientData.fechaLiquidacion,
//             imagenes,
//             arcaNumber
//           )
//         );

//         results.push({
//           clientId,
//           clientName: clientData.clientName,
//           result,
//         });
//       } catch (error) {
//         errors.push({
//           clientId,
//           clientName: clientData.clientName,
//           error: error.message,
//         });
//       }
//     }

//     // Clear cart and related state
//     setCart({});
//     setFilesByClient({});
//     setArcaNumbers({});
//     setCartItemCount(0);
//     setSelectedQuantities({});
//     setShowCart(false);

//     // Show appropriate message
//     if (errors.length === 0) {
//       setAlert({
//         open: true,
//         message: `Liquidaciones generadas correctamente para ${results.length} cliente(s)`,
//         severity: "success",
//       });

//       // ✅ Dispatch notification refresh
//       dispatch(triggerNotificationRefresh());
//     } else if (results.length === 0) {
//       setAlert({
//         open: true,
//         message: `Error al generar liquidaciones: ${errors[0].error}`,
//         severity: "error",
//       });
//     } else {
//       setAlert({
//         open: true,
//         message: `Se generaron ${results.length} liquidaciones correctamente, pero fallaron ${errors.length}. Revise los datos e intente nuevamente.`,
//         severity: "warning",
//       });
//     }

//     // Refresh the data
//     setTableLoading(true);
//     await dispatch(
//       getRemitosPendientesLiquidacion(
//         fechaDesde,
//         fechaHasta,
//         page,
//         ITEMS_PER_PAGE,
//         clienteid,
//         remitoId
//       )
//     );
//     setTableLoading(false);
//   } catch (error) {
//     setAlert({
//       open: true,
//       message: `Error al generar liquidaciones: ${error.message}`,
//       severity: "error",
//     });
//   } finally {
//     setGeneratingLiquidacion(false);
//   }
// };

//f
const generateLiquidacion = async () => {
  if (Object.keys(cart).length === 0) {
    setAlert({
      open: true,
      message: "No hay productos seleccionados para liquidar",
      severity: "error",
    });
    return;
  }

  const clientsWithoutDate = Object.entries(cart)
    .filter(([_, clientData]) => {
      const fechaLiquidacion = clientData.fechaLiquidacion;
      return (
        !fechaLiquidacion ||
        fechaLiquidacion === "null" ||
        fechaLiquidacion === "" ||
        fechaLiquidacion.includes("NaN")
      );
    })
    .map(([_, clientData]) => clientData.clientName);

  if (clientsWithoutDate.length > 0) {
    setAlert({
      open: true,
      message: `Debe establecer una fecha de liquidación para ${clientsWithoutDate.join(", ")}`,
      severity: "warning",
    });
    return;
  }

  setGeneratingLiquidacion(true);

  try {
    const results = [];
    const errors = [];

    for (const [clientId, clientData] of Object.entries(cart)) {
      try {
        const productosArray = Object.values(clientData.products);
        const clientFiles = filesByClient[clientId] || [];
        const arcaNumber = arcaNumbers[clientId] || "";

        // Convert files to base64
        const filePromises = clientFiles.map((file) => {
          return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              resolve({
                nombre: file.name,
                imagen64: event.target.result.split(",")[1], // Remove data URL prefix
              });
            };
            reader.readAsDataURL(file);
          });
        });

        const imagenes = await Promise.all(filePromises);
        console.log(`Images for client ${clientId}:`, imagenes);

        const formattedProducts = productosArray.map((item) => ({
          ...item,
          remitoid: item.remitoid,
          remito_detalleid: item.remitodetalleid,
        }));

        const result = await dispatch(
          generarLiquidacion(
            clientId,
            formattedProducts,
            clientData.fechaLiquidacion,
            imagenes,
            arcaNumber
          )
        );

        results.push({
          clientId,
          clientName: clientData.clientName,
          result,
        });
      } catch (error) {
        console.error(`Error for client ${clientId}:`, error);
        errors.push({
          clientId,
          clientName: clientData.clientName,
          error: error.message,
        });
      }
    }

    // Clear cart and related state
    setCart({});
    setFilesByClient({});
    setArcaNumbers({});
    setCartItemCount(0);
    setSelectedQuantities({});
    setShowCart(false);

    // Show appropriate message
    if (errors.length === 0) {
      setAlert({
        open: true,
        message: `Liquidaciones generadas correctamente para ${results.length} cliente(s)`,
        severity: "success",
      });

      // Dispatch notification refresh
      dispatch(triggerNotificationRefresh());
    } else if (results.length === 0) {
      setAlert({
        open: true,
        message: `Error al generar liquidaciones: ${errors[0].error}`,
        severity: "error",
      });
    } else {
      setAlert({
        open: true,
        message: `Se generaron ${results.length} liquidaciones correctamente, pero fallaron ${errors.length}. Revise los datos e intente nuevamente.`,
        severity: "warning",
      });
    }

    // Refresh the data
    setTableLoading(true);
    await dispatch(
      getRemitosPendientesLiquidacion(
        fechaDesde,
        fechaHasta,
        page,
        ITEMS_PER_PAGE,
        clienteid,
        // remitoId,
        numeroRemito
      )
    );
    setTableLoading(false);
  } catch (error) {
    console.error("General error generating liquidaciones:", error);
    setAlert({
      open: true,
      message: `Error al generar liquidaciones: ${error.message}`,
      severity: "error",
    });
  } finally {
    setGeneratingLiquidacion(false);
  }
};


  const handleCloseAlert = () => {
    setAlert({
      ...alert,
      open: false,
    });
  };

  const isProductInCart = (clientId, productId) => {
    return !!cart[clientId]?.products?.[productId];
  };

  const clearClientSearch = () => {
    setClienteid("");
    setSelectedOption({ nombre: "", apellido: "" });
    setClienteInput("");
    setPage(1);
    setTableLoading(true);

    dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });

    if (!remitoId && !fechaDesde && !fechaHasta) {
      dispatch({
        type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
        payload: {
          data: {},
          paginaUltima: 1,
        },
      });
      setTableLoading(false);
    } else {
      dispatch(
        getRemitosPendientesLiquidacion(
          fechaDesde,
          fechaHasta,
          1,
          ITEMS_PER_PAGE,
          "",
          remitoId
        )
      ).finally(() => {
        setTableLoading(false);
      });
    }
  };

  const handleClientChange = (e, option) => {
    setClienteid(e.target.value);
    setSelectedOption(option);
    setClienteInput("");
    setPage(1);
    setTableLoading(true);

    dispatch(
      getRemitosPendientesLiquidacion(
        fechaDesde,
        fechaHasta,
        1,
        ITEMS_PER_PAGE,
        e.target.value,
        remitoId
      )
    ).finally(() => {
      setTableLoading(false);
    });
  };

  const updateCartQuantity = (clientId, productId, newValue) => {
    const product = cart[clientId]?.products[productId];
    if (!product) return;

    const maxQuantity = product.cantidad;
    const numValue = Number(newValue);

    if (numValue <= 0) {
      removeFromCart(clientId, productId);
      return;
    }

    const value = Math.min(numValue, Number(maxQuantity));

    setCart((prev) => {
      const newCart = { ...prev };
      if (newCart[clientId]?.products[productId]) {
        newCart[clientId].products[productId] = {
          ...newCart[clientId].products[productId],
          selectedQuantity: value,
        };
      }
      return newCart;
    });

    setSelectedQuantities((prev) => ({
      ...prev,
      [productId]: value,
    }));
  };

  return (
    <div className="notificaciones-container">
      <Snackbar
        open={alert.open}
        autoHideDuration={6000}
        onClose={handleCloseAlert}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert onClose={handleCloseAlert} severity={alert.severity}>
          {alert.message}
        </Alert>
      </Snackbar>

      <ModalFiltrosGestionRemitos
        show={show}
        handleClose={handleClose}
        search={searchByFilters}
        reset={reset}
        fechaDesde={fechaDesde}
        handleChangeFechaDesde={handleChangeFechaDesde}
        fechaHasta={fechaHasta}
        handleChangeFechaHasta={handleChangeFechaHasta}
      />

      <div style={{ padding: "20px" }}>
        <h3>Gestión de Liquidaciones</h3>
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            alignItems: "center",
          }}
        >
          <Button
            variant="outlined"
            onClick={() => setShow(true)}
            startIcon={<FilterListIcon />}
          >
            Filtros
          </Button>

          <FormControl sx={{ minWidth: 300 }}>
            <Select
              size="small"
              value={clienteid}
              onChange={(e) => {
                setClienteid(e.target.value);
                setPage(1);
              }}
              displayEmpty
              renderValue={() =>
                clienteid
                  ? `${selectedOption.nombre} ${selectedOption.apellido}`
                  : "Buscar cliente..."
              }
              onOpen={() => {
                setClienteInput("");
              }}
              IconComponent={() => (
                <div style={{ display: "flex", alignItems: "center" }}>
                  {clienteid && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearClientSearch();
                      }}
                      sx={{ marginRight: 1 }}
                    >
                      <Clear />
                    </IconButton>
                  )}
                  <KeyboardArrowDown />
                </div>
              )}
            >
              <ListSubheader>
                <TextField
                  className="client-search-input"
                  size="small"
                  autoFocus
                  placeholder="Buscar cliente..."
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  inputProps={{
                    style: {
                      height: 35,
                      fontSize: 17,
                    },
                  }}
                  onChange={(e) => setClienteInput(e.target.value)}
                  onClick={(e) => e.stopPropagation()}
                  value={clienteInput}
                />
              </ListSubheader>
              {clientes &&
                clientes.map((option) => (
                  <MenuItem
                    key={option.clienteid}
                    value={option.clienteid}
                    onClick={() =>
                      handleClientChange(
                        { target: { value: option.clienteid } },
                        option
                      )
                    }
                  >
                    {option.nombre} {option.apellido}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          {/* <TextField
            size="small"
            placeholder="Buscar por Nº Remito..."
            value={remitoId}
            onChange={(e) => {
              setRemitoId(e.target.value);
              setPage(1);
            }}
            onKeyPress={(e) => {
              if (e.key === "Enter") {
                setTableLoading(true);
                dispatch(
                  getRemitosPendientesLiquidacion(
                    fechaDesde,
                    fechaHasta,
                    1,
                    ITEMS_PER_PAGE,
                    clienteid,
                    remitoId
                  )
                ).finally(() => {
                  setTableLoading(false);
                });
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {remitoId && (
                    <IconButton size="small" onClick={clearRemitoSearch}>
                      <Clear />
                    </IconButton>
                  )}
                  {remitoId && (
                    <IconButton
                      size="small"
                      onClick={() => {
                        setTableLoading(true);
                        dispatch(
                          getRemitosPendientesLiquidacion(
                            fechaDesde,
                            fechaHasta,
                            1,
                            ITEMS_PER_PAGE,
                            clienteid,
                            remitoId
                          )
                        ).finally(() => {
                          setTableLoading(false);
                        });
                      }}
                    >
                      <Search />
                    </IconButton>
                  )}
                </InputAdornment>
              ),
              style: {
                height: "40px",
              },
            }}
            sx={{
              minWidth: 200,
              "& .MuiOutlinedInput-root": {
                padding: "0 8px",
              },
            }}
          /> */}

          <TextField
  size="small"
  placeholder="Buscar por Nº Remito..."
  value={numeroRemito}
  onChange={(e) => {
    setNumeroRemito(e.target.value);
    setPage(1);
  }}
  onKeyPress={(e) => {
    if (e.key === "Enter") {
      setTableLoading(true);
      dispatch(
        getRemitosPendientesLiquidacion(
          fechaDesde,
          fechaHasta,
          1,
          ITEMS_PER_PAGE,
          clienteid,
          numeroRemito
        )
      ).finally(() => {
        setTableLoading(false);
      });
    }
  }}
  InputProps={{
    startAdornment: (
      <InputAdornment position="start">
        <Search />
      </InputAdornment>
    ),
    endAdornment: (
      <InputAdornment position="end">
        {numeroRemito && (
          <IconButton size="small" onClick={clearRemitoSearch}>
            <Clear />
          </IconButton>
        )}
        {numeroRemito && (
          <IconButton
            size="small"
            onClick={() => {
              setTableLoading(true);
              dispatch(
                getRemitosPendientesLiquidacion(
                  fechaDesde,
                  fechaHasta,
                  1,
                  ITEMS_PER_PAGE,
                  clienteid,
                  numeroRemito
                )
              ).finally(() => {
                setTableLoading(false);
              });
            }}
          >
            <Search />
          </IconButton>
        )}
      </InputAdornment>
    ),
    style: {
      height: "40px",
    },
  }}
  sx={{
    minWidth: 200,
    "& .MuiOutlinedInput-root": {
      padding: "0 8px",
    },
  }}
/>

{/* search with numeroRemito */}
          {/* <TextField
  size="small"
  placeholder="Buscar por Nº Remito..."
  value={numeroRemito}  // Changed from remitoId
  onChange={(e) => {
    setNumeroRemito(e.target.value);  // Changed from setRemitoId
    setPage(1);
  }}
  onKeyPress={(e) => {
    if (e.key === "Enter") {
      setTableLoading(true);
      dispatch(
        getRemitosPendientesLiquidacion(
          fechaDesde,
          fechaHasta,
          1,
          ITEMS_PER_PAGE,
          clienteid,
          numeroRemito  // Changed from remitoId
        )
      ).finally(() => {
        setTableLoading(false);
      });
    }
  }}
  InputProps={{
    startAdornment: (
      <InputAdornment position="start">
        <Search />
      </InputAdornment>
    ),
    endAdornment: (
      <InputAdornment position="end">
        {numeroRemito && (  // Changed from remitoId
          <IconButton size="small" onClick={clearRemitoSearch}>
            <Clear />
          </IconButton>
        )}
        {numeroRemito && (  // Changed from remitoId
          <IconButton
            size="small"
            onClick={() => {
              setTableLoading(true);
              dispatch(
                getRemitosPendientesLiquidacion(
                  fechaDesde,
                  fechaHasta,
                  1,
                  ITEMS_PER_PAGE,
                  clienteid,
                  numeroRemito  // Changed from remitoId
                )
              ).finally(() => {
                setTableLoading(false);
              });
            }}
          >
            <Search />
          </IconButton>
        )}
      </InputAdornment>
    ),
    style: {
      height: "40px",
    },
  }}
  sx={{
    minWidth: 200,
    "& .MuiOutlinedInput-root": {
      padding: "0 8px",
    },
  }}
/> */}

          <Tooltip title="Ver liquidaciones">
            <Badge badgeContent={cartItemCount} color="primary">
              <IconButton onClick={() => setShowCart(!showCart)}>
                <PaidIcon />
              </IconButton>
            </Badge>
          </Tooltip>
        </div>
      </div>

      <TableContainer
        component={Paper}
        sx={{
          marginTop: 5,
          width: "90%",
          alignSelf: "center",
          margin: "40px auto",
          marginBottom: 5,
          padding: "0 20px",
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              {[
                "Nro Remito",
                // "Nro de Cliente",
                // "Cliente",
                "Artículo",
                "Descripción",
                "Fecha Entrega",
                "Cantidad Original",
                "Cantidad Liquidada",
                "Cant. Pendiente a Liquidar",
                "Precio",
                "Total",
                // "Estado",
                "Liquidar",
                "Acciones",
              ].map((header) => (
                <TableCell
                  key={header}
                  style={{
                    fontWeight: "bold",
                    fontSize: "90%",
                    padding: "8px 2px",
                  }}
                  align="center"
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {tableLoading ? (
              <TableRow>
                <TableCell colSpan={12} align="center">
                  <ClipLoader loading={tableLoading} size={50} />
                </TableCell>
              </TableRow>
            ) : remitosPendientes &&
              Object.keys(remitosPendientes).length > 0 ? (
              Object.values(remitosPendientes).map((item) => (
                <TableRow key={item.remitodetalleid}>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.numeroremito}
                  </TableCell>
                  {/* <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.clienteid}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.nombrecliente}
                  </TableCell> */}
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.codigoarticulo || "--"}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.nombreproducto || "--"}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {formatDate(item.fechaentrega)}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidad}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidadentregada}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {item.cantidadpendiente || 0}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {formatCurrency(item.precioventa)}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    {formatCurrency(item.totalventa)}
                  </TableCell>
                  {/* <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    <Chip
                      label={item.estado_liquidacion || "Pendiente"}
                      color={getEstadoColor(item.estado_liquidacion)}
                      size="small"
                    />
                  </TableCell> */}
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <IconButton
                        size="small"
                        onClick={() => handleDecrement(item.remitodetalleid)}
                        disabled={isProductInCart(
                          item.clienteid,
                          item.remitodetalleid
                        )}
                      >
                        <RemoveCircleOutline fontSize="small" />
                      </IconButton>
                      <TextField
                        type="number"
                        value={selectedQuantities[item.remitodetalleid] || 0}
                        onChange={(e) =>
                          handleQuantityChange(
                            item.remitodetalleid,
                            e.target.value,
                            item.cantidadpendiente
                          )
                        }
                        InputProps={{
                          inputProps: {
                            min: 0,
                            max: item.cantidad,
                            style: { textAlign: "center", fontSize: "80%" },
                          },
                          readOnly: isProductInCart(
                            item.clienteid,
                            item.remitodetalleid
                          ),
                        }}
                        disabled={isProductInCart(
                          item.clienteid,
                          item.remitodetalleid
                        )}
                        size="small"
                        style={{ width: "70px" }}
                      />
                      <IconButton
                        size="small"
                        onClick={() =>
                          handleIncrement(item.remitodetalleid, item.cantidadpendiente)
                        }
                        disabled={isProductInCart(
                          item.clienteid,
                          item.remitodetalleid
                        )}
                      >
                        <AddCircleOutline fontSize="small" />
                      </IconButton>
                      <Tooltip title="Liquidar cantidad máxima">
                        <span>
                          <IconButton
                            size="small"
                            onClick={() =>
                              handleSetMaxQuantity(
                                item.remitodetalleid,
                                item.cantidadpendiente
                              )
                            }
                            disabled={
                              isProductInCart(
                                item.clienteid,
                                item.remitodetalleid
                              ) ||
                              (selectedQuantities[item.remitodetalleid] ||
                                0) === Number(item.cantidadpendiente)
                            }
                            style={{
                              marginLeft: "2px",
                              fontSize: "10px",
                              padding: "2px",
                              backgroundColor: "#f0f0f0",
                              border: "1px solid #ccc",
                              borderRadius: "2px",
                            }}
                          >
                            <span
                              style={{ fontSize: "10px", fontWeight: "bold" }}
                            >
                              LIQUIDAR
                            </span>
                          </IconButton>
                        </span>
                      </Tooltip>
                    </div>
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "80%", padding: "6px 2px" }}
                    align="center"
                  >
                    <Checkbox
                      checked={isProductInCart(
                        item.clienteid,
                        item.remitodetalleid
                      )}
                      onChange={() => handleCartToggle(item)}
                      disabled={!selectedQuantities[item.remitodetalleid]}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={12} align="center">
                  No hay datos para mostrar. Utilice los filtros para buscar
                  productos pendientes de liquidación.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {paginaUltima > 1 && (
        <Pagination
          count={paginaUltima}
          page={page}
          onChange={handleChangePage}
          color="primary"
          style={{ display: "flex", justifyContent: "center", margin: "20px" }}
        />
      )}

      <Dialog open={showDateModal} onClose={handleCloseDateModal}>
        <DialogTitle>Establecer fecha de liquidación</DialogTitle>
        <DialogContent>
          <TextField
            label="Fecha de liquidación"
            type="date"
            value={newLiquidacionDate}
            onChange={(e) => setNewLiquidacionDate(e.target.value)}
            InputLabelProps={{ shrink: true }}
            fullWidth
            margin="dense"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDateModal}>Cancelar</Button>
          <Button
            onClick={handleUpdateLiquidacionDate}
            variant="contained"
            color="primary"
          >
            Actualizar
          </Button>
        </DialogActions>
      </Dialog>

      {showCart && (
        <Paper
          style={{
            position: "fixed",
            right: "20px",
            top: "80px",
            padding: "20px",
            width: "400px",
            maxHeight: "80vh",
            overflow: "auto",
            zIndex: 1000,
            boxShadow: "0 4px 20px rgba(0,0,0,0.15)",
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "15px",
            }}
          >
            <h4 style={{ margin: 0 }}>Carro de Liquidación</h4>
            <IconButton
              size="small"
              onClick={() => setShowCart(false)}
              style={{
                color: "#666",
                position: "absolute",
                top: "10px",
                right: "10px",
              }}
            >
              <CloseIcon />
            </IconButton>
          </div>
          {Object.keys(cart).length === 0 ? (
            <div style={{ textAlign: "center", padding: "20px 0" }}>
              No hay productos seleccionados
            </div>
          ) : (
            <>
              <Typography
                variant="body2"
                style={{ marginBottom: "15px", color: "#666" }}
              >
                {Object.keys(cart).length} cliente(s) con productos
                seleccionados
              </Typography>

              {/* <div style={{ marginBottom: "15px" }}>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={handleOpenDateModal}
                  style={{ marginBottom: "10px" }}
                >
                  Cambiar Fecha de Liquidación
                </Button>

                <input
                  type="file"
                  id="file-upload"
                  multiple
                  style={{ display: "none" }}
                  onChange={handleFileUpload}
                />
                <label htmlFor="file-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    fullWidth
                    startIcon={<AttachFileIcon />}
                  >
                    Adjuntar Comprobantes
                  </Button>
                </label>

                {files.length > 0 && (
                  <div style={{ marginTop: "10px" }}>
                    <Typography variant="subtitle2">
                      Archivos adjuntos:
                    </Typography>
                    {files.map((file, index) => (
                      <div
                        key={index}
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          marginTop: "5px",
                        }}
                      >
                        <Typography
                          variant="body2"
                          noWrap
                          style={{ maxWidth: "250px" }}
                        >
                          {file.name}
                        </Typography>
                        <IconButton
                          size="small"
                          onClick={() => removeFile(index)}
                          style={{ color: "#f44336" }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </div>
                    ))}
                  </div>
                )}
              </div> */}

              {Object.entries(cart).map(([clientId, clientData]) => (
                <div key={clientId} style={{ marginBottom: "20px" }}>
                  {/* <Paper
                    elevation={2}
                    style={{
                      padding: "10px",
                      backgroundColor: "#e0e0e0",
                      marginBottom: "10px",
                    }}
                  >
                    <Typography
                      variant="subtitle1"
                      style={{ fontWeight: "bold" }}
                    >
                      Cliente: {clientData.clientName}
                    </Typography>
                    <Typography variant="body2" style={{ color: "#666" }}>
                      Fecha de liquidación:{" "}
                      {formatDate(clientData.fechaLiquidacion)}
                    </Typography>
                    <Typography variant="body2" style={{ color: "#666" }}>
                      Productos: {Object.keys(clientData.products).length}
                    </Typography>
                  </Paper> */}
                  <Paper
                    elevation={2}
                    style={{
                      padding: "10px",
                      backgroundColor: "#e0e0e0",
                      marginBottom: "10px",
                    }}
                  >
                    <Typography
                      variant="subtitle1"
                      style={{ fontWeight: "bold" }}
                    >
                      Cliente: {clientData.clientName}
                    </Typography>
                    <Typography variant="body2" style={{ color: "#666" }}>
                      Fecha de liquidación:{" "}
                      {formatDate(clientData.fechaLiquidacion)}
                    </Typography>
                    <Typography variant="body2" style={{ color: "#666" }}>
                      Productos: {Object.keys(clientData.products).length}
                    </Typography>

                    {/* ARCA number input */}
                    <TextField
                      label="Nro. Liquidación ARCA"
                      variant="outlined"
                      size="small"
                      fullWidth
                      margin="normal"
                      value={arcaNumbers[clientId] || ""}
                      onChange={(e) =>
                        setArcaNumbers((prev) => ({
                          ...prev,
                          [clientId]: e.target.value,
                        }))
                      }
                      InputLabelProps={{ shrink: true }}
                    />

                    {/* Add file upload for this client */}
                    <div style={{ marginTop: "10px" }}>
                      <input
                        type="file"
                        id={`file-upload-${clientId}`}
                        multiple
                        style={{ display: "none" }}
                        onChange={(e) => handleFileUpload(e, clientId)}
                      />
                      <label htmlFor={`file-upload-${clientId}`}>
                        <Button
                          variant="outlined"
                          component="span"
                          fullWidth
                          startIcon={<AttachFileIcon />}
                          size="small"
                        >
                          Adjuntar Comprobantes
                        </Button>
                      </label>

                      {filesByClient[clientId]?.length > 0 && (
                        <div style={{ marginTop: "10px" }}>
                          <Typography variant="subtitle2">
                            Archivos adjuntos:
                          </Typography>
                          {filesByClient[clientId].map((file, index) => (
                            <div
                              key={index}
                              style={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                marginTop: "5px",
                              }}
                            >
                              <Typography
                                variant="body2"
                                noWrap
                                style={{ maxWidth: "250px" }}
                              >
                                {file.name}
                              </Typography>
                              <IconButton
                                size="small"
                                onClick={() => removeFile(clientId, index)}
                                style={{ color: "#f44336" }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </Paper>

                  {Object.values(clientData.products).map((item) => (
                    <div
                      key={item.remitodetalleid}
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginBottom: "10px",
                        padding: "8px",
                        backgroundColor: "#f9f9f9",
                        borderRadius: "4px",
                        marginTop: "8px",
                      }}
                    >
                      <div style={{ flex: 1 }}>
                        <div
                          style={{ fontWeight: "bold", marginBottom: "5px" }}
                        >
                          {item.nombreproducto || "Producto sin nombre"}
                        </div>
                        <div style={{ fontSize: "0.9em", color: "#666" }}>
                          Remito: {item.numeroremito}
                        </div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginTop: "5px",
                          }}
                        >
                          <Typography
                            variant="body2"
                            style={{ marginRight: "10px" }}
                          >
                            Cantidad:
                          </Typography>
                          <div
                            style={{ display: "flex", alignItems: "center" }}
                          >
                            <IconButton
                              size="small"
                              onClick={() => {
                                if (item.selectedQuantity <= 1) {
                                  removeFromCart(
                                    clientId,
                                    item.remitodetalleid
                                  );
                                } else {
                                  const newValue = Math.max(
                                    1,
                                    item.selectedQuantity - 1
                                  );
                                  updateCartQuantity(
                                    clientId,
                                    item.remitodetalleid,
                                    newValue
                                  );
                                }
                              }}
                              style={{ padding: "2px" }}
                            >
                              <RemoveCircleOutline fontSize="small" />
                            </IconButton>
                            <TextField
                              type="number"
                              value={item.selectedQuantity}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value === "" || Number(value) <= 0) {
                                  removeFromCart(
                                    clientId,
                                    item.remitodetalleid
                                  );
                                } else {
                                  updateCartQuantity(
                                    clientId,
                                    item.remitodetalleid,
                                    value
                                  );
                                }
                              }}
                              InputProps={{
                                inputProps: {
                                  min: 1,
                                  max: item.cantidadpendiente,
                                  style: {
                                    textAlign: "center",
                                    fontSize: "80%",
                                    padding: "5px",
                                  },
                                },
                              }}
                              size="small"
                              style={{ width: "50px", margin: "0 5px" }}
                            />
                            <IconButton
                              size="small"
                              onClick={() => {
                                const newValue = Math.min(
                                  item.cantidadpendiente,
                                  item.selectedQuantity + 1
                                );
                                updateCartQuantity(
                                  clientId,
                                  item.remitodetalleid,
                                  newValue
                                );
                              }}
                              style={{ padding: "2px" }}
                            >
                              <AddCircleOutline fontSize="small" />
                            </IconButton>
                          </div>
                        </div>
                      </div>
                      <IconButton
                        size="small"
                        onClick={() =>
                          removeFromCart(clientId, item.remitodetalleid)
                        }
                        style={{ color: "#f44336" }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </div>
                  ))}
                </div>
              ))}

              <Button
                variant="contained"
                color="primary"
                fullWidth
                onClick={generateLiquidacion}
                disabled={generatingLiquidacion}
                style={{ marginTop: "20px" }}
              >
                {generatingLiquidacion ? (
                  <>
                    <CircularProgress
                      size={20}
                      color="inherit"
                      style={{ marginRight: "10px" }}
                    />
                    Generando...
                  </>
                ) : (
                  `Generar Liquidación`
                )}
              </Button>
            </>
          )}
        </Paper>
      )}
    </div>
  );
};
