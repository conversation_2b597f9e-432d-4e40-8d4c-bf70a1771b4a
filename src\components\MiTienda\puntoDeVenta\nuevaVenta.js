import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Delete, ShoppingCart } from "@mui/icons-material";
import {
  Autocomplete,
  Button,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
} from "@mui/material";
import {
  agregarArticulo,
  eliminarArticulo,
  finalizarPedido,
  generarTransaccionEnvioADomicilio,
  generarTransaccionEnvioAPunto,
  getCalcularCarrito,
  getCarritoDetalle,
  getCuotasTarjeta,
  getDatosTienda,
  getDescuentosSinPaginado,
  getListadoClientesByName,
  getMetodosEnvio,
  getProductosByNameSkuNuevaVenta,
  getTiposDePago,
  getTiposTarjeta,
  guardarDatosPedidoADomicilio,
  guardarDatosPedidoAPunto,
  modificarCantidadArticulo,
  modificarDescuentoArticulo,
  resetCarrito,
} from "../../../redux/actions/mitienda.actions";
import { ModalImagenProducto } from "../ventas/modalImagenProducto";
import { ModalAgregarCliente } from "../clientes/agregarCliente";
import MuiAlert from "@mui/material/Alert";
import { ModalFinalizarCompra } from "../ventas/modalFinalizarCompra";
import { ModalConfigADomicilio } from "../ventas/modalConfigADomicilio";
import { ModalConfigAPunto } from "../ventas/modalConfigAPunto";
import { ModalProductoEditable } from "../ventas/modalProductoEditable";
import { ModalCancelarFactura } from "./modalCancelarFactura";

export const NuevaVenta = ({ tipofactura, tipocomprobante, setOk }) => {
  // console.log('tipofactura', tipofactura);
  const productsByNameSku = useSelector(
    (state) => state.mitienda.productosNuevaVenta
  );
  const descuentos = useSelector(
    (state) => state.mitienda.descuentosSinPaginado
  );
  const clientes = useSelector((state) => state.mitienda.clientesByName);
  const okAgregarArticulo = useSelector(
    (state) => state.mitienda.okAgregarArticulo
  );
  const detalleCarrito = useSelector((state) => state.mitienda.detalleCarrito);
//   console.log('detalleCarrito',detalleCarrito)
  const calcularCarrito = useSelector(
    (state) => state.mitienda.calcularCarrito
  );
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaDetalleCarrito
  );
  const metodosEnvio = useSelector((state) => state.mitienda.metodos);
  const tiposDePago = useSelector((state) => state.mitienda.tiposDePago);
  const tiposDeTarjetas = useSelector(
    (state) => state.mitienda.tiposDeTarjetas
  );
  const cuotasTarjetas = useSelector((state) => state.mitienda.cuotasTarjetas);
  const okFinalizarPedido = useSelector(
    (state) => state.mitienda.okFinalizarPedido
  );
  const okAgregarCliente = useSelector(
    (state) => state.mitienda.okAgregarCliente
  );
  const okEliminarArticulo = useSelector(
    (state) => state.mitienda.okEliminarArticulo
  );
  const okModificarDescuentoArticulo = useSelector(
    (state) => state.mitienda.modificarDescuentoArticulo
  );
  const okModificarCantidadArticulo = useSelector(
    (state) => state.mitienda.modificarCantidadArticulo
  );
  const okModificarProductoEditable = useSelector(
    (state) => state.mitienda.modificarProductoEditable
  );
  const okCancelar = useSelector((state) => state.mitienda.okCancelarFactura);

  const cliente = useSelector((state) => state.mitienda.clienteById);
  const infoTienda = useSelector((state) => state.mitienda.datostienda);
  const cotizacion = useSelector(
    (state) => state.mitienda.cotizacionEnvioADomicilio
  );
  const transaccionEnvioADomicilio = useSelector(
    (state) => state.mitienda.transaccionEnvioADomicilio
  );
  const cotizacionAPunto = useSelector(
    (state) => state.mitienda.cotizacionEnvioAPunto
  );
  const transaccionEnvioAPunto = useSelector(
    (state) => state.mitienda.transaccionEnvioAPunto
  );

  const [clienteid, setClienteid] = useState("");
  const [product, setProduct] = useState("");
  const [productoEditable, setProductoEditable] = useState("");
  const [cantidad, setCantidad] = useState("1");
  const [descuento, setDescuento] = useState("");
  const [facturaid, setFactura] = useState("");
  const [pedidoid, setPedido] = useState("");
  const [tipoPago, setTipoPago] = useState("");
  const [tipoTarjeta, setTipoTarjeta] = useState("");
  const [tipoCuotas, setTipoCuotas] = useState("0");
  const [puntoId, setPuntoId] = useState("");
  const [fechaPedido, setFechaPedido] = useState(new Date().toISOString().split('T')[0]);

  const handleChangeAutocomplete = (e, v, p) => {
    e.preventDefault();
    // When X is clicked, v will be null, so we need to handle that case
    if (v === null) {
      // Pass a default object with descuento: 0 when v is null
      dispatch(modificarDescuentoArticulo(p.productoid, p.facturadetalleid, { descuento: 0 }));
    } else {
      dispatch(modificarDescuentoArticulo(p.productoid, p.facturadetalleid, v));
    }
  };

  const handleChangeCantidad = (v, p) => {
    // Prevent negative numbers
    const value = Math.max(1, parseInt(v) || 1);
    
    // Get the maximum available stock
    const maxStock = parseInt(p.cantidadactiva) || 999999;
    
    // Limit quantity to available stock
    const limitedValue = Math.min(value, maxStock);
    
    dispatch(modificarCantidadArticulo(p.facturadetalleid, limitedValue));
  };

  const [tipoEnvio, setTipoEnvio] = useState("");
  const handleTipoEnvio = (method) => {
    setTipoEnvio(method);
  };

  const [clienteInput, setClienteInput] = useState("");
  const handleChangeClienteInput = (e) => {
    e.preventDefault();
    setClienteInput(e.target.value);
  };

  const [input, setInput] = useState("");
  const handleChange = (e) => {
    e.preventDefault();
    setInput(e.target.value);
  };

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const [show, setShow] = useState("");
  const handleClose = () => setShow(false);
  const handleShow = (row) => {
    setProductoEditable(row);
    setShow(true);
  };

  const [showCancelar, setShowCancelar] = useState("");
  const handleCloseCancelar = () => setShowCancelar(false);
  const handleShowCancelar = () => {
    setShowCancelar(true);
  };

  const [showAgregar, setShowAgregar] = useState(false);
  const handleCloseAgregar = () => setShowAgregar(false);
  const handleShowAgregar = (e, row) => {
    e.preventDefault();
    setShowAgregar(true);
  };

  const descuentoRef = React.useRef(null);

  const [showFinalizarPedido, setShowFinalizarPedido] = useState(false);
  // Add this function to handle the complete reset
  const completeReset = () => {
    // Reset all form fields
    setClienteInput("");
    setClienteid("");
    setProduct("");
    setCantidad("1");
    setDescuento(""); // Reset the descuento state
    setFactura("");
    setTipoPago("");
    setTipoTarjeta("");
    setTipoCuotas("0");
    setTipoEnvio("");
    setInput("");
    setPage(1);
    
    // Clear search results
    dispatch(getProductosByNameSkuNuevaVenta(""));
    
    // Clear client search results - explicitly set to empty array
    dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
    
    // Reset the cart state
    dispatch(resetCarrito());
  };

  const handleCloseFinalizarPedido = () => {
    setShowFinalizarPedido(false);
    
    // Delay the reset to ensure the modal is fully closed
    // and any pending operations (like PDF generation) are completed
    setTimeout(completeReset, 1000);
  };
  const handleShowFinalizarPedido = () => {
    setShowFinalizarPedido(true);
  };

  const [productoAMostar, setProductoAMostrar] = useState("");
  const [showImagen, setShowImagen] = useState("");
  const handleCloseImagen = () => setShowImagen(false);
  const handleShowImagen = (producto) => {
    setProductoAMostrar(producto);
    setShowImagen(true);
  };

  const [showEnvioaDomicilio, setShowEnvioaDomicilio] = useState(false);
  const handleCloseEnvioaDomicilio = () => setShowEnvioaDomicilio(false);
  const handleShowEnvioaDomicilio = () => setShowEnvioaDomicilio(true);

  const [showEnvioAPunto, setShowEnvioAPunto] = useState(false);
  const handleCloseEnvioAPunto = () => setShowEnvioAPunto(false);
  const handleShowEnvioAPunto = () => setShowEnvioAPunto(true);

  const dispatch = useDispatch();

  //reset
  const resetForm = () => {
    setClienteInput("");
    setClienteid("");
    setProduct("");
    setCantidad("1");
    setDescuento(""); 
    setFactura("");
    setTipoPago("");
    setTipoTarjeta("");
    setTipoCuotas("");
    setTipoEnvio("");
    setPage(1);
    setFechaPedido(new Date().toISOString().split('T')[0]);
    setInput("");  
    
   
    dispatch(resetCarrito());
    dispatch(getProductosByNameSkuNuevaVenta(""));  
    dispatch(getListadoClientesByName("")); 
  }

  // Add a state for the alert message
  const [alertMessage, setAlertMessage] = useState({ open: false, message: "", severity: "error" });

  const handleClickAlert = (message, severity = "success") => {
  setAlertMessage({
    open: true,
    message,
    severity
  });
};

  const handleAddToCart = (cliente) => {
    // Check if a client is selected
    if (!cliente) {
      setAlertMessage({
        open: true,
        message: "Debe seleccionar un cliente antes de agregar productos",
        severity: "warning"
      });
      return;
    }
    
    // Check if a product is selected
    if (!product) {
      setAlertMessage({
        open: true,
        message: "Debe seleccionar un producto",
        severity: "warning"
      });
      return;
    }
    
    // Get the selected product's stock
    const selectedProduct = productsByNameSku?.find(p => p.codigoproducto === product);
    
    // If product not found or no stock info, show error
    if (!selectedProduct) {
      setAlertMessage({
        open: true,
        message: "Producto no encontrado",
        severity: "error"
      });
      return;
    }
    
    const availableStock = parseInt(selectedProduct.cantidadactiva) || 0;
    const requestedQuantity = parseInt(cantidad) || 0;
    
    // Check if requested quantity exceeds available stock
    if (requestedQuantity > availableStock) {
      setAlertMessage({
        open: true,
        message: `Stock insuficiente. Disponible: ${availableStock}`,
        severity: "error"
      });
      return;
    }
    
    dispatch(
      agregarArticulo(cliente, product, cantidad, descuento, tipofactura)
    );
    setProduct("");
    setCantidad("1");
    setInput("");  // Clear the search input
    
    // Clear product search results immediately
    dispatch(getProductosByNameSkuNuevaVenta(""));
  };

  // Add a function to close the alert
  const handleCloseAlert = () => {
    setAlertMessage({ ...alertMessage, open: false });
  };

  //clienteid, facturadetalleid, tipofactura, facturaid
  const handleDeleteProduct = (p) => {
    dispatch(
      eliminarArticulo(clienteid, p.facturadetalleid, tipofactura, facturaid)
    );
  };

  //clienteid, tipoenvio, tipopago, facturaid, tipofactura
  const handleFinalizarPedido = () => {
    dispatch(
      finalizarPedido(
        clienteid,
        tipoEnvio,
        tipoPago,
        facturaid,
        tipofactura,
        fechaPedido
      )
    );
    setOk({ ok: false, tipo: "" });
    handleShowFinalizarPedido();
  };

  const matchDescuento = (porcentaje) => {
    let aux = descuentos.filter((d) => d.descuento === porcentaje);
    return aux[0];
  };

  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });

  const [openCancelar, setOpenCancelar] = React.useState(false);
  const handleClickAlertCancelar = () => {
    setOpenCancelar(true);
  };
  const handleCloseAlertCancelar = () => {
    setOpenCancelar(false);
  };

  useEffect(() => {
    if (okCancelar.success) {
      window.location.reload();
    }
  }, [okCancelar]);

  useEffect(() => {
    if (okAgregarCliente !== "") {
      dispatch(getListadoClientesByName("", okAgregarCliente.clienteid));
      setClienteid(okAgregarCliente.clienteid);
    }
  }, [okAgregarCliente]);

  useEffect(() => {
    if (clienteInput.length > 1) {
      dispatch(getListadoClientesByName(clienteInput, ""));
    } else if (clienteInput === "") {
      // Clear client search results when input is empty
      dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
    }
  }, [clienteInput]);

  //w
  // useEffect(() => {
  //   if (input.length > 4) {
  //     dispatch(getProductosByNameSkuNuevaVenta(input));
  //   }
  // }, [input]);

  //f
  useEffect(() => {
    if (input.length > 4) {
      dispatch(getProductosByNameSkuNuevaVenta(input, tipofactura));
    }
  }, [input]);

  useEffect(() => {
    dispatch(getProductosByNameSkuNuevaVenta(""));
    dispatch(getDescuentosSinPaginado());
    dispatch(getMetodosEnvio());
    dispatch(getTiposDePago());
    dispatch(getTiposTarjeta());
    dispatch(getCuotasTarjeta());
    dispatch(getDatosTienda());
  }, []);

  useEffect(() => {
    if (okAgregarArticulo.success && clienteid !== "") {
      setOk({ ok: true, tipo: tipofactura });
      setFactura(okAgregarArticulo.facturaid);
      dispatch(
        getCarritoDetalle(
          clienteid,
          okAgregarArticulo.facturaid,
          1,
          tipofactura
        )
      );
      dispatch(
        getCalcularCarrito(
          clienteid,
          descuento,
          tipoCuotas.interes,
          tipofactura,
          okAgregarArticulo.facturaid
        )
      );
    }
  }, [okAgregarArticulo]);

  useEffect(() => {
    if (clienteid !== "") {
      dispatch(getCarritoDetalle(clienteid, facturaid, page, tipofactura));
    }
  }, [page, okModificarProductoEditable]);

  useEffect(() => {
    if (clienteid !== "" && okModificarDescuentoArticulo !== "") {
      dispatch(getCarritoDetalle(clienteid, facturaid, page, tipofactura));
      dispatch(
        getCalcularCarrito(
          clienteid,
          descuento,
          tipoCuotas.interes,
          tipofactura,
          okAgregarArticulo.facturaid
        )
      );
    }
  }, [okModificarDescuentoArticulo]);

  useEffect(() => {
    if (clienteid !== "" && okModificarCantidadArticulo !== "") {
      dispatch(getCarritoDetalle(clienteid, facturaid, page, tipofactura));
      dispatch(
        getCalcularCarrito(
          clienteid,
          descuento,
          tipoCuotas.interes,
          tipofactura,
          okAgregarArticulo.facturaid
        )
      );
    }
  }, [okModificarCantidadArticulo]);

  useEffect(() => {
    if (okEliminarArticulo !== "") {
      dispatch(getCarritoDetalle(clienteid, facturaid, page, tipofactura));
      dispatch(
        getCalcularCarrito(
          clienteid,
          descuento,
          tipoCuotas.interes,
          tipofactura,
          okAgregarArticulo.facturaid
        )
      );
    }
  }, [okEliminarArticulo]);

  useEffect(() => {
    if (okFinalizarPedido !== "") {
      setPedido(okFinalizarPedido.pedidoid);
      if (tipoEnvio === "3") {
        if (puntoId !== "") {
          dispatch(
            generarTransaccionEnvioAPunto(
              cotizacionAPunto,
              okFinalizarPedido.pedidoid
            )
          );
        } else {
          dispatch(
            generarTransaccionEnvioADomicilio(
              cotizacion,
              okFinalizarPedido.pedidoid
            )
          );
        }
      }
    }
  }, [okFinalizarPedido]);

  useEffect(() => {
    if (transaccionEnvioADomicilio !== "" && tipoEnvio === "3") {
      dispatch(
        guardarDatosPedidoADomicilio(
          cliente,
          cotizacion,
          clienteid,
          pedidoid,
          transaccionEnvioADomicilio
        )
      );
    }
  }, [transaccionEnvioADomicilio]);

  useEffect(() => {
    if (transaccionEnvioAPunto !== "" && tipoEnvio === "3") {
      dispatch(
        guardarDatosPedidoAPunto(
          cliente,
          cotizacionAPunto,
          clienteid,
          pedidoid,
          transaccionEnvioAPunto,
          puntoId
        )
      );
    }
  }, [transaccionEnvioAPunto]);

  // Add this useEffect to reset everything when the component unmounts
  useEffect(() => {
    return () => {
      // This will run when the component unmounts
      dispatch(resetCarrito());
    };
  }, []);

  return (
    <div className="ventas-container">
      <ModalImagenProducto
        show={showImagen}
        handleClose={handleCloseImagen}
        producto={productoAMostar}
      />
      <ModalAgregarCliente
        show={showAgregar}
        handleClose={handleCloseAgregar}
        handleClickAlert={() => handleClickAlert("Cliente agregado correctamente")}
      />
      <ModalFinalizarCompra
        show={showFinalizarPedido}
        handleClose={handleCloseFinalizarPedido}
        okFinalizarPedido={okFinalizarPedido}
        tipocomprobante={tipocomprobante}
        nombretipocomprobante={tipofactura}
      />
      <ModalConfigADomicilio
        show={showEnvioaDomicilio}
        handleClose={handleCloseEnvioaDomicilio}
        clienteid={clienteid}
        cliente={cliente}
        cotizacion={cotizacion}
        infoTienda={infoTienda}
        productos={detalleCarrito}
        facturaid={facturaid}
        getCalcularCarrito={getCalcularCarrito}
      />
      <ModalConfigAPunto
        show={showEnvioAPunto}
        handleClose={handleCloseEnvioAPunto}
        clienteid={clienteid}
        cliente={cliente}
        cotizacion={cotizacionAPunto}
        infoTienda={infoTienda}
        productos={detalleCarrito}
        facturaid={facturaid}
        getCalcularCarrito={getCalcularCarrito}
        puntoId={puntoId}
        setPuntoId={setPuntoId}
      />
      <ModalProductoEditable
        show={show}
        handleClose={handleClose}
        producto={productoEditable}
      />
      <ModalCancelarFactura
        show={showCancelar}
        handleClose={handleCloseCancelar}
        info={facturaid}
        tipofactura={tipofactura}
        handleOnClickAlert={handleClickAlertCancelar}
      />
      <Snackbar
        open={openCancelar}
        autoHideDuration={10000}
        onClose={handleCloseAlertCancelar}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseAlertCancelar}
          severity={okCancelar.success === true ? "success" : "error"}
          sx={{ width: 400 }}
        >
          <h5>{okCancelar.mensaje}</h5>
        </Alert>
      </Snackbar>
      <Snackbar
        open={alertMessage.open}
        autoHideDuration={5000}
        onClose={handleCloseAlert}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseAlert}
          severity={alertMessage.severity}
          sx={{ width: 400 }}
        >
          <h5>{alertMessage.message}</h5>
        </Alert>
      </Snackbar>
      <div style={{ display: "flex" }}>
        <Paper
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            padding: 20,
            width: "30%",
            marginRight: 10,
          }}
        >
          <div style={{ width: "100%" }}>
            <h4>
              <strong>Pedido</strong>
            </h4>
            <div style={{ display: "flex", width: "100%" }}>
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel id="cliente-select-label">Cliente</InputLabel>
                <Select
                  labelId="cliente-select-label"
                  id="cliente"
                  label="Cliente"
                  size="small"
                  name="clienteid"
                  value={clienteid || ""}
                  onChange={(e) => setClienteid(e.target.value)}
                >
                  <TextField
                    value={clienteInput}
                    name="clienteInput"
                    onChange={handleChangeClienteInput}
                    fullWidth
                    style={{ padding: 10 }}
                  />
                  {clientes &&
                    clientes.map((c) => (
                      <MenuItem value={c.clienteid} key={c.clienteid}>
                        {c.nombre.length + c.apellido.length > 30
                          ? c.cuit +
                            "-" +
                            c.nombre.slice(0, 10) +
                            ".. " +
                            c.apellido.slice(0, 10) +
                            ".."
                          : c.cuit + "-" + c.nombre + " " + c.apellido}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
              <Button
                variant="contained"
                onClick={handleShowAgregar}
                sx={{ mt: 2, ml: 1 }}
              >
                +
              </Button>
            </div>
            <Autocomplete
              options={descuentos || []}
              getOptionLabel={(option) => option.nombre || ''}
              isOptionEqualToValue={(option, value) =>
                option.descuentovtaid === value?.descuentovtaid
              }
              onChange={(event, value) => setDescuento(value ? value.descuentovtaid : "")}
              value={descuentos?.find(d => d.descuentovtaid === descuento) || null}
              renderInput={(params) => (
                <TextField
                  sx={{ mt: 3 }}
                  {...params}
                  variant="outlined"
                  label="Descuento"
                  InputLabelProps={{
                    style: {
                      marginBottom: 10,
                      marginTop: -7,
                    },
                  }}
                />
              )}
            />
            <div style={{ display: "flex", width: "100%" }}>
              <FormControl fullWidth sx={{ mt: 3 }}>
                <InputLabel id="metododepago-select-label">
                  M&eacute;todo de pago
                </InputLabel>
                <Select
                  labelId="metododepago-select-label"
                  id="metododepago"
                  label="M&eacute;todo de pago"
                  size="small"
                  name="tipoPago"
                  value={tipoPago || ""}
                  onChange={(e) => setTipoPago(e.target.value.toString())}
                >
                  {tiposDePago &&
                    tiposDePago.map((c, index) => (
                      <MenuItem value={index + 1} key={index + 1}>
                        {c.nombre}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </div>
            <FormControl fullWidth sx={{ mt: 3 }}>
              <InputLabel
                id="fechapedido-label"
                shrink={true}
                sx={{
                  backgroundColor: "white",
                  padding: "0 8px",
                  transform: "translate(14px, -9px) scale(0.75)",
                }}
              >
                Fecha de Pedido
              </InputLabel>
              <TextField
                type="date"
                size="small"
                name="fechaPedido"
                value={fechaPedido || ""}
                onChange={(e) => setFechaPedido(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                sx={{
                  "& .MuiInputBase-root": {
                    height: "40px", // Increased height for better content display
                  },
                }}
              />
              <p style={{ color: "gray", fontSize: "12px", marginTop: "4px" }}>
                (fecha solicitada por cliente)
              </p>
            </FormControl>
            {tipoPago === "4" && (
              <Autocomplete
                options={tiposDeTarjetas}
                getOptionLabel={(option) => option.nombre}
                isOptionEqualToValue={(option, value) =>
                  option.nombre === value.nombre
                }
                onChange={(event, value) => setTipoTarjeta(value)}
                renderInput={(params) => (
                  <TextField
                    sx={{ mt: 3 }}
                    {...params}
                    variant="outlined"
                    label="Tipo de tarjeta"
                    InputLabelProps={{
                      style: {
                        marginBottom: 10,
                        marginTop: -7,
                      },
                    }}
                  />
                )}
              />
            )}
            {tipoTarjeta.nombre === "CREDITO" && (
              <Autocomplete
                options={cuotasTarjetas}
                onCh
                getOptionLabel={(option) =>
                  option.cuotas + " (" + option.interes + "%)"
                }
                onChange={(event, value) => setTipoCuotas(value)}
                renderInput={(params) => (
                  <TextField
                    sx={{ mt: 3 }}
                    {...params}
                    variant="outlined"
                    label="Cuotas"
                    InputLabelProps={{
                      style: {
                        marginBottom: 10,
                        marginTop: -7,
                      },
                    }}
                  />
                )}
              />
            )}
          </div>
          <div style={{ marginTop: 20 }}>
            {metodosEnvio.default_pickit === "1" && (
              <div
                style={{
                  display: "flex",
                  width: 400,
                  margin: 5,
                  alignItems: "center",
                  padding: 3,
                }}
              >
                <h6>Pickit</h6>
                <FormControlLabel
                  control={<Checkbox />}
                  style={{ color: "black", marginLeft: 1 }}
                  onChange={(e) => handleTipoEnvio("3")}
                  checked={tipoEnvio === "3"}
                  value={"3"}
                  name="tipoEnvio"
                />
                {tipoEnvio === "3" && (
                  <div style={{ display: "flex", width: "100%" }}>
                    <Button
                      style={{ marginTop: 10, width: "60%", margin: 5 }}
                      disabled={detalleCarrito.length === 0}
                      onClick={handleShowEnvioaDomicilio}
                      variant="outlined"
                    >
                      A domicilio
                    </Button>
                    <Button
                      style={{ marginTop: 10, width: "60%", margin: 5 }}
                      disabled={detalleCarrito.length === 0}
                      onClick={handleShowEnvioAPunto}
                      variant="outlined"
                    >
                      A punto
                    </Button>
                  </div>
                )}
              </div>
            )}
            {metodosEnvio.default_enviocliente === "1" && (
              <div
                style={{
                  display: "flex",
                  width: 400,
                  margin: 5,
                  alignItems: "center",
                }}
              >
                <h6>Envio a domicilio</h6>
                <FormControlLabel
                  control={<Checkbox />}
                  style={{ color: "black", marginLeft: 1 }}
                  onChange={(e) => handleTipoEnvio("2")}
                  checked={tipoEnvio === "2"}
                  value={2}
                  name="tipoEnvio"
                />
              </div>
            )}
            {metodosEnvio.default_retirolocal === "1" && (
              <div
                style={{
                  display: "flex",
                  width: 400,
                  margin: 5,
                  alignItems: "center",
                }}
              >
                <h6>Retiro por el local</h6>
                <FormControlLabel
                  control={<Checkbox />}
                  style={{ color: "black", marginLeft: 1 }}
                  onChange={(e) => handleTipoEnvio("1")}
                  checked={tipoEnvio === "1"}
                  value={1}
                  name="tipoEnvio"
                />
              </div>
            )}
          </div>
          <div>
            <Divider style={{ margin: 20 }} />
            <h5 style={{ display: "flex", justifyContent: "space-between" }}>
              <strong>Sub total</strong>
              {calcularCarrito.simbolo}
              {calcularCarrito.subtotal}
            </h5>
            <h5 style={{ display: "flex", justifyContent: "space-between" }}>
              <strong>Interes %</strong>
              {calcularCarrito.intereses}
            </h5>
            <h5 style={{ display: "flex", justifyContent: "space-between" }}>
              <strong>Total a pagar</strong>
              {calcularCarrito.simbolo}
              {calcularCarrito.total}
            </h5>
            <Divider style={{ margin: 20 }} />
          </div>
          <div
            style={{
              display: "flex",
              width: "100%",
              justifyContent: "space-around",
              alignItems: "center",
              padding: 10,
            }}
          >
            <Button
              variant="contained"
              color="error"
              style={{ fontSize: 16 }}
              onClick={handleShowCancelar}
            >
              Cancelar
            </Button>
            <Button
              variant="contained"
              style={{ fontSize: 16 }}
              onClick={handleFinalizarPedido}
            >
              Finalizar
            </Button>
          </div>
        </Paper>
        <Paper
          style={{
            padding: 20,
            width: "70%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <div
            style={{
              display: "flex",
              width: "100%",
              justifyContent: "space-between",
              alignItems: "center",
              height: 70,
            }}
          >
            <div style={{ width: "80%", marginRight: 20 }}>
              <h6>
                <strong>Producto</strong>
              </h6>
              <Select
                size="small"
                fullWidth
                value={product || ""}
                name="product"
                style={{ height: 35 }}
                onChange={(e) => setProduct(e.target.value)}
              >
                <TextField
                  value={input}
                  name="input"
                  onChange={handleChange}
                  fullWidth
                  style={{ padding: 10 }}
                />
                {productsByNameSku &&
                  productsByNameSku.map((t) => (
                    <MenuItem value={t.codigoproducto} key={t.codigoproducto}>
                      {t.codigoproducto} -{" "}
                      {t.nombreproducto.length > 70
                        ? t.nombreproducto.slice(0, 65) + ".."
                        : t.nombreproducto}
                    </MenuItem>
                  ))}
              </Select>
            </div>
            <div style={{ width: "10%", marginRight: 20 }}>
              <h6>
                <strong>Cantidad</strong>
              </h6>
              <TextField
                style={{ width: "100%" }}
                type="number"
                name="cantidad"
                value={cantidad}
                onChange={(e) => {
                  // Prevent negative numbers
                  const value = Math.max(1, parseInt(e.target.value) || 1);
                  
                  // Get the selected product's stock
                  const selectedProduct = productsByNameSku?.find(p => p.codigoproducto === product);
                  const maxStock = selectedProduct?.cantidadactiva ? parseInt(selectedProduct.cantidadactiva) : 999999;
                  
                  // Limit quantity to available stock
                  const limitedValue = Math.min(value, maxStock);
                  
                  setCantidad(limitedValue.toString());
                }}
                InputProps={{
                  inputProps: { 
                    min: 1,
                    // Set max to the available stock if a product is selected
                    max: productsByNameSku?.find(p => p.codigoproducto === product)?.cantidadactiva || 999999
                  }
                }}
              />
            </div>
            <Tooltip title="Agregar al carrito">
              <Button
                style={{ marginTop: 25 }}
                variant="contained"
                onClick={(e) => handleAddToCart(clienteid)}
              >
                <ShoppingCart />
              </Button>
            </Tooltip>
          </div>
          <TableContainer component={Paper} style={{ marginTop: 20 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell style={{ fontWeight: "bold", fontSize: "100%" }}>
                    #
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "100%" }}
                    align="center"
                  >
                    Item
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "100%" }}
                    align="center"
                  >
                    Cantidad
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "100%" }}
                    align="center"
                  >
                    Precio unitario
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "100%" }}
                    align="center"
                  >
                    Descuento
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "100%" }}
                    align="center"
                  >
                    Total
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "100%" }}
                    align="center"
                  ></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {detalleCarrito &&
                  detalleCarrito.map((row) => (
                    <TableRow
                      // key={row.facturaid}
                      sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                    >
                      {row.editadetalle === "1" ? (
                        <TableCell
                          style={{
                            fontSize: "14px",
                            width: "80px",
                            height: "40px",
                            padding: 0,
                            cursor: "pointer",
                          }}
                          align="center"
                          onClick={(e) => handleShow(row)}
                        >
                          {row.codigobarras}
                        </TableCell>
                      ) : (
                        <TableCell
                          style={{
                            fontSize: "14px",
                            width: "80px",
                            height: "40px",
                            padding: 0,
                          }}
                          align="center"
                        >
                          {row.codigobarras}
                        </TableCell>
                      )}
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "160px",
                          height: "40px",
                          padding: 0,
                          cursor: "pointer",
                        }}
                        align="center"
                        onClick={(e) => handleShowImagen(row)}
                      >
                        {row.nombre}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "70px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        <TextField
                          style={{ width: "45%" }}
                          type="number"
                          value={row.cantidad}
                          onChange={(e) => {
                            // Get the value from the input
                            const value = e.target.value;
                            
                            // Validate and limit the value
                            const parsedValue = parseInt(value) || 1;
                            const maxStock = parseInt(row.cantidadactiva) || 999999;
                            const limitedValue = Math.min(Math.max(1, parsedValue), maxStock);
                            
                            // Only dispatch if the value is different and within limits
                            if (limitedValue.toString() !== row.cantidad) {
                              // Update the UI immediately to prevent user from seeing invalid values
                              e.target.value = limitedValue.toString();
                              handleChangeCantidad(limitedValue.toString(), row);
                            }
                          }}
                          onBlur={(e) => {
                            // Additional validation on blur to ensure the value is within limits
                            const parsedValue = parseInt(e.target.value) || 1;
                            const maxStock = parseInt(row.cantidadactiva) || 999999;
                            const limitedValue = Math.min(Math.max(1, parsedValue), maxStock);
                            
                            // Force update the UI and dispatch if needed
                            e.target.value = limitedValue.toString();
                            if (limitedValue.toString() !== row.cantidad) {
                              handleChangeCantidad(limitedValue.toString(), row);
                            }
                          }}
                          InputProps={{
                            inputProps: { 
                              min: 1,
                              max: row.cantidadactiva || 999999,
                              style: { textAlign: 'center' }
                            }
                          }}
                        />
                      </TableCell>
                     
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "70px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        ${row.costounitario}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "140px",
                          height: "40px",
                          padding: 2,
                        }}
                        align="center"
                      >
                        <Autocomplete
                          onChange={(event, value) =>
                            handleChangeAutocomplete(event, value, row)
                          }
                          options={descuentos}
                          defaultValue={matchDescuento(row.descuento)}
                          getOptionLabel={(option) => option.nombre}
                          isOptionEqualToValue={(option, value) =>
                            option.descuento === value.descuento
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              variant="outlined"
                              InputLabelProps={{
                                style: {
                                  marginBottom: 10,
                                  marginTop: -7,
                                },
                              }}
                            />
                          )}
                        />
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "60px",
                          height: "40px",
                          padding: 0,
                        }}
                        align="center"
                      >
                        ${row.montototal}
                      </TableCell>
                      <TableCell
                        style={{
                          fontSize: "14px",
                          width: "60px",
                          height: "40px",
                          padding: 0,
                          cursor: "pointer",
                        }}
                        align="center"
                        onClick={(e) => handleDeleteProduct(row)}
                      >
                        <Delete />
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Pagination
            count={paginaUltima}
            page={page}
            onChange={handleChangePage}
            size="large"
            sx={{ alignSelf: "center", margin: 3 }}
          />
        </Paper>
      </div>
    </div>
  );
};
