import React, { useState } from "react";
import Modal from "react-modal";

import Divider from "@mui/material/Divider";
import {
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import {
  getCondicionesIVA,
  getCondicionesPago,
  getDatosHome,
  getTiposDocumentoTienda,
} from "../../../redux/actions/mitienda.actions";
import { useEffect } from "react";
import { agregarCliente } from "../../../redux/actions/mitienda.actions";
import { Snackbar, Alert } from "@mui/material";

Modal.setAppElement("#root");

export const ModalAgregarCliente = ({
  show,
  handleClose,
  handleClickAlert,
}) => {
  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      height: "60vh",
      borderRadius: "8px",
      maxWidth: "650px",
      right: "50px",
      //   backgroundColor: "#D1D1D1"
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  const tiposDocumentos = useSelector(
    (state) => state.mitienda.tiposDocumentos
  );
  const condicionesIva = useSelector((state) => state.mitienda.condicionesIva);
  const condicionesPago = useSelector(
    (state) => state.mitienda.condicionesPago
  );

  const okAgregarCliente = useSelector((state) => state.mitienda.okAgregarCliente);

  useEffect(() => {
  if (okAgregarCliente.mensaje) {
    setAlert({
      open: true,
      message: okAgregarCliente.mensaje,
      severity: okAgregarCliente.success ? "success" : "error",
    });
  }
}, [okAgregarCliente]);

  const [input, setInput] = useState({
    nombre: "",
    apellido: "",
    tipodocidentidad: "",
    email: "",
    cuit: "",
    celular: "",
    telefono: "",
    tipocondicioniva: "",
    tipocondicionpago: "",
  });

  const [alert, setAlert] = useState({
  open: false,
  message: "",
  severity: "success",
});


  const handleChange = (e) => {
    e.preventDefault();
    setInput({
      ...input,
      [e.target.name]: e.target.value,
    });
  };

  const handleCheck = (e) => {
    e.preventDefault();
    if (e.target.checked === true) {
      setInput({ ...input, [e.target.name]: "1" });
    } else {
      setInput({ ...input, [e.target.name]: "0" });
    }
  };

  const dispatch = useDispatch();

  // const handleOnClick = () => {
  //   // Get vendedorid from localStorage
  //   const vendedorid = localStorage.getItem("idusuario");
  //   // Add vendedorid to input object
  //   const inputWithVendedor = { ...input, vendedorid };
  //   dispatch(agregarCliente(inputWithVendedor));
  //   setTimeout(function () {
  //     handleClickAlert();
  //   }, 1000);
  //   handleClose();
  //   setInput({
  //     nombre: "",
  //     apellido: "",
  //     tipodocidentidad: "",
  //     email: "",
  //     cuit: "",
  //     celular: "",
  //     telefono: "",
  //     tipocondicioniva: "",
  //     tipocondicionpago: "",
  //     esEmpresa: "0",
  //   });
  // };

  //w original
// const handleOnClick = () => {
//   // Get vendedorid from localStorage
//   const vendedorid = localStorage.getItem("idusuario");
//   // Add vendedorid to input object
//   const inputWithVendedor = { ...input, vendedorid };
  
//   // Dispatch the action and handle the promise
//   dispatch(agregarCliente(inputWithVendedor))
//     .then((response) => {
//       // Only proceed if the action was successful
//       if (response?.success !== false) {
//         // Show success alert
//         handleClickAlert();
        
//         // Close the modal
//         handleClose();
        
//         // Reset form fields
//         setInput({
//           nombre: "",
//           apellido: "",
//           tipodocidentidad: "",
//           email: "",
//           cuit: "",
//           celular: "",
//           telefono: "",
//           tipocondicioniva: "",
//           tipocondicionpago: "",
//           esEmpresa: "0",
//         });
//       }
//     })
//     .catch((error) => {
//       // errors will be handled by the Redux action/reducer
//       // the alert will show automatically when okAgregarCliente is updated
//     });
// };

//f
const handleOnClick = () => {
  // Get vendedorid from localStorage
  const vendedorid = localStorage.getItem("idusuario");
  // Add vendedorid to input object
  const inputWithVendedor = { ...input, vendedorid };
  
  // Determine if it's a company or individual (based on esEmpresa checkbox)
  const isClient = input.esEmpresa === "1" ? false : true;
  
  // Dispatch the action with isClient parameter
  dispatch(agregarCliente(inputWithVendedor, isClient))
    .then((response) => {
      // Only proceed if the action was successful
      if (response?.success !== false) {
        // Show success alert
        handleClickAlert();
        
        // Close the modal
        handleClose();
        
        // Reset form fields
        setInput({
          nombre: "",
          apellido: "",
          tipodocidentidad: "",
          email: "",
          cuit: "",
          celular: "",
          telefono: "",
          tipocondicioniva: "",
          tipocondicionpago: "",
          esEmpresa: "0",
        });
      }
    })
    .catch((error) => {
      // errors will be handled by the Redux action/reducer
      console.error("Error adding client:", error);
    });
};

  useEffect(() => {
    if (show) {
      dispatch(getTiposDocumentoTienda());
      dispatch(getCondicionesIVA());
      dispatch(getCondicionesPago());
    }
  }, [show]);

  return show ? (
    <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          marginBottom: 10,
        }}
      >
        <h4>Agregar cliente</h4>
        <button
          onClick={handleClose}
          style={{
            all: "unset",
            color: "black",
            paddingBottom: 10,
            cursor: "pointer",
          }}
        >
          X
        </button>
      </div>
      <Divider />
      <div style={{ display: "flex", flexDirection: "column", marginTop: 10 }}>
        <FormControlLabel
          control={<Checkbox />}
          style={{ color: "black", marginLeft: 1 }}
          onChange={(e) => handleCheck(e)}
          checked={input.esEmpresa === "1" ? true : false}
          name="esEmpresa"
          label="Es empresa?"
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          label="Nombre"
          value={input.nombre}
          name="nombre"
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
          onChange={(e) => handleChange(e)}
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          label="Apellido"
          value={input.apellido}
          name="apellido"
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
          onChange={(e) => handleChange(e)}
        />
        <FormControl fullWidth sx={{ mb: 2, mt: 2 }}>
          <InputLabel id="tipodocumento-select-label">
            Tipo documento
          </InputLabel>
          <Select
            labelId="tipodocumento-select-label"
            label="Tipo documento"
            size="small"
            name="tipodocidentidad"
            value={input.tipodocidentidad || ""}
            onChange={(e) => handleChange(e)}
            color="secondary"
            MenuProps={{ keepMounted: true, disablePortal: true }}
          >
            <MenuItem value="">Seleccione una opcion</MenuItem>
            {tiposDocumentos &&
              tiposDocumentos.map((t) => (
                <MenuItem
                  value={t.tipodocidentidadid}
                  key={t.tipodocidentidadid}
                >
                  {t.nombre}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          label="N&uacute;mero documento"
          value={input.cuit || ""}
          name="cuit"
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
          onChange={(e) => handleChange(e)}
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          label="Email"
          value={input.email || ""}
          name="email"
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
          onChange={(e) => handleChange(e)}
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          label="Celular"
          value={input.celular || ""}
          name="celular"
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
          onChange={(e) => handleChange(e)}
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          label="Telefono"
          value={input.telefono || ""}
          name="telefono"
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
          onChange={(e) => handleChange(e)}
        />
        <FormControl fullWidth sx={{ mb: 2, mt: 2 }}>
          <InputLabel id="condicioniva-select-label">Condicion IVA</InputLabel>
          <Select
            labelId="condicioniva-select-label"
            label="Condicion IVA"
            size="small"
            name="tipocondicioniva"
            value={input.tipocondicioniva || ""}
            onChange={(e) => handleChange(e)}
            color="secondary"
            MenuProps={{ keepMounted: true, disablePortal: true }}
          >
            <MenuItem value="">Seleccione una opcion</MenuItem>
            {condicionesIva &&
              condicionesIva.map((t) => (
                <MenuItem
                  value={t.tipocondicionivaid}
                  key={t.tipocondicionivaid}
                >
                  {t.nombre}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
        <FormControl fullWidth sx={{ mb: 2, mt: 2 }}>
          <InputLabel id="condicionpago-select-label">
            Condicion Pago
          </InputLabel>
          <Select
            labelId="condicionpago-select-label"
            label="Condicion Pago"
            size="small"
            name="tipocondicionpago"
            value={input.tipocondicionpago || ""}
            onChange={(e) => handleChange(e)}
            color="secondary"
            MenuProps={{ keepMounted: true, disablePortal: true }}
          >
            <MenuItem value="">Seleccione una opcion</MenuItem>
            {condicionesPago &&
              condicionesPago.map((t) => (
                <MenuItem
                  value={t.tipocondicionpagoid}
                  key={t.tipocondicionpagoid}
                >
                  {t.nombre}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
      </div>
      <div align="center">
        <Button
          disabled={
            input.tipodocidentidad == "" ||
            input.tipocondicionpago == "" ||
            input.tipocondicioniva == "" ||
            input.nombre == "" ||
            input.email == ""
          }
          size="large"
          variant="contained"
          sx={{ mt: 3 }}
          fullWidth
          onClick={handleOnClick}
        >
          Agregar
        </Button>
      </div>

      <Snackbar
  open={alert.open}
  autoHideDuration={6000}
  onClose={() => setAlert({...alert, open: false})}
  anchorOrigin={{ vertical: "top", horizontal: "center" }}
>
  <Alert 
    onClose={() => setAlert({...alert, open: false})} 
    severity={alert.severity}
    sx={{ width: '100%' }}
  >
    {alert.message}
  </Alert>
</Snackbar>
    </Modal>
  ) : null;
};
