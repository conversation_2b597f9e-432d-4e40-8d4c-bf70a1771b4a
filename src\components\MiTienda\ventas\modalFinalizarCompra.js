import React, { useEffect, useState } from "react";
import Modal from "react-modal";

import Divider from "@mui/material/Divider";
import { Button } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { facturarPuntoDeVenta } from "../../../redux/actions/mitienda.actions";
import Alert from "@mui/material/Alert";
import { CheckCircleOutline, Clear } from "@mui/icons-material";

Modal.setAppElement("#root");

export const ModalFinalizarCompra = ({
  show,
  handleClose,
  okFinalizarPedido,
  tipocomprobante,
  nombretipocomprobante,
}) => {
  const okFacturarPuntoVenta = useSelector(
    (state) => state.mitienda.okFacturarPuntoVenta
  );
  const currentFacturaId = useSelector((state) => state.mitienda.currentFacturaId);

  // console.log('currentFacturaId', currentFacturaId);

  const dispatch = useDispatch();

  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      maxHeight: "90vh",
      borderRadius: "8px",
      maxWidth: "750px",
      //   backgroundColor: "#D1D1D1"
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  const [url, setUrl] = useState("");

  //pedidoplacaid,facturaid
  const handleFacturar = () => {
    if (nombretipocomprobante === "factura_pedido") {
      dispatch(facturarPuntoDeVenta(okFinalizarPedido.pedidoid, "0"));
    } else {
      dispatch(facturarPuntoDeVenta("0", okFinalizarPedido.facturaid));
    }
  };

  useEffect(() => {
    if (okFacturarPuntoVenta.hasOwnProperty("url_pdf")) {
      let aux = okFacturarPuntoVenta.url_pdf.split("dompdf")[1];
      setUrl(process.env.REACT_APP_SERVER + "dompdf" + aux);
    }
  }, [okFacturarPuntoVenta]);

  return (
    show && (
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <button
            onClick={handleClose}
            style={{
              all: "unset",
              color: "black",
              paddingBottom: 10,
              cursor: "pointer",
            }}
          >
            X
          </button>
          {okFinalizarPedido !== "" && okFacturarPuntoVenta === "" ? (
            <Alert
              icon={
                okFinalizarPedido.success ? (
                  <CheckCircleOutline fontSize="inherit" />
                ) : (
                  <Clear />
                )
              }
              severity={okFinalizarPedido.success ? "success" : "error"}
              style={{ width: "95%" }}
            >
              {okFinalizarPedido.success ? (
                <h6>
                  {nombretipocomprobante === "factura_normal"
                    ? "Venta"
                    : nombretipocomprobante === "factura_notacredito"
                    ? "Nota de credito"
                    : nombretipocomprobante === "factura_notadebito"
                    ? "Nota de debito"
                    : nombretipocomprobante === "factura_pedido"
                    ? "Pedido"
                    : nombretipocomprobante === "factura_presupuesto"
                    ? "Presupuesto"
                    : "Remito"}{" "}
                  generado con &eacute;xito!{" "}
                </h6>
              ) : (
                <h6>
                  Hubo un problema al generar{" "}
                  {nombretipocomprobante === "factura_normal"
                    ? "venta"
                    : nombretipocomprobante === "factura_notacredito"
                    ? "nota de credito"
                    : nombretipocomprobante === "factura_notadebito"
                    ? "nota de debito"
                    : nombretipocomprobante === "factura_pedido"
                    ? "pedido"
                    : nombretipocomprobante === "factura_presupuesto"
                    ? "presupuesto"
                    : "remito"}
                </h6>
              )}
            </Alert>
          ) : null}
          {okFacturarPuntoVenta !== "" && okFinalizarPedido !== "" ? (
            <Alert
              icon={
                okFacturarPuntoVenta.success ? (
                  <CheckCircleOutline fontSize="inherit" />
                ) : (
                  <Clear />
                )
              }
              severity={okFacturarPuntoVenta.success ? "success" : "error"}
              style={{ width: "95%", margin: 10 }}
            >
              {okFacturarPuntoVenta.success ? (
                <h6>Factura generada con &eacute;xito! </h6>
              ) : (
                <h6>Hubo un problema al facturar.</h6>
              )}
            </Alert>
          ) : null}
        </div>
        <Divider />
        <div
          style={{
            margin: 15,
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "center",
          }}
        >
          <Button
            variant="contained"
            style={{ width: 150, justifyContent: "center", margin: 5 }}
          >
            {/* <a 
                        href={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=${tipocomprobante}&codigocomprobante=${okFinalizarPedido.facturaid}&tipoaccionpdf=1`}
                        target="_blank"
                        style={{color:"white"}}
                    >  */}
            <a
              href={`${
                process.env.REACT_APP_SERVER
              }dompdf/generarcomprobante_pdf.php?tipocomprobante=${tipocomprobante}&codigocomprobante=${
                currentFacturaId
              }&tipoaccionpdf=1&tiendaid=${localStorage.getItem(
                "tiendausuario"
              )}`}
              target="_blank"
              style={{ color: "white" }}
            >
              Comprobante
            </a>
          </Button>
          {nombretipocomprobante === "factura_normal" ||
          nombretipocomprobante === "factura_notacredito" ||
          nombretipocomprobante === "factura_notadebito" ||
          nombretipocomprobante === "factura_pedido" ? (
            <Button
              style={{ margin: 5 }}
              variant="contained"
              onClick={handleFacturar}
              disabled={
                okFinalizarPedido.success === false ||
                okFacturarPuntoVenta.success === false
              }
            >
              Generar factura
            </Button>
          ) : null}
          {okFacturarPuntoVenta.success ? (
            <Button style={{ margin: 5 }} variant="contained">
              <a style={{ color: "white" }} href={url} target="_blank">
                Descargar factura
              </a>
            </Button>
          ) : null}
        </div>
        <Divider />
      </Modal>
    )
  );
};
