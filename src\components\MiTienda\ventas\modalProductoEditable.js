import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield } from "@mui/material";
import React, { useEffect, useState } from "react"
import Modal from 'react-modal';
import { modificarProductoEditable } from "../../../redux/actions/mitienda.actions";
import { useDispatch, useSelector } from "react-redux";

Modal.setAppElement("#root")

export const ModalProductoEditable= ({show, handleClose, producto}) => {

    const okModificarProductoEditable = useSelector((state) => state.mitienda.modificarProductoEditable)

    const [input, setInput] = useState('')
    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
          justifyContent:"center"
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const dispatch = useDispatch()

    const handleEditarDescripcion =()=>{
        dispatch(modificarProductoEditable(producto.facturadetalleid, producto.cantidad, input.costounitario, input.nombre))
    }

    useEffect(() =>{
        setInput(producto)
    },[producto])

    return (
        show &&
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Editar descripcion producto</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div>
                <TextField 
                    style={{margin:20, width:"90%"}}
                    variant="outlined" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            fontSize:17
                        },
                    }}
                    label="Monto"
                    name="costounitario"
                    value={input.costounitario || ''}
                    onChange={handleChange}
                />
                <TextField 
                    style={{margin:20, width:"90%"}}
                    variant="outlined" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            fontSize:17
                        },
                    }}
                    label="Descripci&oacute;n"
                    name="nombre"
                    value={input.nombre || ''}
                    onChange={handleChange}
                    multiline
                    minRows={5}
                />
                <Divider/>
                {
                    okModificarProductoEditable !== '' &&
                    <Alert
                        severity="info"
                        style={{width:"90%", margin:10}}
                        >
                            {okModificarProductoEditable.mensaje}
                    </Alert>
                }
                <div align="right">
                    <Button size="large"
                    variant="contained"
                    onClick={handleEditarDescripcion}
                    sx={{mt: 3, mr:1}}>Cambiar</Button>
                </div>
            </div>
        </Modal> 
    )
}