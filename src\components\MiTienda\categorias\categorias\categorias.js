import React, { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux";

import Button from "@mui/material/Button";
import { Paper, Snackbar, Tooltip } from "@mui/material";
import AppsIcon from '@mui/icons-material/Apps';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import Pagination from '@mui/material/Pagination';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';

import { AgregarCategoria } from "./agregarCategoria";

import "./categorias.css"
import { AgregarSubcategoria } from "./agregarSubcategoria";
import { 
    getCategorias, 
    getSubCategoria, 
    limpiarAgregarImagenCategoria, 
    limpiarCategoria, 
    limpiarEditarCategoria, 
    limpiarOk, 
    limpiarOkSubcategoria, 
    limpiarSubCategoriaCreada, 
    limpiarSubCategoriaEditada 
} from "../../../../redux/actions/mitienda.actions";
import { EditarCategoria } from "./editarCategoria";
import { EditarSubCategoria } from "../editarSubCategoria";
import { ModalEliminarCategoria } from "./modalEliminar";
import { ModalEliminarSubCategoria } from "./modalEliminarSubcategoria";
import { AgregarImagenCategoria } from "./agregarImagenCategoria";
import { ModalImagenCategoria } from "./visualizarImagenCategoria";
import { AddPhotoAlternate, Check, Clear, FilterList, Image } from "@mui/icons-material";
import MuiAlert from '@mui/material/Alert';
import { ClipLoader } from "react-spinners";
import { ModalFiltros } from "./modalFiltros";

export const Categorias=()=>{
    const okSubcategoria = useSelector((state) => state.mitienda.okConfigSubCategoria)
    const okCategoria = useSelector((state) => state.mitienda.okConfigCategoria)
    const categorias = useSelector((state) => state.mitienda.categorias)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaCategoria)
    const subcategoria = useSelector((state) => state.mitienda.subcategoria)
    const okDesactivarSubCategoria = useSelector((state) => state.mitienda.desactivarSubCategoria)
    const okDesactivarCategoria = useSelector((state) => state.mitienda.desactivarCategoria)
    const okEditarCategoria = useSelector((state) => state.mitienda.editarCategoria)
    const editarSubcategoria = useSelector((state) => state.mitienda.editarSubCategoria)
    const okImagen = useSelector((state) => state.mitienda.agregarImagenCategoria)
    const okEliminarImagen = useSelector((state) => state.mitienda.eliminarImagenCategoria)

    const [categoriaImagen, setCategoriaImagen] = useState('')

    const dispatch = useDispatch()

    const [show, setShow] = useState();

    const handleClose = () => {
        setShow(false);
        dispatch(limpiarCategoria())
    }
    const handleShow = () => setShow(true);

    const [show2, setShow2] = useState(false);
    const [nombreLista, setNombreLista] = useState({})
    const [categoriaid, setCategoriaId] = useState()

    const handleClose2 = () => {
        setShow2(false);
        dispatch(limpiarSubCategoriaCreada())
    }
    const handleShow2 = (e, id, nombre) => {
        e.preventDefault()
        setShow2(true)
        setCategoriaId(id)
        setNombreLista(nombre)
    };

    const [show3, setShow3] = useState(false);

    const handleClose3 = () => {
        setShow3(false);
        dispatch(limpiarEditarCategoria())
    }
    const handleShow3 = (e, nombre) => {
        e.preventDefault()
        setShow3(true)
        setCategoriaImagen(nombre)
    };

    const [show4, setShow4] = useState(false);

    const handleClose4 = () => setShow4(false);
    const handleShow4 = (e, idcategoria, idsubcategoria) => {
        e.preventDefault()
        setShow4(true)
        dispatch(getSubCategoria(idcategoria, idsubcategoria))
        dispatch(limpiarSubCategoriaEditada())
    };

    const [categoriaActDes, setCatActDes] = useState('')

    const [show5, setShow5] = useState(false);

    const handleClose5 = () => setShow5(false);
    const handleShow5 = (e, idcategoria, activo) => {
        e.preventDefault()
        setShow5(true)
        setCatActDes({id: idcategoria, activo: activo})
    }

    const [subcategoriaActDes, setSubCatActDes] = useState('')

    const [show6, setShow6] = useState(false);

    const handleClose6 = () => {
        setShow6(false);
    }
    const handleShow6 = (e, idsubcategoria, activo) => {
        e.preventDefault()
        setShow6(true)
        setSubCatActDes({id: idsubcategoria, activo: activo})
    }

    const [showAgregarImagen, setShowAgregarImagen] = useState();
    const handleCloseAgregarImagen = () => setShowAgregarImagen(false);
    const handleShowAgregarImagen = (e,a) => {
        e.preventDefault()
        setCategoriaImagen(a)
        setShowAgregarImagen(true);
    }

    const [showImagen, setShowImagen] = useState();
    const handleCloseImagen = () => {
        setShowImagen(false);
        dispatch(limpiarAgregarImagenCategoria())
    }
    const handleShowImagen = (e,a) => {
        e.preventDefault()
        setCategoriaImagen(a)
        setShowImagen(true);
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (e,value) => {
        e.preventDefault()
        setPage(value);
    };

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openAgregarCategoria, setOpenAgregarCategoria] = React.useState(false);
    const handleClickAlertAgregarCategoria = () => {
      setOpenAgregarCategoria(true);
    };
    const handleCloseAlertAgregarCategoria = () => {
        setOpenAgregarCategoria(false);
    };

    const [openEditarCategoria, setOpenEditarCategoria] = React.useState(false);
    const handleClickAlertEditarCategoria = () => {
      setOpenEditarCategoria(true);
    };
    const handleCloseAlertEditarCategoria = () => {
        limpiarEditarCategoria()
        setOpenEditarCategoria(false);
    };

    const [openAgregarSubcategoria, setOpenAgregarSubcategoria] = React.useState(false);
    const handleClickAlertAgregarSubcategoria = () => {
      setOpenAgregarSubcategoria(true);
    };
    const handleCloseAlertAgregarSubcategoria = () => {
        setOpenAgregarSubcategoria(false);
    };

    const [openEditarSubcategoria, setOpenEditarSubcategoria] = React.useState(false);
    const handleClickAlertEditarSubcategoria = () => {
      setOpenEditarSubcategoria(true);
    };
    const handleCloseAlertEditarSubcategoria = () => {
        setOpenEditarSubcategoria(false);
    };

    const [openAgregarImagen, setOpenAgregarImagen] = React.useState(false);
    const handleClickAlertAgregarImagen = () => {
      setOpenAgregarImagen(true);
    };
    const handleCloseAlertAgregarImagen = () => {
        setOpenAgregarImagen(false);
    };

    const [showFiltros, setShowFiltros] = useState();
    const handleCloseFiltros = () => {
        setShowFiltros(false)
    };
    const handleShowFiltros = () => setShowFiltros(true);

    const [nombre, setNombre] = useState('')
    const handleChangeNombre = (e) =>{
        setNombre(e.target.value)
    }

    const reset = (e) =>{
        setNombre('')
        setPage(1)
        dispatch(getCategorias(1,''))
    }

    const search = () =>{
        dispatch(getCategorias(page,nombre))
        handleCloseFiltros()
    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[categorias])

    useEffect(() =>{
        setLoading(true)
        dispatch(getCategorias(page,nombre))
        dispatch(limpiarOk())
        dispatch(limpiarOkSubcategoria())
    }, [okCategoria,okImagen,okSubcategoria,page,okDesactivarCategoria,okDesactivarSubCategoria,okEditarCategoria,editarSubcategoria])
    
    return (
        <section className="container-categorias">
            {
                <AgregarCategoria show={show} handleClose={handleClose} handleClickAlert={handleClickAlertAgregarCategoria}/>
            }
            {
                <EditarCategoria show={show3} handleClose={handleClose3} categoria={categoriaImagen} handleClickAlert={handleClickAlertEditarCategoria}/>
            }
            {
                <AgregarSubcategoria show={show2} handleClose={handleClose2} nombre={nombreLista} id={categoriaid} handleClickAlert={handleClickAlertAgregarSubcategoria}/>
            }
            {
                <EditarSubCategoria show={show4} handleClose={handleClose4} subcategoria={subcategoria} handleClickAlert={handleClickAlertEditarSubcategoria}/>
            }
            {
                <ModalEliminarCategoria show={show5} handleClose={handleClose5} categoria={categoriaActDes}/>
            }
            { 
                <ModalEliminarSubCategoria show={show6} handleClose={handleClose6} subcategoria={subcategoriaActDes}/>
            }
            {
                <AgregarImagenCategoria show={showAgregarImagen} handleClose={handleCloseAgregarImagen} categoria={categoriaImagen} handleClickAlert={handleClickAlertAgregarImagen}/>
            }
            {
                <ModalImagenCategoria show={showImagen} handleClose={handleCloseImagen} categoria={categoriaImagen} handleClickAlert={handleClickAlert}/>
            }
            <ModalFiltros
                show={showFiltros}
                handleClose={handleCloseFiltros}
                nombre={nombre}
                handleChangeNombre={handleChangeNombre}
                reset={reset}
                search={search}
            />
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={okEliminarImagen.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEliminarImagen.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarCategoria} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarCategoria}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarCategoria} 
                        severity={okCategoria.ok ? "success" : "error"}
                        sx={{ width: 400 }}>
                        {okCategoria.ok ? <h5>Categor&iacute;a agregada con &eacute;xito!</h5> : <h5>Hubo un problema al crear la categor&iacute;a</h5>}
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEditarCategoria} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditarCategoria}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditarCategoria} 
                        severity={okEditarCategoria ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Categoria editada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarImagen} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarImagen}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarImagen} 
                        severity={okImagen ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Imagen agregada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openAgregarSubcategoria} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregarSubcategoria}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregarSubcategoria} 
                        severity={okSubcategoria ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Subcategoria agregada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEditarSubcategoria} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditarSubcategoria}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditarSubcategoria} 
                        severity={editarSubcategoria ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Subcategoria editada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
            <div  style={{marginBottom:30}}>
                <header className="titulo-categorias">
                    <h3>Categor&iacute;as</h3>
                    <Button 
                        size="large" 
                        variant="contained"
                        className="btn-miplan"
                        onClick={handleShow}
                    >AGREGAR</Button>
                </header>
                <div className="text-categorias">
                    <h4>En esta secci&oacute;n podras agregar las categor&iacute;as y subcategor&iacute;as para luego
                        subir los productos en tu tienda.
                    </h4>
                    <h4>
                        Recomendamos que los nombres de las categor&iacute;as sean representativos y concisos
                        para que tus clientes encuentren <br/> m&aacute;s r&aacute;pido lo que necesiten. 
                    </h4>
                </div>
            </div>
            <Button 
                variant="outlined" 
                sx={{height:40,width:150, marginLeft:6}}
                onClick={handleShowFiltros}>
                <FilterList/> Filtrar
            </Button>
            {
                loading ? 
                <div style={{display:"flex", justifyContent:"center"}}>
                    <ClipLoader 
                        loading={loading}
                        size={50}
                    />
                </div> :
                categorias.length > 0 ? categorias.map ((a) => 
                <ul className="lista">
                    <li key={a.categoriaid}>
                        <Paper className="paper">
                            <div style={{display:"flex"}}>
                                <div className="paper-primer-div"><AppsIcon/></div>
                                <div className="paper-primer-div">{a.nombrecategoria} - {a.categoriaid}</div>
                            </div>
                            <div className="icons">
                                <Tooltip placement="top" title={a.ecommerce === "1" ? "Se muestra en ecommerce" : "No se muestra en ecommerce"} className="icon">
                                    {
                                        a.ecommerce === "1" ? <Check color="success"/> : <Clear color="error"/>
                                    }
                                </Tooltip>
                                <Tooltip placement="top" title={a.activo=== "1" ? "Activada" : "Desactivada"} className="icon">
                                    {
                                        a.activo === "1" ? <Check color="success"/> : <Clear color="error"/>
                                    }
                                </Tooltip>
                                <div onClick={(e) => handleShow2(e, a.categoriaid, a.nombrecategoria)} className="icon">
                                    <Tooltip title="Agregar subcategoria">
                                        <AddIcon style={{color:"black"}} />
                                    </Tooltip>
                                </div>
                                <div onClick={(e) => handleShow3(e, a)} className="icon">
                                    <Tooltip title="Editar">
                                        <EditIcon style={{color:"black"}}/>
                                    </Tooltip>
                                </div>
                                <div onClick={(e) => handleShow5(e, a.categoriaid, a.activo )} className="icon">
                                    <Tooltip title="Activar/Desactivar">
                                        <RemoveRedEyeIcon style={{color:"black"}}/>
                                    </Tooltip>
                                </div>
                                <div onClick={(e) => handleShowAgregarImagen(e, a)} className="icon">
                                    <Tooltip title="Agregar imagen">
                                        <AddPhotoAlternate style={{color:"black"}}/>
                                    </Tooltip>
                                </div>
                                <div onClick={(e) => handleShowImagen(e, a)} className="icon">
                                    <Tooltip title="Ver imagen">
                                        <Image style={{color:"black"}}/>
                                    </Tooltip>
                                </div>
                            </div>
                        </Paper>
                        <ul className="lista">
                            {
                                a.listasubcategorias.length > 0 && a.listasubcategorias.map((b) =>
                                <li className="lista-segundo-li" key={a.subcategoriaid}>
                                    <Paper className="paper">
                                    <div style={{display:"flex"}}>
                                        <div className="paper-primer-div"><AppsIcon/></div>
                                        <div className="paper-primer-div">{b.nombre}</div>
                                    </div>
                                    <div className="icons2">
                                    <Tooltip placement="top" title={b.activo=== "1" ? "Activada" : "Desactivada"} className="icon">
                                        {
                                            b.activo === "1" ? <Check color="success"/> : <Clear color="error"/>
                                        }
                                    </Tooltip>
                                        <div onClick={(e) => handleShow4(e, b.categoriaid, b.subcategoriaid)} className="icon">
                                            <Tooltip title="Editar">
                                                <EditIcon style={{color:"black"}}/>
                                            </Tooltip>
                                        </div>
                                        <div onClick={(e) => handleShow6(e, b.subcategoriaid, b.activo )} className="icon">
                                            <Tooltip title="Activar/Desactivar">
                                                <RemoveRedEyeIcon style={{fontSize:25, marginRight:20, cursor:"pointer",color:"black"}}/>
                                            </Tooltip>
                                        </div>
                                    </div>
                                    </Paper>
                                </li>
                                )
                            }
                        </ul>
                    </li>
                </ul>
                ):
                <div style={{display:"flex", justifyContent:"center"}}>
                    <h3>No hay informaci&oacute;n para mostrar</h3>
                </div>
            }
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </section>
    )
}