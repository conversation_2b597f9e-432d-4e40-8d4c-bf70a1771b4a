import React, { useEffect, useState } from "react";
import Modal from "react-modal";

import {
  Avatar,
  Button,
  InputAdornment,
  Paper,
  Snackbar,
  TextField,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import ShareIcon from "@mui/icons-material/Share";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import RemoveRedEyeIcon from "@mui/icons-material/RemoveRedEye";
import CreateIcon from "@mui/icons-material/Create";
import Tooltip from "@mui/material/Tooltip";

import "./administrarProductos.css";
// import { ModalMiPlan } from "./modal";
import { useDispatch, useSelector } from "react-redux";

import {
  getProductosFiltrados,
  limpiarProductoDesactivado,
} from "../../../../redux/actions/mitienda.actions";
import { ModalShareOptions } from "./modalShareOptions";
import { ModalActivar } from "./modalActivar";
import { Link } from "react-router-dom";
import { ModalFiltros } from "./modalFiltros";
import Pagination from "@mui/material/Pagination";

import "./administrarProductos.css";
import { ModalEliminar } from "./modalEliminar";
import {
  AutoAwesomeMotion,
  Check,
  Clear,
  MessageSharp,
  Search,
} from "@mui/icons-material";
import MuiAlert from "@mui/material/Alert";
import { ModalMensajesProducto } from "./modalMensajesProducto";
import ClipLoader from "react-spinners/ClipLoader";

Modal.setAppElement("#root");

export const AdministrarProductos = () => {
  const dispatch = useDispatch();
  const productos = useSelector((state) => state.mitienda.productos);
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaProductos
  );
  const okAsociarProducto = useSelector(
    (state) => state.mitienda.okAsociarProducto
  );

  const [nombre, setNombre] = useState("");
  const [activo, setActivo] = useState("");
  const [productoId, setProductoId] = useState("");

  const [tallaid, setTallaid] = useState(0);

  const [marcaid, setMarcaid] = useState(0);

  const [categoriaid, setCategoriaid] = useState(0);

  const [stock, setStock] = useState("1");
  const handleChangeStock = (e) => {
    e.preventDefault();
    if (e.target.checked === true) {
      setStock("1");
    } else {
      setStock("0");
    }
  };

  const [resetear, setResetear] = useState(false);

  const hasAdminProfile = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    return Object.values(perfiles).includes(7); // 7 is admin profile ID
  };

  const reset = (e) => {
    setCategoriaid(0);
    setMarcaid(0);
    setTallaid(0);
    setPage(1);
    setResetear(true);
    handleClose4();
  };

  const searchByFilters = () => {
    setResetear(false);
    setPage(1);
    dispatch(
      getProductosFiltrados(
        nombre,
        sku,
        categoriaid,
        marcaid,
        tallaid,
        stock,
        page
      )
    );
    handleClose4();
  };

  const handleChangeNombre = (e) => {
    setNombre(e.target.value);
  };

  const [sku, setSku] = useState("");

  const handleChangeSku = (e) => {
    setSku(e.target.value);
  };

  const [search, setSearch] = useState(false);
  const searchBySku = () => {
    setPage(1);
    setSearch(true);
    dispatch(
      getProductosFiltrados(
        nombre,
        sku,
        categoriaid,
        marcaid,
        tallaid,
        stock,
        page
      )
    );
  };

  const searchByName = () => {
    setPage(1);
    setSearch(true);
    dispatch(
      getProductosFiltrados(
        nombre,
        sku,
        categoriaid,
        marcaid,
        tallaid,
        stock,
        page
      )
    );
  };

  const [productoCompartir, setProductoCompartir] = useState("");

  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = (e, id) => {
    setProductoCompartir(id);
    setShow(true);
  };

  const [show2, setShow2] = useState(false);
  const handleClose2 = () => setShow2(false);
  const handleShow2 = (e, id, activo) => {
    e.preventDefault();
    setShow2(true);
    setProductoId(id);
    setActivo(activo);
  };

  const [show3, setShow3] = useState(false);
  const handleClose3 = () => setShow3(false);
  const handleShow3 = () => setShow3(true);

  const [show4, setShow4] = useState(false);
  const handleClose4 = () => setShow4(false);
  const handleShow4 = () => setShow4(true);

  const [productoMensaje, setProductoMensajes] = useState("");
  const [showProductoMensajes, setShowProductoMensajes] = useState(false);
  const handleCloseProductoMensajes = () => setShowProductoMensajes(false);
  const handleShowProductoMensajes = (row) => {
    setProductoMensajes(row);
    setShowProductoMensajes(true);
  };

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });
  const [open, setOpen] = React.useState(false);
  const handleClickAlert = () => {
    setOpen(true);
  };
  const handleCloseAlert = () => {
    setOpen(false);
    dispatch(limpiarProductoDesactivado());
  };

  useEffect(() => {
    if (okAsociarProducto) {
      dispatch(
        getProductosFiltrados(
          nombre,
          sku,
          categoriaid,
          marcaid,
          tallaid,
          stock,
          page
        )
      );
    }
  }, [okAsociarProducto]);

  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(false);
  }, [productos]);

  useEffect(() => {
    setLoading(true);
    dispatch(
      getProductosFiltrados(
        nombre,
        sku,
        categoriaid,
        marcaid,
        tallaid,
        stock,
        page
      )
    );
  }, [page]);

  useEffect(() => {
    if (resetear) {
      dispatch(
        getProductosFiltrados(
          nombre,
          sku,
          categoriaid,
          marcaid,
          tallaid,
          stock,
          page
        )
      );
    }
  }, [resetear]);

  useEffect(() => {
    if (search === true && nombre === "") {
      dispatch(
        getProductosFiltrados(
          nombre,
          sku,
          categoriaid,
          marcaid,
          tallaid,
          stock,
          page
        )
      );
      setSearch(false);
    }
  }, [search, nombre]);

  return (
    <section className="container-adm-productos">
      {
        <ModalShareOptions
          show={show}
          handleClose={handleClose}
          info={productoCompartir}
        />
      }
      {
        <ModalActivar
          show={show2}
          handleClose={handleClose2}
          id={productoId}
          activo={activo}
          handleClickAlert={handleClickAlert}
        />
      }
      {<ModalEliminar show={show3} handleClose={handleClose3} />}
      {
        <ModalFiltros
          show={show4}
          handleClose={handleClose4}
          setMarca={setMarcaid}
          marca={marcaid}
          setCategoriaid={setCategoriaid}
          categoriaid={categoriaid}
          setTalla={setTallaid}
          tallaid={tallaid}
          handleChangeStock={handleChangeStock}
          stock={stock}
          search={searchByFilters}
          reset={reset}
        />
      }
      <ModalMensajesProducto
        show={showProductoMensajes}
        handleClose={handleCloseProductoMensajes}
        producto={productoMensaje}
      />
      {
        <Snackbar
          open={open}
          autoHideDuration={10000}
          onClose={handleCloseAlert}
          anchorOrigin={{ vertical: "top", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseAlert}
            severity={okAsociarProducto ? "success" : "error"}
            sx={{ width: 400 }}
          >
            <h5>Informaci&oacute;n modificada con &eacute;xito!</h5>
          </Alert>
        </Snackbar>
      }
      {/* <header className="header-adm-productos">
                <h3>Administrar productos</h3>
                <Link to="/Mitienda/agregar" style={{color:"black"}}>
                    <Button 
                        size="large" 
                        variant="contained"
                        className="btn-miplan"
                        sx={{mr:9}}
                    >AGREGAR PRODUCTO</Button>
                </Link>
            </header> */}
      <header className="header-adm-productos">
        <h3>Administrar productos</h3>
        {hasAdminProfile() && (
          <Link to="/Mitienda/agregar" style={{ color: "black" }}>
            <Button
              size="large"
              variant="contained"
              className="btn-miplan"
              sx={{ mr: 9 }}
            >
              AGREGAR PRODUCTO
            </Button>
          </Link>
        )}
      </header>
      <div className="list-adm-productos">
        <TextField
          label="Buscar por nombre"
          variant="outlined"
          margin="normal"
          placeholder="Nombre del producto"
          sx={{ width: 300, marginRight: 1 }}
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
          name="nombre"
          onChange={(e) => handleChangeNombre(e)}
          onKeyPress={(ev) => {
            if (ev.key == "Enter") {
              ev.preventDefault();
              searchByName();
            }
          }}
        />
        <Tooltip title="Buscar por nombre">
          <Button
            variant="outlined"
            onClick={searchByName}
            sx={{ height: 40, marginTop: 2, marginRight: 3 }}
          >
            <Search />
          </Button>
        </Tooltip>

        <TextField
          label="Buscar por SKU"
          variant="outlined"
          margin="normal"
          placeholder="Codigo del producto"
          sx={{ width: 300, marginRight: 1 }}
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
          name="sku"
          onChange={(e) => handleChangeSku(e)}
          onKeyPress={(ev) => {
            if (ev.key == "Enter") {
              ev.preventDefault();
              searchBySku();
            }
          }}
        />
        <Tooltip title="Buscar por SKU">
          <Button
            variant="outlined"
            sx={{ height: 40, marginTop: 2, marginRight: 3 }}
            onClick={searchBySku}
          >
            <Search />
          </Button>
        </Tooltip>
        <Button
          variant="outlined"
          sx={{ height: 40, width: 150, marginTop: 2 }}
          onClick={handleShow4}
        >
          <FilterListIcon /> Filtrar
        </Button>
      </div>
      {loading ? (
        <div style={{ display: "flex", justifyContent: "center" }}>
          <ClipLoader loading={loading} size={50} />
        </div>
      ) : (
        productos.data &&
        productos.data.map((row) => (
          <div>
            <ul className="lista">
              <li key={row.codigoproducto}>
                <div className="card-producto">
                  <div className="avatar-productos">
                    <Avatar
                      sx={{ width: 130, height: 130, alignSelf: "center" }}
                      src={
                        process.env.REACT_APP_SERVER +
                        row.imagenes[0].pathImagenProducto +
                        row.imagenes[0].productoid +
                        "/" +
                        row.imagenes[0].file
                      }
                    />
                  </div>
                  <div className="datos-producto">
                    <h5>
                      <strong className="titulo-producto">
                        {row.nombreproducto}
                      </strong>
                    </h5>
                    <div className="datos-producto-listado">
                      <div className="datos-info">
                        {/* <h6 className="dato"><strong>Marca</strong>: {row.nombremarca}</h6> */}
                        <h6 className="dato">
                          <strong>SKU</strong>: {row.codigoproducto}
                        </h6>
                        <h6 className="dato">
                          <strong>Stock</strong>: {row.cantidadactiva}
                        </h6>
                      </div>
                      <div className="datos-precios">
                        <h6 className="dato">
                          <strong>Precio costo</strong>: {row.precioCosto}
                        </h6>
                        <h6 className="dato">
                          <strong>Precio venta</strong>: {row.precioVenta}
                        </h6>
                      </div>
                    </div>
                  </div>
                  {/* //w original */}
                  {/* <div className="list-icons-producto">
                    <div
                      style={{
                        fontSize: 25,
                        marginRight: 15,
                        cursor: "pointer",
                      }}
                    >
                      <Tooltip
                        title={row.activo === "1" ? "Activado" : "Desactivado"}
                      >
                        {row.activo === "1" ? (
                          <Check color="success" />
                        ) : (
                          <Clear color="error" />
                        )}
                      </Tooltip>
                    </div>
                    <div
                      onClick={(e) => handleShowProductoMensajes(row)}
                      style={{
                        fontSize: 25,
                        marginRight: 10,
                        cursor: "pointer",
                      }}
                    >
                      <Tooltip title="Preguntas">
                        <MessageSharp />
                      </Tooltip>
                    </div>
                    <div
                      onClick={(e) =>
                        handleShow(e, {
                          id: row.productoid,
                          nombre: row.nombreproducto,
                        })
                      }
                      style={{
                        fontSize: 25,
                        marginRight: 15,
                        cursor: "pointer",
                      }}
                    >
                      <Tooltip title="Compartir">
                        <ShareIcon />
                      </Tooltip>
                    </div>
                    <Link
                      to={`/Mitienda/ClonarProducto/${row.productoid}`}
                      style={{ color: "black" }}
                    >
                      <div
                        style={{
                          fontSize: 25,
                          marginRight: 20,
                          cursor: "pointer",
                        }}
                      >
                        <Tooltip title="Clonar">
                          <ContentCopyIcon />
                        </Tooltip>
                      </div>
                    </Link>
                    <Link
                      to={`/Mitienda/AsociarProducto/${row.productoid}`}
                      style={{ color: "black" }}
                    >
                      <div
                        style={{
                          fontSize: 25,
                          marginRight: 20,
                          cursor: "pointer",
                        }}
                      >
                        <Tooltip title="Asociar">
                          <AutoAwesomeMotion />
                        </Tooltip>
                      </div>
                    </Link>
                    <div
                      onClick={(e) =>
                        handleShow2(e, row.productoid, row.activo)
                      }
                      style={{
                        fontSize: 25,
                        marginRight: 20,
                        cursor: "pointer",
                      }}
                    >
                      <Tooltip title="Activar">
                        <RemoveRedEyeIcon />
                      </Tooltip>
                    </div>
                    <Link
                      to={`/Mitienda/EditarProducto/${row.productoid}`}
                      style={{ color: "black" }}
                    >
                      <div
                        style={{
                          fontSize: 25,
                          marginRight: 20,
                          cursor: "pointer",
                        }}
                      >
                        <Tooltip title="Editar">
                          <CreateIcon />
                        </Tooltip>
                      </div>
                    </Link>
                  </div> */}
                  <div className="list-icons-producto">
                    {/* Status indicator - keep visible for all */}
                    <div
                      style={{
                        fontSize: 25,
                        marginRight: 15,
                        cursor: "pointer",
                      }}
                    >
                      <Tooltip
                        title={row.activo === "1" ? "Activado" : "Desactivado"}
                      >
                        {row.activo === "1" ? (
                          <Check color="success" />
                        ) : (
                          <Clear color="error" />
                        )}
                      </Tooltip>
                    </div>

                    {/* Questions - keep visible for all */}
                    <div
                      onClick={(e) => handleShowProductoMensajes(row)}
                      style={{
                        fontSize: 25,
                        marginRight: 10,
                        cursor: "pointer",
                      }}
                    >
                      <Tooltip title="Preguntas">
                        <MessageSharp />
                      </Tooltip>
                    </div>

                    {/* Share - keep visible for all */}
                    <div
                      onClick={(e) =>
                        handleShow(e, {
                          id: row.productoid,
                          nombre: row.nombreproducto,
                        })
                      }
                      style={{
                        fontSize: 25,
                        marginRight: 15,
                        cursor: "pointer",
                      }}
                    >
                      <Tooltip title="Compartir">
                        <ShareIcon />
                      </Tooltip>
                    </div>

                    {/* Clone - only for admin */}
                    {hasAdminProfile() && (
                      <Link
                        to={`/Mitienda/ClonarProducto/${row.productoid}`}
                        style={{ color: "black" }}
                      >
                        <div
                          style={{
                            fontSize: 25,
                            marginRight: 20,
                            cursor: "pointer",
                          }}
                        >
                          <Tooltip title="Clonar">
                            <ContentCopyIcon />
                          </Tooltip>
                        </div>
                      </Link>
                    )}

                    {/* Associate - only for admin */}
                    {hasAdminProfile() && (
                      <Link
                        to={`/Mitienda/AsociarProducto/${row.productoid}`}
                        style={{ color: "black" }}
                      >
                        <div
                          style={{
                            fontSize: 25,
                            marginRight: 20,
                            cursor: "pointer",
                          }}
                        >
                          <Tooltip title="Asociar">
                            <AutoAwesomeMotion />
                          </Tooltip>
                        </div>
                      </Link>
                    )}

                    {/* Activate/Deactivate - only for admin */}
                    {hasAdminProfile() && (
                      <div
                        onClick={(e) =>
                          handleShow2(e, row.productoid, row.activo)
                        }
                        style={{
                          fontSize: 25,
                          marginRight: 20,
                          cursor: "pointer",
                        }}
                      >
                        <Tooltip title="Activar">
                          <RemoveRedEyeIcon />
                        </Tooltip>
                      </div>
                    )}

                    {/* Edit - only for admin */}
                    {hasAdminProfile() && (
                      <Link
                        to={`/Mitienda/EditarProducto/${row.productoid}`}
                        style={{ color: "black" }}
                      >
                        <div
                          style={{
                            fontSize: 25,
                            marginRight: 20,
                            cursor: "pointer",
                          }}
                        >
                          <Tooltip title="Editar">
                            <CreateIcon />
                          </Tooltip>
                        </div>
                      </Link>
                    )}
                  </div>
                </div>
              </li>
            </ul>
          </div>
        ))
      )}

      {productos.data.length === 0 && productos.loading === false ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            marginTop: 20,
            marginBottom: 20,
          }}
        >
          <h3>No hay informaci&oacute;n para mostrar</h3>
        </div>
      ) : null}
      <Pagination
        count={paginaUltima}
        page={page}
        onChange={handleChangePage}
        size="large"
        sx={{ alignSelf: "center" }}
      />
    </section>
  );
};
