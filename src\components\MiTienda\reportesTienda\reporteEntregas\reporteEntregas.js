import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Pagination,
  Button,
  FormControl,
  Select,
  MenuItem,
  ListSubheader,
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import {
  Search,
  Clear,
  KeyboardArrowDown,
  MoreHoriz,
} from "@mui/icons-material";
import ClipLoader from "react-spinners/ClipLoader";
import {
  getEntregas,
  getListadoClientesByName,
} from "../../../../redux/actions/mitienda.actions";
import { ModalFiltrosReporteEntregas } from "./modalFiltrosReporteEntregas";
import { formatDate } from "../../../shared/utility";

const TruncatedCell = ({ text }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) return <span>-</span>;

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: "4px",
      }}
    >
      <div
        style={{
          whiteSpace: isExpanded ? "normal" : "nowrap",
          overflow: isExpanded ? "visible" : "hidden",
          textOverflow: isExpanded ? "unset" : "ellipsis",
          maxWidth: "150px",
          fontSize: "80%",
        }}
      >
        {text}
      </div>
      {text.length > 20 && (
        <Tooltip title={isExpanded ? "Ver menos" : "Ver más"}>
          <IconButton
            size="small"
            onClick={() => setIsExpanded(!isExpanded)}
            style={{ padding: 0 }}
          >
            <MoreHoriz fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
    </div>
  );
};

export const ReporteEntregas = () => {
  const dispatch = useDispatch();
  const [page, setPage] = useState(1);
  const [fechaDesde, setFechaDesde] = useState(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth() - 12, 1)
      .toISOString()
      .split("T")[0];
  });
  const [fechaHasta, setFechaHasta] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [show, setShow] = useState(false);

  // New state variables for search
  const [clienteInput, setClienteInput] = useState("");
  const [clienteid, setClienteid] = useState("");
  const [selectedOption, setSelectedOption] = useState({
    nombre: "",
    apellido: "",
  });
  const [pedidoId, setPedidoId] = useState("");

  const { entregas, loading } = useSelector((state) => ({
    entregas: state.mitienda.entregas.data || {},
    loading: state.mitienda.entregas.loading,
  }));

  const clientes = useSelector((state) => state.mitienda.clientesByName);
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaEntregas
  );

  useEffect(() => {
    if (fechaDesde && fechaHasta && page) {
      dispatch(getEntregas(fechaDesde, fechaHasta, page, clienteid, pedidoId));
    }
  }, [dispatch, page, clienteid, pedidoId]);

  useEffect(() => {
    if (clienteInput.length > 1) {
      dispatch(getListadoClientesByName(clienteInput, ""));
    }
  }, [clienteInput, dispatch]);

  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const handleClose = () => setShow(false);

  const handleChangeFechaDesde = (e) => {
    setFechaDesde(e.target.value);
  };

  const handleChangeFechaHasta = (e) => {
    setFechaHasta(e.target.value);
  };

  const searchByFilters = () => {
    setPage(1);
    dispatch(getEntregas(fechaDesde, fechaHasta, 1, clienteid, pedidoId));
    handleClose();
  };

  //w original
  // const reset = () => {
  //   const today = new Date();
  //   const initialFechaDesde = "2021-04-01";
  //   const initialFechaHasta = today.toISOString().split("T")[0];

  //   setFechaDesde(initialFechaDesde);
  //   setFechaHasta(initialFechaHasta);
  //   setPage(1);
  //   setClienteid("");
  //   setClienteInput("");
  //   setSelectedOption({ nombre: "", apellido: "" });
  //   setPedidoId("");

  //   dispatch(getEntregas(initialFechaDesde, initialFechaHasta, 1, "", ""));
  //   handleClose();
  // };

  const reset = () => {
  const today = new Date();
  // Set initialFechaDesde to 12 months ago (matching initial load)
  const initialFechaDesde = new Date(today.getFullYear(), today.getMonth() - 12, 1)
    .toISOString()
    .split("T")[0];
  const initialFechaHasta = today.toISOString().split("T")[0];

  setFechaDesde(initialFechaDesde);
  setFechaHasta(initialFechaHasta);
  setPage(1);
  setClienteid("");
  setClienteInput("");
  setSelectedOption({ nombre: "", apellido: "" });
  setPedidoId("");

  dispatch(getEntregas(initialFechaDesde, initialFechaHasta, 1, "", ""));
  handleClose();
};

  //w
  const handlePedidoSearch = (e) => {
    setPedidoId(e.target.value);
    setPage(1);
  };

  const clearClienteSearch = () => {
    setClienteid("");
    setSelectedOption({ nombre: "", apellido: "" });
    setClienteInput("");
    dispatch(getEntregas(fechaDesde, fechaHasta, page, "", pedidoId));
  };

  const clearPedidoSearch = () => {
    setPedidoId("");
    dispatch(getEntregas(fechaDesde, fechaHasta, page, clienteid, ""));
  };

  const handleClienteChange = (e, option) => {
    const newClienteId = e.target.value;
    setClienteid(newClienteId);
    setSelectedOption(option);
    setClienteInput("");
    setPage(1); // This will trigger the useEffect above
  };

  return (
    <div className="notificaciones-container">
      <ModalFiltrosReporteEntregas
        show={show}
        handleClose={handleClose}
        handleChangeFechaDesde={handleChangeFechaDesde}
        fechaDesde={fechaDesde}
        handleChangeFechaHasta={handleChangeFechaHasta}
        fechaHasta={fechaHasta}
        search={searchByFilters}
        reset={reset}
      />
      <div style={{ padding: "20px" }}>
        <h3>Reporte de Entregas</h3>
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            alignItems: "center",
          }}
        >
          <Button
            variant="outlined"
            onClick={() => setShow(true)}
            startIcon={<FilterListIcon />}
          >
            Filtrar por Fecha
          </Button>

          {/* original */}
          {/* <FormControl sx={{ minWidth: 300 }}>
            <Select
              size="small"
              value={clienteid}
              onChange={(e) => {
                const selectedClient = clientes.find(c => c.clienteid === e.target.value);
                handleClienteChange(e, selectedClient || { nombre: "", apellido: "" });
              }}
              displayEmpty
              renderValue={() =>
                clienteid
                  ? `${selectedOption.nombre} ${selectedOption.apellido}`
                  : "Buscar cliente..."
              }
              onOpen={() => {
                setClienteInput("");
              }}
              IconComponent={() => (
                <div style={{ display: "flex", alignItems: "center" }}>
                  {clienteid !== "" && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearClienteSearch();
                      }}
                      sx={{ marginRight: 1 }}
                    >
                      <Clear />
                    </IconButton>
                  )}
                  <KeyboardArrowDown />
                </div>
              )}
            >
              <ListSubheader>
                <TextField
                  size="small"
                  autoFocus
                  placeholder="Buscar cliente..."
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  inputProps={{
                    style: {
                      height: 35,
                      fontSize: 17,
                    },
                  }}
                  onChange={(e) => setClienteInput(e.target.value)}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  onKeyDown={(e) => {
                    e.stopPropagation();
                  }}
                  value={clienteInput}
                />
              </ListSubheader>
              {clientes &&
                clientes.map((option) => (
                  <MenuItem
                    key={option.clienteid}
                    value={option.clienteid}
                    onClick={() => {
                      setSelectedOption(option);
                      setClienteInput("");
                    }}
                  >
                    {option.nombre} {option.apellido}
                  </MenuItem>
                ))}
            </Select>
          </FormControl> */}

          <FormControl sx={{ minWidth: 300 }}>
            <Select
              size="small"
              value={clienteid}
              onChange={(e) => {
                const selectedClient = clientes.find(
                  (c) => c.clienteid === e.target.value
                );
                handleClienteChange(
                  e,
                  selectedClient || { nombre: "", apellido: "" }
                );
              }}
              displayEmpty
              renderValue={() =>
                clienteid
                  ? `${selectedOption.nombre} ${selectedOption.apellido}`
                  : "Buscar cliente..."
              }
              onOpen={() => {
                setClienteInput("");
              }}
              MenuProps={{
                disableAutoFocusItem: true, // Prevents auto-focus on menu items
                autoFocus: false, // Prevents menu from taking focus
              }}
              IconComponent={() => (
                <div style={{ display: "flex", alignItems: "center" }}>
                  {clienteid !== "" && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearClienteSearch();
                      }}
                      sx={{ marginRight: 1 }}
                    >
                      <Clear />
                    </IconButton>
                  )}
                  <KeyboardArrowDown />
                </div>
              )}
            >
              <ListSubheader>
                <TextField
                  size="small"
                  autoFocus
                  placeholder="Buscar cliente..."
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                    endAdornment: clienteInput && (
                      <InputAdornment position="end">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            setClienteInput("");
                          }}
                        >
                          <Clear fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  inputProps={{
                    style: {
                      height: 35,
                      fontSize: 17,
                    },
                  }}
                  onChange={(e) => setClienteInput(e.target.value)}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                    }
                    e.stopPropagation();
                  }}
                  value={clienteInput}
                />
              </ListSubheader>
              {clientes &&
                clientes.map((option) => (
                  <MenuItem
                    key={option.clienteid}
                    value={option.clienteid}
                    onClick={() => {
                      setSelectedOption(option);
                      setClienteInput("");
                    }}
                  >
                    {option.nombre} {option.apellido}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <TextField
            size="small"
            placeholder="Buscar por Nº Pedido..."
            value={pedidoId}
            onChange={handlePedidoSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {pedidoId && (
                    <IconButton size="small" onClick={clearPedidoSearch}>
                      <Clear />
                    </IconButton>
                  )}
                </InputAdornment>
              ),
              style: {
                height: "40px",
              },
            }}
            sx={{
              minWidth: 200,
              "& .MuiOutlinedInput-root": {
                padding: "0 8px",
              },
            }}
          />
        </div>
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyItems: "center",
        }}
      >
        <TableContainer
          component={Paper}
          sx={{
            marginTop: 5,
            width: "90%",
            alignSelf: "center",
            margin: "40px auto",
            marginBottom: 5,
            padding: "0 20px",
          }}
        >
          <Table aria-label="entregas table">
            <TableHead>
              <TableRow>
                {[
                  "Pedido",
                  // "Remito",
                  "Nro de Cliente",
                  "Cliente",
                  "Artículo",
                  "Fecha Emisión",
                  "Fecha Entrega",
                  "Fecha Vencimiento",
                  "Descripción",
                  "Cantidad Original (kg)",
                  "Cantidad Entregada (kg)",
                  "Cantidad Pendiente (kg)",
                  "TERM PAGO",
                  "Observaciones",
                ].map((header) => (
                  <TableCell
                    key={header}
                    style={{
                      fontWeight: "bold",
                      fontSize: "80%", // Changed from 90% to 80%
                      padding: "8px 2px",
                    }}
                    align="center"
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={14} align="center">
                    <ClipLoader loading={true} size={50} />
                  </TableCell>
                </TableRow>
              ) : entregas && Object.keys(entregas).length > 0 ? (
                Object.values(entregas).map((row) => (
                  <TableRow
                    key={row.id}
                    sx={{
                      "&:hover": { backgroundColor: "#f5f5f5" },
                      "&:last-child td, &:last-child th": { border: 0 },
                      minHeight: "45px",
                    }}
                  >
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.pedidoplacaid}
                    </TableCell>
                    {/* <TableCell align="center" style={{ fontSize: "80%", padding: "6px 2px" }}>{row.numeroremito}</TableCell> */}
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.clienteid}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.nombrecliente}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.codigoarticulo}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.fechaemision ? formatDate(row.fechaemision) : "-"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.fechaentrega ? formatDate(row.fechaentrega) : "-"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.fechavencimiento
                        ? formatDate(row.fechavencimiento)
                        : "-"}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 8px" }}
                    >
                      <TruncatedCell text={row.nombreproducto} />
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.cantidad}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.cantidadentregada}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{ fontSize: "80%", padding: "6px 2px" }}
                    >
                      {row.cantidadpendiente}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{
                        fontSize: "80%",
                        padding: "6px 2px",
                        color: "red",
                      }}
                    >
                      {row.tipopago}
                    </TableCell>
                    <TableCell
                      align="center"
                      style={{
                        fontSize: "80%",
                        padding: "6px 2px",
                      }}
                    >
                      {row.observacion}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={14} align="center">
                    <h5>No hay información para mostrar</h5>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {paginaUltima > 1 && (
          <Pagination
            count={paginaUltima}
            page={page}
            onChange={handleChangePage}
            color="primary"
            size="large"
            sx={{ alignSelf: "center", marginY: 4 }}
          />
        )}
      </div>
    </div>
  );
};
