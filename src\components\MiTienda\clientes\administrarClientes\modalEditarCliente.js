import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, FormControl, InputLabel, MenuItem, Select, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { editarCliente, getCondicionesIVA, getCondicionesPago, getTiposDocumentoTienda } from "../../../../redux/actions/mitienda.actions";
import { useEffect } from "react";
import { matchCondicionIva, matchCondicionPago, matchTipoDocumento } from "./utilsClientes";
Modal.setAppElement("#root")

export const ModalEditarCliente = ({show, 
  handleClose, 
  cliente,
  handleClickAlert
}) =>{

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };

  const tiposDocumentos = useSelector((state) => state.mitienda.tiposDocumentos)
  const condicionesIva = useSelector((state) => state.mitienda.condicionesIva)
  const condicionesPago = useSelector((state) => state.mitienda.condicionesPago)

  const [input, setInput] = useState('')

  const handleChange = (e) => {
      e.preventDefault()
      setInput({
          ...input,
          [e.target.name]: e.target.value
      })
  }

  const dispatch = useDispatch()

  const handleOnClick = () => {
    dispatch(editarCliente(input))
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  useEffect(() => {
    if(cliente){
      setInput(cliente)
    }
  },[cliente])

  useEffect(() =>{
    if(show){
      dispatch(getTiposDocumentoTienda())
      dispatch(getCondicionesIVA())
      dispatch(getCondicionesPago())
    }
  },[show])

  return (
      show ? 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
              <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                  <h4>Editar cliente</h4>
                  <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
              </div>
              <Divider/>
              <div style={{display:"flex", flexDirection:"column", marginTop:10}}>
                  <TextField
                      style={{marginTop:15, marginBottom:15}}
                      label="Nombre"
                      value={input.nombre}
                      name="nombre"
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                      onChange={(e) => handleChange(e)}
                  />
                  <TextField
                    style={{marginTop:15, marginBottom:15}}
                    label="Apellido"
                    value={input.apellido}
                    name="apellido"
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                      }}
                    inputProps={{
                        style: {
                          height: 40,
                          fontSize:17
                        },
                    }}
                    onChange={(e) => handleChange(e)}
                  />
                  <FormControl fullWidth sx={{mb:2, mt:2}}>
                      <InputLabel id="tipodocumento-select-label">Tipo documento</InputLabel>
                      <Select
                      labelId="tipodocumento-select-label"
                      label="Tipo documento"
                      size="small"
                      name="tipodocidentidadid"
                      value={input.tipodocidentidadid || ''}
                      onChange={(e) => handleChange(e)}
                      color="secondary"
                      MenuProps={{ keepMounted: true, disablePortal: true }}
                      >
                      <MenuItem value="">Seleccione una opcion</MenuItem>
                      {               
                          tiposDocumentos && tiposDocumentos.map((t) =>
                              <MenuItem value={t.tipodocidentidadid} key={t.tipodocidentidadid}>{t.nombre}</MenuItem>
                          )
                      }
                      </Select>
                  </FormControl>
                  <TextField
                      style={{marginTop:15, marginBottom:15}}
                      label="N&uacute;mero documento"
                      value={input.cuit || ''}
                      name="cuit"
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                      onChange={(e) => handleChange(e)}
                  />
                  <TextField
                      style={{marginTop:15, marginBottom:15}}
                      label="Email"
                      value={input.email || ''}
                      name="email"
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                      onChange={(e) => handleChange(e)}
                  />
                  <TextField
                      style={{marginTop:15, marginBottom:15}}
                      label="Celular"
                      value={input.celular || ''}
                      name="celular"
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                      onChange={(e) => handleChange(e)}
                  />
                  <TextField
                      style={{marginTop:15, marginBottom:15}}
                      label="Telefono"
                      value={input.telefono || ''}
                      name="telefono"
                      InputLabelProps={{
                          style: {
                              marginBottom:10,
                              marginTop: -7
                          },
                        }}
                      inputProps={{
                          style: {
                            height: 40,
                            fontSize:17
                          },
                      }}
                      onChange={(e) => handleChange(e)}
                  />
                  <FormControl fullWidth sx={{mb:2, mt:2}}>
                      <InputLabel id="condicioniva-select-label">Condicion IVA</InputLabel>
                      <Select
                      labelId="condicioniva-select-label"
                      label="Condicion IVA"
                      size="small"
                      name="tipocondicionivaid"
                      value={input.tipocondicionivaid || ''}
                      onChange={(e) => handleChange(e)}
                      color="secondary"
                      MenuProps={{ keepMounted: true, disablePortal: true }}
                      >
                      <MenuItem value="">Seleccione una opcion</MenuItem>
                      {               
                          condicionesIva && condicionesIva.map((t) =>
                              <MenuItem value={t.tipocondicionivaid} key={t.tipocondicionivaid}>{t.nombre}</MenuItem>
                          )
                      }
                      </Select>
                  </FormControl>
                  <FormControl fullWidth sx={{mb:2, mt:2}}>
                      <InputLabel id="condicionpago-select-label">Condicion Pago</InputLabel>
                      <Select
                      labelId="condicionpago-select-label"
                      label="Condicion Pago"
                      size="small"
                      name="tipocondicionpagoid"
                      value={input.tipocondicionpagoid || ''}
                      onChange={(e) => handleChange(e)}
                      color="secondary"
                      MenuProps={{ keepMounted: true, disablePortal: true }}
                      >
                      <MenuItem value="">Seleccione una opcion</MenuItem>
                      {               
                          condicionesPago && condicionesPago.map((t) =>
                              <MenuItem value={t.tipocondicionpagoid} key={t.tipocondicionpagoid}>{t.nombre}</MenuItem>
                          )
                      }
                      </Select>
                  </FormControl>
              </div>
              <div align="center">
              <Button 
                disabled={
                  input.tipodocidentidad == "" ||
                  input.tipocondicionpago == "" ||
                  input.tipocondicioniva == "" ||
                  input.nombre == "" ||
                  input.email == "" 
                }
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={handleOnClick}
              >Editar</Button>
              </div>
      </Modal> : null
  )
}