import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, FormControl, InputLabel, MenuItem, Select, Snackbar, TextField } from '@mui/material';
import { useDispatch, useSelector } from "react-redux";
import { editarDescuento, editarFacturaProveedor, editarPromocion } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalEditarFactura = ({show, handleClose, info, handleClickAlert}) =>{

    const proveedores = useSelector((state) => state.mitienda.proveedoresSinPaginado)
    const tiposComprobantes = useSelector((state) => state.mitienda.tiposComprobantes)
    const monedas = useSelector((state) => state.mitienda.monedas)

  const [input, setInput] = useState()

  const handleChange = (e) => {
    e.preventDefault()
    setInput({...input, [e.target.name]: e.target.value })
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }

  const dispatch = useDispatch()

  const handleClick = () => {
    dispatch(editarFacturaProveedor(input))
    setTimeout(function(){
        handleClickAlert()
    }, 1000);
    handleClose()
}
  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    useEffect(() => {
        setInput(info)
    }, [info])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Editar factura #{info.facturacompraid}</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <FormControl fullWidth style={{marginTop:15, marginBottom:15}} >
                <InputLabel id="proveedor-select-label">Proveedor</InputLabel>
                <Select
                    labelId="proveedor-select-label"
                    id="proveedor"
                    label="Proveedor"
                    size="small"
                    name="proveedorid"
                    value={input.proveedorid}
                    onChange={handleChange}
                    style={{height:45}}
                    MenuProps={{ keepMounted: true, disablePortal: true }}
                    >
                    <MenuItem value="">Seleccione una opcion</MenuItem>
                    {               
                        proveedores && proveedores.map((t) =>
                            <MenuItem value={t.proveedorid} key={t.proveedorid}>{t.nombre}</MenuItem>
                        )
                    }
                </Select>
            </FormControl>
            <FormControl fullWidth style={{marginTop:15, marginBottom:15}} >
                <InputLabel id="tipo_comprob-select-label">Tipo comprobante</InputLabel>
                <Select
                    labelId="tipo_comprob-select-label"
                    id="tipo_comprob"
                    label="Tipo comprobante"
                    size="small"
                    name="tipofacturacompraid"
                    value={input.tipofacturacompraid}
                    onChange={handleChange}
                    style={{height:45}}
                    MenuProps={{ keepMounted: true, disablePortal: true }}
                    >
                    <MenuItem value="0">Seleccione una opcion</MenuItem>
                    {               
                        tiposComprobantes && tiposComprobantes.map((t) =>
                            <MenuItem value={t.tipofacturaid} key={t.tipofacturaid}>{t.nombre}</MenuItem>
                        )
                    }
                </Select>
            </FormControl>
            <FormControl style={{marginTop:15, marginBottom:15}} fullWidth>
                <InputLabel id="monedalabel">Moneda</InputLabel>
                <Select
                labelId="monedalabel"
                id="moneda"
                label="Moneda"
                size="small"
                name="monedaid"
                value={input.monedaid || ''}
                onChange={handleChange}
                style={{height:45}}
                MenuProps={{ keepMounted: true, disablePortal: true }}
                >
                <MenuItem value={0}>Seleccione una opcion</MenuItem>
                {
                    monedas && monedas.map((m) => 
                        <MenuItem value={m.monedaid} key={m.monedaid}>{m.nombre}</MenuItem>
                    )
                }
                </Select>
            </FormControl>
            <TextField 
                style={{marginTop:15, marginBottom:15}} 
                type="date"
                variant="outlined"
                name="fechafactura"
                label="Fecha de factura"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                value={input.fechafactura}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField
                style={{marginTop:15, marginBottom:15}}
                variant="outlined"
                label="Letra"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                name="letra"
                value={input.letra}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                style={{marginTop:15, marginBottom:15}}
                variant="outlined"
                label="Punto comprobante"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                name="punto_comprob"
                value={input.punto_comprob}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                style={{marginTop:15, marginBottom:15}}
                variant="outlined"
                label="N&uacute;mero comprobante"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                name="numero_comprob"
                value={input.numero_comprob}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                style={{marginTop:15, marginBottom:15}}
                variant="outlined"
                label="Observaci&oacute;n"  
                fullWidth margin="normal" 
                onChange={(e)=> handleChange(e)}
                name="observaciones"
                value={input.observaciones}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                multiline
                minRows={3}
            />
            <Divider/>
            <div align="right">
                <Button 
                size="large" 
                variant="contained" 
                onClick={(e)=>(handleClick(e))}
                sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>
    </Modal>
    )
}