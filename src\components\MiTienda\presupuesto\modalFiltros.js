import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, FormControl, InputAdornment, InputLabel, ListSubheader, MenuItem, Select, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getListadoClientesByName } from "../../../redux/actions/mitienda.actions";
import { Search } from "@mui/icons-material";

Modal.setAppElement("#root")

export const ModalFiltrosPresupuesto = ({
  show, 
  handleClose, 
  handleChangeNrocomprobante, 
  nrocomprobante, 
  setClienteId,
  handleChangeFechaDesde, 
  fechaDesde,
  handleChangeFechaHasta, 
  fechaHasta,
  search, 
  reset
}) =>{

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };

  const info = useSelector((state) => state.mitienda.clientesByName)

  const dispatch = useDispatch()

  const [nombre, setNombre] = useState('')
  const [selectedOption, setSelectedOption] = useState('');

  useEffect(() =>{
    if(nombre.length > 4){
        dispatch(getListadoClientesByName(nombre,''))
    }
  }, [nombre])

  return (
      show && 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
              <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                  <h4>FILTRAR</h4>
                  <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
              </div>
              <Divider/>
              <div style={{display:"flex", flexDirection:"column"}}>
                <FormControl fullWidth sx={{marginTop:2}} size="small">
                    <InputLabel id="search-select-label">Cliente</InputLabel>
                        <Select
                            style={{height:40}}
                            MenuProps={{ autoFocus: false,  disablePortal: true }}
                            labelId="search-select-label"
                            id="search-select"
                            value={selectedOption.clienteid || ''}
                            label="Cliente"
                            onClose={() => {
                                dispatch(getListadoClientesByName(""))
                            }}
                            size="small"
                            renderValue={() => selectedOption.nombre+' '+selectedOption.apellido}
                        >
                    <ListSubheader>
                    <TextField
                        size="small"
                        autoFocus
                        placeholder="Buscar cliente..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombre(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {info.map((option, i) => (
                        <MenuItem key={i} value={option.clienteid} onClick={() => {
                            setSelectedOption(option)
                            setNombre(option.nombre+' '+option.apellido)
                            setClienteId(option.clienteid)
                        }}>
                        {option.nombre+' '+option.apellido}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>
                <TextField 
                    style={{marginTop:20,marginBottom:20}}
                    label="Numero comprobante"  
                    variant="outlined"  
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nrocomprobante"
                    onChange={(e) => handleChangeNrocomprobante(e)}
                    value={nrocomprobante}
                />
                <div style={{display:"flex", flexDirection:"column"}}>
                    <h6>Fecha desde</h6>
                  <TextField 
                    style={{marginBottom:20}}
                    variant="outlined" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="fechaDesde"
                    onChange={(e) => handleChangeFechaDesde(e)}
                    value={fechaDesde}
                    type="date"
                  />
                </div>  
                <div style={{display:"flex", flexDirection:"column"}}>
                    <h6>Fecha hasta</h6>
                    <TextField 
                        style={{marginBottom:20}}
                        variant="outlined" 
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        name="fechaHasta"
                        onChange={(e) => handleChangeFechaHasta(e)}
                        value={fechaHasta}
                        type="date"
                    /> 
                </div>   
              </div>
              <div align="center">
              <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={search}
              >Aplicar filtros</Button>
              <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={() => {
                reset()
                setNombre('')
                setSelectedOption('')
                }}
              >Limpiar filtros</Button>
              </div>
      </Modal>
  )
}