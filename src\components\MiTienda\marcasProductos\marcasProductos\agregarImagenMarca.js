import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { But<PERSON>, TextField } from "@mui/material";
import { configMarcaTienda, postImagenMarca } from "../../../../redux/actions/mitienda.actions";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";

Modal.setAppElement("#root")

export const AgregarImagenMarca = ({show, handleClose, marca,  handleClickAlert}) =>{

  const [imagen, setImagen] = useState("")

  const dispatch = useDispatch()

  const handleClick = () => {
    dispatch(postImagenMarca(imagen, marca.marcaid))
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        maxHeight: "90vh",
        borderRadius: "8px",
        maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
    };

    useEffect(() => {
      setImagen("")
    },[])

    return (
        show ? 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
          <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
              <h4>Agregar imagen</h4>
              <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
          </div>
          <Divider/>
          <div style={{marginTop:15, marginBottom:15}}>
            <TextField 
              variant="standard"
              fullWidth margin="normal" 
              name="imagen"
              type="file"
              color="secondary"
              InputLabelProps={{
                  style: {
                      marginBottom:10,
                      marginTop: -7
                  },
              }}
              inputProps={{
                  style: {
                      height: 40,
                      fontSize:17
                  },
              }}
              onChange={(e) => setImagen(e.target.files[0])}
            />
          </div>
          <Divider/>
          <div align="right">
          <Button 
            disabled={imagen == ""}
            size="large" 
            variant="contained" 
            sx={{mt: 3}}
            onClick={handleClick}
            >Agregar</Button>
          </div>
        </Modal> : null 
    )
}