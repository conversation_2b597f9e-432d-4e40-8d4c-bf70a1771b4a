import React, { useEffect, useRef, useState } from "react"
import Modal from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { getDetalleCuentaCorriente, getDetalleCuentaCorrienteProveedor, getPagosCuentaCorriente, getPagosCuentaCorrienteProveedor } from "../../../../redux/actions/mitienda.actions";
import { useReactToPrint } from 'react-to-print';

Modal.setAppElement("#root")

export const ModalImprimir = ({show, handleClose, info}) =>{

    const detalle = useSelector((state) => state.mitienda.detalleCuentaCorrienteProveedor)
    const pagosRealizados = useSelector((state) => state.mitienda.pagosCuentaCorrienteProveedor)

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "950px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const conponentPDF= useRef();

    const generatePDF= useReactToPrint({
        content: ()=>conponentPDF.current,
        documentTitle:"DetalleCuentaCorrienteProveedor",
    });

    useEffect(() =>{
        if(info !== '' && info !== undefined){
            dispatch(getDetalleCuentaCorrienteProveedor(1,info.proveedorid))
            dispatch(getPagosCuentaCorrienteProveedor(1,info.proveedorid))
        }
    },[info])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Detalle cuenta corriente</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <TableContainer ref={conponentPDF} component={Paper} className='tabla' style={{padding:10}}>
                    <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell 
                                    colSpan={5}
                                    style={{fontWeight:"bold", fontSize:"90%", padding:2, backgroundColor:"#1976d2", color:"white"}} 
                                    align="center">
                                    Detalle de Cuenta Corriente del Proveedor {info.nombre}
                                </TableCell>
                            
                            </TableRow>
                        </TableHead>
                        <TableBody>
                                <TableRow
                                    key={info.clienteid}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0, borderBottom:1 } }}
                                >
                                    <TableCell 
                                        style={{fontWeight:"bold", fontSize:"100%", padding:10, height:40, width:200}} align="right">
                                        Importe a Favor: ${info.totalAFavor}
                                    </TableCell>
                                </TableRow>
                        </TableBody>
                    </Table>

                    <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell 
                                colSpan={7}
                                    style={{fontWeight:"bold", fontSize:"90%", padding:2, borderTop:1, backgroundColor:"#1976d2", color:"white"}} 
                                    align="center">
                                    Cuentas por cobrar
                                </TableCell>
                            
                            </TableRow>
                        </TableHead>
                        <TableHead>
                            <TableRow style={{backgroundColor:"#cccccc"}}>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:0}} align="center">
                                    Origen
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:0}} align="center">
                                    N&uacute;mero
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:0}} align="center">
                                    Nombre
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:0}} align="center">
                                    Fecha
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:0}} align="center">
                                    Monto operaci&oacute;n
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:0}} align="center">
                                    Monto afectado
                                </TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:0}} align="center">
                                    Monto deudor
                                </TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {
                            detalle.length > 0 && detalle.map((row) => 
                            <TableRow
                                key={row.clienteid}
                                sx={{ '&:last-child td, &:last-child th': { border: 0, borderBottom:1 } }}
                            >
                                <TableCell style={{fontSize:"80%", padding:0, height:40, width:100}} align="center">
                                    {row.origen}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                    {row.nro}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:200}} align="center">
                                    {row.nombre}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:50}} align="center">
                                    {row.fechaComprobante}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">
                                    ${row.totalcomprobante}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">
                                    ${row.totalafectado}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">
                                    ${row.totalrestanteafectar}
                                </TableCell>
                            </TableRow>
                        )}
                        </TableBody>
                        
                        <TableHead>
                            <TableRow>
                                <TableCell 
                                colSpan={7}
                                    style={{fontWeight:"bold", fontSize:"90%", padding:2, borderTop:1, backgroundColor:"#1976d2", color:"white"}} 
                                    align="center">
                                    Registro de Pagos
                                </TableCell>
                            
                            </TableRow>
                        </TableHead>
                        <TableHead>
                            <TableRow style={{backgroundColor:"#cccccc"}}>
                                <TableCell colSpan={2} style={{fontWeight:"bold", 
                                    fontSize:"80%"
                                }} align="center">Fecha</TableCell>
                                <TableCell style={{fontWeight:"bold", 
                                    fontSize:"80%"
                                }} align="center">Nro recibo</TableCell>
                                <TableCell style={{fontWeight:"bold", 
                                    fontSize:"80%",
                                    width:200
                                }} align="center">Tipo pago</TableCell>
                                <TableCell style={{fontWeight:"bold", 
                                    fontSize:"80%"
                                }} align="center">Monto</TableCell>
                                <TableCell colSpan={2} style={{fontWeight:"bold", 
                                    fontSize:"80%"
                                }} align="center">Inter&eacute;s</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {pagosRealizados && pagosRealizados.map((row) => (
                            <TableRow
                                key={row.pagosfacturacompraid}
                                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                            >
                                <TableCell 
                                    colSpan={2}
                                    style={{ffontSize:"80%", width:200}} align="center">
                                    {row.fechapago}
                                </TableCell>
                                <TableCell 
                                    style={{fontSize:"80%", width:200}} align="center">
                                    {row.numerorecibo}
                                </TableCell>
                                <TableCell 
                                    style={{fontSize:"80%"}} align="center">
                                    {row.tipopago}
                                </TableCell>
                                <TableCell 
                                    style={{fontSize:"80%"}} align="center">
                                    ${row.montoabono}
                                </TableCell>
                                <TableCell 
                                    colSpan={2}
                                    style={{fontSize:"80%"}} align="center">
                                    ${row.intereses}
                                </TableCell>
                            </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="contained" 
                    onClick={handleClose}
                    sx={{mt: 3, mr:2}}>Cerrar</Button>
                    <Button size="large"
                    variant="contained" 
                    onClick={generatePDF}
                    sx={{mt: 3}}>Imprimir</Button>
                </div>
        </Modal>
    )
}