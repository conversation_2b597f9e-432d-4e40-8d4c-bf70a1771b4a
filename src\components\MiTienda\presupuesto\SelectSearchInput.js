import React from "react"
import './ofertas.css'

export const SelectSearchInput = ({info,value,setValue,setProductoId,open, setOpen, setProductDetail}) =>{

    return (
        <div className="input-grup-ofertas">
            <input onClick={(e)=> {
                setOpen(true)
            }} value={value} onChange={(e)=> setValue(e.target.value)}/>
            <div className="list-info"  > 
                {
                    info.length > 0 && open ? 
                    info.map((i) =>
                        <div className="menu-items" onClick={(e)=> {
                            setOpen(false)
                            setProductoId(i.codigoproducto)
                            setValue(i.nombreproducto)
                            setProductDetail(i)
                        }}>
                            {i.nombreproducto}
                        </div>
                    ) : null
                }
            </div>  
        </div>
    )
}