import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';
import { 
  editarProveedorTienda, 
  getCondicionesIVA, 
  getDescuentosSinPaginado, 
  getTiposDocumentoTienda 
} from "../../../redux/actions/mitienda.actions";
import { useDispatch, useSelector } from "react-redux";
import { 
  Button, 
  Checkbox, 
  Divider, 
  FormControl, 
  FormControlLabel, 
  InputLabel, 
  MenuItem, 
  Select, 
  TextField 
} from "@mui/material";

Modal.setAppElement("#root")

export const ModalEditarProveedor = ({show, 
  handleClose, 
  proveedor,
  handleClickAlert
}) =>{

  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      height: "70vh",
      borderRadius: "8px",
      maxWidth: "650px",
      right: '50px',
    //   backgroundColor: "#D1D1D1"
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  const tiposDocumentos = useSelector((state) => state.mitienda.tiposDocumentos)
  const condicionesIva = useSelector((state) => state.mitienda.condicionesIva)

  let pathname = window.location.pathname 
  let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
  .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

  const dispatch = useDispatch()

  const [input, setInput] = useState('')

  const handleChange = (e) => {
      e.preventDefault()
      setInput({
          ...input,
          [e.target.name]: e.target.value
      })
  }

  const handleCheck = (e) =>{
    e.preventDefault()
    if(e.target.checked === true){
        setInput({...input, [e.target.name] : "1"})
    }else{
        setInput({...input, [e.target.name] : "0"})
    }
  }

  const handleOnClick = () => {
    dispatch(editarProveedorTienda(input))
    setTimeout(function(){
      handleClickAlert()
    }, 1000);
    handleClose()
  }

  useEffect(() =>{
    dispatch(getTiposDocumentoTienda())
    dispatch(getDescuentosSinPaginado())
    dispatch(getCondicionesIVA())
  },[])

  useEffect(() =>{
    setInput(proveedor)
    
  },[proveedor])

  return (
      show ? 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
            <h4>Editar proveedor</h4>
            <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
        </div>
        <Divider style={{marginBottom:15}}/>
        <TextField 
          label="Nombre"  
          name="nombre"
          value={input.nombre || ''}
          fullWidth margin="normal" 
          onChange={(e)=> handleChange(e)}
          InputLabelProps={{
            style: {
              marginBottom:10,
              marginTop: -7
            },
          }}
          inputProps={{
              style: {
              height: 40,
              fontSize:17
            },
          }}
        />
        <FormControl fullWidth sx={{mb:2, mt:2}}>
          <InputLabel id="tipodocumento-select-label">Tipo documento</InputLabel>
          <Select
          labelId="tipodocumento-select-label"
          label="Tipo documento"
          size="small"
          name="tipodocidentidadid"
          value={input.tipodocidentidadid || ''}
          onChange={(e) => handleChange(e)}
          color="secondary"
          MenuProps={{ keepMounted: true, disablePortal: true }}
          >
          <MenuItem value="">Seleccione una opcion</MenuItem>
          {               
              tiposDocumentos && tiposDocumentos.map((t) =>
                <MenuItem value={t.tipodocidentidadid} key={t.tipodocidentidadid}>{t.nombre}</MenuItem>
              )
          }
          </Select>
        </FormControl>
        <TextField 
          label="N&uacute;mero de documento"  
          name="cuit"
          value={input.cuit || ''}
          fullWidth margin="normal" 
          onChange={(e)=> handleChange(e)}
          InputLabelProps={{
            style: {
              marginBottom:10,
              marginTop: -7
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize:17
            },
          }}
        />
        <TextField 
          label="Tel&eacute;fono"  
          name="telefono"
          value={input.telefono || ''}
          fullWidth margin="normal" 
          onChange={(e)=> handleChange(e)}
          InputLabelProps={{
            style: {
              marginBottom:10,
              marginTop: -7
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize:17
            },
          }}
        />
        <TextField 
          label="Email"  
          name="email"
          value={input.email || ''}
          fullWidth margin="normal" 
          onChange={(e)=> handleChange(e)}
          InputLabelProps={{
            style: {
              marginBottom:10,
              marginTop: -7
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize:17
            },
          }}
        />
        <TextField 
          label="Email facturaci&oacute;n"  
          name="emailfactura"
          value={input.emailfactura || ''}
          fullWidth margin="normal" 
          onChange={(e)=> handleChange(e)}
          InputLabelProps={{
            style: {
              marginBottom:10,
              marginTop: -7
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize:17
            },
          }}
        />
        <FormControl fullWidth sx={{mb:2, mt:2}}>
          <InputLabel id="condicioniva-select-label">Condicion IVA</InputLabel>
          <Select
          labelId="condicioniva-select-label"
          label="Condicion IVA"
          size="small"
          name="tipocondicionivaid"
          value={input.tipocondicionivaid || ''}
          onChange={(e) => handleChange(e)}
          color="secondary"
          MenuProps={{ keepMounted: true, disablePortal: true }}
          >
          <MenuItem value="">Seleccione una opcion</MenuItem>
          {               
              condicionesIva && condicionesIva.map((t) =>
                <MenuItem value={t.tipocondicionivaid} key={t.tipocondicionivaid}>{t.nombre}</MenuItem>
              )
          }
          </Select>
        </FormControl>
        <TextField 
          label="Descuento"  
          name="descuento"
          value={input.descuento || ''}
          fullWidth margin="normal" 
          onChange={(e)=> handleChange(e)}
          InputLabelProps={{
            style: {
              marginBottom:10,
              marginTop: -7
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize:17
            },
          }}
        />
        {
          permisos_acciones.activar === "1" ?
          <div style={{marginTop:15, marginBottom:5}}>
            <FormControlLabel
              control={
              <Checkbox/>
              }
              style={{color:"black"}}
              onChange={(e)=> handleCheck(e)}
              checked={input.activo === "1" ? true : false}
              label="Activo"
              name="activo"
            />
          </div> : null
        }
        <div align="right">
          <Button 
            size="large" 
            variant="contained" 
            onClick={(e)=>(handleOnClick(e))}
            sx={{mt: 3, mr:3}}>Guardar
          </Button>
        </div>
      </Modal> : null
  )
}