import React, { useEffect, useState } from "react"
import Modal from 'react-modal';
import { useDispatch, useSelector } from "react-redux";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Delete, Search } from "@mui/icons-material";
import { agregarCategoriaRentabilidad, eliminarCategoriaRentabilidad, getCategoriasByName, getCategoriasRentabilidad } from "../../../../redux/actions/mitienda.actions";
import { ClipLoader } from "react-spinners";
import { Button, Divider, FormControl, InputAdornment, InputLabel, ListSubheader, MenuItem, Pagination, Paper, Select, Snackbar, TextField } from "@mui/material";
import MuiAlert from '@mui/material/Alert';

Modal.setAppElement("#root")

export const ModalCategoriasRentabilidad = ({show, handleClose, rentabilidadid}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "70%",
          height: "85vh",
          borderRadius: "8px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const info = useSelector((state) => state.mitienda.rentabilidadCategorias)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaCategoriaRentabilidad)
    const okEliminar = useSelector((state) => state.mitienda.okEliminarCategoriaRentabilidad)
    const ok = useSelector((state) => state.mitienda.okCategoriaRentabilidad)
    const categorias = useSelector((state) => state.mitienda.categoriasByName)

    const [nombreCategoria, setNombreCategoria] = useState('')
    const [selectedOption, setSelectedOption] = useState('');

    const dispatch = useDispatch()
    
    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const handleEliminar = (row) => {
        dispatch(eliminarCategoriaRentabilidad(rentabilidadid.rentabilidadcatid, row.categoriaid))
        setTimeout(function(){
            handleClickAlertEliminar()
        }, 2000);
    }

    const handleAgregar = () => {
        dispatch(agregarCategoriaRentabilidad(
            rentabilidadid.rentabilidadcatid, 
            selectedOption.categoriaid
        ))
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    }


    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openEliminar, setOpenEliminar] = useState(false)
    const handleClickAlertEliminar = () => {
      setOpenEliminar(true);
    };
    const handleCloseAlertEliminar = () => {
        setOpenEliminar(false);
    };

    useEffect(() =>{
        if(nombreCategoria.length > 2){
            dispatch(getCategoriasByName(nombreCategoria))
        }
    }, [nombreCategoria])

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() => {
        if(show){
            setLoading(true)
            dispatch(getCategoriasRentabilidad(page,rentabilidadid.rentabilidadcatid))
        }
    },[show, ok, okEliminar, page])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <Snackbar
                open={open} 
                autoHideDuration={10000} onClose={handleCloseAlert}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlert} 
                    severity={ok.success ? "success" : "error"}
                    sx={{ width: 400 }}>
                    <h5>{ok.mensaje}</h5>
                </Alert>
            </Snackbar>
            <Snackbar
                open={openEliminar} 
                autoHideDuration={10000} onClose={handleCloseAlertEliminar}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertEliminar} 
                    severity={okEliminar.success ? "success" : "error"}
                    sx={{ width: 400 }}>
                    <h5>{okEliminar.mensaje}</h5>
                </Alert>
            </Snackbar>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Rentabilidad - Listado categor&iacute;as</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            
            <div style={{display:"flex", justifyContent:"center", alignItems:"center"}}>
                <FormControl sx={{marginTop:2, marginBottom:2, width:"70%",marginLeft:3}}>
                    <InputLabel id="search-select-label">Categor&iacute;as</InputLabel>
                    <Select
                    // Disables auto focus on MenuItems and allows TextField to be in focus
                    MenuProps={{ autoFocus: false,  disablePortal: true }}
                    labelId="search-select-label"
                    id="search-select"
                    value={selectedOption.categoriaid || ''}
                    label="Categor&iacute;as"
                    onClose={() => {
                        dispatch(getCategoriasByName(""))
                    }}
                    size="small"
                    // This prevents rendering empty string in Select's value
                    // if search text would exclude currently selected option.
                    renderValue={() => selectedOption.nombrecategoria}
                    >
                    {/* TextField is put into ListSubheader so that it doesn't
                        act as a selectable item in the menu
                        i.e. we can click the TextField without triggering any selection.*/}
                    <ListSubheader>
                    <TextField
                        size="small"
                        // Autofocus on textfield
                        autoFocus
                        placeholder="Buscar categor&iacute;a..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreCategoria(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {categorias.map((option, i) => (
                        <MenuItem key={i} value={option.categoriaid} onClick={() => {
                            setSelectedOption(option)
                            setNombreCategoria(option.nombrecategoria)
                        }}>
                        {option.nombrecategoria}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>
                <Button 
                    variant="contained" 
                    style={{marginLeft:20,padding:10}}
                    onClick={() => {
                        handleAgregar()
                        setSelectedOption('')
                        setNombreCategoria('')
                        dispatch(getCategoriasByName(""))
                    }}
                >
                    agregar categor&iacute;a
                </Button>
            </div>

            <div style={{display:"flex", flexDirection:"column", justifyContent:"center", marginTop:30}}>
                <TableContainer sx={{width:"90%", alignSelf:"center", marginLeft:3}} component={Paper}>
                    {
                        info.length === 0 ?
                        <h5 style={{display:"flex", justifyContent:"center"}}>No hay datos</h5> :
                        <Table aria-label="simple table" size="small">
                            <TableHead>
                                <TableRow>
                                    <TableCell style={{fontWeight:"bold", 
                                        fontSize:"110%"
                                    }} align="center">Nombre</TableCell>
                                    <TableCell style={{fontWeight:"bold", 
                                        fontSize:"110%",
                                    }} align="center">Eliminar</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                            {
                                loading ? 
                                <TableRow sx={{p:20}}>
                                    <TableCell colSpan={2} align='center'>
                                        <ClipLoader 
                                            loading={loading}
                                            size={50}
                                        />
                                    </TableCell>
                                </TableRow> :
                                info && info.map((row) => (
                                <TableRow
                                    key={row.categoriaid}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                >
                                    <TableCell 
                                        style={{fontSize:"16px", width:"200px"}} align="center">
                                        {row.nombre_categoria || ''}
                                    </TableCell>
                                    <TableCell style={{fontSize:"100%", width:"50px"}} align="center" 
                                    onClick={(e)=>handleEliminar(row)}><div><Delete/></div></TableCell>
                                </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    }
                </TableContainer>
                <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", mt:5}} />
            </div>
        </Modal>
    )
}