function matchCondicionPago (proveedor,condiciones){
    let condicionid = condiciones.filter((c) => c.nombre === proveedor.tipocondicionpago )

    return condicionid[0].tipocondicionpagoid
}

function matchCondicionIva (proveedor,condiciones){
    let condicionid = condiciones.filter((c) => c.nombre === proveedor.tipocondicioniva )

    return condicionid[0].tipocondicionivaid
}

function matchTipoDocumento (proveedor,condiciones){
    let condicionid = condiciones.filter((c) => c.nombre === proveedor.tipodocidentidad )

    return condicionid[0].tipodocidentidadid
}

module.exports = {
    matchCondicionPago,
    matchCondicionIva,
    matchTipoDocumento
}