import React, { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { getSubdiarioCompras } from '../../../../redux/actions/mitienda.actions'
import { ModalFiltrosSubdiarioCompras } from './modalFiltrosSubdiarioCompras';
import { calcularTotales } from './utilsCompras';
import ClipLoader from "react-spinners/ClipLoader";

export const SubdiarioCompras = () => {

    //Obtengo la informacion para la tabla 
    const info = useSelector((state) => state.mitienda.subdiarioCompras)

    //Obtengo la ultima pagina para el componente Pagination
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaSubdiarioComprasPaginado)
    //Creo la constante que usare para cambiar las paginas y su handle change
    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };
    // const totales = calcularTotales(info)
    //Declaro la variable para despachar acciones
    const dispatch = useDispatch()

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    //Manejo el abrir y cerrar Modal de Filtros
    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    //Declaro las constantes que utilizare para filtrar la informacion
    const [fecha_desde, setFecha_desde] = useState(0)
    const handleChangeFecha_desde = (e) =>{
        setFecha_desde(e.target.value)
    }

    const [fecha_hasta, setFecha_hasta] = useState(0)
    const handleChangeFecha_hasta = (e) =>{
        setFecha_hasta(e.target.value)
    }

    const formatoFecha = (fecha) =>{
        let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
    }

    //Funcion para el boton Aplicar filtrps
    const searchByFilters = () =>{
        setPage(1)
        dispatch(getSubdiarioCompras(fecha_desde, fecha_hasta, page))
        handleClose()
    }


    //Funcion para el boton Limpiar filtros, se reinician las constantes a sus valores iniciales
    const reset = (e) =>{
        setFecha_desde(0)
        setFecha_hasta(0)

    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() => {
        setLoading(true)
        dispatch(getSubdiarioCompras(fecha_desde, fecha_hasta, page))
    },[page]) 

    return (
        <div className="notificaciones-container">
            {
                <ModalFiltrosSubdiarioCompras
                    show={show} 
                    handleClose={handleClose}
                    search={searchByFilters}
                    reset={reset}
                    fecha_desde={fecha_desde}
                    handleChangeFecha_desde={handleChangeFecha_desde}
                    fecha_hasta={fecha_hasta}
                    handleChangeFecha_hasta={handleChangeFecha_hasta}

                />
            }
            <div>
                <header className="titulo-notificaciones">
                    <h3>Reporte: Subdiario Compras</h3>
                    <Button 
                        variant="outlined" 
                        sx={{height:40,width:150}}
                        onClick={handleShow}>
                        <FilterListIcon/> Filtrar
                    </Button>
                </header>
            </div>
            {
                permisos_acciones?.listar !== "0" && 
                <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                    <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3}}>
                        <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Fecha</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Comprobante</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Razon Social</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">CUIT</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Gravado</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">IVA</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">No Gravado</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Otros Impuestos</TableCell>
                                {/* <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Monto</TableCell> */}
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Total</TableCell>
    
                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {   
                            loading ? 
                            <TableRow sx={{p:20}}>
                                <TableCell colSpan={13} align='center'>
                                    <ClipLoader 
                                        loading={loading}
                                        size={50}
                                    />
                                </TableCell>
                            </TableRow> :
                            info.data.length > 0 ? info.data.map((row) => (
                            <TableRow
                                key={row.id}
                                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                            >
                                <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                                    {formatoFecha(row.fechafactura)}
                                </TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:150}} align="center">{row.numero}</TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.razonsocial}</TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.cuit}</TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">{parseFloat(row.baseimp21).toFixed(2)}</TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.importeiva21.toFixed(2)}</TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.nogravado.toFixed(2)}</TableCell>
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.otrosimpuestos.toFixed(2)}</TableCell>
                                {/* <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.monto.toFixed(2)}</TableCell> */}
                                <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">{row.totalGravado.toFixed(2)}</TableCell>
    
                            </TableRow>
                            )):
                            <TableRow>
                                <TableCell colSpan={10}>
                                    <h5>No hay informaci&oacute;n para mostrar</h5>
                                </TableCell>
                            </TableRow>
                            }
    
                        <TableRow>
                            <TableCell colSpan={3}/>
                            <TableCell colSpan={1} style={{fontWeight:700, fontSize:"100%"}}>Total</TableCell>
                            <TableCell align="center" style={{fontWeight:700, fontSize:"100%"}}>{info.totales.total_gravado}</TableCell>
                            <TableCell align="center" style={{fontWeight:700, fontSize:"100%"}}>{info.totales.total_iva}</TableCell>
                            <TableCell align="center" style={{fontWeight:700, fontSize:"100%"}}>{info.totales.total_nogravado}</TableCell>
                            <TableCell align="center" style={{fontWeight:700, fontSize:"100%"}}>{info.totales.total_otros}</TableCell>
                            <TableCell align="center" style={{fontWeight:700, fontSize:"100%"}}>{info.totales.total_gral}</TableCell>
                        </TableRow>
    
                        </TableBody>
                        </Table>
                    </TableContainer>
                    <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", marginTop:5}} />
                </div>
            }
        </div>
    )
}