.home{
    background-color: #ECF9FF;
    width: auto;    
    height: 100vh;    
    background-position: center center;    
    background-repeat: no-repeat;    
    background-size: cover;  
    background-attachment: fixed;
    overflow:auto;
    padding: 20px;
}

@media screen and (max-width: 600px ){
    .home{
        width: 100%;
        display: flex;
        justify-content: center;
        padding-bottom: 40px;
    }

    .listIcons{
        padding: 20px;
    }
}

.listIcons{
    display: flex;
    justify-content: center;
    flex-direction: row;
    flex-wrap: wrap;
    float: left;
    padding-top: 80px;
    padding-right:40px;
    padding-left: 40px;
    padding-bottom: 40px;
}

.icon{
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ebebeb;
}

.card-home{
    width: 350px;
    height: 120px;
    border-radius: 20px;
    padding: 5px;
    box-shadow: rgba(151, 65, 252, 0.2) 0 15px 30px -5px;
    position: relative;
    margin: 20px;
    margin-left: 60px;
    margin-right: 60px;
    border-radius: 5px;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.link-home{
    color: white
}

.icon:hover{
    color:white
}

.blue{
    background-color: #7286D3;
}

.red{
    background-color: #FD5D5D;
}

.green{
    background-image: linear-gradient(144deg,#AF40FF, #5B42F3 50%,#00DDEB);
}

.purple{
    background-color: #AC7D88;
}

.orange{
    background-color: #FEB139;
}

.pink{
    background-color: #E78EA9;
}

.purple2{
    background-color: #C060A1;
}

.grey{
    background-color: #BDCDD6;
}

.black{
    background-color: #2A2F4F;
}

.computer{
    background-color: #19A7CE;
}


.flip-card {
    background-color: transparent;
    width: 350px;
    height: 120px;
    perspective: 1000px;
    font-family: sans-serif;
    margin: 20px;
    margin-left: 60px;
    margin-right: 60px;
}

.title-card {
    font-size: 1.5em;
    font-weight: 900;
    text-align: center;
    margin: 0;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front-orange, .flip-card-back-orange,
.flip-card-front-grey, .flip-card-back-grey,
.flip-card-front-green, .flip-card-back-green,
.flip-card-front-black, .flip-card-back-black,
.flip-card-front-blue, .flip-card-back-blue,
.flip-card-front-default, .flip-card-back-default {
    box-shadow: 0 8px 14px 0 rgba(0,0,0,0.2);
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 1rem;
}

.flip-card-front-orange {
    background: #FEB139;
    color: white;
}

.flip-card-back-orange {
    background: #FEB139;
    color: white;
    transform: rotateY(180deg);
}

.flip-card-front-grey {
    background: #BDCDD6;
    color: white;
}

.flip-card-back-grey {
    background: #BDCDD6;
    color: white;
    transform: rotateY(180deg);
}

.flip-card-front-green {
    background: #38E54D;
    color: white;
}

.flip-card-back-green {
    background: #38E54D;
    color: white;
    transform: rotateY(180deg);
}

.flip-card-front-black {
    background: #2A2F4F;
    color: white;
}

.flip-card-back-black {
    background: #2A2F4F;
    color: white;
    transform: rotateY(180deg);
}

.flip-card-front-blue {
    background-color: #7286D3;
    color: white;
}

.flip-card-back-blue {
    background-color: #7286D3;
    color: white;
    transform: rotateY(180deg);
}

.flip-card-front-default {
    background-color: #19A7CE;
    color: white;
}

.flip-card-back-default {
    background-color: #19A7CE;
    color: white;
    transform: rotateY(180deg);
}