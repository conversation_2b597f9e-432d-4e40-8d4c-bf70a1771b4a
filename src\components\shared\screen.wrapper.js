import React, { useState } from 'react'
import { useDispatch } from 'react-redux'
import { StyledInput, StyledSelect } from './inputs/styled-inputs'
import StyledTable from './table/styled-table'
import Spinner from './spinner/spinner'

const ScreenWrapper = ({ data, onRemove, Form }) => {
  const [form, setForm] = useState({ edit: false, open: false })
  const [keyword, setKeyword] = useState('')

  const dispatch = useDispatch()

  const fields = Object.keys(data?.list[0] || {})

  const handleRemove = (doc) => {
    dispatch({
      type: 'SET_POPUP',
      payload: {
        message: 'Are you sure?',
        detail: 'Do you really want to delete?',
        confirm: () => onRemove(doc),
      },
    })
  }
  return (
    <div className="fadein column scroll-area h-100">
      <div className="d-flex align-items-center flex-wrap">
        <StyledInput
          style={{ marginRight: '20px', width: '400px' }}
          type="text"
          placeholder="Buscar"
          icon="fas fa-search"
          actionVariant="light"
          onChange={(e) => setKeyword(e.target.value)}
        />
        <StyledSelect style={{ marginRight: '20px' }}>
          <option value=""> -- key</option>
          {fields.map((key) => (
            <option key={key} value={key}>
              {key}
            </option>
          ))}
        </StyledSelect>
        <StyledSelect style={{ marginRight: '20px' }}>
          <option value=""> -- sort by </option>
          {fields.map((key) => (
            <option key={key} value={key}>
              {key}
            </option>
          ))}
        </StyledSelect>
        <StyledSelect style={{ marginRight: '20px' }}>
          <option value="">Lastupdate</option>
        </StyledSelect>
        <button
          style={{ marginRight: '20px' }}
          className="btn btn-light"
          onClick={() => setForm({ open: true, edit: false })}
        >
          + Add New
        </button>
      </div>
      {data.loading ? (
        <Spinner />
      ) : data.error ? (
        <div className="center-all">
          <i className="fas fa-bug text-danger display-3 mb-4" />
          <p className="strong-text text-danger display-6 mb-2">REQUEST FAIL</p>
          <p className="text-danger">{data.error}</p>
        </div>
      ) : data.list.length ? (
        <StyledTable
          data={data.list}
          headers={fields}
          properties={fields}
          edit={(edit) => setForm({ edit, open: true })}
          remove={handleRemove}
          pagginate
        />
      ) : (
        <div className="center-all">
          <i className="fas fa-folder-open text-warning display-3 mb-4" />
          <p className="strong-text text-warning">NO FILES FOUND</p>
        </div>
      )}
      {form.open && (
        <Form
          edit={form.edit}
          close={() => setForm({ edit: false, open: false })}
        />
      )}
    </div>
  )
}

export default ScreenWrapper
