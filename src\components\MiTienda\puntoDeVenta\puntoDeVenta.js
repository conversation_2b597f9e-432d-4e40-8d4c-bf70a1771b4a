//f with focus on 'Pedido' by default and dinamic tab rendering

import * as React from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import { NuevaVenta } from "./nuevaVenta";
import { Presupuesto } from "./nuevoPresupuesto";
import { NotaDeCredito } from "./notaDeCredito";
import { Remito } from "./remito";
import { Pedido } from "./nuevoPedido";
import { NotaDeDebito } from "./notaDeDebito";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { getDatosTienda } from "../../../redux/actions/mitienda.actions";

export const PuntoDeVenta = () => {
  const dispatch = useDispatch();
  const datosTienda = useSelector((state) => state.mitienda.datostienda);

  useEffect(() => {
    dispatch(getDatosTienda());
  }, [dispatch]);

  const [value, setValue] = React.useState("factura_pedido");
  const [facturaCreada, setFacturaCreada] = React.useState({
    ok: false,
    tipo: "",
  });

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  if (!datosTienda) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container-punto-de-venta">
      <Tabs
        value={value}
        onChange={handleChange}
        textColor="primary"
        indicatorColor="primary"
        aria-label="primary tabs example"
      >
        <Tab
          value="factura_pedido"
          label="Pedido"
          disabled={facturaCreada.ok && facturaCreada.tipo !== "factura_pedido"}
        />
        {datosTienda.default_venta === "1" && (
          <Tab
            value="factura_normal"
            label="Venta"
            disabled={
              facturaCreada.ok && facturaCreada.tipo !== "factura_normal"
            }
          />
        )}
        {datosTienda.default_presupuesto === "1" && (
          <Tab
            value="factura_presupuesto"
            label="Presupuesto"
            disabled={
              facturaCreada.ok && facturaCreada.tipo !== "factura_presupuesto"
            }
          />
        )}
        {datosTienda.default_notacredito === "1" && (
          <Tab
            value="factura_notacredito"
            label="Nota de cr&eacute;dito"
            disabled={
              facturaCreada.ok && facturaCreada.tipo !== "factura_notacredito"
            }
          />
        )}
        {datosTienda.default_notacredito === "1" && (
          <Tab
            value="factura_notadebito"
            label="Nota de d&eacute;bito"
            disabled={
              facturaCreada.ok && facturaCreada.tipo !== "factura_notadebito"
            }
          />
        )}
        {datosTienda.default_remito === "1" && (
          <Tab
            value="factura_remito"
            label="Remito"
            disabled={
              facturaCreada.ok && facturaCreada.tipo !== "factura_remito"
            }
          />
        )}
      </Tabs>
      {value === "factura_normal" ? (
        <Pedido
          tipofactura={value}
          tipocomprobante={"124"}
          setOk={setFacturaCreada}
        />
      ) : value === "factura_presupuesto" ? (
        <Presupuesto
          tipofactura={value}
          tipocomprobante={"128"}
          setOk={setFacturaCreada}
        />
      ) : value === "factura_notacredito" ? (
        <NotaDeCredito
          tipofactura={value}
          tipocomprobante={"128"}
          setOk={setFacturaCreada}
        />
      ) : value === "factura_notadebito" ? (
        <NotaDeDebito
          tipofactura={value}
          tipocomprobante={"128"}
          setOk={setFacturaCreada}
        />
      ) : value === "factura_remito" ? (
        <Remito
          tipofactura={value}
          tipocomprobante={"130"}
          setOk={setFacturaCreada}
        />
      ) : (
        <NuevaVenta
          tipofactura={value}
          tipocomprobante={"128"}
          setOk={setFacturaCreada}
        />
      )}
    </div>
  );
};

//w original
// import * as React from 'react';
// import Tabs from '@mui/material/Tabs';
// import Tab from '@mui/material/Tab';
// import { NuevaVenta } from './nuevaVenta';
// import { Presupuesto } from './nuevoPresupuesto';
// import { NotaDeCredito } from './notaDeCredito';
// import { Remito } from './remito';
// import { Pedido } from './nuevoPedido';
// import { NotaDeDebito } from './notaDeDebito';

// export const PuntoDeVenta = () => {
//   const [value, setValue] = React.useState('factura_pedido');

//   const handleChange = (event, newValue) => {
//     setValue(newValue);
//   };

//   const [facturaCreada, setFacturaCreada] = React.useState({
//     ok: false,
//     tipo: ''
//   })

//   return (
//     <div className='container-punto-de-venta'>
//     <Tabs
//       value={value}
//       onChange={handleChange}
//       textColor="primary"
//       indicatorColor="primary"
//       aria-label="primary tabs example"
//     >
//       <Tab
//         value="factura_normal"
//         label="Venta"
//         disabled={facturaCreada.ok && facturaCreada.tipo !== "factura_normal"}
//       />
//       <Tab
//         value="factura_presupuesto"
//         label="Presupuesto"
//         disabled={facturaCreada.ok && facturaCreada.tipo !== "factura_presupuesto"}
//       />
//       <Tab
//         value="factura_notacredito"
//         label="Nota de cr&eacute;dito"
//         disabled={facturaCreada.ok && facturaCreada.tipo !== "factura_notacredito"}
//       />
//       <Tab
//         value="factura_notadebito"
//         label="Nota de d&eacute;bito"
//         disabled={facturaCreada.ok && facturaCreada.tipo !== "factura_notadebito"}
//       />
//       <Tab
//         value="factura_remito"
//         label="Remito"
//         disabled={facturaCreada.ok && facturaCreada.tipo !== "factura_remito"}
//       />
//       <Tab
//         value="factura_pedido"
//         label="Pedido"
//         disabled={facturaCreada.ok && facturaCreada.tipo !== "factura_pedido"}
//       />
//     </Tabs>
//     {
//       value === "factura_normal" ?
//       <Pedido tipofactura={value} tipocomprobante={"124"} setOk={setFacturaCreada}/> :
//       value === "factura_presupuesto" ?
//       <Presupuesto tipofactura={value} tipocomprobante={"128"} setOk={setFacturaCreada}/> :
//       value === "factura_notacredito" ?
//       <NotaDeCredito tipofactura={value} tipocomprobante={"128"} setOk={setFacturaCreada}/> :
//       value === "factura_notadebito" ?
//       <NotaDeDebito tipofactura={value} tipocomprobante={"128"} setOk={setFacturaCreada}/> :
//       value === "factura_remito" ?
//       <Remito tipofactura={value} tipocomprobante={"130"} setOk={setFacturaCreada}/> :
//       <NuevaVenta tipofactura={value} tipocomprobante={"128"} setOk={setFacturaCreada}/>
//     }
//     </div>
//   );
// }
