import React, { useEffect } from "react"
import Modal from 'react-modal';
import { useDispatch, useSelector } from "react-redux";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';

Modal.setAppElement("#root")

export const ModalVerDirecciones = ({show, handleClose, info}) =>{

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "950px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Direcciones</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <TableContainer sx={{width:"95%", alignSelf:"center", marginLeft:3}}>
                {
                    info.length === 0 ?
                    <h5 style={{display:"flex", justifyContent:"center"}}>No hay datos</h5> :
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Tipo direcci&oacute;n</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Provincia</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Ciudad</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%",
                                width:200
                            }} align="center">Calle</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Altura</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">CP</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {info && info.map((row) => (
                        <TableRow
                            key={row.pagosid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                            <TableCell 
                                style={{fontSize:"14px", width:200}} align="center">
                                {row.tipodireccion}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                {row.provincia}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                {row.ciudad}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                {row.direccion}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                {row.altura}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                {row.codigopostal}
                            </TableCell>
                        </TableRow>
                        ))}
                    </TableBody>
                </Table>
                }
            </TableContainer>
        </Modal>
    )
}