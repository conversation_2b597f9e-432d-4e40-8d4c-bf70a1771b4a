import { TextField } from '@material-ui/core';
import { Check, Clear, Delete, Edit, FormatListBulleted } from '@mui/icons-material';
import React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { agregarProductoAlHome, getProductosByNameSkuNuevaVenta, getProductosHome } from '../../../redux/actions/mitienda.actions';
import { useSelector } from 'react-redux';
import Pagination from '@mui/material/Pagination';
import { useState } from 'react';
import { Button, MenuItem, Select, Snackbar } from '@mui/material';
import MuiAlert from '@mui/material/Alert';
import { ModalEliminarProductoHome } from './eliminarProductoHome';
import { ClipLoader } from 'react-spinners';

export const ProductosHome = () => {
    
    const info = useSelector((state) => state.mitienda.productosHome)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaProductosHomn)
    const okCrear = useSelector((state) => state.mitienda.okAgregarProductoHome)
    const okEliminar = useSelector((state) => state.mitienda.okEliminarProductoHome)
    const productsByNameSku = useSelector((state) => state.mitienda.productosNuevaVenta)

    const dispatch = useDispatch()

    const [paraEditar, setParaEditar] = useState('')
    const [product, setProduct] = useState("")
    const [input, setInput] = useState('')

    const handleChange = (e) => {
        e.preventDefault()
        setInput(e.target.value)
    }

    const [showEliminar, setShowEliminar] = useState(false);
    const handleCloselimintar = () => setShowEliminar(false);
    const handleShowEliminar = (e,row) => {
        e.preventDefault()
        setShowEliminar(true);
        setParaEditar(row)
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [openAgregar, setOpenAgregar] = React.useState(false);
    const handleClickAlertAgregar = () => {
      setOpenAgregar(true);
    };
    const handleCloseAlertAgregar = () => {
        setOpenAgregar(false);
    };

    const [openEliminar, setOpenEliminar] = React.useState(false);
    const handleClickAlertEliminar = () => {
      setOpenEliminar(true);
    };
    const handleCloseAlertEliminar = () => {
        setOpenEliminar(false);
    };

    const handleClickAgregar = () => {
        dispatch(agregarProductoAlHome(product))
        setTimeout(function(){
            handleClickAlertAgregar()
        }, 1000);
        setInput('')
        dispatch(getProductosByNameSkuNuevaVenta('')) 
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    useEffect(() =>{
        if(input.length > 4){
            dispatch(getProductosByNameSkuNuevaVenta(input)) 
        }
    }, [input])

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        setLoading(true)
        dispatch(getProductosHome(page))
    },[page,okCrear,okEliminar])

    return (
        <div className="ventas-container">
            <Snackbar
                open={openAgregar} 
                autoHideDuration={10000} onClose={handleCloseAlertAgregar}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertAgregar} 
                    severity={okCrear.success ? "success" : "error"}
                    sx={{ width: 500 }}>
                    <h5>{okCrear.mensaje}</h5>
                </Alert>
            </Snackbar>
            <Snackbar
                open={openEliminar} 
                autoHideDuration={10000} onClose={handleCloseAlertEliminar}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertEliminar} 
                    severity={okEliminar.success ? "success" : "error"}
                    sx={{ width: 500 }}>
                    <h5>{okEliminar.mensaje}</h5>
                </Alert>
            </Snackbar>

            <ModalEliminarProductoHome
                show={showEliminar}
                handleClickAlert={handleClickAlertEliminar}
                handleClose={handleCloselimintar}
                id={paraEditar.producto_homeid}
            />

            <div className="titulo-ventas">
                <h3 className="text-ventas">Productos del Home</h3>
            </div>

            <Paper className="paper-form" style={{marginLeft:"15px"}}>
                <div style={{display:"flex", alignItems:"center"}}>
                    <Select
                        size="small"
                        fullWidth
                        value={product || ""}
                        name='product'
                        style={{height:35, marginRight:40}}
                        onChange={(e)=>setProduct(e.target.value)}
                    >
                    <TextField
                        variant='outlined'
                        value={input}
                        name="input"
                        onChange={handleChange}
                        fullWidth
                        style={{padding:10}}
                    />
                    {               
                        productsByNameSku && productsByNameSku.map((t) =>
                            <MenuItem value={t.productoid} key={t.productoid}>
                                {t.codigoproducto} | {t.nombreproducto} - ${t.precioVenta}
                            </MenuItem>
                        )
                    }
                    </Select>
                    <Button variant='contained'
                    onClick={(e)=>handleClickAgregar()}>Agregar</Button>
                </div>
            </Paper>
           
            <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                <Table aria-label="simple table">
                <TableHead>
                    <TableRow>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Nombre</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">C&oacute;digo</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Precio</TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"110%"}} align="center">Borrar</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                {
                    loading ? 
                    <TableRow sx={{p:20}}>
                        <TableCell colSpan={8} align='center'>
                            <ClipLoader 
                                loading={loading}
                                size={50}
                            />
                        </TableCell>
                    </TableRow> :
                    info.length > 0 ? info.map((row) => (
                    <TableRow
                        key={row.producto_homeid}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 }}}
                    >
                        <TableCell style={{fontSize:"15px", width:"50%", height:"40px", padding: 1}} align="center"
                        >{row.nombre}</TableCell>
                        <TableCell style={{fontSize:"15px", width:"30%", height:"40px", padding: 0}} align="center"
                        >{row.codigoarticulo}</TableCell>
                        <TableCell style={{fontSize:"15px", width:"30%", height:"40px", padding: 1}} align="center"
                        >${row.precioventa}</TableCell>
                        <TableCell style={{fontSize:"100%"}} align="center" 
                        onClick={(e)=>handleShowEliminar(e,row)}><div><Delete/></div></TableCell>
                    </TableRow>
                    )):
                    <TableRow>
                        <TableCell colSpan={4}>
                            <h5>No hay informaci&oacute;n para mostrar</h5>
                        </TableCell>
                    </TableRow>}
                </TableBody>
                </Table>
            </TableContainer>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}