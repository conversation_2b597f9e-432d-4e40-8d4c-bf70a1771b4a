{"login": {"user": "User", "password": "Password", "forgot-password": "Forgot your password?", "log-in": "LOGIN", "welcometo": "Welcome to", "recoveryPassword": "Recovery password", "backToLogin": "Back to login", "enterYourEmail": "Enter your email", "sendEmail": "Send email"}, "menu": {"log-out": "Log out"}, "table": {"firstPage": "First page", "lastPage": "Last page", "next": "Next", "previous": "Previous", "rows": "Rows", "filterBy": "Filter by", "search": "Search", "loadedDataReport": "Loaded data report"}, "abm-menu": {"name": "Name", "order": "Order", "language": "Language", "typeOfMenu": "Type of Menu", "address": "Address", "active": "Active", "addRecord": "Add record", "selectAnOption": "Select an option", "yes": "Yes", "no": "No", "modify": "Modify", "actions": "Actions", "nameOfSubmenu": "Name of submenu", "file": "File", "delete": "Delete", "loadedDataReport": "Loaded data report"}, "submenu": {"nameOfSubmenu": "Name of submenu", "active": "Active", "order": "Order", "file": "File", "permissions": "Permissions", "modify": "Modify", "actions": "Actions", "addRecord": "Add record", "delete": "Delete", "loadedDataReport": "Loaded data report"}, "parameter": {"parameters": "Parameters", "add": "Add", "search": "Search", "branchOffice": "Branch office", "orderShowsIva": "Order shows IVA", "billShowsIva": "Bill shows IVA", "orderShowsPayment": "Order shows payment", "billShowsPayment": "Bill shows payment", "productsPriceWithIva": "Product's price with IVA", "budgetShowsIva": "Budget shows IVA", "orderWithDiscount": "Order with discount", "budgetShowsPayment": "Budget shows payment", "delete": "Delete", "loadedDataReport": "Loaded data report", "branchOffices": "Branch offices", "showIvaOnOrders": "Show IVA on orders", "showIvaOnBills": "Show IVA on bills", "showPaymentsOnPrintOrders": "Show payments on print orders", "showPaymentsOnPrintBills": "Show payments on print bills", "orderShowsDiscount": "Order shows discount", "budgetShowsIVA": "Budget shows IVA", "budgetShowsDiscount": "Budget shows discount", "budgetShowsPaymentsOnPrint": "Budget shows payments on print", "orderShowsCUITonPrint": "Order shows CUIT on print", "budgetShowsCUITonPrint": "Order shows CUIT on print", "orderShowsExpiryDate": "Order shows expiry date", "showBarcode": "Show barcode", "assignASellerToTheCustomer": "Assign a seller to the customer", "showPriceWithoutIVAonOrders": "Shows price without IVA on orders", "showPriceWithoutIVAonBills": "Shows price without IVA on bills", "showPriceWithoutIVAonBudgets": "Shows price without IVA on budgets", "showsIVAonBills": "Shows IVA on bills", "optional": "Optional", "parametersForm": "Parameters form", "multi-branch": "Multi-branch", "manualDataSynchronization": "Manual data synchronization", "syncUp": "Sync up", "withoutSlashAtTheEnd": "without slash at the end", "retailListType": "Retail list type", "wholesaleListType": "Wholesale list type (optional)", "integrateOnlyProductsWithStock": "Integrate only products with stock", "integrateImages": "Integrate images", "GFuserToRunIntegrations": "Gestion Factura user to run integrations", "GFpasswordToRunIntegrations": "Gestion Factura password to run integrations", "lastIntegration": "Last integration", "productsToIntegrate": "Products to integrate", "integratedProducts": "Integrated products", "productsWithErrors": "Products with errors", "updatedProducts": "Updated products", "productsCreated": "Products created", "productsRemoved": "Products removed", "result": "Result", "technicalRequirements": "Technical requirements", "showEcommerce": "Show Ecommerce", "infoEcommerce": "Ecommerce information", "ecommerceImage": "Ecommerce image", "ecommerceInformation": "Ecommerce information", "informationToDisplayInOptionList": "Information to display in option list", "showTakeAway": "Show take away", "showMP": "Show Mercado Pago", "showCashPayment": "Show cash payment", "ecommerceLogo": "Ecommerce logo", "showEcommerceIndex": "Show ecommerce index", "defaultRounding": "Default rounding", "defaultDecimals": "Default decimals", "priceList": "Price list", "showCategories": "Show categories", "showBestSeller": "Show best seller", "showProductWithStock": "Show product with stock", "number": "Number", "emailAddress": "Email address", "emailInformation": "Email information", "catalogConfiguration": "Catalog configuration", "showCatalog": "Show catalog"}, "users": {"branchOffices": "Branch offices", "user": "User", "name": "Name", "lastName": "Last name", "email": "Email", "phone": "Phone", "address": "Address", "homepage": "Homepage", "applyDiscountToTheProduct": "Apply discount to the product", "commission": "Commission", "valuePerHour": "Value per hour", "assign/change permissions": "Assign / change permissions", "userProfile": "User profile", "userSecurity": "User security", "active": "Active", "expirationDate": "Expiration date", "permissions": "Permissions", "recoverPassword": "Recover password", "modify": "Modify", "delete": "Delete", "loadedDataReport": "Loaded data report"}, "profiles": {"securityProfiles": "Security Profiles", "profileName": "Profile Name", "discount": "Discount", "addRecord": "Add record", "listOfSecurityProfiles": "List of Security Profiles", "profile": "Profile", "permissions": "Permissions", "modify": "Modify", "delete": "Delete"}, "companies": {"companyShopInfo": "Company/Shop info", "name": "Name", "taxIdentificationNumber": "Tax Identification Number", "businessName": "BusinessName", "salePointId": "Sale point id", "typeOfTaxpayer": "Type of taxpayer", "startDate": "Start date", "businessAddress": "Business address", "taxText": "Tax text", "webAddress": "Web address", "logoImage": "Logo image", "isServer": "Is server", "serverIP": "Server IP", "image": "Image", "modify": "Modify", "delete": "Delete", "add": "Add", "admissionDate": "Admission date"}, "branchOffices": {"branchOffices": "Branch offices", "company": "Company", "country": "Country", "province": "Province", "name": "Name", "address": "Address", "phone": "Phone", "fax": "Fax", "email": "Email", "mpPublishableKey": "Mercado Pago Publishable Key", "mpAccessToken": "Mercado Pago Access Token", "publicEncryptionKey": "Gestion Factura Public encryption key (jwk-format)", "privateEncryptionKey": "Gestion Factura Private encryption key (jwk-format)", "generatePairOfPublicAndPrivateKeysForEncryption": "Generate pair of public and private keys for encryption of Gestion Factura", "importantTakeThisActionIfYouBelieveThePrivateKeyHasBeenCompromised": "Important: take this action if you believe the private key has been compromised", "generateEncryptionKeys": "Generate encryption keys", "publicKeySigning": "Gestion Factura public key signing (jwk-format)", "privateKeySigning": "Gestion Factura private key signing (jwk-format)", "generatePairOfPublicAndPrivateKeysForSigning": "Generate pair of public and private keys for signing of Gestion Factura", "generateSigningKeys": "Generate signing keys", "allDiscounts": "All discounts", "isTheMotherStore": "Is the mother store", "AFIPDefaultData": "AFIP default data", "defaultUseFiscalPrinter": "Default use fiscal printer", "salePointId": "Sale point ID", "salePointDefaultData": "Sale point default data", "defaultClient": "Default client", "defaultPrinterType": "Default printer type", "defaultCurrency": "Default currency", "defaultPrinter": "Default printer", "defaultBarCodeReader": "Default bar code reader", "defaultShowRequests": "Default show requests", "defaultShowPayments": "Default show payments", "defaultShowRemito": "Default show remito", "defaultShowCreditNote": "Default show credit note", "defaultShowBudget": "Default show budget", "defaultShowTiempoDeVta": "Default show requests", "turneroDefaultData": "Turnero default data", "defaultMechanicTurnero": "Default mechanic turnero", "defaultDevice": "Default device", "salePoint": "Sale point", "add": "Add", "active": "Active", "parameters": "Parameters", "modify": "Modify", "delete": "Delete"}, "clients": {"clients": "Clients", "add": "Add", "search": "Search", "idNumber": "ID number", "showClientsWithoutPurchases": "Show clients without purchases", "name": "Name", "exportToExcel": "Export to excel", "print": "Print", "idType": "Id type", "cellphone": "Cell phone", "phone": "Phone", "IVAcondition": "IVA condition", "paymentCondition": "Payment condition", "discount": "Discount", "billingEmail": "Billing email", "active": "Active", "recoverPassword": "Recover password", "seller": "<PERSON><PERSON>", "addresses": "Addresses", "modify": "Modify", "delete": "Delete", "branchOffices": "Branch offices", "assignSeller": "Assign seller", "observations": "Observations", "hasPayments": "Has payments"}}