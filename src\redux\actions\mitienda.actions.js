import axios from "axios";
import { newSchedule } from "./utilsActions";

export const configTienda = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&NV_nombre=${userInfo.nombre}&NV_email=${userInfo.email}&NV_direccion=${userInfo.direccion}&NV_telefono=${userInfo.telefono}&NV_monedaid=${userInfo.default_monedaid}&NV_altura=${userInfo.altura}&NI_provinciaid=${userInfo.provinciaid}&NV_codigopostal=${userInfo.codigopostal}&NV_ciudad=${userInfo.ciudad}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_TIENDA", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getConfigTienda = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetConfigTienda&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
    );
    if (finded) {
      localStorage.setItem(
        "default_monedaid",
        finded.data.confingtienda.default_monedaid
      );
      dispatch({ type: "GET_CONFIG_TIENDA", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getAbonos = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var develoneclienteid = localStorage.getItem("develoneclienteid");
  var ip_server = localStorage.getItem("ip_server");

  try {
    let array = [];
    array[0] = await axios.get(
      ip_server +
        "php/GestionEmpresa.php?type=GEAbonosPendientes&tiendaid=" +
        tiendausuario +
        "&api_key=" +
        api_key +
        "&clienteid=" +
        develoneclienteid +
        "&paginaResultado=10&pagActual=" +
        page +
        "&solo_pendientes=0"
    );
    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.abonos);
    array = array.flat();
    array[array.length] = paginaUltima;
    if (array) {
      dispatch({ type: "GET_ABONOS", payload: array });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const setGSPagoMP =
  (clienteid, pedidoid, facturaid, url_mp, dominio, paginaStatus) =>
  async () => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var ip_server = localStorage.getItem("ip_server");
    var api_key_develone = localStorage.getItem("api_key_develone");

    if (url_mp !== "") {
      try {
        const data = new URLSearchParams();
        data.append("tiendaId", tiendausuario);
        data.append("api_key", api_key_develone);
        data.append("clienteId", clienteid);
        data.append("pedidoId", pedidoid);
        data.append("facturaId", facturaid);
        data.append("dominio_servidor", ip_server);
        data.append("urlstatusMP", paginaStatus);
        data.append("dominiocart", process.env.REACT_APP_SERVER);

        const response = await fetch(url_mp, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
          },
          body: data,
        });

        if (!response.ok) {
          throw new Error(`Network response was not ok: ${response.status}`);
        }
        const responseData = await response.json();
        const redirect_url = responseData.url;
        window.location.href = redirect_url;
        return responseData;
      } catch (err) {
        console.error("Error occurred:", err);
        // Handle the error gracefully, e.g., show an error message to the user
      }
    } else {
      console.log("falla la API de MP");
    }
  };

export const avisoDePago =
  (
    tipopagoid,
    pedidoplacaid,
    monedaid,
    montoabona,
    numerorecibo,
    fechaingreso,
    fechapago,
    descripcion
  ) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var develoneclienteid = localStorage.getItem("develoneclienteid");
    var idusuario = localStorage.getItem("idusuario");
    var cajaid = localStorage.getItem("cajaid");
    var ip_server = localStorage.getItem("ip_server");
    var api_key_develone = localStorage.getItem("api_key_develone");

    try {
      const finded = await axios.get(
        `${ip_server}php/GestionEmpresa.php?type=GECancelaAbono&tiendaid_develone=${tiendausuario}&api_key_develone=${api_key_develone}&usuarioid=${idusuario}&cajaid=${cajaid}&clienteid_develone=${develoneclienteid}&tipopagoid=${tipopagoid}&pedidoplacaid=${pedidoplacaid}&monedaid=${monedaid}&montoabona=${montoabona}&numerorecibo=${numerorecibo}&fechaingreso=${fechaingreso}&fechapago=${fechapago}&descripcion=${descripcion}`
      );
      if (finded) {
        dispatch({ type: "AVISO_PAGO_ABONO", payload: finded.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const configPagos = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigPagos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_PAGOS", payload: finded.data.success });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const configChat = (url) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigChat&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&NV_whatsapp_url_api=${url.url}&NI_whatsapp_api=${url.usarapi}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_CHAT", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getConfigChat = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({ type: "GET_CONFIG_CHAT", payload: finded.data.parametro });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const configRedes = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigRedes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&NV_facebook_url=${userInfo.facebook_url}&NV_instagram_url=${userInfo.instagram_url}&NV_twitter_url=${userInfo.twitter_url}&NV_telegram_url=${userInfo.telegram_url}&NV_mail_direccion=${userInfo.mail_direccion}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_REDES", payload: finded.data });
    } else throw new Error("usuario no existe");
  } catch (error) {
    throw new Error(error);
  }
};

export const getRedesSociales = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({ type: "GET_REDES_SOCIALES", payload: finded.data.parametro });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const configSEO = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigSEO&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&NV_ecommerce_mtadesc=${userInfo.ecommerce_mtadesc}&NV_ecommerce_mtagooglervfi=${userInfo.ecommerce_mtagooglervfi}&NV_ecommerce_mtarobots=${userInfo.ecommerce_mtarobots}&NV_ecommerce_mtatitle=${userInfo.ecommerce_mtatitle}&NV_ecommerce_analytics=${userInfo.ecommerce_analytics}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_SEO", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getEtiquetasHtml = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({ type: "GET_ETIQUETAS", payload: finded.data.parametro });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getMetodosPago = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetFormaPago&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&filtraFormaPago=0`
    );
    if (finded) {
      let aux = Object.values(finded.data.formaspagos).flat();
      dispatch({ type: "GET_METODOS_PAGO", payload: aux });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const configRetiroEnLocal = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetConfigEnvios&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&NI_default_retiro_local=${userInfo.default_retirolocal}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_RETIRO_LOCAL", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getDefaultMetodosDePago = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({ type: "GET_DEFAULT", payload: finded.data.parametro });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const modificarMetodosDePago =
  (metodo, activoDesactivo, publicKey, privateKey) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    //pickit es 3
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigPagos&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&NI_metodo=${metodo}&NI_default_efectivo=${activoDesactivo}&NI_default_mercadopago=${activoDesactivo}&mppublickey=${publicKey}&mpprivatekey=${privateKey}`
      );
      if (finded) {
        dispatch({ type: "GET_MODIFICAR", payload: finded.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const modificarPickit =
  (activoDesactivo, pickitkey, pickittoken) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigPagos&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&NI_metodo=3&NI_default_efectivo=&NI_default_mercadopago=&mppublickey=&mpprivatekey=&NI_default_pickit=${activoDesactivo}&pickitkey=${pickitkey}&pickittoken=${pickittoken}`
      );
      if (finded) {
        dispatch({ type: "CONFIG_PICKIT", payload: finded.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const limpiarConfigMetodos = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_CONFIG_METODOS", payload: "" });
};

export const configEnvioADomicilio = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetConfigRetira&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&NI_default_enviocliente=${userInfo.default_enviocliente}`
    );
    if (finded) {
      dispatch({
        type: "CONFIG_ENVIO_DOMICILIO",
        payload: finded.data.success,
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getMetodosEnvio = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({ type: "GET_METODOS_ENVIO", payload: finded.data.parametro });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//----------------------------------- PRODUCTOS ----------------------------------------

export const getProductos = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let array = [];
    array[0] = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&pagActual=${page}&trae_todo=1 `
    );
    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.listaproductos);
    array = array.flat();

    for (let j = 0; j < array.length; j++) {
      array[j].imagenes = Object.values(array[j].imagenes).flat();
    }
    array[array.length] = paginaUltima;

    if (array) {
      dispatch({ type: "GET_PRODUCTOS", payload: array });
    } else throw new Error("no hay informacion");
  } catch (error) {
    throw { message: error };
  }
};

export const getProductosByName = (name, page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let array = [];
    array[0] = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=${name}&codigoarticulo=&pagActual=${page}&trae_todo=1 `
    );
    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.listaproductos);
    array = array.flat();

    for (let j = 0; j < array.length; j++) {
      array[j].imagenes = Object.values(array[j].imagenes).flat();
    }
    array[array.length] = paginaUltima;

    if (array) {
      dispatch({ type: "GET_PRODUCTOS_BY_NAME", payload: array });
    } else throw new Error("no hay informacion");
  } catch (error) {
    throw { message: error };
  }
};

export const getProductoBySKU = (codigo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTListaProductos&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&paginaResultado=10&OI_categoriaid=&OI_marcaid=0&nombreProducto=&codigoarticulo=${codigo}&trae_todo=1`
    );
    if (finded) {
      let aux = Object.values(finded.data.listaproductos);
      aux[0].imagenes = Object.values(aux[0].imagenes);
      dispatch({ type: "GET_PRODUCTO_BY_SKU", payload: aux });
    } else throw new Error("no pudo obtenerse");
  } catch (error) {
    throw new Error(error);
  }
};

export const agregarAlCarrito = (info) => async (dispatch) => {
  var idusuario = localStorage.getItem("idusuario");
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetProducto&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&nombre=${info.nombre}&categoriaid=${info.categoria}&tipoDeVenta=${info.tipoDeVenta}&descripcion=${info.descripcion}&precio=${info.precio}&stock=${info.stock}&sku=${info.sku}&usarAtributos=${info.usarAtributos}&alto=${info.alto}&ancho=${info.ancho}&profundidad=${info.profundidad}&peso=${info.peso}&monedaid=${info.monedaId}&activo=1&productoivaid=${info.productoIvaId}&productoid=0&marcaid=${info.marcaId}&colorid=${info.colorId}&tallaid=${info.tallaId}&idusuario=${idusuario}&subcategoriaid=${info.subcategoriaid}`
    );
    if (finded) {
      if (finded.data.success) {
        dispatch({
          type: "AGREGAR_PRODUCTO",
          payload: {
            success: finded.data.success,
            mensaje: "Producto agregado con exito!",
          },
        });
      } else {
        dispatch({
          type: "AGREGAR_PRODUCTO",
          payload: {
            success: finded.data.success,
            mensaje: "Hubo un problema al crear el producto",
          },
        });
      }
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const agregarProducto = (info) => async (dispatch) => {
  var idusuario = localStorage.getItem("idusuario");
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetProducto&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&nombre=${info.nombre}&categoriaid=${info.categoria}&tipoDeVenta=${info.tipoDeVenta}&descripcion=${info.descripcion}&precio=${info.precio}&stock=${info.stock}&sku=${info.sku}&usarAtributos=${info.usarAtributos}&alto=${info.alto}&ancho=${info.ancho}&profundidad=${info.profundidad}&peso=${info.peso}&monedaid=${info.monedaId}&activo=1&productoivaid=${info.productoIvaId}&productoid=0&marcaid=${info.marcaId}&colorid=${info.colorId}&tallaid=${info.tallaId}&idusuario=${idusuario}&subcategoriaid=${info.subcategoriaid}&es_digital=${info.es_digital}&preciovariable=${info.preciovariable}&editadetalle=${info.editadetalle}`
    );
    if (finded) {
      if (finded.data.success) {
        dispatch({
          type: "AGREGAR_PRODUCTO",
          payload: {
            success: finded.data.success,
            mensaje: "Producto agregado con exito!",
          },
        });
      } else {
        dispatch({
          type: "AGREGAR_PRODUCTO",
          payload: {
            success: finded.data.success,
            mensaje: "Hubo un problema al crear el producto",
          },
        });
      }
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const clonarProducto = (info, check) => async (dispatch) => {
  var idusuario = localStorage.getItem("idusuario");
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetProducto&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&nombre=${info.nombreproducto}&categoriaid=${info.categoriaid}&tipoDeVenta=${info.tipo_venta}&descripcion=${info.descripcion}&precio=${info.precioVenta}&stock=${info.cantidadactiva}&sku=${info.codigoproducto}&usarAtributos=${info.usar_atributos}&alto=${info.alto}&ancho=${info.ancho}&profundidad=${info.profundidad}&peso=${info.peso}&monedaid=${info.moneda}&activo=${info.activo}&productoivaid=${info.productoivaid}&productoid=0&marcaid=${info.marcaid}&tallaid=${info.tallaid}&colorid=${info.colorid}&idusuario=${idusuario}&mueve_stock=${check}&subcategoriaid=${info.subcategoriaid}&preciovariable=${info.preciovariable}&editadetalle=${info.editadetalle}`
    );
    if (finded) {
      dispatch({ type: "CLONAR_PRODUCTO", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const limpiarClonarProducto = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_CLONAR_PRODUCTO", payload: false });
};

export const asociarProducto = (info) => async (dispatch) => {
  var idusuario = localStorage.getItem("idusuario");
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetProducto&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&nombre=${info.nombreproducto}&categoriaid=${info.categoriaid}&tipoDeVenta=${info.tipo_venta}&descripcion=${info.descripcion}&precio=${info.precioVenta}&stock=${info.cantidadactiva}&sku=0&usarAtributos=${info.usar_atributos}&alto=${info.alto}&ancho=${info.ancho}&profundidad=${info.profundidad}&peso=${info.peso}&monedaid=${info.moneda}&activo=${info.activo}&productoivaid=${info.productoivaid}&marcaid=${info.marcaid}&colorid=${info.colorid}&tallaid=${info.tallaid}&idusuario=${idusuario}&asociar=1&productoprincipalid=${info.productoprincipalid}&subcategoriaid=${info.subcategoriaid}&preciovariable=${info.preciovariable}&editadetalle=${info.editadetalle}&es_digital=${info.es_digital}`
    );
    if (finded) {
      dispatch({ type: "ASOCIAR_PRODUCTO", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const limpiarAsociarProducto = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ASOCIAR_PRODUCTO", payload: false });
};

export const editarProducto = (info, check) => async (dispatch) => {
  var idusuario = localStorage.getItem("idusuario");
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetProducto&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&nombre=${info.nombreproducto}&categoriaid=${info.categoriaid}&tipoDeVenta=${info.tipo_venta}&descripcion=${info.descripcion}&precio=${info.precioCosto}&stock=0&sku=${info.codigoproducto}&usarAtributos=${info.usar_atributos}&alto=${info.alto}&ancho=${info.ancho}&profundidad=${info.profundidad}&peso=${info.peso}&monedaid=${info.moneda}&activo=${info.activo}&productoivaid=${info.productoivaid}&productoid=${info.productoid}&marcaid=${info.marcaid}&colorid=${info.colorid}&tallaid=${info.tallaid}&idusuario=${idusuario}&mueve_stock=1&subcategoriaid=${info.subcategoriaid}&es_digital=${info.es_digital}&preciovariable=${info.preciovariable}&editadetalle=${info.editadetalle}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_PRODUCTO", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const desactivarProducto = (id, activo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetProductoActivaDesactiva&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_productoid=${id}&OI_activo=${activo}`
    );
    if (finded) {
      dispatch({ type: "DESACTIVAR_PRODUCTO", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const eliminarImagenProducto =
  (productoid, imagenid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaImagen&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&categoriaid=0&marcaid=0&productoid=${productoid}&producto_imagenid=${imagenid}&origen=1`
      );
      if (finded) {
        dispatch({ type: "ELIMINAR_IMAGEN_PRODUCTO", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const getAumentoMasivo = (porcentaje) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=AumentaPrecio&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&porcentaje=${porcentaje}&productoid=0`
    );
    if (finded) {
      dispatch({ type: "AUMENTO_MASIVO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarAumentoMasivo = () => async (dispatch) => {
  dispatch({
    type: "LIMPIAR_AUMENTO_MASIVO",
    payload: { success: false, mensaje: "" },
  });
};

export const limpiarEliminarImagenProducto = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ELIMINAR_IMAGEN_PRODUCTO", payload: "" });
};

export const limpiarGeneral = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_GENERAL", payload: false });
};

export const limpiarProductoAgregado = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGADO", payload: false });
};

export const limpiarProductoDesactivado = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_DESACTIVADO", payload: false });
};

export const limpiarProductoEditado = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_PRODUCTO_EDITADO", payload: false });
};

export const limpiarProductos = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_PRODUCTOS", payload: [] });
};

export const limpiarProductoBySku = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_PRODUCTO_BY_SKU", payload: [] });
};

export const limpiarProductosByName = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_PRODUCTOS_BY_NAME", payload: [] });
};

export const getProductoById = (id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleProducto&tiendaid=${tiendausuario}&OV_api_key=${api_key}&productoid=${id}`
    );
    if (finded) {
      let aux = Object.values(finded.data.detalleProducto);
      aux[0].imagenes = Object.values(aux[0].imagenes).flat();
      aux[0].descripcion = aux[0].descripcion
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&quot;/g, '"')
        .toString();
      dispatch({ type: "GET_PRODUCTO_BY_ID", payload: aux[0] });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getTasasIva = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetTasaIva&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
    );
    if (finded) {
      let aux = Object.values(finded.data.tasa);
      dispatch({ type: "GET_TASAS_IVA", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const postImages = (images, id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var bodyFormData = new FormData();
  bodyFormData.append("img1", images.img1);
  bodyFormData.append("img2", images.img2);
  bodyFormData.append("img3", images.img3);
  bodyFormData.append("img4", images.img4);
  bodyFormData.append("img5", images.img5);
  bodyFormData.append("img6", images.img6);
  bodyFormData.append("type", "GTSetImagen");
  bodyFormData.append("OV_api_key", api_key);
  bodyFormData.append("productoid", id);
  bodyFormData.append("OI_tiendaid", tiendausuario);
  bodyFormData.append("categoriaid", 0);
  bodyFormData.append("marcaid", 0);
  bodyFormData.append("origen", 1);

  try {
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then(function (response) {
        //handle success
        console.log(response);
      })
      .catch(function (response) {
        //handle error
        console.log(response);
      });
  } catch (error) {
    throw new Error(error);
  }
};

export const getTipoVenta = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetTiposVenta&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
    );
    if (finded) {
      let aux = Object.values(finded.data.tipo_venta);
      dispatch({ type: "GET_TIPO_VENTA", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//--------------------------------------- DESCUENTOS ------------------------------------------

export const getDescuentos = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetListaDescuentos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaActual=${page}&paginaResultado=10&trae_todo=1&activo=0`
    );
    if (finded) {
      let paginaUltima = finded.data.paginaUltima;
      let aux = Object.values(finded.data.descuento);
      aux[aux.length] = paginaUltima;
      dispatch({ type: "GET_DESCUENTOS", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//w
// export const getDescuentosSinPaginado = () => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   try {
//     const finded = await axios.post(
//       `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetListaDescuentos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&trae_todo=1&activo=0`
//     );
//     if (finded) {
//       let paginaUltima = finded.data.paginaUltima;
//       let aux = Object.values(finded.data.descuento);
//       dispatch({ type: "GET_DESCUENTOS_SIN_PAGINADO", payload: aux });
//     } else throw new Error("no pudo modificarse");
//   } catch (error) {
//     throw new Error(error);
//   }
// };

//f
export const getDescuentosSinPaginado = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetListaDescuentos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&trae_todo=1&activo=0`
    );
    if (finded && finded.data && finded.data.success !== false) {
      let aux = finded.data.descuento
        ? Object.values(finded.data.descuento)
        : [];
      dispatch({ type: "GET_DESCUENTOS_SIN_PAGINADO", payload: aux });
    } else {
      console.error(
        "Error fetching discounts:",
        finded?.data?.message || "Unknown error"
      );
      dispatch({ type: "GET_DESCUENTOS_SIN_PAGINADO", payload: [] });
    }
  } catch (error) {
    console.error("Exception in getDescuentosSinPaginado:", error);
    dispatch({ type: "GET_DESCUENTOS_SIN_PAGINADO", payload: [] });
  }
};

export const agregarDescuento = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetDescuento&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&nombre_descuento=${info.nombre}&porcentaje=${info.porcentaje}&activo=${info.activo}&ecommerce=${info.ecommerce}`
    );
    if (finded) {
      dispatch({ type: "AGREGAR_DESCUENTOS", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const editarDescuento = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetDescuento&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&nombre_descuento=${info.nombredescuento}&porcentaje=${info.descuento}&activo=${info.activo}&tipodescuentoid=${info.descuentovtaid}&ecommerce=${info.ecommerce}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_DESCUENTO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

//------------------------------ RENTABILIDAD ------------------------------------------
export const getRentabilidad = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetListaRentabilidad&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaActual=${page}&paginaResultado=10`
    );
    if (finded) {
      let paginaUltima = finded.data.paginaUltima;
      let aux = Object.values(finded.data.rentabilidad);
      aux[aux.length] = paginaUltima;
      dispatch({ type: "GET_RENTABILIDAD", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const agregarRentabilidad = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetRentabilidad&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&porcentaje=${info.porcentaje}&rentabilidadid=0&categoriasid=${info.categoriaids}`
    );
    if (finded) {
      dispatch({ type: "AGREGAR_RENTABILIDAD", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const editarRentabilidad = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetRentabilidad&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&porcentaje=${info.rentabilidad}&rentabilidadid=${info.rentabilidadcatid}&categoriasid=${info.categoriaids}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_RENTABILIDAD", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const eliminarRentabilidad = (rentabilidadid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaRentabilidad&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&rentabilidadid=${rentabilidadid}`
    );
    if (finded) {
      dispatch({ type: "ELIMINAR_RENTABILIDAD", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

//----------------------- GET CATEGORIAS RENTABILIDAD ----------------------------------
export const getCategoriasRentabilidad =
  (page, rentabilidadid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.post(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetListaCategoriasRentabilidad&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&rentabilidadid=${rentabilidadid}&paginaActual=${page}&paginaResultado=8`
      );
      if (finded) {
        dispatch({ type: "GET_CATEGORIAS_RENTABILIDAD", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

//----------------------------- ELIMINAR CATEGORIA RENTABILIDAD ---------------------------------
export const eliminarCategoriaRentabilidad =
  (rentabilidadid, categoriaid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaCategoriaRentabilidad&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&rentabilidadid=${rentabilidadid}&categoriaid=${categoriaid}`
      );
      if (finded) {
        dispatch({
          type: "ELIMINAR_CATEGORIA_RENTABILIDAD",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const agregarCategoriaRentabilidad =
  (rentabilidadid, categoriaid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCategoriaRentabilidad&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&rentabilidadid=${rentabilidadid}&categoriaid=${categoriaid}`
      );
      if (finded) {
        dispatch({
          type: "AGREGAR_CATEGORIA_RENTABILIDAD",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };


  //w original
// export const getDatosHome = () => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   try {
//     const finded = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetHomeEstadisticas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
//     );
//     if (finded) {
//       dispatch({ type: "GET_DATOS_HOME", payload: finded.data });
//     } else throw new Error("no pudo modificarse");
//   } catch (error) {
//     throw new Error(error);
//   }
// };

//f with filter by vendedor
export const getDatosHome = () => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");
  
  // Check user profile
  const hasAdminProfile = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    return Object.values(perfiles).includes(7); // 7 is admin profile
  };

  const isVendedor = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    return Object.values(perfiles).includes(18); // 18 is vendedor profile
  };

  try {
    let url = `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetHomeEstadisticas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`;

    // Add vendedorid parameter if user is vendedor (not admin)
    if (isVendedor() && !hasAdminProfile()) {
      const vendedorid = localStorage.getItem("idusuario");
      url += `&OI_vendedorid=${vendedorid}`;
    }

    const response = await axios.get(url);
    
    if (response.data) {
      dispatch({ type: "GET_DATOS_HOME", payload: response.data });
    } else {
      throw new Error("No se pudieron obtener los datos");
    }
  } catch (error) {
    console.error("Error fetching home statistics:", error);
    throw error;
  }
};

export const getLocales = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetTiendas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_empresaid=154`
    );
    if (finded) {
      let aux = Object.values(finded.data.tienda);
      dispatch({ type: "GET_LOCALES", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEditarProducto = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_PRODUCTOS", payload: false });
};

export const limpiarRedesSociales = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_REDES_SOCIALES", payload: false });
};

export const limpiarEtiquetas = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ETIQUETAS", payload: false });
};

export const getNombreTienda = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetConfigTienda&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
    );
    if (finded) {
      dispatch({
        type: "GET_NOMBRE_TIENDA",
        payload: finded.data.confingtienda.nombre,
      });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarChat = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_CHAT", payload: false });
};

export const limpiarOkEnvioCliente = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ENVIO_CLIENTE", payload: false });
};

export const limpiarOkRetiro = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_RETIRO", payload: false });
};

export const getConfigMP = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDatosTienda&OV_api_key=${api_key}&tiendaid=${tiendausuario}`
    );
    if (finded) {
      localStorage.setItem("pickitkey", finded.data.pickitkey);
      localStorage.setItem("pickittoken", finded.data.pickittoken);
      dispatch({ type: "GET_CONFIG_MP", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getConfigPickit = () => async () => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDatosTienda&OV_api_key=${api_key}&tiendaid=${tiendausuario}`
    );
    if (finded) {
      localStorage.setItem("pickitkey", finded.data.pickitkey);
      localStorage.setItem("pickittoken", finded.data.pickittoken);
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getDatosTienda = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDatosTienda&OV_api_key=${api_key}&tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({ type: "GET_DATOS_TIENDA", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//-------------------------------- PEDIDOS ------------------------------------------------------------


//w original
// export const getPedidos =
//   (
//     clienteid,
//     numerofactura,
//     nrocomprobante,
//     nombrecliente,
//     fechaDesde,
//     fechaHasta,
//     page
//   ) =>
//   async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");
//     try {
//       let array = [];
//       array[0] = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMisPedidos&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_clienteid=${clienteid}&b_numerofactura=${numerofactura}&b_nrocomprobante=${nrocomprobante}&b_nombrecliente=${nombrecliente}&b_fecha_desde=${fechaDesde}&b_fecha_hasta=${fechaHasta}&pagActual=${page}&paginaResultado=10`
//       );
//       let paginaUltima = array[0].data.paginaUltima;
//       array[0] = Object.values(array[0].data.pedidos);
//       array = array.flat();
//       array.sort(function (a, b) {
//         if (parseInt(a.pedidoplacaid) < parseInt(b.pedidoplacaid)) {
//           return 1;
//         }
//         if (parseInt(a.pedidoplacaid) > parseInt(b.pedidoplacaid)) {
//           return -1;
//         }
//         return 0;
//       });
//       array[array.length] = paginaUltima;

//       if (array) {
//         dispatch({ type: "GET_PEDIDOS", payload: array });
//       } else throw new Error("hubo un problema");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//f with vendedorid query
export const getPedidos = (
  clienteid,
  numerofactura,
  nrocomprobante,
  nombrecliente,
  fechaDesde,
  fechaHasta,
  page
) => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");
  const vendedorid = localStorage.getItem("idusuario");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    // Admins (profile 7) always see everything
    if (profileValues.includes(7)) return "ALL";
    // Vendedores (profile 18) without admin see only their data
    if (profileValues.includes(18)) return "OWN";
    // Default: see everything
    return "ALL";
  };

  try {
    let array = [];
    const accessLevel = getAccessLevel();
    const vendedorParam = accessLevel === "OWN" 
      ? `&OI_vendedorid=${vendedorid}`
      : "";

    const url = `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMisPedidos&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_clienteid=${clienteid}&b_numerofactura=${numerofactura}&b_nrocomprobante=${nrocomprobante}&b_nombrecliente=${nombrecliente}&b_fecha_desde=${fechaDesde}&b_fecha_hasta=${fechaHasta}&pagActual=${page}&paginaResultado=10${vendedorParam}`;

    array[0] = await axios.get(url);
    
    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.pedidos);
    array = array.flat();
    
    // Sort by pedidoplacaid (newest first)
    array.sort((a, b) => parseInt(b.pedidoplacaid) - parseInt(a.pedidoplacaid));
    
    array.push(paginaUltima); // Add pagination info

    dispatch({ type: "GET_PEDIDOS", payload: array });
  } catch (error) {
    console.error("Error fetching pedidos:", error);
    throw new Error(error.message || "Hubo un problema al obtener los pedidos");
  }
};

//w
// export const getPedidosDetalle = (clienteid, facturaid, fechaDesde, fechaHasta, page, paginaResultado) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");

//   try {
//     const response = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisPedidos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${clienteid}&facturaid=${facturaid}&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//     );

//     if (response.data.success) {
//       dispatch({
//         type: "GET_PEDIDOS_DETALLE",
//         payload: {
//           detalle: response.data.detalle,
//           paginaUltima: response.data.paginaUltima
//         }
//       });
//     } else {
//       throw new Error("Failed to fetch pedidos detalle");
//     }
//   } catch (error) {
//     console.error("Error fetching pedidos detalle:", error);
//     throw error;
//   }
// };

//f (con clientid hardcoreado a 0)
// export const getPedidosDetalle = (fechaDesde, fechaHasta, page = 1, paginaResultado = 10) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");

//   try {
//     const response = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisPedidos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=0&facturaid=0&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//     );

//     if (response.data) {
//       dispatch({
//         type: "GET_PEDIDOS_DETALLE",
//         payload: {
//           detalle: response.data.detalle,
//           paginaUltima: response.data.paginaUltima
//         }
//       });
//     } else {
//       throw new Error("No data received");
//     }
//   } catch (error) {
//     console.error("Error in getPedidosDetalle:", error);
//     throw error;
//   }
// };

//f with clientid search
// export const getPedidosDetalle = (fechaDesde, fechaHasta, page, paginaResultado, clienteid = 0) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");

//   try {
//     const response = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisPedidos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${clienteid}&facturaid=0&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//     );

//     if (response.data) {
//       dispatch({
//         type: "GET_PEDIDOS_DETALLE",
//         payload: {
//           detalle: response.data.detalle,
//           paginaUltima: response.data.paginaUltima
//         }
//       });
//     } else {
//       throw new Error("No data received");
//     }
//   } catch (error) {
//     console.error("Error in getPedidosDetalle:", error);
//     throw error;
//   }
// };

//w2 with faturaid search (search by nro pedido) (base)
// export const getPedidosDetalle = (fechaDesde, fechaHasta, page, paginaResultado, clienteid = 0, facturaid = 0) => async (dispatch) => {
//   console.log('clienteid:', clienteid, 'facturaid:', facturaid);
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");

//   try {
//     const response = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisPedidos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${clienteid}&facturaid=${facturaid}&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//     );

//     if (response.data) {
//       dispatch({
//         type: "GET_PEDIDOS_DETALLE",
//         payload: {
//           detalle: response.data.detalle,
//           paginaUltima: response.data.paginaUltima
//         }
//       });
//     } else {
//       throw new Error("No data received");
//     }
//   } catch (error) {
//     console.error("Error in getPedidosDetalle:", error);
//     throw error;
//   }
// };

//w
// export const getPedidosDetalle =
//   (
//     fechaDesde,
//     fechaHasta,
//     page,
//     paginaResultado,
//     clienteid = null,
//     facturaid = 0
//   ) =>
//   async (dispatch) => {
//     // console.log('clienteid:', clienteid, 'facturaid:', facturaid);
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");

//     // console.log('fechaDesde', fechaDesde)

//     // Check if we need to find all clients or a specific one
//     const useClientId = clienteid && clienteid !== "0" ? clienteid : "";

//     try {
//       const response = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisPedidos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${useClientId}&facturaid=${facturaid}&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//       );

//       if (response.data) {
//         dispatch({
//           type: "GET_PEDIDOS_DETALLE",
//           payload: {
//             detalle: response.data.detalle,
//             paginaUltima: response.data.paginaUltima,
//           },
//         });
//       } else {
//         throw new Error("No data received");
//       }
//     } catch (error) {
//       console.error("Error in getPedidosDetalle:", error);
//       throw error;
//     }
//   };

//f
export const getPedidosDetalle = (
  fechaDesde,
  fechaHasta,
  page,
  paginaResultado,
  clienteid = null,
  facturaid = 0
) => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");
  const vendedorid = localStorage.getItem("idusuario");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    // Admins (profile 7) always see everything
    if (profileValues.includes(7)) return "ALL";
    // Vendedores (profile 18) without admin see only their data
    if (profileValues.includes(18)) return "OWN";
    // Default: see everything
    return "ALL";
  };

  // Check if we need to find all clients or a specific one
  const useClientId = clienteid && clienteid !== "0" ? clienteid : "";
  const accessLevel = getAccessLevel();
  const vendedorParam = accessLevel === "OWN" 
    ? `&OI_vendedorid=${vendedorid}`
    : "";

  try {
    const response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisPedidos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${useClientId}&facturaid=${facturaid}&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}${vendedorParam}`
    );

    if (response.data) {
      dispatch({
        type: "GET_PEDIDOS_DETALLE",
        payload: {
          detalle: response.data.detalle,
          paginaUltima: response.data.paginaUltima,
        },
      });
    } else {
      throw new Error("No data received");
    }
  } catch (error) {
    console.error("Error in getPedidosDetalle:", error);
    throw error;
  }
};

//w original
// export const getPedidosGrafico =
//   (
//     clienteid,
//     numerofactura,
//     nrocomprobante,
//     nombrecliente,
//     fechaDesde,
//     fechaHasta,
//     page
//   ) =>
//   async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");
//     try {
//       let array = [];
//       array[0] = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMisPedidos&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_clienteid=${clienteid}&b_numerofactura=${numerofactura}&b_nrocomprobante=${nrocomprobante}&b_nombrecliente=${nombrecliente}&b_fecha_desde=${fechaDesde}&b_fecha_hasta=${fechaHasta}&pagActual=${page}&paginaResultado=500`
//       );
//       let paginaUltima = array[0].data.paginaUltima;
//       array[0] = Object.values(array[0].data.pedidos);
//       array = array.flat();
//       if (array) {
//         dispatch({ type: "GET_PEDIDOS_GRAFICO", payload: array });
//       } else throw new Error("hubo un problema");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//f with vendedorid
export const getPedidosGrafico = (
  clienteid,
  numerofactura,
  nrocomprobante,
  nombrecliente,
  fechaDesde,
  fechaHasta,
  page,
  vendedorid = ''  // Add this new parameter
) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let array = [];
    
    // Add vendedorid to the URL if provided
    const vendedorParam = vendedorid ? `&OI_vendedorid=${vendedorid}` : '';
    
    array[0] = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMisPedidos&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_clienteid=${clienteid}&b_numerofactura=${numerofactura}&b_nrocomprobante=${nrocomprobante}&b_nombrecliente=${nombrecliente}&b_fecha_desde=${fechaDesde}&b_fecha_hasta=${fechaHasta}&pagActual=${page}&paginaResultado=500${vendedorParam}`
    );
    
    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.pedidos);
    array = array.flat();
    if (array) {
      dispatch({ type: "GET_PEDIDOS_GRAFICO", payload: array });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//w (base)
export const getPedido = (facturaid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMiPedido&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}`
    );
    let aux = Object.values(finded.data.pedido);
    aux[1] = Object.values(aux[1]);
    let aux2;
    for (let i = 0; i < 3; i++) {
      aux2 = aux[1].pop();
    }
    aux[3] = {
      precioTotalCompra: finded.data.pedido.detalle.t_precio,
      precioTotalIva: finded.data.pedido.detalle.t_iva,
      precioTotal: finded.data.pedido.detalle.t_total,
    };
    if (aux[0].tipo_envio === "Pickit") {
      let envio = aux[1].shift();
      aux[5] = envio;
    }
    aux[6] = Object.values(finded.data.pedido.informacion_entrega)[0];
    if (aux) {
      dispatch({ type: "GET_PEDIDO", payload: { data: aux, loading: false } });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//f
// export const getPedido = (facturaid) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   try {
//     const finded = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMiPedido&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}`
//     );

//     // Check if we have valid data
//     if (!finded.data || !finded.data.pedido) {
//       throw new Error("No se encontraron datos del pedido");
//     }

//     let aux = Object.values(finded.data.pedido);

//     // Check if detalle exists before accessing it
//     if (finded.data.pedido.detalle) {
//       aux[1] = Object.values(finded.data.pedido.detalle).filter(item =>
//         typeof item === 'object' && item !== null && !('t_precio' in item && 't_iva' in item && 't_total' in item)
//       );

//       aux[3] = {
//         precioTotalCompra: finded.data.pedido.detalle.t_precio || 0,
//         precioTotalIva: finded.data.pedido.detalle.t_iva || 0,
//         precioTotal: finded.data.pedido.detalle.t_total || 0,
//       };
//     } else {
//       aux[1] = [];
//       aux[3] = {
//         precioTotalCompra: 0,
//         precioTotalIva: 0,
//         precioTotal: 0,
//       };
//     }

//     if (aux[0] && aux[0].tipo_envio === "Pickit") {
//       let envio = aux[1].shift();
//       aux[5] = envio;
//     }

//     // Safely access informacion_entrega
//     if (finded.data.pedido.informacion_entrega && Object.keys(finded.data.pedido.informacion_entrega).length > 0) {
//       aux[6] = Object.values(finded.data.pedido.informacion_entrega)[0];
//     } else {
//       aux[6] = null;
//     }

//     if (aux) {
//       dispatch({ type: "GET_PEDIDO", payload: { data: aux, loading: false } });
//     } else throw new Error("hubo un problema");
//   } catch (error) {
//     console.error("Error in getPedido:", error);
//     dispatch({ type: "GET_PEDIDO", payload: { data: [], loading: false, error: error.message } });
//   }
// };

//f
// export const getPedidoDetails = (facturaid, clienteid, page = 1, pageSize = 10) => async (dispatch) => {
//   if (!facturaid || !clienteid) {
//     throw new Error('facturaid and clienteid are required');
//   }

//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");

//   try {
//     const finded = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMiPedido&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_clienteid=${clienteid}&OI_facturaid=${facturaid}&pagActual=${page}&paginaResultado=${pageSize}`
//     );

//     let aux = Object.values(finded.data.pedido);
//     aux[1] = Object.values(aux[1]);
//     let aux2;
//     for (let i = 0; i < 3; i++) {
//       aux2 = aux[1].pop();
//     }
//     aux[3] = {
//       precioTotalCompra: finded.data.pedido.detalle.t_precio,
//       precioTotalIva: finded.data.pedido.detalle.t_iva,
//       precioTotal: finded.data.pedido.detalle.t_total,
//     };
//     if (aux[0].tipo_envio === "Pickit") {
//       let envio = aux[1].shift();
//       aux[5] = envio;
//     }
//     aux[6] = Object.values(finded.data.pedido.informacion_entrega)[0];

//     if (aux) {
//       dispatch({ type: "GET_PEDIDO_DETAILS", payload: { data: aux, loading: false } });
//     } else throw new Error("hubo un problema");
//   } catch (error) {
//     throw new Error(error);
//   }
// };

//f
export const getPedidoDetails =
  (facturaid, clienteid, page = 1, pageSize = 10) =>
  async (dispatch) => {
    if (!facturaid || !clienteid) {
      throw new Error("facturaid and clienteid are required");
    }

    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMiPedido&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_clienteid=${clienteid}&OI_facturaid=${facturaid}&pagActual=${page}&paginaResultado=${pageSize}`
      );

      if (!response.data.success) {
        throw new Error("La solicitud no fue exitosa");
      }

      const pedidoData = response.data.pedido;

      // Remove totals from detalle object
      const { t_precio, t_iva, t_total, ...detalleProducts } =
        pedidoData.detalle;

      // Format the data as needed
      const formattedData = {
        cabecera: pedidoData.cabecera,
        productos: Object.values(detalleProducts), // Convert products object to array
        totales: {
          precioTotal: t_precio,
          ivaTotal: t_iva,
          total: t_total,
        },
        pagos: pedidoData.pagos,
        informacionEntrega: pedidoData.informacion_entrega,
      };

      dispatch({
        type: "GET_PEDIDO_DETAILS",
        payload: {
          data: formattedData,
          loading: false,
        },
      });

      return formattedData; // Return the formatted data for component use
    } catch (error) {
      console.error("Error in getPedidoDetails:", error);
      throw error;
    }
  };

export const limpiarPedido = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_PEDIDO", payload: null });
};

export const cancelarPedido = (pedido) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCanceladoPedido&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&pedidoid=${pedido}&pedidoplacaestadoid=3`
    );
    if (finded) {
      dispatch({ type: "CANCELAR_PEDIDO", payload: finded.data.mensaje });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarPedidoCancelado = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_PEDIDO_CANCELADO", payload: "" });
};

export const cambiarEstadoPedido = (pedido, estado) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  console.log("pedido desde action", pedido, "estado", estado);
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetEstadoPedido&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&pedidoid=${pedido}&OI_pedidoplacaestadoid=${estado}`
    );
    if (finded) {
      dispatch({ type: "CAMBIAR_ESTADO_PEDIDO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const getEstadosPedido = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaEstadoPedidos&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );
    if (finded) {
      let aux = Object.values(finded.data.listaestadipedidos);
      aux.sort(function (a, b) {
        if (a.orden > b.orden) {
          return 1;
        }
        if (a.orden < b.orden) {
          return -1;
        }
        // a must be equal to b
        return 0;
      });
      dispatch({ type: "GET_ESTADOS_PEDIDO", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const registrarPago = (info) => async (dispatch) => {
  var idusuario = localStorage.getItem("idusuario");
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var cajaid = localStorage.getItem("cajaid");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTRegistraPagoPedido&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&usuarioid=${idusuario}&pedidoid=${info.pedidoid}&fechapago=${info.fecha}&tipopagoid=${info.tipopago}&monedaid=${info.moneda}&numerorecibo=${info.numerorecibo}&descripcion=${info.descripcion}&montoreal=${info.montoreal}&url_site=${process.env.REACT_APP_SERVER}&OI_facturaid=${info.facturaid}&OI_clienteid&OV_tipofactura&payment_id&merchant_order_id&external_reference&status=&cajaid=${cajaid}`
    );
    if (finded) {
      dispatch({
        type: "REGISTRAR_PAGO",
        payload: {
          success: finded.data.success,
          mensaje: finded.data.mensaje || finded.data.mensajeError,
        },
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getPagosRealizados = (pedidoid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetPagosPedido&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&pedidoplacaid=${pedidoid}`
    );
    if (finded) {
      let aux = Object.values(finded.data.pagos);
      let eliminado = aux.pop();
      dispatch({
        type: "GET_PAGOS_REALIZADOS",
        payload: { pagos: aux, totalpagado: eliminado },
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const limpiarRegistrarPedido = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_REGISTRAR_PEDIDO", payload: "" });
};

export const getProductosFiltrados =
  (nombre, codigo, categoria, marca, talla, stock, page) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      let array = [];
      array[0] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_categoriaid=${categoria}&OI_marcaid=${marca}&nombreProducto=${nombre}&codigoarticulo=${codigo}&OI_tallaid=${talla}&pagActual=${page}&paginaResultado=10&trae_todo=${stock}`
      );
      let paginaUltima = array[0].data.paginaUltima;
      array[0] = Object.values(array[0].data.listaproductos);
      array = array.flat();

      for (let j = 0; j < array.length; j++) {
        array[j].imagenes = Object.values(array[j].imagenes).flat();
      }
      array[array.length] = paginaUltima;

      if (array) {
        dispatch({ type: "GET_PRODUCTOS", payload: array });
      } else throw new Error("no hay informacion");
    } catch (error) {
      throw { message: error };
    }
  };

export const getStock =
  (
    marca,
    categoria,
    talla,
    color,
    nombre,
    codigo,
    disponible,
    reservada,
    page
  ) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let array = [];
      array[0] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetStock&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&marcaid=${marca}&categoriaid=${categoria}&tallaid=${talla}&colorid=${color}&codigoArticulo=${codigo}&nombreArticulo=${nombre}&cantidadDisponible=${disponible}&cantidadReservada=${reservada}&paginaResultado=10&paginaActual=${page}`
      );
      let paginaUltima = array[0].data.paginaUltima;
      array[0] = Object.values(array[0].data.stock);
      let aux;
      array = array.flat();
      array[array.length] = paginaUltima;

      if (array) {
        dispatch({ type: "GET_STOCK", payload: array });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

//-------------------- TALLAS ------------------------------------------

export const configTalla = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTConfigTalla&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_tallaid=0&NV_nombre=${info.nombre}&OI_activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_TALLA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarAgregarTalla = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGAR_TALLA", payload: false });
};

export const editarTalla = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTConfigTalla&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_tallaid=${info.tallaid}&NV_nombre=${info.nombretalla}&OI_activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_TALLA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEditarTalla = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_EDITAR_TALLA", payload: false });
};

export const eliminarTalla = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminarMedida&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&medidaid=${info.tallaid}`
    );
    if (finded) {
      dispatch({ type: "ELIMINAR_TALLA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEliminarTalla = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ELIMINAR_MARCA_TIENDA", payload: false });
};

export const getTallas = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSListaTallas&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_tallaid=0&trae_todo=1&activo=0`
    );
    if (finded) {
      let aux = Object.values(finded.data.listatallas);
      dispatch({ type: "GET_TALLAS", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getTallasPaginado = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSListaTallas&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_tallaid=0&pagActual=${page}&paginaResultado=10&trae_todo=1&activo=0`
    );
    if (finded) {
      let paginaUltima = finded.data.paginaUltima;
      let aux = Object.values(finded.data.listatallas);
      aux[aux.length] = paginaUltima;
      dispatch({ type: "GET_TALLAS_PAGINADO", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//----------------------- COLORES --------------------------------------
export const configColorTienda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  let color = info.color.replace("#", "%23");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigColor&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_colorid=0&NV_nombre=${info.nombre}&OI_activo=${info.activo}&NV_color=${color}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_COLOR_TIENDA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarAgregarColorTienda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGAR_COLOR_TIENDA", payload: false });
};

export const editarColorTienda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  let color = info.color.replace("#", "%23");

  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigColor&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_colorid=${info.colorid}&NV_nombre=${info.nombre}&OI_activo=${info.activo}&NV_color=${color}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_COLOR_TIENDA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEditarColorTienda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_EDITAR_COLOR_TIENDA", payload: false });
};

export const eliminarColorTienda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetColorActivaDesactiva&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_colorid=${info.colorid}&OI_activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "ELIMINAR_COLOR_TIENDA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEliminarColorTienda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ELIMINAR_MARCA_TIENDA", payload: false });
};

export const getColoresTienda = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetColores&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&trae_todo=1&activo=0`
    );
    if (finded) {
      let aux = Object.values(finded.data.colores);
      dispatch({ type: "GET_COLORES_TIENDA", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getColoresTiendaPaginado = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetColores&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&pagActual=${page}&paginaResultado=10&trae_todo=1&activo=0`
    );
    if (finded) {
      let paginaUltima = finded.data.paginaUltima;
      let aux = Object.values(finded.data.colores);
      aux[aux.length] = paginaUltima;
      dispatch({ type: "GET_COLORES_TIENDA_PAGINADO", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//--------------------------- MONEDAS -------------------------------------------

export const configMoneda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetMoneda&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&monedaid=0&nombre=${info.nombre}&activo=${info.activo}&simbolo=${info.simbolo}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_MONEDA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarAgregarMoneda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGAR_MONEDA", payload: false });
};

export const editarMoneda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetMoneda&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&monedaid=${info.monedaid}&nombre=${info.nombre}&activo=${info.activo}&simbolo=${info.simbolo}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_MONEDA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEditarMoneda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_EDITAR_MONEDA", payload: false });
};

export const eliminarMoneda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminarMoneda&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&monedaid=${info.monedaid}`
    );
    if (finded) {
      dispatch({ type: "ELIMINAR_MONEDA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEliminarMoneda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ELIMINAR_MONEDA", payload: false });
};

export const getMonedas = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetMonedas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&trae_todo=1&activo=0`
    );
    if (finded) {
      let aux = Object.values(finded.data.monedas);
      dispatch({ type: "GET_MONEDAS", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getMonedasPaginado = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetMonedas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&pagActual=${page}&paginaResultado=10&trae_todo=1&activo=0`
    );
    if (finded) {
      let paginaUltima = finded.data.paginaUltima;
      let aux = Object.values(finded.data.monedas);
      aux[aux.length] = paginaUltima;
      dispatch({ type: "GET_MONEDAS_PAGINADO", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};
//--------------------------- CLIENTES -------------------------------------------

export const getClientes = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let finded = [];
    finded[0] = await axios.get(
      `${
        process.env.REACT_APP_SERVER
      }php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=0&paginaActual=${1}&paginaResultado=500`
    );
    let paginaUltima = finded[0].data.paginaUltima;
    finded[0] = Object.values(finded[0].data.clientes);
    let aux;
    for (let i = 1; i < paginaUltima; i++) {
      finded[i] = await axios.get(
        `${
          process.env.REACT_APP_SERVER
        }php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=0&paginaActual=${
          i + 1
        }&paginaResultado=500`
      );
      aux = Object.values(finded[i].data.clientes);
      finded[i] = aux;
    }
    finded = finded.flat();
    finded.sort(function (a, b) {
      if (a.nombre.toLocaleLowerCase() > b.nombre.toLocaleLowerCase()) {
        return 1;
      }
      if (a.nombre.toLocaleLowerCase() < b.nombre.toLocaleLowerCase()) {
        return -1;
      }
      return 0;
    });
    finded[finded.length] = paginaUltima;
    if (finded) {
      dispatch({ type: "GET_CLIENTES", payload: finded });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//w returns all clients
// export const getListadoClientes =
//   (page, nombre, cuit, email) => async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");
//     try {
//       let finded = [];
//       finded[0] = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=0&paginaActual=${page}&paginaResultado=10&OV_nombre=${nombre}&NV_cuit=${cuit}&OV_email=${email}`
//       );
//       let paginaUltima = finded[0].data.paginaUltima;
//       finded[0] = Object.values(finded[0].data.clientes);
//       finded = finded.flat();
//       finded[finded.length] = paginaUltima;
//       if (finded) {
//         dispatch({ type: "GET_LISTADO_CLIENTES", payload: finded });
//       } else throw new Error("hubo un problema");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//f return all clients of specific user logged in
// export const getListadoClientes =
//   (page, nombre, cuit, email) => async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");
//     var vendedorid = localStorage.getItem("idusuario");
//     try {
//       let finded = [];
//       finded[0] = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=0&paginaActual=${page}&paginaResultado=10&OV_nombre=${nombre}&NV_cuit=${cuit}&OV_email=${email}&vendedorid=${vendedorid}`
//       );
//       let paginaUltima = finded[0].data.paginaUltima;
//       finded[0] = Object.values(finded[0].data.clientes);
//       finded = finded.flat();
//       finded[finded.length] = paginaUltima;
//       if (finded) {
//         dispatch({ type: "GET_LISTADO_CLIENTES", payload: finded });
//       } else throw new Error("hubo un problema");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//f returns all clients if Admin, or clients of specific user logged in if not Admin
// export const getListadoClientes =
//   (page, nombre, cuit, email) => async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");
//     var vendedorid = localStorage.getItem("idusuario");

//     // Check if user has admin profile
//     const hasAdminProfile = () => {
//       const perfiles = localStorage.getItem('perfiles')
//         ? JSON.parse(localStorage.getItem('perfiles'))
//         : {};
//       return Object.values(perfiles).includes(7);
//     };

//     try {
//       let finded = [];
//       // If admin, don't send vendedorid to get all clients
//       // If not admin, send vendedorid to get only their clients
//       const vendedorParam = hasAdminProfile() ? "" : `&vendedorid=${vendedorid}`;

//       finded[0] = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=0&paginaActual=${page}&paginaResultado=10&OV_nombre=${nombre}&NV_cuit=${cuit}&OV_email=${email}${vendedorParam}`
//       );
//       let paginaUltima = finded[0].data.paginaUltima;
//       finded[0] = Object.values(finded[0].data.clientes);
//       finded = finded.flat();
//       finded[finded.length] = paginaUltima;
//       if (finded) {
//         dispatch({ type: "GET_LISTADO_CLIENTES", payload: finded });
//       } else throw new Error("hubo un problema");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//f accepts client id
export const getListadoClientes =
  (page, nombre, cuit, email, clienteid = "") =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var vendedorid = localStorage.getItem("idusuario");

    // Check if user has admin profile
    const hasAdminProfile = () => {
      const perfiles = localStorage.getItem("perfiles")
        ? JSON.parse(localStorage.getItem("perfiles"))
        : {};
      return Object.values(perfiles).includes(7);
    };

    try {
      let finded = [];
      // If admin, don't send vendedorid to get all clients
      // If not admin, send vendedorid to get only their clients
      const vendedorParam = hasAdminProfile()
        ? ""
        : `&vendedorid=${vendedorid}`;
      // Add clienteid parameter if provided
      const clienteParam = clienteid ? `&OI_clienteid=${clienteid}` : "";

      finded[0] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=0${clienteParam}&paginaActual=${page}&paginaResultado=10&OV_nombre=${nombre}&NV_cuit=${cuit}&OV_email=${email}${vendedorParam}`
      );
      let paginaUltima = finded[0].data.paginaUltima;
      finded[0] = Object.values(finded[0].data.clientes);
      finded = finded.flat();
      finded[finded.length] = paginaUltima;
      if (finded) {
        dispatch({ type: "GET_LISTADO_CLIENTES", payload: finded });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

//w original
// export const agregarCliente = (info, isClient) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   let esPersona = isClient ? "1" : "0";
//   try {
//     const finded = await axios.post(
//       `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCliente&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&clienteid=${info.clienteid}&fisica_juridica=${esPersona}&nombre=${info.nombre}&apellido=${info.apellido}&tipodocumentoid=${info.tipodocidentidad}&numerodocumento=${info.cuit}&celular=${info.celular}&telefono=${info.telefono}&email=${info.email}&tipocondicioniva=${info.tipocondicioniva}&tipocondicionpago=${info.tipocondicionpago}`
//     );
//     if (finded) {
//       dispatch({ type: "AGREGAR_CLIENTE", payload: finded.data });
//     } else throw new Error("no pudo modificarse");
//   } catch (error) {
//     throw new Error(error);
//   }
// };

//f with vendedorid (base)

// export const agregarCliente = (info, isClient) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   let esPersona = isClient ? "1" : "0";
//   try {
//     const finded = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCliente&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&clienteid=${info.clienteid}&fisica_juridica=${esPersona}&nombre=${info.nombre}&apellido=${info.apellido}&tipodocumentoid=${info.tipodocidentidad}&numerodocumento=${info.cuit}&celular=${info.celular}&telefono=${info.telefono}&email=${info.email}&tipocondicioniva=${info.tipocondicioniva}&tipocondicionpago=${info.tipocondicionpago}&vendedorid=${info.vendedorid}`
//     );
//     if (finded) {
//       dispatch({ type: "AGREGAR_CLIENTE", payload: finded.data });
//     } else throw new Error("no pudo modificarse");
//   } catch (error) {
//     throw new Error(error);
//   }
// };

export const agregarCliente = (info, isClient) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  let esPersona = isClient ? "1" : "0";

  try {
    const response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCliente&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&clienteid=${info.clienteid}&fisica_juridica=${esPersona}&nombre=${info.nombre}&apellido=${info.apellido}&tipodocumentoid=${info.tipodocidentidad}&numerodocumento=${info.cuit}&celular=${info.celular}&telefono=${info.telefono}&email=${info.email}&tipocondicioniva=${info.tipocondicioniva}&tipocondicionpago=${info.tipocondicionpago}&vendedorid=${info.vendedorid}`
    );

    if (response.data.success) {
      dispatch({
        type: "AGREGAR_CLIENTE",
        payload: {
          success: true,
          mensaje: "Cliente agregado correctamente",
        },
      });
    } else {
      dispatch({
        type: "AGREGAR_CLIENTE_ERROR",
        payload: {
          success: false,
          mensaje: response.data.mensaje,
        },
      });
    }
  } catch (error) {
    dispatch({
      type: "AGREGAR_CLIENTE_ERROR",
      payload: {
        success: false,
        mensaje: error.message,
      },
    });
  }
};

export const editarCliente = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCliente&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&clienteid=${info.clienteid}&fisica_juridica=${info.fisica_juridica}&nombre=${info.nombre}&apellido=${info.apellido}&tipodocumentoid=${info.tipodocidentidadid}&numerodocumento=${info.cuit}&celular=${info.celular}&telefono=${info.telefono}&email=${info.email}&tipocondicioniva=${info.tipocondicionivaid}&tipocondicionpago=${info.tipocondicionpagoid}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_CLIENTE", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const activarDesactivarCliente = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetClienteActivaDesactiva&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&clienteid=${info.clienteid}&activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "ACTIVAR_DESACTIVAR_CLIENTE", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getTiposDocumentoTienda = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetTipoDocumento&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
    );
    if (finded) {
      let aux = Object.values(finded.data.tipo_doc);
      dispatch({ type: "GET_TIPOS_DOCUMENTO_TIENDA", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getCondicionesIVA = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetCondicionIva&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
    );
    if (finded) {
      let aux = Object.values(finded.data.cond_iva);
      dispatch({ type: "GET_CONDICIONES_IVA", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getCondicionesPago = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetCondicionesPago&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}`
    );
    if (finded) {
      let aux = Object.values(finded.data.cond_pago);
      dispatch({ type: "GET_CONDICIONES_PAGO", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//----------------------------CATEGORIAS-----------------------------
export const configCategoria = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigCategoria&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&NV_nombre=${userInfo.nombrecategoria}&NI_ecommerce=${userInfo.ecommerce}&OI_activo=${userInfo.activo}&NV_descripcion=${userInfo.descripcion}&OI_categoriaid=${userInfo.id}`
    );
    if (finded) {
      dispatch({
        type: "CONFIG_CATEGORIA",
        payload: { ok: finded.data.success, id: finded.data.categoriaid },
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const editarCategoria = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigCategoria&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&NV_nombre=${userInfo.nombrecategoria}&NI_ecommerce=${userInfo.ecommerce}&OI_activo=${userInfo.activo}&NV_descripcion=${userInfo.descripcion}&OI_categoriaid=${userInfo.categoriaid}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_CATEGORIA", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEditarCategoria = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_EDITAR_CATEGORIA", payload: false });
};

export const getCategoria = (id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaCategorias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_categoriaid=${id}`
    );
    let aux = Object.values(finded.data.listacategorias).flat();
    if (finded) {
      dispatch({ type: "GET_CATEGORIA", payload: aux[0] });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const configSubCategoria = (userInfo, id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigSubCategoria&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&NV_nombre=${userInfo.nombre}&NI_ecommerce=${userInfo.ecommerce}&OI_activo=${userInfo.activo}&OI_categoriaid=${id}&OI_subcategoriaid=0`
    );
    if (finded) {
      dispatch({ type: "CONFIG_SUB_CATEGORIA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const editarSubCategoria = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigSubCategoria&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&NV_nombre=${userInfo.nombre}&NI_ecommerce=${userInfo.ecommerce}&OI_activo=${userInfo.activo}&OI_categoriaid=${userInfo.categoriaid}&OI_subcategoriaid=${userInfo.subcategoriaid}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_SUB_CATEGORIA", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarSubCategoriaEditada = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_EDITAR_SUB_CATEGORIA", payload: false });
};

export const limpiarSubCategoriaCreada = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_SUBCATEGORIA_CREADA", payload: false });
};

export const getSubCategoria =
  (idcategoria, idsubcategoria) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.post(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaCategorias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_categoriaid=${idcategoria}`
      );
      let aux = Object.values(finded.data.listacategorias).flat();
      let subcategorias = Object.values(aux[0].listasubcategorias);
      let subcategoria = subcategorias.find(
        (s) => s.subcategoriaid == idsubcategoria
      );
      if (finded) {
        dispatch({ type: "GET_SUB_CATEGORIAS", payload: subcategoria });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getSubCategoriasByCategoria =
  (idcategoria) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaCategorias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_categoriaid=${idcategoria}`
      );
      let aux = Object.values(finded.data.listacategorias).flat();
      let subcategorias = Object.values(aux[0].listasubcategorias);
      if (finded) {
        dispatch({
          type: "GET_SUB_CATEGORIAS_BY_CATEGORIA",
          payload: subcategorias,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

//f
// export const getSubCategoriasByCategoria = (idcategoria) => async (dispatch) => {
//     const tiendausuario = localStorage.getItem("tiendausuario");
//     const api_key = localStorage.getItem("api_key");
    
//     try {
//         const response = await axios.get(
//             `${process.env.REACT_APP_SERVER}php/GestionTienda.php`, 
//             {
//                 params: {
//                     type: "GTListaCategorias",
//                     OI_tiendaid: tiendausuario,
//                     OV_api_key: api_key,
//                     OI_categoriaid: idcategoria
//                 }
//             }
//         );

//         // Validate response structure
//         if (!response.data || !response.data.listacategorias) {
//             throw new Error("Invalid API response structure");
//         }

//         // Safely extract subcategories
//         const categories = Object.values(response.data.listacategorias);
//         let subcategorias = [];
        
//         if (categories.length > 0 && categories[0].listasubcategorias) {
//             subcategorias = Object.values(categories[0].listasubcategorias);
//         }

//         dispatch({
//             type: "GET_SUB_CATEGORIAS_BY_CATEGORIA",
//             payload: subcategorias
//         });

//         return subcategorias;
        
//     } catch (error) {
//         console.error("Error fetching subcategories:", error);
//         dispatch({
//             type: "GET_SUB_CATEGORIAS_BY_CATEGORIA",
//             payload: [] // Return empty array on error
//         });
//         throw error;
//     }
// };

export const getCategorias = (page, nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let array = [];
    array[0] = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaCategorias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&nombre=${nombre}&paginaResultado=10&pagActual=${page}&trae_todo=1&activo=0 `
    );
    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.listacategorias);
    array = array.flat();
    for (let j = 0; j < array.length; j++) {
      if (!Array.isArray(array[j])) {
        array[j].listasubcategorias = Object.values(
          array[j].listasubcategorias
        ).flat();
      }
    }
    array[array.length] = paginaUltima;
    if (array) {
      dispatch({ type: "GET_CATEGORIAS", payload: array });
    } else throw new Error("no hay informacion");
  } catch (error) {
    throw { message: error };
  }
};

export const getProductosByNameSelect = (nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    if (nombre !== "") {
      let array = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=5&nombreProducto=${nombre}&codigoarticulo=&pagActual=1&trae_todo=1`
      );
      if (array) {
        dispatch({ type: "GET_PRODUCTOS_BY_NAME_SELECT", payload: array.data });
      } else throw new Error("no hay informacion");
    } else {
      dispatch({
        type: "GET_PRODUCTOS_BY_NAME_SELECT",
        payload: { listaproductos: [] },
      });
    }
  } catch (error) {
    throw { message: error };
  }
};

export const getCategoriasByName = (nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    if (nombre !== "") {
      let array = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaCategorias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&pagActual=1&trae_todo=1&activo=0&nombre=${nombre}`
      );
      if (array) {
        dispatch({ type: "GET_CATEGORIAS_BY_NAME", payload: array.data });
      } else throw new Error("no hay informacion");
    } else {
      dispatch({
        type: "GET_CATEGORIAS_BY_NAME",
        payload: { listacategorias: [] },
      });
    }
  } catch (error) {
    throw { message: error };
  }
};

export const getMarcasByName = (nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    if (nombre !== "") {
      let array = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSListaMarcas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&pagActual=1&trae_todo=1&activo=0&nombre=${nombre}`
      );
      if (array) {
        dispatch({ type: "GET_MARCAS_BY_NAME", payload: array.data });
      } else throw new Error("no pudo modificarse");
    } else {
      dispatch({ type: "GET_MARCAS_BY_NAME", payload: { listamarcas: [] } });
    }
  } catch (error) {
    throw new Error(error);
  }
};

export const getTallasByName = (nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    if (nombre !== "") {
      let array = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSListaTallas&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_tallaid=0&trae_todo=1&activo=0&nombre=${nombre}`
      );
      if (array) {
        dispatch({ type: "GET_TALLAS_BY_NAME", payload: array.data });
      } else throw new Error("no pudo modificarse");
    } else {
      dispatch({ type: "GET_TALLAS_BY_NAME", payload: { listatallas: [] } });
    }
  } catch (error) {
    throw new Error(error);
  }
};

export const getColoresByName = (nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    if (nombre !== "") {
      let array = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetColores&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&trae_todo=1&activo=0&nombre=${nombre}`
      );
      if (array) {
        dispatch({ type: "GET_COLORES_BY_NAME", payload: array.data });
      } else throw new Error("no pudo modificarse");
    } else {
      dispatch({ type: "GET_COLORES_BY_NAME", payload: { colores: [] } });
    }
  } catch (error) {
    throw new Error(error);
  }
};

export const getCategoriasSinPag = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let array = [];
    array[0] = await axios.get(
      `${
        process.env.REACT_APP_SERVER
      }php/GestionTienda.php?type=GTListaCategorias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=20&pagActual=${1}&trae_todo=1&activo=0`
    );
    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.listacategorias);
    let aux;
    for (let j = 1; j < paginaUltima; j++) {
      array[j] = await axios.get(
        `${
          process.env.REACT_APP_SERVER
        }php/GestionTienda.php?type=GTListaCategorias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=20&pagActual=${
          j + 1
        }&trae_todo=1&activo=0`
      );
      aux = Object.values(array[j].data.listacategorias);
      array[j] = aux;
    }
    array = array.flat();
    if (array) {
      dispatch({ type: "GET_CATEGORIAS_SIN_PAG", payload: array });
    } else throw new Error("no hay informacion");
  } catch (error) {
    throw { message: error };
  }
};

export const desactivarCategoria = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCategoriaActivaDesactiva&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_categoriaid=${info.id}&OI_activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "DESACTIVAR_CATEGORIA", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarOk = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_OK", payload: false });
};

export const desactivarSubCategoria = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetSubCategoriaActivaDesactiva&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_subcategoriaid=${info.id}&OI_activo=${info.activo}`
    );
    if (finded) {
      dispatch({
        type: "DESACTIVAR_SUBCATEGORIA",
        payload: finded.data.success,
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarOkSubcategoria = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_SUBCATEGORIA_OK", payload: false });
};

export const limpiarCategoria = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_CATEGORIA", payload: false });
};

export const postImagenCategoria = (images, id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var bodyFormData = new FormData();
  bodyFormData.append("img1", images);
  bodyFormData.append("type", "GTSetImagen");
  bodyFormData.append("OV_api_key", api_key);
  bodyFormData.append("productoid", 0);
  bodyFormData.append("OI_tiendaid", tiendausuario);
  bodyFormData.append("categoriaid", id);
  bodyFormData.append("marcaid", 0);
  bodyFormData.append("origen", 3);

  try {
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then(function (response) {
        dispatch({
          type: "AGREGAR_IMAGEN_CATEGORIA",
          payload: response.data.success,
        });
      })
      .catch(function (response) {
        //handle error
        console.log(response);
      });
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarAgregarImagenCategoria = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGAR_IMAGEN_CATEGORIA", payload: false });
};

export const eliminarImagenCategoria =
  (categoriaid, imagenid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaImagen&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&categoriaid=${categoriaid}&marcaid=0&productoid=0&producto_imagenid=${imagenid}&origen=3`
      );
      if (finded) {
        dispatch({ type: "ELIMINAR_IMAGEN_CATEGORIA", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const limpiarEliminarImagenCategoria = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGAR_IMAGEN_CATEGORIA", payload: false });
};

//---------------------------- MARCAS -------------------------------
export const configMarcaTienda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigMarca&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_marcaid=${info.id}&NV_nombre=${info.nombremarca}&NI_ecommerce=${info.ecommerce}&OI_activo=${info.activo}&NV_imagenmarca=${info.imagen}`
    );
    if (finded) {
      dispatch({
        type: "CONFIG_MARCA_TIENDA",
        payload: { ok: finded.data.success, id: finded.data.marcaid },
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarAgregarMarcaTienda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGAR_MARCA_TIENDA", payload: false });
};

export const editarMarcaTienda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigMarca&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_marcaid=${info.marcaid}&NV_nombre=${info.nombremarca}&NI_ecommerce=${info.ecommerce}&OI_activo=${info.activo}&NV_imagenmarca=`
    );
    if (finded) {
      dispatch({ type: "EDITAR_MARCA_TIENDA", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEditarMarcaTienda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_EDITAR_MARCA_TIENDA", payload: false });
};

export const eliminarMarcaTienda = (marca) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTConfigMarcaDel&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_marcaid=${marca.marcaid}`
    );
    if (finded) {
      dispatch({ type: "ELIMINAR_MARCA_TIENDA", payload: finded.data.success });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarEliminarMarcaTienda = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ELIMINAR_MARCA_TIENDA", payload: false });
};

export const getMarcaTienda = (id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaCategorias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_categoriaid=${id}`
    );
    let aux = Object.values(finded.data.listacategorias).flat();
    if (finded) {
      dispatch({ type: "GET_CATEGORIA", payload: aux[0] });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getMarcasTienda = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let array = [];
    array[0] = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSListaMarcas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&pagActual=1&trae_todo=1&activo=0`
    );
    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.listamarcas);
    let aux;
    if (paginaUltima > 1) {
      for (let j = 1; j < paginaUltima; j++) {
        array[j] = await axios.get(
          `${
            process.env.REACT_APP_SERVER
          }php/GestionShop.php?type=GSListaMarcas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&pagActual=${
            j + 1
          }&trae_todo=1&activo=0`
        );
        aux = Object.values(array[j].data.listamarcas);
        array[j] = aux;
      }
    }
    array = array.flat();
    if (array) {
      dispatch({ type: "GET_MARCAS", payload: array });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getMarcasTiendaPaginado = (page, nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let array = [];
    array[0] = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSListaMarcas&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&nombre=${nombre}&paginaResultado=10&pagActual=${page}&trae_todo=1&activo=0`
    );

    let paginaUltima = array[0].data.paginaUltima;
    array[0] = Object.values(array[0].data.listamarcas);
    array = array.flat();
    array[array.length] = paginaUltima;
    if (array) {
      dispatch({ type: "GET_MARCAS_TIENDA_PAGINADO", payload: array });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const postImagenMarca = (images, id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var bodyFormData = new FormData();
  bodyFormData.append("img1", images);
  bodyFormData.append("type", "GTSetImagen");
  bodyFormData.append("OV_api_key", api_key);
  bodyFormData.append("productoid", 0);
  bodyFormData.append("OI_tiendaid", tiendausuario);
  bodyFormData.append("categoriaid", 0);
  bodyFormData.append("marcaid", id);
  bodyFormData.append("origen", 4);

  try {
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then(function (response) {
        dispatch({
          type: "AGREGAR_IMAGEN_MARCA",
          payload: response.data.success,
        });
      })
      .catch(function (response) {
        //handle error
        console.log(response);
      });
  } catch (error) {
    throw new Error(error);
  }
};

export const limpiarAgregarImagenMarca = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGAR_IMAGEN_MARCA", payload: false });
};

export const eliminarImagenMarca = (marcaid, imagenid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaImagen&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&categoriaid=0&marcaid=${marcaid}&productoid=0&producto_imagenid=${imagenid}&origen=4`
    );
    if (finded) {
      dispatch({ type: "ELIMINAR_IMAGEN_MARCA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const limpiarEliminarImagenMarca = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_AGREGAR_IMAGEN_MARCA", payload: false });
};

//----------------- PARAMETROS -----------------------

export const configParametros = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  let colorprimario = info.colorPrimario.replace("#", "%23");
  let colorsecundario = info.colorSecundario.replace("#", "%23");
  let colorterciario = info.colorTerciario.replace("#", "%23");
  let colorFondoSubNav = info.colorFondoSubNav.replace("#", "%23");
  let colorHoverFondoSubNav = info.colorHoverFondoSubNav.replace("#", "%23");
  let colorTextoSubNav = info.colorTextoSubNav.replace("#", "%23");
  let colorHoverTextoSubNav = info.colorHoverTextoSubNav.replace("#", "%23");
  let colorMenuDesplegable = info.colorMenuDesplegable.replace("#", "%23");
  let colorHoverFondoMenuDesplegable =
    info.colorHoverFondoMenuDesplegable.replace("#", "%23");
  let colorTextoMenuDesplegable = info.colorTextoMenuDesplegable.replace(
    "#",
    "%23"
  );
  let colorHoverTextoMenuDesplegable =
    info.colorHoverTextoMenuDesplegable.replace("#", "%23");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetConfigInfoFront&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&mostrar_info=${info.mostrar_info}&ecommerce_info=${info.ecommerce_info}&ecommerce_informacion=${info.ecommerce_informacion}&ecommerce_info2=${info.ecommerce_info2}&ecommerce_informacion2=${info.ecommerce_informacion2}&informacion_lista=${info.informacion_lista}&default_categorias=${info.default_categorias}&default_masvendido=${info.default_masvendido}&default_prodstock=${info.default_prodstock}&ecommerce_colorPrimario=${colorprimario}&ecommerce_colorSecundario=${colorsecundario}&ecommerce_colorTerciario=${colorterciario}&default_marcas=${info.default_marcas}&default_mantenimiento=${info.default_mantenimiento}&default_productohome=${info.default_productohome}&menu_desplegable=${info.menu_desplegable}&default_onlyread=${info.default_onlyread}&ecommerce_colorFondoSubNav=${colorFondoSubNav}&ecommerce_colorHoverFondoSubNav=${colorHoverFondoSubNav}&ecommerce_colorTextoSubNav=${colorTextoSubNav}&ecommerce_colorHoverTextoSubNav=${colorHoverTextoSubNav}&ecommerce_colorMenuDesplegable=${colorMenuDesplegable}&ecommerce_colorHoverFondoMenuDesplegable=${colorHoverFondoMenuDesplegable}&ecommerce_colorTextoMenuDesplegable=${colorTextoMenuDesplegable}&ecommerce_colorHoverTextoMenuDesplegable=${colorHoverTextoMenuDesplegable}`
    );
    if (finded) {
      dispatch({
        type: "CONFIG_PARAMETROS_TIENDA",
        payload: { success: finded.data.success },
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getConfigParametros = () => async (dispatch) => {
  var api_key = localStorage.getItem("api_key");
  var tiendausuario = localStorage.getItem("tiendausuario");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({
        type: "GET_CONFIG_PARAMETROS_TIENDA",
        payload: {
          parametros: {
            mostrar_info: finded.data.parametro.mostrar_info,
            ecommerce_info: finded.data.parametro.ecommerce_info,
            ecommerce_informacion: finded.data.parametro.ecommerce_informacion,
            ecommerce_info2: finded.data.parametro.ecommerce_info2,
            ecommerce_informacion2:
              finded.data.parametro.ecommerce_informacion2,
            default_categorias: finded.data.parametro.default_categorias,
            default_masvendido: finded.data.parametro.default_masvendido,
            default_prodstock: finded.data.parametro.default_prodstock,
            informacion_lista: finded.data.parametro.informacion_lista,
            colorPrimario: finded.data.parametro.ecommerce_colorPrimario,
            colorSecundario: finded.data.parametro.ecommerce_colorSecundario,
            colorTerciario: finded.data.parametro.ecommerce_colorTerciario,
            colorFondoSubNav: finded.data.parametro.ecommerce_colorFondoSubNav,
            colorHoverFondoSubNav:
              finded.data.parametro.ecommerce_colorHoverFondoSubNav,
            colorTextoSubNav: finded.data.parametro.ecommerce_colorTextoSubNav,
            colorHoverTextoSubNav:
              finded.data.parametro.ecommerce_colorHoverTextoSubNav,
            colorMenuDesplegable:
              finded.data.parametro.ecommerce_colorMenuDesplegable,
            colorHoverFondoMenuDesplegable:
              finded.data.parametro.ecommerce_colorHoverFondoMenuDesplegable,
            colorTextoMenuDesplegable:
              finded.data.parametro.ecommerce_colorTextoMenuDesplegable,
            colorHoverTextoMenuDesplegable:
              finded.data.parametro.ecommerce_colorHoverTextoMenuDesplegable,
            default_mantenimiento: finded.data.parametro.default_mantenimiento,
            default_marcas: finded.data.parametro.default_marcas,
            default_productohome: finded.data.parametro.default_productohome,
            menu_desplegable: finded.data.parametro.menu_desplegable,
            default_onlyread: finded.data.parametro.default_onlyread,
          },
          imagenes1: {
            imagen1_informacion: finded.data.parametro.imagen1_informacion,
            imagen2_informacion: finded.data.parametro.imagen2_informacion,
            imagen3_informacion: finded.data.parametro.imagen3_informacion,
          },
          imagenes2: {
            imagen1_informacion: finded.data.parametro.imagen1_informacion2,
            imagen2_informacion: finded.data.parametro.imagen2_informacion2,
            imagen3_informacion: finded.data.parametro.imagen3_informacion2,
          },
          logo: finded.data.parametro.imagenlogo,
        },
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const postImagenesParametros1 = (images) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var bodyFormData = new FormData();
  bodyFormData.append("type", "GTSetImagenFront");
  bodyFormData.append("OV_api_key", api_key);
  bodyFormData.append("OI_tiendaid", tiendausuario);
  bodyFormData.append("imagen1", images.imagen1);
  bodyFormData.append("img1", images.img1);
  bodyFormData.append("imagen2", images.imagen2);
  bodyFormData.append("img2", images.img2);
  bodyFormData.append("imagen3", images.imagen3);
  bodyFormData.append("img3", images.img3);
  bodyFormData.append("origen", 1);

  try {
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then(function (response) {
        dispatch({
          type: "AGREGAR_IMAGEN_PARAMETROS1",
          payload: response.data.success,
        });
      })
      .catch(function (response) {
        //handle error
        console.log(response);
      });
  } catch (error) {
    throw new Error(error);
  }
};

export const postImagenesParametros2 = (images) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var bodyFormData = new FormData();
  bodyFormData.append("type", "GTSetImagenFront");
  bodyFormData.append("OV_api_key", api_key);
  bodyFormData.append("OI_tiendaid", tiendausuario);
  bodyFormData.append("imagen1", images.imagen1);
  bodyFormData.append("img1", images.img1);
  bodyFormData.append("imagen2", images.imagen2);
  bodyFormData.append("img2", images.img2);
  bodyFormData.append("imagen3", images.imagen3);
  bodyFormData.append("img3", images.img3);
  bodyFormData.append("origen", 2);

  try {
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then(function (response) {
        dispatch({
          type: "AGREGAR_IMAGEN_PARAMETROS2",
          payload: response.data.success,
        });
      })
      .catch(function (response) {
        //handle error
        console.log(response);
      });
  } catch (error) {
    throw new Error(error);
  }
};

export const postLogo = (image) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var bodyFormData = new FormData();
  bodyFormData.append("type", "GTSetImagenLogo");
  bodyFormData.append("OV_api_key", api_key);
  bodyFormData.append("OI_tiendaid", tiendausuario);
  bodyFormData.append("img1", image);

  try {
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then(function (response) {
        dispatch({
          type: "AGREGAR_IMAGEN_LOGO",
          payload: response.data.success,
        });
      })
      .catch(function (response) {
        //handle error
        console.log(response);
      });
  } catch (error) {
    throw new Error(error);
  }
};

//w
// export const getLogo = () => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   try {
//     const finded = await axios.post(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
//     );
//     console.log('finded', finded);
//     if (finded) {
//       dispatch({ type: "GET_LOGO", payload: finded.data.parametro.imagenlogo });
//     } else throw new Error("no pudo modificarse");
//   } catch (error) {
//     throw new Error(error);
//   }
// };

//f with error handling
export const getLogo = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );

    // Check if the response is a string containing error messages
    if (
      typeof response.data === "string" &&
      response.data.includes("Error al decodificar JSON")
    ) {
      console.warn("Server returned error:", response.data);
      dispatch({ type: "GET_LOGO", payload: "" });
      return;
    }

    // Check if the response has the expected structure
    if (
      response.data &&
      response.data.parametro &&
      response.data.parametro.hasOwnProperty("imagenlogo")
    ) {
      dispatch({
        type: "GET_LOGO",
        payload: response.data.parametro.imagenlogo,
      });
    } else {
      // If the structure is not as expected, dispatch an empty string
      console.warn("Logo data structure is not as expected:", response.data);
      dispatch({ type: "GET_LOGO", payload: "" });
    }
  } catch (error) {
    console.error("Error fetching logo:", error);
    // In case of error, dispatch an empty string
    dispatch({ type: "GET_LOGO", payload: "" });
  }
};

export const getMonedaDefault = () => async () => {
  var api_key = localStorage.getItem("api_key");
  var tiendausuario = localStorage.getItem("tiendausuario");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSParametroTienda&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}`
    );
    if (finded) {
      localStorage.setItem(
        "default_monedaid",
        finded.data.parametro.default_monedaid
      );
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//---------------------------- REPORTES --------------------------------------------
export const getReporteListaDePrecios =
  (marca, categoria, nombre, lista, codigo, page) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let array = [];
      array[0] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GIListaPrecio&tiendaid=${tiendausuario}&api_key=${api_key}&marcaid=${marca}&categoriaid=${categoria}&estiloid=&codigo=${codigo}&nombre=${nombre}&listaid=${lista}&paginaResultado=20&pagActual=${page}`
      );
      let paginaUltima = array[0].data.paginaUltima;
      array[0] = Object.values(array[0].data.listaprecio);
      array = array.flat();
      array[array.length] = paginaUltima;

      if (array) {
        //type es el nombre con el que luego se relaciona esta accion con el reducer que guardara la informacion
        dispatch({ type: "GET_REPORTE_LISTA_PRECIOS", payload: array });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getSubdiarioCompras =
  (fecha_desde, fecha_hasta, page) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let array = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GISubdiarioCompras&tiendaid=${tiendausuario}&api_key=${api_key}&fecha_desde=${fecha_desde}&fecha_hasta=${fecha_hasta}&paginaResultado=20&pagActual=${page}`
      );
      if (array) {
        //type es el nombre con el que luego se relaciona esta accion con el reducer que guardara la informacion
        dispatch({ type: "GET_SUBDIARIO_COMPRAS", payload: array.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getSubdiarioVentas =
  (fecha_desde, fecha_hasta, page) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let data = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GISubdiarioVentas&tiendaid=${tiendausuario}&api_key=${api_key}&fecha_desde=${fecha_desde}&fecha_hasta=${fecha_hasta}&paginaResultado=20&pagActual=${page}`
      );
      if (data) {
        //type es el nombre con el que luego se relaciona esta accion con el reducer que guardara la informacion
        dispatch({ type: "GET_SUBDIARIO_VENTAS", payload: data.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getFacturasElectronicas =
  (fecha_desde, fecha_hasta, page) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let array = [];
      array[0] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GIFacturasElectronicas&tiendaid=${tiendausuario}&api_key=${api_key}&fecha_desde=${fecha_desde}&fecha_hasta=${fecha_hasta}&paginaResultado=20&pagActual=${page}`
      );
      let paginaUltima = array[0].data.paginaUltima;
      array[0] = Object.values(array[0].data.facturaselectronicas);
      array = array.flat();
      array[array.length] = paginaUltima;

      if (array) {
        //type es el nombre con el que luego se relaciona esta accion con el reducer que guardara la informacion
        dispatch({ type: "GET_FACTURAS_ELECTRONICAS", payload: array });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getStockVendido =
  (fecha_desde, fecha_hasta) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let array = [];
      array[0] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GIStockVendido&tiendaid=${tiendausuario}&api_key=${api_key}&fecha_desde=${fecha_desde}&fecha_hasta=${fecha_hasta}`
      );
      let aux = Object.values(array[0].data.stockvendido);
      if (array) {
        //type es el nombre con el que luego se relaciona esta accion con el reducer que guardara la informacion
        dispatch({ type: "GET_STOCK_VENDIDO", payload: aux });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

//obtiene todos los estilos que se listaran a la hora de filtrar por estilo
export const getEstilosTienda = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetEstilos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&trae_todo=1&activo=0`
    );
    if (finded) {
      let aux = Object.values(finded.data.colores);
      dispatch({ type: "GET_ESTILOS_TIENDA", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//----------------------- GET INFO PICKIT -----------------------------
export const getInfoPickit =
  (clientepickitid, clienteid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetPickitCliente&tiendaid=${tiendausuario}&api_key=${api_key}&clientepickitid=${clientepickitid}&clienteid=${clienteid}`
      );
      if (finded) {
        dispatch({
          type: "GET_INFO_PICKIT",
          payload: finded.data.envios_pickit[clientepickitid],
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

//----------------------- GET TICKER PICKIT ----------------------------
export const getTicketPickit = (info_pedido_pickit) => async (dispatch) => {
  let token = localStorage.getItem("pickittoken");
  let apiKey = localStorage.getItem("pickitkey");

  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_PICKIT}api/etiquetaPDF/${info_pedido_pickit.transactionid}`,
      { token: token, apiKey: apiKey }
    );
    if (finded) {
      dispatch({ type: "GET_TICKET_PICKIT", payload: finded.data.url });
    } else throw new Error("Error api pickit");
  } catch (error) {
    throw new Error(error);
  }
};

//------------------------ GET PROVINCIAS --------------------------------
export const getListadoProvincias = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetProvincias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paisid=40&provinciaid=&mercadolibreid=0&nombreprovincia=`
    );
    if (finded) {
      let aux = Object.values(finded.data.provincia);
      dispatch({ type: "GET_LISTADO_PROVINCIAS", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//------------------------ GET PROVINCIA --------------------------------
export const getProvincia = (provinciaid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  if (provinciaid !== undefined) {
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetProvincias&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paisid=40&provinciaid=${provinciaid}&mercadolibreid=0&nombreprovincia=`
      );
      if (finded) {
        dispatch({
          type: "GET_PROVINCIA",
          payload: finded.data.provincia[provinciaid].nombre,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  }
};

//------------------------ GET PROVINCIA --------------------------------
export const getCiudad = (provinciaid, localidadid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  if (provinciaid !== undefined && localidadid !== undefined) {
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetLocalidades&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paisid=40&provinciaid=${provinciaid}&localidadid=${localidadid}&nombrelocalidad=&codigopostal=`
      );
      if (finded) {
        dispatch({
          type: "GET_CIUDAD",
          payload: finded.data.localidad[localidadid].nombrelocalidad,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  }
};
//------------------------ GET PUNTO PICKIT --------------------------------
export const getPuntoPickit = (id) => async (dispatch) => {
  let token = localStorage.getItem("pickittoken");
  let apiKey = localStorage.getItem("pickitkey");

  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_PICKIT}api/listadoPuntos?id=${id}`,
      { token: token, apiKey: apiKey }
    );
    if (finded) {
      dispatch({ type: "GET_PUNTO_PICKIT", payload: finded.data[0] });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//------------------------- GET PROVEEDORES ---------------------------------
export const getProveedoresTienda = (page, nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetProveedores&api_key=${api_key}&tiendaid=${tiendausuario}&OI_proveedorid=0&nombre=${nombre}&paginaActual=${page}&paginaResultado=10`
    );
    if (finded) {
      let paginaUltima = finded.data.paginaUltima;
      let array = Object.values(finded.data.proveedores);
      array = array.flat();
      array[array.length] = paginaUltima;
      dispatch({ type: "GET_PROVEEDORES_TIENDA", payload: array });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getListadoProveedorByName = (nombre) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let finded = "";
    if (nombre !== "") {
      finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetProveedores&api_key=${api_key}&tiendaid=${tiendausuario}&OI_proveedorid=0&nombre=${nombre}&paginaActual=1&paginaResultado=10`
      );
      finded = Object.values(finded.data.proveedores);
    } else {
      finded = [];
    }

    if (finded) {
      dispatch({ type: "GET_LISTADO_PROVEEDORES_BY_NAME", payload: finded });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};
//------------------------- AGREGAR PROVEEDOR ---------------------------------
export const agregarProveedorTienda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetProveedor&api_key=${api_key}&tiendaid=${tiendausuario}&proveedorid=0&nombre=${info.nombre}&tipodocumentoid=${info.tipodocumentoid}&numerodocumento=${info.numerodocumento}&telefono=${info.telefono}&email=${info.email}&emailfacturacion=${info.emailfacturacion}&tipocondicioniva=${info.tipocondicioniva}&descuento=${info.descuento}&activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "AGREGAR_PROVEEDOR_TIENDA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//------------------------- EDITAR PROVEEDOR ---------------------------------
export const editarProveedorTienda = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetProveedor&api_key=${api_key}&tiendaid=${tiendausuario}&proveedorid=${info.proveedorid}&nombre=${info.nombre}&tipodocumentoid=${info.tipodocidentidadid}&numerodocumento=${info.cuit}&telefono=${info.telefono}&email=${info.email}&emailfacturacion=${info.emailfactura}&tipocondicioniva=${info.tipocondicionivaid}&descuento=${info.descuento}&activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_PROVEEDOR_TIENDA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//--------------------------------------- COTIZACIONES ------------------------------------------

export const getCotizaciones = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.post(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetMonedaCotizaciones&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&monedaid=0&cotizacion_monedaid=0&pagActual=${page}&paginaResultado=10`
    );
    if (finded) {
      let paginaUltima = finded.data.paginaUltima;
      let aux = Object.values(finded.data.cotizaciones);
      aux[aux.length] = paginaUltima;
      dispatch({ type: "GET_COTIZACIONES", payload: aux });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const agregarCotizacion = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetMonedaCotizacion&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&cotizacion_monedaid=0&monedaid=${info.moneda}&fecha=${info.fecha}&valorcompra=${info.valorCompra}&valorventa=${info.valorVenta}&valor=`
    );
    if (finded) {
      dispatch({ type: "AGREGAR_COTIZACION", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const editarCotizacion = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetMonedaCotizacion&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&monedaid=${info.monedaid}&cotizacion_monedaid=${info.cotizacion_monedaid}&fecha=${info.fecha}&valorcompra=${info.valorcompra}&valorventa=${info.valorventa}&valor=`
    );
    if (finded) {
      dispatch({ type: "EDITAR_COTIZACION", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

//-------------------------- FACTURA ELECTRONICA ----------------------------------
export const facturarPedido = (pedidoplacaid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}afip/GestionFacturaAfip.php?type=GFAGeneraFCAFIP&tiendaid=${tiendausuario}&api_key=${api_key}&pedidoplacaid=${pedidoplacaid}&usuarioid=${idusuario}`
    );
    if (finded) {
      dispatch({ type: "FACTURAR_PEDIDO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const facturarPuntoDeVenta =
  (pedidoplacaid, facturaid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var idusuario = localStorage.getItem("idusuario");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}afip/GestionFacturaAfip.php?type=GFAGeneraFCAFIP&tiendaid=${tiendausuario}&api_key=${api_key}&pedidoplacaid=${pedidoplacaid}&facturaid=${facturaid}&usuarioid=${idusuario}`
      );
      if (finded) {
        dispatch({ type: "FACTURAR_PUNTO_VENTA", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const facturarVariosPedidos = (array) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  var errores = [];
  var finded;

  try {
    for (let i = 0; i < array.length; i++) {
      finded = await axios.get(
        `${process.env.REACT_APP_SERVER}afip/GestionFacturaAfip.php?type=GFAGeneraFCAFIP&tiendaid=${tiendausuario}&api_key=${api_key}&pedidoplacaid=${array[i]}&usuarioid=${idusuario}`
      );
      if (finded.data.success === false) {
        errores.push({ id: array[i], mensaje: finded.data.mensaje });
      }
    }
    if (errores.length === 0) {
      dispatch({
        type: "FACTURAR_MASIVAMENTE",
        payload: {
          success: "success",
          mensaje: "Pedidos facturados con éxito!",
        },
      });
    } else {
      dispatch({
        type: "FACTURAR_MASIVAMENTE",
        payload: {
          success: "warning",
          mensaje:
            "Hubo un problema para facturar alguno/s de los pedidos. Revise que los datos del cliente sean correctos.",
        },
      });
    }
  } catch (error) {
    throw { message: error };
  }
};

export const postConfigAfip = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetConfigAFIP&tiendaid=${tiendausuario}&api_key=${api_key}&empresaconfid=${info.empresaconfid}&empresaid=${info.empresaid}&idtipodocumento=2&cuit=${info.cuit}&puntoventanro=${info.puntoventanro}&tipo_documentoafip=${info.tipo_documentoafip}&tipowebserviceid=${info.tipowebserviceid}&passphrase=${info.passphrase}&wsdlconexion=${info.wsdlconexion}&urlconexion=${info.urlconexion}&urlfev1=${info.urlfev1}&wsdlfev1=${info.wsdlfev1}&tope_cf=${info.tope_cf}&tope_fce=${info.tope_fce}&activo=${info.activo}&ptovta=${info.ptovta}&pedidos=${info.pedidos}&email_fc=${info.email_fc}`
    );
    if (finded) {
      dispatch({ type: "CONFIG_AFIP", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const getConfigAfip = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetConfigAFIP&tiendaid=${tiendausuario}&api_key=${api_key}`
    );
    if (finded) {
      dispatch({ type: "GET_CONFIG_AFIP", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const postCertificados =
  (empresaconfid, certificado, privatekey) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var bodyFormData = new FormData();

    bodyFormData.append("type", "GTSetCertificadoAFIP");
    bodyFormData.append("api_key", api_key);
    bodyFormData.append("tiendaid", tiendausuario);
    bodyFormData.append("empresaconfid", empresaconfid);
    bodyFormData.append("certificado", certificado);
    bodyFormData.append("privatekey", privatekey);

    try {
      axios({
        method: "POST",
        url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
        data: bodyFormData,
        headers: { "Content-Type": "text/plain" },
      })
        .then(function (response) {
          dispatch({
            type: "AGREGAR_CERTIFICADOS_AFIP",
            payload: response.data,
          });
        })
        .catch(function (response) {
          console.log(response);
        });
    } catch (error) {
      throw new Error(error);
    }
  };

//---------------------------- NUEVA VENTA ---------------------------------
//w returns all products regardless of whether they have stock or not
// export const getProductosByNameSkuNuevaVenta = (input) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   try {
//     let array = [];
//     if (input === "") {
//       dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: array });
//     } else {
//       if (isNaN(input)) {
//         array[0] = await axios.get(
//           `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=${input}&codigoarticulo=&pagActual=1&trae_todo=1&OI_activo=1 `
//         );
//       } else {
//         array[0] = await axios.get(
//           `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=&codigoarticulo=${input}&pagActual=1&trae_todo=1&OI_activo=1 `
//         );
//       }
//       array[0] = Object.values(array[0].data.listaproductos);
//       array = array.flat();

//       for (let j = 0; j < array.length; j++) {
//         array[j].imagenes = Object.values(array[j].imagenes).flat();
//       }

//       if (array) {
//         dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: array });
//       } else throw new Error("no hay informacion");
//     }
//   } catch (error) {
//     throw { message: error };
//   }
// };

//f filtra segun tipofactura (PERO en local no funciona, aun trae productos con stock 0)
// export const getProductosByNameSkuNuevaVenta = (input, tipofactura) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   try {
//     let array = [];
//     if (input === "") {
//       dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: array });
//     } else {
//       // Set trae_todo based on tipofactura
//       // For "factura_pedido" tab, only show products with stock (trae_todo=0)
//       // For other tabs like "factura_presupuesto", show all products (trae_todo=1)
//       const trae_todo = tipofactura === "factura_pedido" ? "0" : "1";

//       if (isNaN(input)) {
//         array[0] = await axios.get(
//           `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=${input}&codigoarticulo=&pagActual=1&trae_todo=${trae_todo}&OI_activo=1 `
//         );
//       } else {
//         array[0] = await axios.get(
//           `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=&codigoarticulo=${input}&pagActual=1&trae_todo=${trae_todo}&OI_activo=1 `
//         );
//       }
//       array[0] = Object.values(array[0].data.listaproductos);
//       array = array.flat();

//       for (let j = 0; j < array.length; j++) {
//         array[j].imagenes = Object.values(array[j].imagenes).flat();
//       }

//       if (array) {
//         dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: array });
//       } else throw new Error("no hay informacion");
//     }
//   } catch (error) {
//     throw { message: error };
//   }
// };

// export const getProductosByNameSkuNuevaVenta = (input, tipofactura) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   try {
//     let array = [];
//     if (input === "") {
//       dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: array });
//     } else {
//       // Set trae_todo based on tipofactura
//       const trae_todo = tipofactura === "factura_pedido" ? "0" : "1";

//       if (isNaN(input)) {
//         array[0] = await axios.get(
//           `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=${input}&codigoarticulo=&pagActual=1&trae_todo=${trae_todo}&OI_activo=1 `
//         );
//       } else {
//         array[0] = await axios.get(
//           `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=&codigoarticulo=${input}&pagActual=1&trae_todo=${trae_todo}&OI_activo=1 `
//         );
//       }

//       // If we're in "factura_pedido" mode, filter out products with zero stock in the frontend as well
//       if (tipofactura === "factura_pedido" && array[0].data.listaproductos) {
//         const filteredProducts = {};
//         Object.entries(array[0].data.listaproductos).forEach(([key, product]) => {
//           if (parseInt(product.cantidadactiva) > 0) {
//             filteredProducts[key] = product;
//           }
//         });
//         array[0].data.listaproductos = filteredProducts;
//       }

//       // Only proceed if there are products after filtering
//       if (Object.keys(array[0].data.listaproductos).length > 0) {
//         array[0] = Object.values(array[0].data.listaproductos);
//         array = array.flat();

//         for (let j = 0; j < array.length; j++) {
//           array[j].imagenes = Object.values(array[j].imagenes).flat();
//         }

//         dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: array });
//       } else {
//         dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: [] });
//       }
//     }
//   } catch (error) {
//     dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: [] });
//     throw { message: error };
//   }
// };

export const getProductosByNameSkuNuevaVenta =
  (input, tipofactura) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let array = [];
      if (input === "") {
        dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: array });
      } else {
        // Set trae_todo based on tipofactura
        // For "factura_pedido" tab, try to get only products with stock (trae_todo=0)
        // For other tabs like "factura_presupuesto", show all products (trae_todo=1)
        const trae_todo = tipofactura === "factura_pedido" ? "0" : "1";

        if (isNaN(input)) {
          array[0] = await axios.get(
            `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=${input}&codigoarticulo=&pagActual=1&trae_todo=${trae_todo}&OI_activo=1 `
          );
        } else {
          array[0] = await axios.get(
            `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=&codigoarticulo=${input}&pagActual=1&trae_todo=${trae_todo}&OI_activo=1 `
          );
        }

        // For Pedido tab, strictly filter out products with zero stock
        if (tipofactura === "factura_pedido" && array[0].data.listaproductos) {
          const filteredProducts = {};
          Object.entries(array[0].data.listaproductos).forEach(
            ([key, product]) => {
              // Only include products with stock greater than 0
              if (parseInt(product.cantidadactiva) > 0) {
                filteredProducts[key] = product;
              }
            }
          );

          // Replace the original products with filtered ones
          array[0].data.listaproductos = filteredProducts;
        }

        // Process the products if there are any
        if (
          array[0].data.listaproductos &&
          Object.keys(array[0].data.listaproductos).length > 0
        ) {
          array[0] = Object.values(array[0].data.listaproductos);
          array = array.flat();

          for (let j = 0; j < array.length; j++) {
            array[j].imagenes = Object.values(array[j].imagenes).flat();
          }

          dispatch({
            type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA",
            payload: array,
          });
        } else {
          dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: [] });
        }
      }
    } catch (error) {
      dispatch({ type: "GET_PRODUCTOS_BY_NAME_NUEVA_VENTA", payload: [] });
      throw { message: error };
    }
  };

//w (busca entre todos los clientes)
// export const getListadoClientesByName = (nombre, id) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   try {
//     let finded = "";
//     if (id !== "") {
//       finded = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=${id}&paginaActual=1&paginaResultado=10&OV_nombre=&NV_cuit=&OV_email=`
//       );
//       finded = Object.values(finded.data.clientes);
//     } else {
//       if (nombre !== "") {
//         finded = await axios.get(
//           `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=&paginaActual=1&paginaResultado=10&OV_nombre=${nombre}&NV_cuit=&OV_email=`
//         );
//         finded = Object.values(finded.data.clientes);
//       } else {
//         finded = [];
//       }
//     }

//     if (finded) {
//       dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: finded });
//     } else throw new Error("hubo un problema");
//   } catch (error) {
//     throw new Error(error);
//   }
// };

//f busqueda condicional si es admin o no
// export const getListadoClientesByName = (nombre, id) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   var vendedorid = localStorage.getItem("idusuario");

//   // Check if user has admin profile
//   const hasAdminProfile = () => {
//     const perfiles = localStorage.getItem("perfiles")
//       ? JSON.parse(localStorage.getItem("perfiles"))
//       : {};
//     return Object.values(perfiles).includes(7);
//   };

//   // If not admin, add vendedorid parameter to only get their clients
//   const vendedorParam = hasAdminProfile() ? "" : `&vendedorid=${vendedorid}`;

//   try {
//     let finded = "";
//     if (id !== "") {
//       finded = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=${id}&paginaActual=1&paginaResultado=10&OV_nombre=&NV_cuit=&OV_email=${vendedorParam}`
//       );
//       finded = Object.values(finded.data.clientes);
//     } else {
//       if (nombre !== "") {
//         finded = await axios.get(
//           `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=&paginaActual=1&paginaResultado=10&OV_nombre=${nombre}&NV_cuit=&OV_email=${vendedorParam}`
//         );
//         finded = Object.values(finded.data.clientes);
//       } else {
//         finded = [];
//       }
//     }

//     if (finded) {
//       dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: finded });
//     } else throw new Error("hubo un problema");
//   } catch (error) {
//     throw new Error(error);
//   }
// };

//ff with error handling in case the backend sends empty response
// export const getListadoClientesByName = (nombre, id) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   var vendedorid = localStorage.getItem("idusuario");

//   // Check if user has admin profile
//   const hasAdminProfile = () => {
//     const perfiles = localStorage.getItem("perfiles")
//       ? JSON.parse(localStorage.getItem("perfiles"))
//       : {};
//     return Object.values(perfiles).includes(7);
//   };

//   // If not admin, add vendedorid parameter to only get their clients
//   const vendedorParam = hasAdminProfile() ? "" : `&vendedorid=${vendedorid}`;

//   try {
//     let response;

//     if (id !== "") {
//       response = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=${id}&paginaActual=1&paginaResultado=10&OV_nombre=&NV_cuit=&OV_email=${vendedorParam}`
//       );
//     } else if (nombre !== "") {
//       response = await axios.get(
//         `${
//           process.env.REACT_APP_SERVER
//         }php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=&paginaActual=1&paginaResultado=10&OV_nombre=${encodeURIComponent(
//           nombre
//         )}&NV_cuit=&OV_email=${vendedorParam}`
//       );
//     } else {
//       // If no search criteria, dispatch empty array
//       dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
//       return;
//     }

//     // Check if response has data and the expected structure
//     if (response && response.data) {
//       // console.log("Response data:", response.data);

//       // Handle empty response body
//       if (Object.keys(response.data).length === 0) {
//         // console.log("Empty response body received");
//         dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
//         return;
//       }

//       // Handle response with clients
//       if (response.data.success && response.data.clientes) {
//         const clientesArray = Object.values(response.data.clientes);
//         // console.log("Clients found:", clientesArray);
//         dispatch({
//           type: "GET_LISTADO_CLIENTES_BY_NAME",
//           payload: clientesArray,
//         });
//         return;
//       }
//     }

//     // If we reach here, something unexpected happened with the response
//     console.log("Unexpected response structure");
//     dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
//   } catch (error) {
//     console.error("Error fetching clients:", error);
//     dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
//   }
// };

//f can serch by cuit
export const getListadoClientesByName = (nombre, id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var vendedorid = localStorage.getItem("idusuario");

  // Check if user has admin profile
  const hasAdminProfile = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    return Object.values(perfiles).includes(7);
  };

  // If not admin, add vendedorid parameter to only get their clients
  const vendedorParam = hasAdminProfile() ? "" : `&vendedorid=${vendedorid}`;

  try {
    let response;
    if (id !== "") {
      response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=${id}&paginaActual=1&paginaResultado=10&OV_nombre=&NV_cuit=&OV_email=${vendedorParam}`
      );
    } else if (nombre !== "") {
      // Check if input is likely a CUIT (only numbers)
      const isCuit = /^\d+$/.test(nombre);

      if (isCuit) {
        // If it's a CUIT, search by CUIT
        response = await axios.get(
          `${
            process.env.REACT_APP_SERVER
          }php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=&paginaActual=1&paginaResultado=10&OV_nombre=&NV_cuit=${encodeURIComponent(
            nombre
          )}&OV_email=${vendedorParam}`
        );
      } else {
        // If it's not a CUIT, search by name as before
        response = await axios.get(
          `${
            process.env.REACT_APP_SERVER
          }php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=&paginaActual=1&paginaResultado=10&OV_nombre=${encodeURIComponent(
            nombre
          )}&NV_cuit=&OV_email=${vendedorParam}`
        );
      }
    } else {
      // If no search criteria, dispatch empty array
      dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
      return;
    }

    // Check if response has data and the expected structure
    if (response && response.data) {
      // console.log("Response data:", response.data);
      // Handle empty response body
      if (Object.keys(response.data).length === 0) {
        // console.log("Empty response body received");
        dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
        return;
      }

      // Handle response with clients
      if (response.data.success && response.data.clientes) {
        const clientesArray = Object.values(response.data.clientes);
        // console.log("Clients found:", clientesArray);
        dispatch({
          type: "GET_LISTADO_CLIENTES_BY_NAME",
          payload: clientesArray,
        });
        return;
      }
    }

    // If we reach here, something unexpected happened with the response
    console.log("Unexpected response structure");
    dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
  } catch (error) {
    console.error("Error fetching clients:", error);
    dispatch({ type: "GET_LISTADO_CLIENTES_BY_NAME", payload: [] });
  }
};

export const agregarArticulo =
  (clienteid, articulo, cantidad, descuento, tipofactura, facturaid) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var idusuario = localStorage.getItem("idusuario");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSAgregoArticulo&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_clienteid=${clienteid}&OV_facturaid=${facturaid}&OV_codigobarras=${articulo}&OV_cantidad=${cantidad}&OI_tipodescuentoid=${descuento}&OV_tipofactura=${tipofactura}&OI_usuarioid=${idusuario}`
      );
      if (finded) {
        dispatch({ type: "AGREGAR_ARTICULO", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const eliminarArticulo =
  (clienteid, facturadetalleid, tipofactura, facturaid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSArticuloDetalleBorrar&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_facturadetalleid=${facturadetalleid}&OI_clienteid=${clienteid}&OV_tipofactura=${tipofactura}&OI_facturaid=${facturaid}`
      );
      if (finded) {
        dispatch({ type: "ELIMINAR_ARTICULO", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const vaciarCarrito = (clienteid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSVaciarCarrito&OV_api_key=${api_key}&OV_tiendaid=${tiendausuario}&OI_tiendaid=${tiendausuario}&OI_vaciarcarrito=-1&OI_clienteid=${clienteid}`
    );
    if (finded) {
      dispatch({ type: "VACIAR_CARRITO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getCarritoDetalle =
  (clienteid, factura, page, tipofactura) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSArticuloDetalle&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OV_facturaid=${factura}&OV_tipofactura=${tipofactura}&OI_clienteid=${clienteid}&pagActual=${page}&paginaResultado=10&es_ecommerce=0`
      );
      if (finded) {
        dispatch({ type: "GET_CARRITO_DETALLE", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getCalcularCarrito =
  (clienteid, descuentoid, intereses = 0, tipofactura, facturaid) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSCalcularFactura&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_clienteid=${clienteid}&OI_facturaid=${facturaid}&OV_tipofactura=${tipofactura}&OV_descuento=${descuentoid}&OV_intereses=${intereses}`
      );
      if (finded) {
        dispatch({ type: "CALCULAR_CARRITO", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const resetCarrito = () => {
  return {
    type: "RESET_CARRITO",
  };
};

//w
// export const finalizarPedido =
//   (clienteid, tipoenvio, tipopago, facturaid, tipofactura) =>
//   async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");

//     try {
//       const finded = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSFinFactura&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}&OI_clienteid=${clienteid}&OI_tipo_envio=${tipoenvio}&OV_tipofactura=${tipofactura}&OI_tipo_pago=${tipopago}&url_site=${process.env.REACT_APP_SERVER}`
//       );
//       if (finded) {
//         dispatch({ type: "FINALIZAR_PEDIDO", payload: finded.data });
//       } else throw new Error("no pudo modificarse");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//f with fecha de pedido del cliente (base)
// export const finalizarPedido =
//   (clienteid, tipoenvio, tipopago, facturaid, tipofactura, fechaPedido) =>
//   async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");

//     try {
//       const finded = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSFinFactura&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}&OI_clienteid=${clienteid}&OI_tipo_envio=${tipoenvio}&OV_tipofactura=${tipofactura}&OI_tipo_pago=${tipopago}&OD_fechapedido=${fechaPedido}&url_site=${process.env.REACT_APP_SERVER}`
//       );
//       if (finded) {
//         dispatch({ type: "FINALIZAR_PEDIDO", payload: finded.data });
//       } else throw new Error("no pudo modificarse");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//w with fecha de pedido del cliente, ND_fecha_entrega, OI_ecommerce
// export const finalizarPedido =
//   (
//     clienteid,
//     tipoenvio,
//     tipopago,
//     facturaid,
//     tipofactura,
//     fechaPedido,
//     fechaEntrega = "",
//     ecommerce = 0
//   ) =>
//   async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");

//     try {
//       const finded = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSFinFactura&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}&OI_clienteid=${clienteid}&OI_tipo_envio=${tipoenvio}&OV_tipofactura=${tipofactura}&OI_tipo_pago=${tipopago}&OD_fechapedido=${fechaPedido}&ND_fecha_entrega=${fechaEntrega}&OI_ecommerce=${ecommerce}&url_site=${process.env.REACT_APP_SERVER}`
//       );
//       if (finded) {
//         dispatch({ type: "FINALIZAR_PEDIDO", payload: finded.data });
//       } else throw new Error("no pudo modificarse");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//f 
export const finalizarPedido =
  (
    clienteid,
    tipoenvio,
    tipopago,
    facturaid,
    tipofactura,
    fechaPedido,
    fechaEntrega = "",
    ecommerce = 0
  ) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var idusuario = localStorage.getItem("idusuario"); // Get the user ID from localStorage

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSFinFactura&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}&OI_clienteid=${clienteid}&OI_tipo_envio=${tipoenvio}&OV_tipofactura=${tipofactura}&OI_tipo_pago=${tipopago}&OD_fechapedido=${fechaPedido}&ND_fecha_entrega=${fechaEntrega}&OI_ecommerce=${ecommerce}&OI_vendedorid=${parseInt(idusuario) || 0}&url_site=${process.env.REACT_APP_SERVER}`
      );
      if (finded) {
        dispatch({ type: "FINALIZAR_PEDIDO", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

//w editar fecha de entrega
// export const editarFechaEntrega = (facturaid, fechaEntrega) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");

//   try {
//     const response = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetFechaEntrega&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}&OD_fecha_entrega=${fechaEntrega}`
//     );

//     if (response.data && response.data.success) {
//       dispatch({ type: "EDITAR_FECHA_ENTREGA", payload: response.data });
//       return response.data;
//     } else {
//       throw new Error(response.data.mensaje || "No se pudo modificar la fecha de entrega");
//     }
//   } catch (error) {
//     console.error("Error en editarFechaEntrega:", error);
//     throw error;
//   }
// };

//f with error message
export const editarFechaEntrega =
  (facturaid, fechaEntrega) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      console.log("Sending request with params:", {
        tiendaid: tiendausuario,
        api_key: api_key,
        facturaid: facturaid,
        fecha_entrega: fechaEntrega,
      });

      const response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetFechaEntrega&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}&OD_fecha_entrega=${fechaEntrega}`
      );

      console.log("API response:", response.data);

      // Always dispatch the result, even if success is false
      dispatch({
        type: "EDITAR_FECHA_ENTREGA",
        payload: response.data,
      });

      return response.data;
    } catch (error) {
      console.error("Error in editarFechaEntrega:", error);

      // Dispatch an error state to the reducer
      dispatch({
        type: "EDITAR_FECHA_ENTREGA",
        payload: {
          success: false,
          mensaje: error.message || "Error al actualizar la fecha de entrega",
        },
      });

      throw error;
    }
  };

export const getTiposDePago = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetFormaPago&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&filtraFormaPago=0`
    );
    if (finded) {
      dispatch({
        type: "GET_TIPOS_DE_PAGO",
        payload: Object.values(finded.data.formaspagos),
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getTiposTarjeta = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetTiposTarjeta&tiendaid=${tiendausuario}&api_key=${api_key}`
    );
    if (finded) {
      dispatch({
        type: "GET_TIPOS_DE_TARJETAS",
        payload: Object.values(finded.data.tarjetas),
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getCuotasTarjeta = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetCuotasTarjeta&tiendaid=${tiendausuario}&api_key=${api_key}`
    );
    if (finded) {
      dispatch({
        type: "GET_CUOTAS_TARJETAS",
        payload: Object.values(finded.data.cuotas_tarjetas),
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const modificarDescuentoArticulo =
  (productoid, facturadetalleid, descuento) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var idusuario = localStorage.getItem("idusuario");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTModificarDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&productoid=${productoid}&facturadetalleid=${facturadetalleid}&descuento=${descuento.descuento}&nombre_desc=${descuento.nombre}`
      );
      if (finded) {
        dispatch({
          type: "MODIFICAR_DESCUENTO_ARTICULO",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const modificarCantidadArticulo =
  (facturadetalleid, cantidad) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTModificarDetalleCantidad&tiendaid=${tiendausuario}&api_key=${api_key}&facturadetalleid=${facturadetalleid}&cantidad=${cantidad}&monto=&nombre_desc=`
      );
      if (finded) {
        dispatch({ type: "MODIFICAR_CANTIDAD_ARTICULO", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const modificarProductoEditable =
  (facturadetalleid, cantidad, monto, descripcion) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    monto = monto.replace(",", ".");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGuardaProductoEditable&tiendaid=${tiendausuario}&api_key=${api_key}&facturadetalleid=${facturadetalleid}&cantidad=${cantidad}&monto=${monto}&descripcion=${descripcion}`
      );
      if (finded) {
        dispatch({ type: "MODIFICAR_PRODUCTO_EDITABLE", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const limpiarModificarProductoEditable = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_MODIFICAR_PRODUCTO_EDITABLE", payload: "" });
};

//--------------------------- GET CLIENTE BY ID -------------------------------

export const getClienteById = (id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let finded = "";
    finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetClientes&OV_api_key=${api_key}&OI_tiendaid=${tiendausuario}&OI_clienteid=${id}&paginaActual=1&paginaResultado=20&OV_nombre=&NV_cuit=&OV_email=`
    );

    let cliente = Object.values(finded.data.clientes)[0];
    if (finded) {
      dispatch({
        type: "GET_CLIENTE_BY_ID",
        payload: {
          client: cliente.nombre,
          lastName: cliente.apellido,
          cuit: cliente.cuit,
          email: cliente.email,
          phone: cliente.telefono,
          address:
            (Object.keys(cliente.direccioncliente).length > 0 &&
              Object.values(cliente.direccioncliente)[0].direccion) ||
            "",
          streetNumber:
            (Object.keys(cliente.direccioncliente).length > 0 &&
              Object.values(cliente.direccioncliente)[0].altura) ||
            "",
          province:
            (Object.keys(cliente.direccioncliente).length > 0 &&
              Object.values(cliente.direccioncliente)[0].provincia) ||
            "",
          postalCode:
            Object.keys(cliente.direccioncliente).length > 0 &&
            Object.values(cliente.direccioncliente)[0].codigopostal,
          city:
            Object.keys(cliente.direccioncliente).length > 0 &&
            Object.values(cliente.direccioncliente)[0].ciudad,
          cityid:
            Object.keys(cliente.direccioncliente).length > 0 &&
            Object.values(cliente.direccioncliente)[0].ciudadid,
          provinceid:
            Object.keys(cliente.direccioncliente).length > 0 &&
            Object.values(cliente.direccioncliente)[0].provinciaid,
        },
      });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//---------------------------------- COTIZAR ENVIO A DOMICILIO ----------------------------------
export const cotizarEnvioADomicilio =
  (productosAux, retailer, info) => async (dispatch) => {
    let token = localStorage.getItem("pickittoken");
    let apikey = localStorage.getItem("pickitkey");

    let productos = [];
    let numero;
    for (let i = 0; i < productosAux.length; i++) {
      if (
        productosAux[i].montototal.includes(",") &&
        productosAux[i].montototal.includes(".")
      ) {
        let array = productosAux[i].montototal.split(",");
        let array2 = array[0].split(".");
        numero = array2[0] + array2[1] + "." + array[1];
      } else {
        if (
          productosAux[i].montototal.includes(",") &&
          !productosAux[i].montototal.includes(".")
        ) {
          let array = productosAux[i].montototal.split(",");
          numero = array[0] + "." + array[1];
        }
      }
      productos.push({
        name: productosAux[i].nombre,
        weight: {
          amount: 1000,
          unit: "g",
        },
        length: {
          amount: parseInt(productosAux[i].ancho),
          unit: "cm",
        },
        height: {
          amount: parseInt(productosAux[i].alto),
          unit: "cm",
        },
        width: {
          amount: parseInt(productosAux[i].profundidad),
          unit: "cm",
        },
        price: parseFloat(numero),
        sku: productosAux[i].codigobarras,
        amount: parseInt(productosAux[i].cantidad),
      });
    }

    var data = JSON.stringify({
      token: token,
      apiKey: apikey,
      sandbox: process.env.REACT_APP_PICKIT_SANDBOX === "false" ? false : true,
      pais: process.env.REACT_APP_PICKIT_PAIS,
      products: productos,
      retailer: {
        address: retailer.direccion,
        streetNumber: retailer.altura,
        province: retailer.provincia,
        postalCode: retailer.codigopostal,
        city: retailer.ciudad,
      },
      customer: {
        name: info.client,
        lastName: info.lastName,
        pid: info.cuit,
        email: info.email,
        phone: info.phone,
        address: info.address,
        streetNumber: info.streetNumber,
        province: info.province,
        postalCode: info.postalCode,
        city: info.city,
      },
    });

    var myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");

    var requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: data,
      redirect: "follow",
    };

    return await fetch(
      `${process.env.REACT_APP_PICKIT}api/cotizarDomicilio`,
      requestOptions
    )
      .then((r) => r.json())
      .then((d) => {
        dispatch({ type: "COTIZAR_ENVIO_A_DOMICILIO", payload: d });
      });
  };

//--------------------------- GENERAR TRANSACCION ENVIO A DOMICILIO ---------------------------
export const generarTransaccionEnvioADomicilio =
  (infoCotizacion, pedidoid) => async (dispatch) => {
    let token = localStorage.getItem("pickittoken");
    let apikey = localStorage.getItem("pickitkey");

    var myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");

    var raw = JSON.stringify({
      token: token,
      apiKey: apikey,
      order: pedidoid,
      sandbox: process.env.REACT_APP_PICKIT_SANDBOX === "false" ? false : true,
      pais: process.env.REACT_APP_PICKIT_PAIS,
    });

    var requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };

    return await fetch(
      `${process.env.REACT_APP_PICKIT}api/transaccion/${infoCotizacion.uuid}`,
      requestOptions
    )
      .then((r) => r.json())
      .then((d) => {
        dispatch({ type: "TRANSACCION_ENVIO_A_DOMICILIO", payload: d });
      })
      .catch((err) => console.log(err));
  };

//-------------------------- COTIZAR ENVIO A PUNTO --------------------------
export const cotizarEnvioAPunto =
  (productosAux, retailer, info, pointid) => async (dispatch) => {
    let token = localStorage.getItem("pickittoken");
    let apikey = localStorage.getItem("pickitkey");

    let productos = [];
    let numero;
    for (let i = 0; i < productosAux.length; i++) {
      if (
        productosAux[i].montototal.includes(",") &&
        productosAux[i].montototal.includes(".")
      ) {
        let array = productosAux[i].montototal.split(",");
        let array2 = array[0].split(".");
        numero = array2[0] + array2[1] + "." + array[1];
      } else {
        if (
          productosAux[i].montototal.includes(",") &&
          !productosAux[i].montototal.includes(".")
        ) {
          let array = productosAux[i].montototal.split(",");
          numero = array[0] + "." + array[1];
        }
      }
      productos.push({
        name: productosAux[i].nombre,
        weight: {
          amount: 1000,
          unit: "g",
        },
        length: {
          amount: parseInt(productosAux[i].ancho),
          unit: "cm",
        },
        height: {
          amount: parseInt(productosAux[i].alto),
          unit: "cm",
        },
        width: {
          amount: parseInt(productosAux[i].profundidad),
          unit: "cm",
        },
        price: parseFloat(numero),
        sku: productosAux[i].codigobarras,
        amount: parseInt(productosAux[i].cantidad),
      });
    }

    var data = JSON.stringify({
      token: token,
      apiKey: apikey,
      pointId: pointid,
      sandbox: process.env.REACT_APP_PICKIT_SANDBOX === "false" ? false : true,
      pais: process.env.REACT_APP_PICKIT_PAIS,
      products: productos,
      retailer: {
        address: retailer.direccion,
        streetNumber: retailer.altura,
        province: retailer.provincia,
        postalCode: retailer.codigopostal,
        city: retailer.ciudad,
      },
      customer: {
        name: info.client,
        lastName: info.lastName,
        pid: info.cuit,
        email: info.email,
        phone: info.phone,
        address: info.address,
        streetNumber: info.streetNumber,
        province: info.province,
        postalCode: info.postalCode,
        city: info.city,
      },
    });

    var myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");

    var requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: data,
      redirect: "follow",
    };

    return await fetch(
      `${process.env.REACT_APP_PICKIT}api/cotizar`,
      requestOptions
    )
      .then((r) => r.json())
      .then((d) => {
        dispatch({ type: "COTIZAR_ENVIO_A_PUNTO", payload: d });
      });
  };

//--------------------------- TRANSACCCION ENVIO A PUNTO ---------------------------
export const generarTransaccionEnvioAPunto =
  (infoCotizacion, pedidoid) => async (dispatch) => {
    let token = localStorage.getItem("pickittoken");
    let apikey = localStorage.getItem("pickitkey");

    var myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");

    var raw = JSON.stringify({
      token: token,
      apiKey: apikey,
      order: pedidoid,
      sandbox: process.env.REACT_APP_PICKIT_SANDBOX === "false" ? false : true,
      pais: process.env.REACT_APP_PICKIT_PAIS,
    });

    var requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };

    return await fetch(
      `${process.env.REACT_APP_PICKIT}api/transaccion/${infoCotizacion.uuid}`,
      requestOptions
    )
      .then((r) => r.json())
      .then((d) => {
        dispatch({ type: "TRANSACCION_ENVIO_A_PUNTO", payload: d });
      })
      .catch((err) => console.log(err));
  };

//-------------------------- GET PUNTOS DE RETIRO -----------------------------
//w (with no error handling)
// export const getPuntosDeRetiro = (cp) => async (dispatch) => {
//   let token = localStorage.getItem("pickittoken");
//   let apikey = localStorage.getItem("pickitkey");

//   var myHeaders = new Headers();
//   myHeaders.append("Content-Type", "application/json");

//   var raw = JSON.stringify({
//     token: token,
//     apiKey: apikey,
//     sandbox: process.env.REACT_APP_PICKIT_SANDBOX === "false" ? false : true,
//     pais: process.env.REACT_APP_PICKIT_PAIS,
//   });

//   var requestOptions = {
//     method: "POST",
//     headers: myHeaders,
//     body: raw,
//     redirect: "follow",
//   };

//   return await fetch(
//     `${process.env.REACT_APP_PICKIT}api/listadoPuntos?codigoPostal=${cp}`,
//     requestOptions
//   )
//     .then((r) => r.json())
//     .then((d) => {
//       let array = [];
//       if (d.length > 0) {
//         array = d.map((p) => {
//           return {
//             id: p.id,
//             address: p.address,
//             name: p.name,
//             schedule: newSchedule(p.schedule),
//             lat: p.lat,
//             lng: p.lng,
//           };
//         });
//       }
//       dispatch({ type: "GET_PUNTOS_DE_RETIRO_PICKIT", payload: array });
//     });
// };

//f with error handling
export const getPuntosDeRetiro = (cp) => async (dispatch) => {
  let token = localStorage.getItem("pickittoken");
  let apikey = localStorage.getItem("pickitkey");

  // Check if both token and apikey exist before proceeding
  if (!token || !apikey) {
    // If either is missing, dispatch empty array and return early
    dispatch({ type: "GET_PUNTOS_DE_RETIRO_PICKIT", payload: [] });
    return;
  }

  var myHeaders = new Headers();
  myHeaders.append("Content-Type", "application/json");

  var raw = JSON.stringify({
    token: token,
    apiKey: apikey,
    sandbox: process.env.REACT_APP_PICKIT_SANDBOX === "false" ? false : true,
    pais: process.env.REACT_APP_PICKIT_PAIS,
  });

  var requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: raw,
    redirect: "follow",
  };

  try {
    const response = await fetch(
      `${process.env.REACT_APP_PICKIT}api/listadoPuntos?codigoPostal=${cp}`,
      requestOptions
    );
    const data = await response.json();

    let array = [];
    if (data.length > 0) {
      array = data.map((p) => {
        return {
          id: p.id,
          address: p.address,
          name: p.name,
          schedule: newSchedule(p.schedule),
          lat: p.lat,
          lng: p.lng,
        };
      });
    }
    dispatch({ type: "GET_PUNTOS_DE_RETIRO_PICKIT", payload: array });
  } catch (error) {
    console.error("Error fetching pickup points:", error);
    dispatch({ type: "GET_PUNTOS_DE_RETIRO_PICKIT", payload: [] });
  }
};

//--------------------------- AGREGAR CONCEPTO ENVIO ---------------------------
export const agregarConceptoEnvio =
  (clientId, facturaid, precio, iva, total) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    return await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSAgregoArticuloConcepto&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OV_facturaid=${facturaid}&OI_clienteid=${clientId}&OV_cantidad=1&OV_tipofactura=factura_pedido&concepto_envio=ENVIO PICKIT&precio_envio=${precio}&total_envio=${total}&tasa_iva_envio=${iva}`
    )
      .then((r) => r.json())
      .then((d) => {
        dispatch({ type: "AGREGAR_CONCEPTO_ENVIO", payload: d });
      })
      .catch((err) => console.log(err));
  };

//--------------------------- GUARDAR DATOS ENVIO A DOMICILIO -----------------------------
export const guardarDatosPedidoADomicilio =
  (dataEnvio, infoCotizacion, clienteid, pedidoid, infoTransaccion) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    return await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTSetPickitCliente&tiendaid=${tiendausuario}&api_key=${api_key}&clientepickitid=0&clienteid=${clienteid}&pedidoid=${pedidoid}&uuid=${infoCotizacion.uuid}&costo_envio=${infoCotizacion.price}&impuesto_envio=${infoCotizacion.tax}&total_envio=${infoCotizacion.totalPrice}&transactionid=${infoTransaccion.transactionId}&pickit_code=${infoTransaccion.pickitCode}&url_tracking=${infoTransaccion.urlTracking}&nombre=${dataEnvio.client}&apellido=${dataEnvio.lastName}&email=${dataEnvio.email}&telefono=${dataEnvio.phone}&calle=${dataEnvio.address}&altura=${dataEnvio.streetNumber}&localidadid=${dataEnvio.cityid}&provinciaid=${dataEnvio.provinceid}&codigopostal=${dataEnvio.postalCode}&origen_envio=2&punto_envio=`
    )
      .then((r) => r.json())
      .then((d) => {
        return d;
      })
      .catch((err) => console.log(err));
  };

//----------------------------- GUARDAR DATOS ENVIO A PUNTO --------------------------------
export const guardarDatosPedidoAPunto =
  (dataEnvio, infoCotizacion, clienteid, pedidoid, infoTransaccion, punto) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    return await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTSetPickitCliente&tiendaid=${tiendausuario}&api_key=${api_key}&clientepickitid=0&clienteid=${clienteid}&pedidoid=${pedidoid}&uuid=${infoCotizacion.uuid}&costo_envio=${infoCotizacion.price}&impuesto_envio=${infoCotizacion.tax}&total_envio=${infoCotizacion.totalPrice}&transactionid=${infoTransaccion.transactionId}&pickit_code=${infoTransaccion.pickitCode}&url_tracking=${infoTransaccion.urlTracking}&nombre=${dataEnvio.client}&apellido=${dataEnvio.lastName}&email=${dataEnvio.email}&telefono=${dataEnvio.phone}&calle=${dataEnvio.address}&altura=${dataEnvio.streetNumber}&localidadid=${dataEnvio.cityid}&provinciaid=${dataEnvio.provinceid}&codigopostal=${dataEnvio.postalCode}&origen_envio=1&punto_envio=${punto}`
    )
      .then((r) => r.json())
      .then((d) => {
        return d;
      })
      .catch((err) => console.log(err));
  };

//------------------------------- PRESUPUESTO ----------------------------------

//w original
// export const getPresupuestos =
//   (
//     numerofactura,
//     nrocomprobante,
//     nombrecliente,
//     fechaDesde,
//     fechaHasta,
//     clienteid,
//     page
//   ) =>
//   async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");
//     try {
//       let response = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTMisPresupuestos&tiendaid=${tiendausuario}&api_key=${api_key}&numerofactura=${numerofactura}&nrocomprobante=${nrocomprobante}&clienteid=${clienteid}&nombrecliente=${nombrecliente}&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&pagActual=${page}&paginaResultado=10`
//       );
//       if (response) {
//         dispatch({ type: "GET_PRESUPUESTOS", payload: response.data });
//       } else throw new Error("hubo un problema");
//     } catch (error) {
//       throw new Error(error);
//     }
//   };

//f with OI_vendedorid
export const getPresupuestos = (
  numerofactura,
  nrocomprobante,
  nombrecliente,
  fechaDesde,
  fechaHasta,
  clienteid,
  page
) => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");
  const vendedorid = localStorage.getItem("idusuario");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    // Admins (profile 7) always see everything
    if (profileValues.includes(7)) return "ALL";
    // Vendedores (profile 18) without admin see only their data
    if (profileValues.includes(18)) return "OWN";
    // Default: see everything
    return "ALL";
  };

  const accessLevel = getAccessLevel();
  const vendedorParam = accessLevel === "OWN" 
    ? `&OI_vendedorid=${vendedorid}`
    : "";

  try {
    const response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTMisPresupuestos&tiendaid=${tiendausuario}&api_key=${api_key}&numerofactura=${numerofactura}&nrocomprobante=${nrocomprobante}&clienteid=${clienteid}&nombrecliente=${nombrecliente}&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&pagActual=${page}&paginaResultado=10${vendedorParam}`
    );

    if (response.data) {
      dispatch({ type: "GET_PRESUPUESTOS", payload: response.data });
    } else {
      throw new Error("Hubo un problema al obtener los presupuestos");
    }
  } catch (error) {
    console.error("Error fetching presupuestos:", error);
    throw error;
  }
};

export const getPresupuesto = (presupuestoplacaid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTMiPresupuesto&tiendaid=${tiendausuario}&api_key=${api_key}&presupuestoplacaid=${presupuestoplacaid}`
    );
    if (response) {
      dispatch({ type: "GET_PRESUPUESTO", payload: response.data.presupuesto });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const crearPresupuesto = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetPresupuesto&tiendaid=${tiendausuario}&api_key=${api_key}&presupuestoplacaid=0&nombre=${info.nombre}&clienteid=${info.clienteid}&monedaid=${info.monedaid}&observacion=${info.observacion}&fechapresupuesto=${info.fecha}&tipodescuentoid=${info.tipodescuentoid}&vendedorid=${info.vendedorid}&usuarioid=${idusuario}`
    );
    if (response) {
      dispatch({ type: "CREAR_PRESUPUESTO", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const crearDetallePresupuesto =
  (
    presupuestoplacaid,
    info,
    cantidad,
    presupuestoplaca_productoid,
    descuentoid
  ) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetPresupuestoDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&presupuestoplacaid=0&presupuestoplaca_productoid=${presupuestoplaca_productoid}&nombre=${info.nombre}&cantidad=${cantidad}&descuentos_nombre=&descuentos=${descuentoid}&precioventa=${info.precioventa}&productoid=${info.productoid}&preciocosto=${info.preciocosto}&rentabilidad=0&iva=21&preciosiniva=${info.preciosiniva}`
      );
      if (response) {
        dispatch({ type: "CREAR_DETALLE_PRESUPUESTO", payload: response.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const editarDetallePresupuesto =
  (presupuestoplacaid, info, cantidad, descuentoid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    let ivaproducto = info.ivaproducto.split(",")[0];

    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetPresupuestoDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&presupuestoplacaid=${presupuestoplacaid}&presupuestoplaca_productoid=0&nombre=${info.nombreproducto}&cantidad=${cantidad}&descuentos_nombre=&descuentos=${descuentoid}&precioventa=${info.precioVenta}&productoid=${info.productoid}&preciocosto=${info.precioCosto}&rentabilidad=0&iva=${ivaproducto}&preciosiniva=${info.preciosiniva}`
      );
      if (response) {
        dispatch({
          type: "EDITAR_DETALLE_PRESUPUESTO",
          payload: response.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getDetallePresupuesto =
  (presupuestoplacaid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTPresupuestoDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&presupuestoplacaid=${presupuestoplacaid}`
      );
      if (response) {
        dispatch({
          type: "GET_DETALLE_PRESUPUESTO",
          payload: Object.values(response.data.presupuesto.detalle),
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const clonarPresupuesto = (presupuestoplacaid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTClonarPresupuesto&tiendaid=${tiendausuario}&api_key=${api_key}&presupuestoplacaid=${presupuestoplacaid}`
    );
    if (response) {
      dispatch({ type: "CLONAR_PRESUPUESTO", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const generarPedidoPresupuesto =
  (presupuestoplacaid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTPresupuestoToPedido&tiendaid=${tiendausuario}&api_key=${api_key}&presupuestoplacaid=${presupuestoplacaid}`
      );
      if (response) {
        dispatch({
          type: "GENERAR_PEDIDO_PRESUPUESTO",
          payload: response.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const enviarEmail =
  (codigo, email, tipocomprobante) => async (dispatch) => {
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=${tipocomprobante}&codigocomprobante=${codigo}&tipoaccionpdf=2&emailfactura=${email}&mostrarjson=1`
      );
      if (response) {
        dispatch({
          type: "ENVIAR_EMAIL",
          payload: {
            mensaje: response.data.menssage,
            success: response.data.menssage.toLowerCase().includes("error")
              ? false
              : true,
          },
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      console.log(error);
    }
  };

export const limpiarEnviarEmail = () => async (dispatch) => {
  dispatch({ type: "LIMPIAR_ENVIAR_EMAIL", payload: "" });
};

//----------------------------------------- PROMOCIONES -----------------------------------------
export const getPromociones = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetPromociones&tiendaid=${tiendausuario}&api_key=${api_key}&promocionid=0&trae_todo=1&pagActual=${page}&paginaResultado=10`
    );
    if (response) {
      dispatch({ type: "GET_PROMOCIONES", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const agregarPromocion = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetPromocion&tiendaid=${tiendausuario}&api_key=${api_key}&promocionid=0&nombre=${info.nombre}&fechainicio=${info.fechainicio}&fechafin=${info.fechafin}&observacion=${info.observacion}&descuento=${info.descuento}&codigo${info.codigo}&activo=${info.activo}&ecommerce=${info.ecommerce}`
    );
    if (response) {
      dispatch({ type: "AGREGAR_PROMOCION", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const editarPromocion = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetPromocion&tiendaid=${tiendausuario}&api_key=${api_key}&promocionid=${info.promocion_prodid}&nombre=${info.nombre}&fechainicio=${info.fechainicio}&fechafin=${info.fechafin}&observacion=${info.observacion}&descuento=${info.descuento}&codigo${info.codigo}&activo=${info.activo}&ecommerce=${info.ecommerce}`
    );
    if (response) {
      dispatch({ type: "EDITAR_PROMOCION", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const eliminarPromocion = (id) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaPromocion&tiendaid=${tiendausuario}&api_key=${api_key}&promocionid=${id}`
    );
    if (response) {
      dispatch({ type: "ELIMINAR_PROMOCION", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getProductosPromocion = (id, page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetPromocionProductos&tiendaid=${tiendausuario}&api_key=${api_key}&promocionid=${id}&pagActual=${page}&paginaResultado=5`
    );
    if (response) {
      dispatch({ type: "GET_PRODUCTOS_PROMOCION", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const agregarProductoPromocion =
  (promocionid, productoid, cantidad) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetPromocionProductos&tiendaid=${tiendausuario}&api_key=${api_key}&promocionprodid=0&promocionid=${promocionid}&productoid=${productoid}&cantidad=${cantidad}`
      );
      if (response) {
        dispatch({
          type: "AGREGAR_PRODUCTO_PROMOCION",
          payload: response.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const editarProductoPromocion =
  (promocionprodid, promocionid, productoid, cantidad) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetPromocionProductos&tiendaid=${tiendausuario}&api_key=${api_key}&promocionprodid=${promocionprodid}&promocionid=${promocionid}&productoid=${productoid}&cantidad=${cantidad}`
      );
      if (response) {
        dispatch({ type: "EDITAR_PRODUCTO_PROMOCION", payload: response.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const eliminarProductoPromocion =
  (promocionid, promocionprodid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaPromocionProducto&tiendaid=${tiendausuario}&api_key=${api_key}&promocionid=${promocionid}&promocionprodid=${promocionprodid}`
      );
      if (response) {
        dispatch({
          type: "ELIMINAR_PRODUCTO_PROMOCION",
          payload: response.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

//-------------------------------- PRODUCTOS HOME --------------------------------
export const agregarProductoAlHome = (productoid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetProductoHome&tiendaid=${tiendausuario}&api_key=${api_key}&productoid=${productoid}`
    );
    if (response) {
      dispatch({ type: "AGREGAR_PRODUCTO_HOME", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getProductosHome = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetProductosHome&tiendaid=${tiendausuario}&api_key=${api_key}`
    );
    if (response) {
      dispatch({ type: "GET_PRODUCTOS_HOME", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const eliminarProductoHome = (productohomeid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaProductoHome&tiendaid=${tiendausuario}&api_key=${api_key}&productohomeid=${productohomeid}`
    );
    if (response) {
      dispatch({ type: "ELIMINAR_PRODUCTO_HOME", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//------------------------------  REPORTE POR VENDEDOR ------------------------------

export const getReportePorVendedor =
  (fechadesde, fechahasta, vendedor) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      if (vendedor !== "") {
        let response = await axios.get(
          `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GIReporteVendedor&tiendaid=${tiendausuario}&api_key=${api_key}&vendedorid=${vendedor}&fecha_desde=${fechadesde}&fecha_hasta=${fechahasta}`
        );
        if (response) {
          let reporte_vendedor = Object.values(response.data.reporte_vendedor);

          if (reporte_vendedor[0].clienteid === null) {
            dispatch({
              type: "GET_REPORTE_POR_VENDEDOR",
              payload: {
                reporte: [],
                totales: {
                  comisionvta: "0,00",
                  totalfactura: "0,00",
                  totalcomision: "0,00",
                  total_mas_comision: "0,00",
                },
              },
            });
          } else {
            let total_mas_comision = reporte_vendedor.pop();
            let totalcomision = reporte_vendedor.pop();
            let totalfactura = reporte_vendedor.pop();
            let comisionvta = reporte_vendedor.pop();

            reporte_vendedor = reporte_vendedor.map((r) => {
              return {
                detalle: [],
                facturaid: r.facturaid,
                facturanota: r.facturanota,
                color: r.color,
                numerofactura: r.numerofactura,
                nrocomprobante: r.nrocomprobante,
                tipofactura: r.tipofactura,
                facturaremito: r.facturaremito,
                fechafactura: r.fechafactura,
                clienteid: r.clienteid,
                nombrecliente: r.nombrecliente,
                nombretienda: r.nombretienda,
                nombrevendedor: r.nombrevendedor,
                tipopagos: r.tipopagos,
                intereses: r.intereses,
                total: r.total,
                pagos: r.pagos,
                saldo: r.saldo,
              };
            });

            let aux;

            try {
              for (let i = 0; i < reporte_vendedor.length; i++) {
                aux = await axios.get(
                  `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GIReporteVendedorDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&facturaid=${reporte_vendedor[i].facturaid}`
                );
                reporte_vendedor[i].detalle = Object.values(aux.data.detalle);
              }
            } catch (error) {
              throw new Error(error);
            }

            dispatch({
              type: "GET_REPORTE_POR_VENDEDOR",
              payload: {
                reporte: reporte_vendedor,
                totales: {
                  comisionvta: comisionvta || "0,00",
                  totalfactura: totalfactura || "0,00",
                  totalcomision: totalcomision || "0,00",
                  total_mas_comision: total_mas_comision || "0,00",
                },
              },
            });
          }
        } else throw new Error("hubo un problema");
      } else {
        dispatch({
          type: "GET_REPORTE_POR_VENDEDOR",
          payload: {
            reporte: [],
            totales: {
              comisionvta: "0,00",
              totalfactura: "0,00",
              totalcomision: "0,00",
              total_mas_comision: "0,00",
            },
          },
        });
      }
    } catch (error) {
      throw new Error(error);
    }
  };

export const getDetalleReporteVendedor = (facturaid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GIReporteVendedorDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&facturaid=${facturaid}`
    );
    return Object.values(response.data.detalle);
  } catch (error) {
    throw new Error(error);
  }
};

export const getVendedores = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let finded = [];
    finded[0] = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetVendedores&tiendaid=${tiendausuario}&api_key=${api_key}&vendedorid=0&pagActual=1&paginaResultado=10`
    );
    let paginaUltima = finded[0].data.paginaUltima;
    finded[0] = Object.values(finded[0].data.vendedores);
    let aux;
    for (let i = 1; i < paginaUltima; i++) {
      finded[i] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetVendedores&tiendaid=${tiendausuario}&api_key=${api_key}&vendedorid=0&pagActual=${i}&paginaResultado=10`
      );
      aux = Object.values(finded[i].data.vendedores);
      finded[i] = aux;
    }
    finded = finded.flat();
    if (finded) {
      dispatch({ type: "GET_VENDEDORES", payload: finded });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//------------------------------ FACTURA PROVEEDOR ------------------------------
export const getProveedoresSinPaginado = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let finded = [];
    finded[0] = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetProveedores&api_key=${api_key}&tiendaid=${tiendausuario}&OI_proveedorid=&OV_nombre=&paginaActual=7&paginaResultado=100`
    );
    let paginaUltima = finded[0].data.paginaUltima;
    finded[0] = Object.values(finded[0].data.proveedores);
    let aux;
    for (let i = 1; i < paginaUltima; i++) {
      finded[i] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetProveedores&api_key=${api_key}&tiendaid=${tiendausuario}&OI_proveedorid=&OV_nombre=&paginaActual=${i}&paginaResultado=30`
      );
      aux = Object.values(finded[i].data.proveedores);
      finded[i] = aux;
    }
    finded = finded.flat();
    if (finded) {
      dispatch({
        type: "GET_PROVEEDORES_SIN_PAGINADO_TIENDA",
        payload: finded,
      });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getTiposComprobantes = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetTiposComprobantes&tiendaid=${tiendausuario}&api_key=${api_key}`
    );
    if (response) {
      dispatch({ type: "GET_TIPOS_COMPROBANTES", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getConceptos = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSGetConceptos&tiendaid=${tiendausuario}&api_key=${api_key}&pagActual=1&paginaResultado=10`
    );
    if (response) {
      dispatch({ type: "GET_CONCEPTOS_PROVEEDOR", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const crearFacturaProveedor = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetFactura&tiendaid=${tiendausuario}&api_key=${api_key}&facturacompraid=0&proveedorid=${info.proveedorid}&fecha_emision=${info.fecha_emision}&monedaid=${info.monedaid}&letra=${info.letra}&tipo_comprob=${info.tipo_comprob}&tipofacturacompraid=${info.tipo_comprob}&punto_comprob=${info.punto_comprob}&numero_comprob=${info.numero_comprob}&observaciones=${info.observaciones}`
    );
    if (response) {
      dispatch({ type: "AGREGAR_FACTURA_PROVEEDOR", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const editarFacturaProveedor = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetFactura&tiendaid=${tiendausuario}&api_key=${api_key}&facturacompraid=${info.facturacompraid}&proveedorid=${info.proveedorid}&fecha_emision=${info.fechafactura}&monedaid=${info.monedaid}&letra=${info.letra}&tipo_comprob=${info.tipofacturacompraid}&tipofacturacompraid=${info.tipofacturacompraid}&punto_comprob=${info.punto_comprob}&numero_comprob=${info.numero_comprob}&observaciones=${info.observaciones}`
    );
    if (response) {
      dispatch({ type: "EDITAR_FACTURA_PROVEEDOR", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const crearDetalleFacturaProveedor =
  (facturacompraid, productoid, concepto, cantidad, descuento, preciocompra) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetFacturaDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&facturacompraid=${facturacompraid}&facturacompraproductoid=0&productoid=${productoid}&conceptoid=${concepto}&cantidad=${cantidad}&descuento=${descuento}&preciocompra=${preciocompra}`
      );
      if (response) {
        dispatch({
          type: "AGREGAR_DETALLE_FACTURA_PROVEEDOR",
          payload: response.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const editarItemFacturaProveedor =
  (
    facturacompraid,
    facturacompraproductoid,
    productoid,
    concepto,
    cantidad,
    descuento,
    preciocompra
  ) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetFacturaDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&facturacompraid=${facturacompraid}&facturacompraproductoid=${facturacompraproductoid}&productoid=${productoid}&conceptoid=${concepto}&cantidad=${cantidad}&descuento=${descuento}&preciocompra=${preciocompra}`
      );
      if (response) {
        dispatch({
          type: "EDITAR_ITEM_FACTURA_PROVEEDOR",
          payload: response.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getFacturasProveedor =
  (page, numero_comprob, nombre, proveedorid, fecha_desde, fecha_hasta) =>
  async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetFacturas&tiendaid=${tiendausuario}&api_key=${api_key}&numero_comprob=${numero_comprob}&nombre=${nombre}&proveedorid=${proveedorid}&fecha_desde=${fecha_desde}&fecha_hasta=${fecha_hasta}&paginaActual=${page}&paginaResultado=10`
      );
      if (response) {
        dispatch({ type: "GET_FACTURAS_PROVEEDOR", payload: response.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getItemsFacturaProveedor =
  (page, facturacompraid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetFacturaDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&facturacompraid=${facturacompraid}&facturacompraproductoid=&productoid=&conceptoid=&nombreproducto=&codigoarticulo=&paginaResultado=10&paginaActual=${page}`
      );
      if (response) {
        dispatch({
          type: "GET_ITEMS_FACTURA_PROVEEDOR",
          payload: response.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const eliminarFactura = (facturaid, proveedorid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCEliminarFactura&tiendaid=${tiendausuario}&api_key=${api_key}&facturaid=${facturaid}&proveedorid=${proveedorid}`
    );
    if (response) {
      dispatch({ type: "ELIMINAR_FACTURA_PROVEEDOR", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const eliminarItemFactura =
  (facturacompraid, facturacompraproductoid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCEliminaFacturaDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&facturacompraid=${facturacompraid}&facturacompraproductoid=${facturacompraproductoid}`
      );
      if (response) {
        dispatch({
          type: "ELIMINAR_ITEM_FACTURA_PROVEEDOR",
          payload: response.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getProductosByNameSkuPromocion = (input) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    let array = [];
    if (input === "") {
      dispatch({ type: "GET_PRODUCTOS_BY_NAME_PROMOCION", payload: array });
    } else {
      if (isNaN(input)) {
        array[0] = await axios.get(
          `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=${input}&codigoarticulo=&pagActual=1&trae_todo=1 `
        );
      } else {
        array[0] = await axios.get(
          `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTListaProductos&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&paginaResultado=10&nombreProducto=&codigoarticulo=${input}&pagActual=1&trae_todo=1 `
        );
      }
      array[0] = Object.values(array[0].data.listaproductos);
      array = array.flat();

      for (let j = 0; j < array.length; j++) {
        array[j].imagenes = Object.values(array[j].imagenes).flat();
      }

      if (array) {
        dispatch({ type: "GET_PRODUCTOS_BY_NAME_PROMOCION", payload: array });
      } else throw new Error("no hay informacion");
    }
  } catch (error) {
    throw { message: error };
  }
};

export const getPedidoPuntoDeVenta = (facturaid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSMiPedido&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_facturaid=${facturaid}`
    );
    let aux = Object.values(finded.data.pedido);
    aux[1] = Object.values(aux[1]);
    let aux2;
    for (let i = 0; i < 3; i++) {
      aux2 = aux[1].pop();
    }
    aux[3] = {
      precioTotalCompra: finded.data.pedido.detalle.t_precio,
      precioTotalIva: finded.data.pedido.detalle.t_iva,
      precioTotal: finded.data.pedido.detalle.t_total,
    };
    if (aux[0].tipo_envio === "Pickit") {
      let envio = aux[1].shift();
      aux[5] = envio;
    }
    if (aux) {
      dispatch({ type: "GET_PEDIDO_PUNTO_VENTA", payload: aux });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//---------------------------------- MENSAJES ----------------------------------
export const enviarMensaje = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  let titulo = info.titulo.replace("#", "%23");

  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetMensaje&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&usuariodestino=&mensajeid=0&mensaje=${info.mensaje}&titulo=${titulo}&leido=0&pedidoid=${info.pedidoid}`
    );
    if (response) {
      dispatch({ type: "SEND_MESSAGE_USER", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const enviarMensajeProducto =
  (info, usuariodestinoid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var idusuario = localStorage.getItem("idusuario");

    let titulo = info.titulo.replace("#", "%23");

    try {
      let response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetMensaje&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&usuariodestino=${usuariodestinoid}&mensajeid=0&mensaje=${info.mensaje}&titulo=${titulo}&leido=1&productoid=${info.productoid}`
      );
      if (response) {
        dispatch({ type: "SEND_MESSAGE_PRODUCTO", payload: response.data });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const marcarMensajeLeido = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  let titulo = info.titulo.replace("#", "%23");

  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetMensaje&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${info.usuarioid}&usuariodestino=0&mensajeid=${info.mensajeid}&mensaje=${info.descripcion}&titulo=${titulo}&leido=1&productoid=${info.productoid}`
    );
    if (response) {
      dispatch({ type: "MARCAR_MENSAJE_LEIDO", payload: response.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//---------------------- FOOTER ----------------------
export const getDatosFooter = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDatosTienda&OV_api_key=${api_key}&tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({ type: "GET_DATOS_FOOTER", payload: finded.data.footer });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const agregarDatosFooter = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSSetFooter&tiendaid=${tiendausuario}&api_key=${api_key}&footerid=0&titulo1=${info.titulo}&titulo2=${info.subtitulo}&descripcion=${info.descripcion}&imagen=&url=${info.url}&orden=${info.orden}`
    );
    if (finded) {
      dispatch({ type: "AGREGAR_DATOS_FOOTER", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const agregarSubtituloFooter = (titulo, info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSSetFooter&tiendaid=${tiendausuario}&api_key=${api_key}&footerid=0&titulo1=${titulo}&titulo2=${info.subtitulo}&descripcion=${info.descripcion}&imagen=&url=${info.url}&orden=${info.orden}`
    );
    if (finded) {
      dispatch({ type: "AGREGAR_SUBTITULO_FOOTER", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const editarDatosFooter = (titulo, info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSSetFooter&tiendaid=${tiendausuario}&api_key=${api_key}&footerid=${info.footerid}&titulo1=${titulo}&titulo2=${info.subtitulo}&descripcion=${info.descripcion}&imagen=&url=${info.url}&orden=${info.orden}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_DATOS_FOOTER", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const eliminarItemFooter = (footerid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSEliminaFooter&tiendaid=${tiendausuario}&api_key=${api_key}&footerid=${footerid}`
    );
    if (finded) {
      dispatch({ type: "ELIMINAR_DATOS_FOOTER", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getMensajesPedido = (pedidoid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetMensajes&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&usuariodestinoid=&pedidoid=${pedidoid}&mensajeid=&mensaje&pagActual=1&paginaResultado=15`
    );
    if (response) {
      let arrayMensajes = Object.values(response.data.mensajes).map((row) => {
        return {
          mensajeid: row.mensajeid,
          fecha: row.fechaingreso.split(" ")[0],
          hora: row.fechaingreso.split(" ")[1].slice(0, 5),
          titulo: row.titulo,
          descripcion: row.descripcion,
          usuarioid: row.usuarioid,
          usuariodestinoid: row.usuariodestinoid,
          pedidoid: row.pedidoid,
          leido: row.leido,
          productoid: row.productoid,
        };
      });
      dispatch({ type: "GET_MESSAGES_BY_PEDIDO", payload: arrayMensajes });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const getMensajesProducto = (productoid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  try {
    let response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetMensajes&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&usuariodestinoid=&productoid=${productoid}&mensajeid=&mensaje&pagActual=1&paginaResultado=15`
    );
    if (response) {
      let arrayMensajes = Object.values(response.data.mensajes).map((row) => {
        return {
          mensajeid: row.mensajeid,
          fecha: row.fechaingreso.split(" ")[0],
          hora: row.fechaingreso.split(" ")[1].slice(0, 5),
          titulo: row.titulo,
          descripcion: row.descripcion,
          usuarioid: row.usuarioid,
          usuariodestinoid: row.usuariodestinoid,
          pedidoid: row.pedidoid,
          leido: row.leido,
          productoid: row.productoid,
        };
      });
      arrayMensajes.sort(function (a, b) {
        if (parseInt(a.usuarioid) < parseInt(b.titulo)) {
          return 1;
        }
        if (parseInt(a.titulo) > parseInt(b.usuarioid)) {
          return -1;
        }
        return 0;
      });
      dispatch({ type: "GET_MESSAGES_BY_PRODUCTOID", payload: arrayMensajes });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//------------------------------ URL MERCADOPAGO -------------------------------------
export const getUrlMercadoPago = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDatosTienda&OV_api_key=${api_key}&tiendaid=${tiendausuario}`
    );
    if (finded) {
      dispatch({ type: "GET_URL_MERCADOPAGO", payload: finded.data.url_mp });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

//---------------------------- RENTABILIDAD PRODUCTOS ----------------------------
export const getReporteRentabilidadProductos =
  (codigo, nombreProducto, marcaid, categoriaid, page) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GIReporteRentabilidadProducto&tiendaid=${tiendausuario}&api_key=${api_key}&codigo=${codigo}&nombreProducto=${nombreProducto}&marcaid=${marcaid}&categoriaid=${categoriaid}&estiloid&pagActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({
          type: "GET_REPORTE_RENTABILIDAD_PRODUCTOS",
          payload: finded.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

//---------------------------- RENTABILIDAD PRODUCTOS ----------------------------
export const getReporteCuentaCorrienteDeudas =
  (diasdeuda, clienteid, page) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionInformes.php?type=GIReporteCtaCteDeuda&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${clienteid}&diasdeuda=${diasdeuda}&pagActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({
          type: "GET_REPORTE_CUENTA_CORRIENTE_DEUDAS",
          payload: finded.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

//--------------------------------- IMPORTAR STOCK ---------------------------------
export const getListadoArchivos = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetImportarProducto&tiendaid=${tiendausuario}&api_key=${api_key}&productoimportaid=&titulo=`
    );
    if (finded) {
      dispatch({ type: "GET_ARCHIVOS", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const crearEncabezadoImportacion = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GSetTImportarProducto&tiendaid=${tiendausuario}&api_key=${api_key}&productoimportaid=&titulo=${info.titulo}&descripcion=${info.descripcion}&proveedorid=${info.proveedorid}&activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "CREAR_ENCABEZADO_IMPORTACION", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const editarEncabezadoImportacion = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GSetTImportarProducto&tiendaid=${tiendausuario}&api_key=${api_key}&productoimportaid=${info.productoimportaid}&titulo=${info.titulo}&descripcion=${info.descripcion}&proveedorid=${info.proveedorid}&activo=${info.activo}`
    );
    if (finded) {
      dispatch({ type: "EDITAR_ENCABEZADO_IMPORTACION", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const procesarListado = (productoimportaid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTImportarProductoProcesa&tiendaid=${tiendausuario}&api_key=${api_key}&productoimportaid=${productoimportaid}`
    );
    if (finded) {
      dispatch({ type: "PROCESAR_LISTADO", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const subirArchivoImportar =
  (archivo, productoimportaid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    var bodyFormData = new FormData();
    bodyFormData.append("type", "GTImportarProductoSubeArchivo");
    bodyFormData.append("tiendaid", tiendausuario);
    bodyFormData.append("api_key", api_key);
    bodyFormData.append("productoimportaid", productoimportaid);
    bodyFormData.append("archivo", archivo);

    try {
      axios({
        method: "POST",
        url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
        data: bodyFormData,
        headers: { "Content-Type": "multipart/form-data" },
      })
        .then(function (response) {
          dispatch({ type: "SUBIR_ARCHIVO_IMPORTA", payload: response.data });
        })
        .catch(function (response) {
          //handle error
          console.log(response);
        });
    } catch (error) {
      throw new Error(error);
    }
  };

//--------------------------- IMPORTAR LISTA PRECIOS ----------------------------------
export const getListadoArchivosListaPrecios = (page) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionImportar.php?type=GIGetImportarListaPrecio&tiendaid=${tiendausuario}&api_key=${api_key}&listaprecioid=0&titulo=&paginaActual=${page}&paginaResultado=10`
    );
    if (finded) {
      dispatch({ type: "GET_ARCHIVOS_LISTA_PRECIOS", payload: finded.data });
    } else throw new Error("hubo un problema");
  } catch (error) {
    throw new Error(error);
  }
};

export const crearEncabezadoImportacionListaPrecios =
  (info) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionImportar.php?type=GISetTImportarListaPrecio&tiendaid=${tiendausuario}&api_key=${api_key}&listaprecioid=0&titulo=${info.titulo}&descripcion=${info.descripcion}&proveedorid=${info.proveedorid}&activo=${info.activo}`
      );
      if (finded) {
        dispatch({
          type: "CREAR_ENCABEZADO_IMPORTACION_LISTA_PRECIOS",
          payload: finded.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const editarEncabezadoImportacionListaPrecios =
  (info) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionImportar.php?type=GISetTImportarListaPrecio&tiendaid=${tiendausuario}&api_key=${api_key}&listaprecioid=${info.listaprecioid}&titulo=${info.titulo}&descripcion=${info.descripcion}&proveedorid=${info.proveedorid}&activo=${info.activo}`
      );
      if (finded) {
        dispatch({
          type: "EDITAR_ENCABEZADO_IMPORTACION_LISTA_PRECIOS",
          payload: finded.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const procesarListadoListaPrecios =
  (listaprecioid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionImportar.php?type=GIImportarListaPrecioProcesa&tiendaid=${tiendausuario}&api_key=${api_key}&listaprecioid=${listaprecioid}`
      );
      if (finded) {
        dispatch({
          type: "PROCESAR_LISTADO_LISTA_PRECIOS",
          payload: finded.data,
        });
      } else throw new Error("hubo un problema");
    } catch (error) {
      throw new Error(error);
    }
  };

export const subirArchivoImportarListaPrecios =
  (archivo, listaprecioid, importa) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    var bodyFormData = new FormData();
    bodyFormData.append("type", "GIImportarListaPrecioSubeArchivo");
    bodyFormData.append("archivo", archivo);

    try {
      axios({
        method: "POST",
        url: `${process.env.REACT_APP_SERVER}php/GestionImportar.php?tiendaid=${tiendausuario}&api_key=${api_key}&listaprecioid=${listaprecioid}&importa=${importa}`,
        data: bodyFormData,
        headers: { "Content-Type": "multipart/form-data" },
      })
        .then(function (response) {
          dispatch({
            type: "SUBIR_ARCHIVO_IMPORTA_LISTA_PRECIOS",
            payload: response.data,
          });
        })
        .catch(function (response) {
          //handle error
          console.log(response);
        });
    } catch (error) {
      throw new Error(error);
    }
  };

//--------------------------- REGISTRAR PAGO FACTURA PROVEEDOR -------------------------
export const registrarPagoFacturaProveedor =
  (facturacompraid, info) => async (dispatch) => {
    var idusuario = localStorage.getItem("idusuario");
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var cajaid = localStorage.getItem("cajaid");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetPagosFactura&tiendaid=${tiendausuario}&api_key=${api_key}&pagosfacturacompraid=0&facturacompraid=${facturacompraid}&usuarioid=${idusuario}&cajaid=${cajaid}&tipopagoid=${info.tipopagoid}&monedaid=${info.monedaid}&montoabono=${info.montoabono}&numerorecibo=${info.numerorecibo}&descripcion=${info.descripcion}&proveedorid&fechapago=${info.fechapago}&cuentabancoid=${info.cuentabancoid}&nrocheque=${info.nrocheque}&fechaemisioncheque=${info.fechaemisioncheque}&fechapagocheque=${info.fechapagocheque}&chequerecibidoid=${info.chequerecibidoid}&nrotransferencia=${info.nrotransferencia}&cantcuotastarjeta${info.cantcuotastarjeta}=2&porcentajeinterestarjeta=${info.porcentajeinterestarjeta}`
      );
      if (finded) {
        dispatch({
          type: "REGISTRAR_PAGO_FACTURA_PROVEEDOR",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw new Error(error);
    }
  };

export const getCuentasBanco = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetCuentasBanco&tiendaid=${tiendausuario}&api_key=${api_key}&pagActual=1&paginaResultado=100`
    );
    if (finded) {
      dispatch({ type: "GET_CUENTAS_BANCO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getChequesBanco = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetChequesRecibidos&tiendaid=${tiendausuario}&api_key=${api_key}&chequerecibidoid=0&pagActual=1&paginaResultado=100`
    );
    if (finded) {
      dispatch({ type: "GET_CHEQUES_BANCO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getTiposChequesBanco = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GTGetTiposCheque&tiendaid=${tiendausuario}&api_key=${api_key}&pagActual=1&paginaResultado=100`
    );
    if (finded) {
      dispatch({ type: "GET_TIPOS_CHEQUES_BANCO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getTarjetas = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetTarjetas&tiendaid=${tiendausuario}&api_key=${api_key}`
    );
    if (finded) {
      dispatch({ type: "GET_TARJETAS", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getTiposTarjetas = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetTiposTarjeta&tiendaid=${tiendausuario}&api_key=${api_key}`
    );
    if (finded) {
      dispatch({ type: "GET_TIPOS_TARJETAS", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

export const getPagosRealizadosFacturaProveedor =
  (page, facturacompraid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetPagosFactura&tiendaid=${tiendausuario}&api_key=${api_key}&facturacompraid=${facturacompraid}&pagActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({
          type: "GET_PAGOS_REALIZADOS_FACTURA_PROVEEDOR",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//---------------------------------- CUENTAS CORRIENTES ----------------------------------
export const getCuentasCorrientes =
  (page, clienteid, solo_deuda, cuit) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    let soloDeuda = solo_deuda === true ? "1" : "0";

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetCtaCte&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${clienteid}&cuit=${cuit}&solo_deuda=${soloDeuda}&pagActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({ type: "GET_CUENTAS_CORRIENTES", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//f
// export const getCuentasCorrientes_2 = (page, clienteid = 0, pedidoid = 0, fecha = '') => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");

//   try {
//     const finded = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionTesoreria.php?type=GTCuentaXCobrarDetalle&OI_tiendaid=${tiendausuario}&api_key=${api_key}&OI_clienteid=${clienteid}&b_pedidoid=${pedidoid}&b_fechapedido=${fecha}&pagActual=${page}&paginaResultado=10`
//     );
//     if (finded) {
//       dispatch({
//         type: "GET_CUENTAS_CORRIENTES_2",
//         payload: {
//           ctacte: finded.data.ctasxcobrardetalle,
//           paginaUltima: finded.data.paginaUltima,
//           sumatotalComprobante: finded.data.sumatotalComprobante
//         }
//       });
//     } else throw new Error("no pudo modificarse");
//   } catch (error) {
//     throw { message: error };
//   }
// };

//w original
// export const getCuentasCorrientes_2 =
//   (page, clienteid = "0", codigo = "0", fechaPedido = "") =>
//   async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");

//     // Format the date if it exists
//     const formattedDate = fechaPedido
//       ? new Date(fechaPedido).toISOString().split("T")[0]
//       : "";

//     // Convert empty strings to "0"
//     const formattedClienteId = clienteid || "0";
//     const formattedCodigo = codigo || "0";

//     try {
//       const url = `${process.env.REACT_APP_SERVER}php/GestionTesoreria.php?type=GTCuentaXCobrarDetalle&OI_tiendaid=${tiendausuario}&api_key=${api_key}&OI_clienteid=${formattedClienteId}&b_pedidoid=${formattedCodigo}&b_fechapedido=${formattedDate}&pagActual=${page}&paginaResultado=10`;

//       // console.log("Request URL:", url);

//       const response = await axios.get(url);

//       if (response.data) {
//         // console.log("Response data:", response.data);

//         dispatch({
//           type: "GET_CUENTAS_CORRIENTES_2",
//           payload: {
//             ctacte: response.data.ctasxcobrardetalle,
//             paginaUltima: response.data.paginaUltima,
//             sumatotalComprobante: response.data.sumatotalComprobante,
//           },
//         });
//       } else {
//         throw new Error("No data received");
//       }
//     } catch (error) {
//       console.error("Error in getCuentasCorrientes_2:", error);
//       throw error;
//     }
//   };

//f with OI_vendedorid

export const getCuentasCorrientes_2 = (
  page, 
  clienteid = "0", 
  codigo = "0", 
  fechaPedido = ""
) => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");
  const vendedorid = localStorage.getItem("idusuario");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    // Admins (profile 7) always see everything
    if (profileValues.includes(7)) return "ALL";
    // Vendedores (profile 18) without admin see only their data
    if (profileValues.includes(18)) return "OWN";
    // Default: see everything
    return "ALL";
  };

  // Format the date if it exists
  const formattedDate = fechaPedido
    ? new Date(fechaPedido).toISOString().split("T")[0]
    : "";

  // Convert empty strings to "0"
  const formattedClienteId = clienteid || "0";
  const formattedCodigo = codigo || "0";

  try {
    const accessLevel = getAccessLevel();
    const vendedorParam = accessLevel === "OWN" 
      ? `&OI_vendedorid=${vendedorid}`
      : "";

    const url = `${process.env.REACT_APP_SERVER}php/GestionTesoreria.php?type=GTCuentaXCobrarDetalle&OI_tiendaid=${tiendausuario}&api_key=${api_key}&OI_clienteid=${formattedClienteId}&b_pedidoid=${formattedCodigo}&b_fechapedido=${formattedDate}&pagActual=${page}&paginaResultado=10${vendedorParam}`;

    const response = await axios.get(url);

    if (response.data) {
      dispatch({
        type: "GET_CUENTAS_CORRIENTES_2",
        payload: {
          ctacte: response.data.ctasxcobrardetalle,
          paginaUltima: response.data.paginaUltima,
          sumatotalComprobante: response.data.sumatotalComprobante,
        },
      });
    } else {
      throw new Error("No data received");
    }
  } catch (error) {
    console.error("Error in getCuentasCorrientes_2:", error);
    throw error;
  }
};

export const getDetalleCuentaCorriente =
  (page, clienteid, pedidoid, fecha) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetCtaCteDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${clienteid}&pedidoid=${pedidoid}&fechapedido=${fecha}&pagActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({
          type: "GET_DETALLE_CUENTA_CORRIENTE",
          payload: finded.data,
        });
        return finded.data;
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//pagosctaxcobrarid,clienteid,fechapago,tipopagoid,monedaid,montoabonado,numerorecibo,descripcion
export const pagarCuentaCorriente = (info, clienteid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var idusuario = localStorage.getItem("idusuario");
  var api_key = localStorage.getItem("api_key");
  var cajaid = localStorage.getItem("cajaid");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCtaCtePago&tiendaid=${tiendausuario}&api_key=${api_key}&pagosctaxcobrarid=${info.pagosctaxcobrarid}&usuarioid=${idusuario}&clienteid=${clienteid}&cajaid=${cajaid}&fechapago=${info.fechapago}&tipopagoid=${info.tipopagoid}&monedaid=${info.monedaid}&montoabonado=${info.montoabonado}&numerorecibo=${info.numerorecibo}&descripcion=${info.descripcion}&bancoid=${info.bancoid}&nrocuenta=${info.nrocuenta}&nrocheque=${info.nrocheque}&fechaemisioncheque=${info.fechaemisioncheque}&fechacobrocheque=${info.fechacobrocheque}&tipotarjetaproveedorid=${info.tipotarjetaproveedorid}&tipotarjetaid=${info.tipotarjetaid}&nrotarjeta=${info.nrotarjeta}&cantcuotastarjeta=${info.cantcuotastarjeta}`
    );
    if (finded) {
      dispatch({
        type: "PAGAR_CUENTA_CORRIENTE_GENERAL",
        payload: finded.data,
      });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const getPagosCuentaCorriente =
  (page, clienteid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetCtaCtePagos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${clienteid}&pagActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({ type: "GET_PAGOS_CUENTA_CORRIENTE", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const eliminarPagoCuentaCorriente =
  (pagosctaxcobrarid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTEliminaCtaCtePago&tiendaid=${tiendausuario}&api_key=${api_key}&pagosctaxcobrarid=${pagosctaxcobrarid}`
      );
      if (finded) {
        dispatch({
          type: "ELIMINAR_PAGO_CUENTA_CORRIENTE",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//clienteid, pedidoid, pagosctaxcobrarid, montoabono
export const afectarPagoCuentaCorriente =
  (clienteid, pedidoid, pagosctaxcobrarid, montoabono) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var idusuario = localStorage.getItem("idusuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetAfectarPago&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&pagofacturaid=0&clienteid=${clienteid}&pagosid=0&pedidoid=${pedidoid}&pagosctaxcobrarid=${pagosctaxcobrarid}&montoabono=${montoabono}`
      );
      if (finded) {
        dispatch({
          type: "AFECTAR_PAGO_CUENTA_CORRIENTE",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//---------------------------------- CUENTAS CORRIENTES PROVEEDORES ----------------------------------
export const getCuentasCorrientesProveedor =
  (page, proveedorid, solo_deuda) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    let soloDeuda = solo_deuda === true ? "1" : "0";

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetCtaCte&tiendaid=${tiendausuario}&api_key=${api_key}&proveedorid=${proveedorid}&solo_deuda=${soloDeuda}&paginaActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({
          type: "GET_CUENTAS_CORRIENTES_PROVEEDOR",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const getDetalleCuentaCorrienteProveedor =
  (page, clienteid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      let finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetCtaCteDetalle&tiendaid=${tiendausuario}&api_key=${api_key}&proveedorid=${clienteid}&pagActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({
          type: "GET_DETALLE_CUENTA_CORRIENTE_PROVEEDOR",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//pagosctaxcobrarid,clienteid,fechapago,tipopagoid,monedaid,montoabonado,numerorecibo,descripcion
export const pagarCuentaCorrienteProveedor =
  (info, proveedorid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var idusuario = localStorage.getItem("idusuario");
    var api_key = localStorage.getItem("api_key");
    var cajaid = localStorage.getItem("cajaid");
    let descripcion =
      info.tipopagoid !== "3" && info.tipopagoid !== "4"
        ? "PAGO CONTADO"
        : info.tipopagoid === "3"
        ? "PAGO CHEQUE"
        : "PAGO TARJETA";

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetCtaCtePago&tiendaid=${tiendausuario}&api_key=${api_key}&pagosctaxpagarid=0&usuarioid=${idusuario}&cajaid=${cajaid}&tipopagoid=${info.tipopagoid}&proveedorid=${proveedorid}&fechapago=${info.fechapago}&monedaid=${info.monedaid}&monto=${info.montoabonado}&numerorecibo=${info.numerorecibo}&descripcion=${descripcion}&bancoid=${info.bancoid}&nrocuenta=${info.nrocuenta}&nrocheque=${info.nrocheque}&fechapagocheque=${info.fechapagocheque}&fechaemisioncheque=${info.fechaemisioncheque}&tipotarjetaproveedorid=${info.tipotarjetaproveedorid}&tipotarjetaid=${info.tipotarjetaid}&nrotarjeta=${info.nrotarjeta}&cantcuotastarjeta=${info.cantcuotastarjeta}`
      );
      if (finded) {
        dispatch({
          type: "PAGAR_CUENTA_CORRIENTE_PROVEEDOR",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const getPagosCuentaCorrienteProveedor =
  (page, proveedorid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCGetCtaCtePagos&tiendaid=${tiendausuario}&api_key=${api_key}&proveedorid=${proveedorid}&paginaActual=${page}&paginaResultado=10`
      );
      if (finded) {
        dispatch({
          type: "GET_PAGOS_CUENTA_CORRIENTE_PROVEEDOR",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const eliminarPagoCuentaCorrienteProveedor =
  (pagosctaxpagarid, proveedorid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCEliminarCtaCtePago&tiendaid=${tiendausuario}&api_key=${api_key}&proveedorid=${proveedorid}&pagosctaxpagarid=${pagosctaxpagarid}`
      );
      if (finded) {
        dispatch({
          type: "ELIMINAR_PAGO_CUENTA_CORRIENTE_PROVEEDOR",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//clienteid, pedidoid, pagosctaxcobrarid, montoabono
export const afectarPagoCuentaCorrienteProveedor =
  (clienteid, pedidoid, pagosctaxcobrarid, montoabono) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var idusuario = localStorage.getItem("idusuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionCompras.php?type=GCSetAfectarPago&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&pagofacturaid=0&proveedorid=${clienteid}&pagosid=0&pedidoid=${pedidoid}&pagosctaxcobrarid=${pagosctaxcobrarid}&montoabono=${montoabono}`
      );
      if (finded) {
        dispatch({
          type: "AFECTAR_PAGO_CUENTA_CORRIENTE_PROVEEDOR",
          payload: finded.data,
        });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//---------------------------- MANEJO CAJA ----------------------------
export const getEstadoCaja = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var cajaid = localStorage.getItem("cajaid");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetEstadoCaja&tiendaid=${tiendausuario}&api_key=${api_key}&cajaid=${cajaid}`
    );
    if (finded) {
      dispatch({ type: "GET_ESTADO_CAJA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const getTotalCaja = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var cajaid = localStorage.getItem("cajaid");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetTotalCaja&tiendaid=${tiendausuario}&api_key=${api_key}&cajaid=${cajaid}&tipoconfcaja=ABIERTA`
    );
    if (finded) {
      dispatch({ type: "GET_TOTAL_CAJA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const grabarMovimiento = (info) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var idusuario = localStorage.getItem("idusuario");
  var api_key = localStorage.getItem("api_key");
  var cajaid = localStorage.getItem("cajaid");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTSetCaja&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&cajaid=${cajaid}&tipoConfCaja=${info.tipoConfCaja}&monto=${info.monto}&observacion=${info.observacion}`
    );
    if (finded) {
      dispatch({ type: "GRABAR_MOVIMIENTO", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

//------------------------------- CANCELAR FACTURA ------------------------------
export const cancelarFactura = (facturaid, tipofactura) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTCancelarFactura&tiendaid=${tiendausuario}&api_key=${api_key}&facturaid=${facturaid}&tipofactura=${tipofactura}`
    );
    if (finded) {
      dispatch({ type: "CANCELAR_FACTURA", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const getCarritoDetalleVerificado =
  (clienteid, factura, page, tipofactura) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var idusuario = localStorage.getItem("idusuario");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTVerifica_factura&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&facturanumero=${factura}&tipofactura=${tipofactura}`
      );
      if (finded.data.success) {
        const finded2 = await axios.get(
          `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSArticuloDetalle&OV_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OV_facturaid=${factura}&OV_tipofactura=${tipofactura}&OI_clienteid=${clienteid}&pagActual=${page}&paginaResultado=10`
        );

        if (finded2) {
          dispatch({ type: "TRAER_FACTURA_TRUE", payload: finded2.data });
        } else {
          dispatch({ type: "TRAER_FACTURA_FALSE", payload: finded.data });
        }
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

//----------------------- DATOS TIENDA BY TIENDAID --------------------------
export const getDatosTiendaByTiendaId = (tiendaid) => async (dispatch) => {
  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetDatosTienda&tiendaid=${tiendaid}`
    );
    if (finded) {
      if (finded.data.api_key === null) {
        dispatch({
          type: "GET_DATOS_TIENDA_ID",
          payload: {
            success: false,
            mensaje: "No existe la tienda",
          },
        });
      } else {
        localStorage.setItem("api_key", finded.data.api_key);
        localStorage.setItem("tiendausuario", finded.data.tiendaid);
        localStorage.setItem("api_key_develone", finded.data.api_key_develone);
        localStorage.setItem(
          "develoneclienteid",
          finded.data.develoneclienteid
        );
        dispatch({
          type: "GET_DATOS_TIENDA_ID",
          payload: {
            success: true,
            mensaje: "Sucursal cambiada con exito",
          },
        });
      }
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw new Error(error);
  }
};

//-------------------------- GESTION STOCK ----------------------------------
export const getProductosStock =
  (page, nombre, codigoarticulo) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGestionStock&tiendaid=${tiendausuario}&api_key=${api_key}&nombre=${nombre}&codigoarticulo=${codigoarticulo}&paginaResultado=20&paginaActual=${page}`
      );
      if (finded) {
        if (finded.data.hasOwnProperty("gestionstock")) {
          dispatch({ type: "GET_PRODUCTOS_STOCK", payload: finded.data });
        } else {
          dispatch({
            type: "GET_PRODUCTOS_STOCK",
            payload: { gestionstock: [], paginaUltima: 0 },
          });
        }
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const aumentarStock = (productoid, cantidad) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTAjusteStockManual&tiendaid=${tiendausuario}&api_key=${api_key}&signo=suma&usuarioid=${idusuario}&productoid=${productoid}&cantidad=${cantidad}&ordencompraid=0`
    );
    if (finded) {
      dispatch({ type: "AUMENTAR_STOCK", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const restarStock = (productoid, cantidad) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  try {
    const finded = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTAjusteStockManual&tiendaid=${tiendausuario}&api_key=${api_key}&signo=resta&usuarioid=${idusuario}&productoid=${productoid}&cantidad=${cantidad}&ordencompraid=0`
    );
    if (finded) {
      dispatch({ type: "RESTAR_STOCK", payload: finded.data });
    } else throw new Error("no pudo modificarse");
  } catch (error) {
    throw { message: error };
  }
};

export const restarStockReservado =
  (productoid, cantidad) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var idusuario = localStorage.getItem("idusuario");

    try {
      const finded = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTAjusteStockReserva&tiendaid=${tiendausuario}&api_key=${api_key}&usuarioid=${idusuario}&productoid=${productoid}&cantidad=${cantidad}&ordencompraid=0`
      );
      if (finded) {
        dispatch({ type: "RESTAR_STOCK_RESERVADO", payload: finded.data });
      } else throw new Error("no pudo modificarse");
    } catch (error) {
      throw { message: error };
    }
  };

export const subirArchivoImportarStock = (archivo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  var idusuario = localStorage.getItem("idusuario");

  var bodyFormData = new FormData();
  bodyFormData.append("type", "GIImportarStockSubeProcesaArchivo");
  bodyFormData.append("tiendaid", tiendausuario);
  bodyFormData.append("api_key", api_key);
  bodyFormData.append("idusuario", idusuario);
  bodyFormData.append("archivo", archivo);

  try {
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_SERVER}php/GestionImportar.php`,
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then(function (response) {
        dispatch({
          type: "SUBIR_ARCHIVO_IMPORTA_STOCK",
          payload: response.data,
        });
      })
      .catch(function (response) {
        //handle error
        console.log("response");
      });
  } catch (error) {
    throw new Error(error);
  }
};

//REPORTE  ENTREGAS
//w original
// export const getEntregas = (
//   fechaDesde,
//   fechaHasta,
//   page,
//   clienteid = "",
//   pedidoId = ""
// ) => {
//   return async (dispatch) => {
//     const tiendausuario = localStorage.getItem("tiendausuario");
//     const api_key = localStorage.getItem("api_key");

//     dispatch({ type: "GET_ENTREGAS_REQUEST" });

//     try {
//       // Convert clienteid to number if it exists, otherwise use 0
//       const numericClienteId = clienteid ? Number(clienteid) : 0;

//       const response = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//         {
//           params: {
//             type: "GSDetalleMisEntregas",
//             tiendaid: tiendausuario,
//             api_key: api_key,
//             clienteid: numericClienteId,
//             facturaid: pedidoId || 0,
//             fecha_desde: fechaDesde,
//             fecha_hasta: fechaHasta,
//             paginaResultado: 10,
//             pagActual: page,
//           },
//         }
//       );

//       if (response.data && response.data.success) {
//         dispatch({
//           type: "GET_ENTREGAS_SUCCESS",
//           payload: {
//             detalle: response.data.detalle || {},
//             loading: false,
//             paginaUltima: response.data.paginaUltima || 1,
//           },
//         });
//       } else {
//         throw new Error(response.data.message || "La respuesta no fue exitosa");
//       }
//     } catch (error) {
//       console.error("Error in getEntregas:", error);
//       dispatch({
//         type: "GET_ENTREGAS_FAILURE",
//         payload: error.message,
//       });
//     }
//   };
// };

//f
export const getEntregas = (
  fechaDesde,
  fechaHasta,
  page,
  clienteid = "",
  pedidoId = ""
) => {
  return async (dispatch) => {
    const tiendausuario = localStorage.getItem("tiendausuario");
    const api_key = localStorage.getItem("api_key");
    const vendedorid = localStorage.getItem("idusuario");

    // Check user profiles and determine access level
    const getAccessLevel = () => {
      const perfiles = localStorage.getItem("perfiles")
        ? JSON.parse(localStorage.getItem("perfiles"))
        : {};
      const profileValues = Object.values(perfiles);
      
      // Admins (profile 7) always see everything
      if (profileValues.includes(7)) return "ALL";
      // Vendedores (profile 18) without admin see only their data
      if (profileValues.includes(18)) return "OWN";
      // Default: see everything
      return "ALL";
    };

    dispatch({ type: "GET_ENTREGAS_REQUEST" });

    try {
      // Convert clienteid to number if it exists, otherwise use 0
      const numericClienteId = clienteid ? Number(clienteid) : 0;
      const accessLevel = getAccessLevel();
      const vendedorParam = accessLevel === "OWN" 
        ? { OI_vendedorid: vendedorid }
        : {};

      const response = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
        {
          params: {
            type: "GSDetalleMisEntregas",
            tiendaid: tiendausuario,
            api_key: api_key,
            clienteid: numericClienteId,
            facturaid: pedidoId || 0,
            fecha_desde: fechaDesde,
            fecha_hasta: fechaHasta,
            paginaResultado: 10,
            pagActual: page,
            ...vendedorParam // Spread the vendedor parameter conditionally
          },
        }
      );

      if (response.data && response.data.success) {
        dispatch({
          type: "GET_ENTREGAS_SUCCESS",
          payload: {
            detalle: response.data.detalle || {},
            loading: false,
            paginaUltima: response.data.paginaUltima || 1,
          },
        });
      } else {
        throw new Error(response.data.message || "La respuesta no fue exitosa");
      }
    } catch (error) {
      console.error("Error in getEntregas:", error);
      dispatch({
        type: "GET_ENTREGAS_FAILURE",
        payload: error.message,
      });
    }
  };
};

// GESTION REMITOS
//w original
// export const generateRemito =
//   (clienteid, pedidos, fechaEntrega) => async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");
//     var idusuario = localStorage.getItem("idusuario");

//     // console.log('pedidos', pedidos)

//     try {
//       // Format the data according to the required structure
//       const remitoData = {
//         type: "GSGeneraRemito",
//         tiendaid: tiendausuario,
//         api_key: api_key,
//         usuarioid: idusuario || 0,
//         OI_vendedorid: parseInt(idusuario) || 0,
//         cabecera: {
//           clienteid: clienteid,
//           facturaid: pedidos[0].facturaid_pedido || 0,
//           fecha_pedido: pedidos[0].fechapedido.split(" ")[0],
//           fecha_entrega: fechaEntrega,
//           tipo_factura: "factura_remito",
//           metodo_pago: 0,
//         },
//         detalle: pedidos.map((item) => ({
//           pedidoplacaid: item.pedidoplacaid,
//           pedidoplaca_productoid: item.pedidoplaca_productoid,
//           cantidad: item.selectedQuantity,
//         })),
//       };

//       console.log("Sending remito data:", remitoData);

//       // Use fetch instead of axios to have more control over headers
//       const response = await fetch(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//         {
//           method: "POST",
//           headers: {
//             // Don't set Content-Type to avoid preflight CORS issues
//             // The browser will set it automatically to the correct value
//             Accept: "application/json",
//           },
//           body: JSON.stringify(remitoData),
//         }
//       );

//       if (!response.ok) {
//         throw new Error(`Server responded with status: ${response.status}`);
//       }

//       const data = await response.json();

//       if (!data || !data.success) {
//         throw new Error(data?.mensaje || "Error al generar remito");
//       }

//       dispatch({
//         type: "GENERATE_REMITO_SUCCESS",
//         payload: {
//           clienteid,
//           data: data,
//         },
//       });

//       return data;
//     } catch (error) {
//       console.error("Error generating remito:", error);
//       dispatch({
//         type: "GENERATE_REMITO_ERROR",
//         payload: {
//           clienteid,
//           error: error.message,
//         },
//       });
//       throw error;
//     }
//   };

//f sending vendedorid

export const generateRemito =
  (clienteid, pedidos, fechaEntrega, vendedorid) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");
    var idusuario = localStorage.getItem("idusuario");

    try {
      // Format the data according to the required structure
      const remitoData = {
        type: "GSGeneraRemito",
        tiendaid: tiendausuario,
        api_key: api_key,
        usuarioid: idusuario || 0,
        OI_vendedorid: parseInt(vendedorid) || 0,  // Use the passed vendedorid
        cabecera: {
          clienteid: clienteid,
          facturaid: pedidos[0].facturaid_pedido || 0,
          fecha_pedido: pedidos[0].fechapedido.split(" ")[0],
          fecha_entrega: fechaEntrega,
          tipo_factura: "factura_remito",
          metodo_pago: 0,
        },
        detalle: pedidos.map((item) => ({
          pedidoplacaid: item.pedidoplacaid,
          pedidoplaca_productoid: item.pedidoplaca_productoid,
          cantidad: item.selectedQuantity,
        })),
      };

      console.log("Sending remito data:", remitoData);

      const response = await fetch(
        `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
        {
          method: "POST",
          headers: {
            Accept: "application/json",
          },
          body: JSON.stringify(remitoData),
        }
      );

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (!data || !data.success) {
        throw new Error(data?.mensaje || "Error al generar remito");
      }

      dispatch({
        type: "GENERATE_REMITO_SUCCESS",
        payload: {
          clienteid,
          data: data,
        },
      });

      return data;
    } catch (error) {
      console.error("Error generating remito:", error);
      dispatch({
        type: "GENERATE_REMITO_ERROR",
        payload: {
          clienteid,
          error: error.message,
        },
      });
      throw error;
    }
  };


  //w original
// export const getRemitos =
//   (
//     clienteid = 0,
//     facturaid = 0,
//     nrocomprobante = "",
//     fechaDesde = "",
//     fechaHasta = "",
//     page = 1
//   ) =>
//   async (dispatch) => {
//     var tiendausuario = localStorage.getItem("tiendausuario");
//     var api_key = localStorage.getItem("api_key");

//     try {
//       if (nrocomprobante && !isNaN(nrocomprobante) && facturaid === 0) {
//         facturaid = parseInt(nrocomprobante, 10);
//       }

//       // Format the data according to the required structure
//       const remitoData = {
//         type: "GTMisRemitos",
//         tiendaid: tiendausuario,
//         api_key: api_key,
//         clienteid: clienteid,
//         facturaid: facturaid,
//         nrocomprobante: nrocomprobante,
//         fecha_desde: fechaDesde,
//         fecha_hasta: fechaHasta,
//         pagActual: page,
//         paginaResultado: 10,
//       };

//       const response = await fetch(
//         `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
//         {
//           method: "POST",
//           headers: {
//             Accept: "application/json",
//           },
//           body: JSON.stringify(remitoData),
//         }
//       );

//       if (!response.ok) {
//         throw new Error(`Server responded with status: ${response.status}`);
//       }

//       const data = await response.json();

//       if (!data || !data.success) {
//         throw new Error(data?.mensaje || "Error al obtener remitos");
//       }

//       dispatch({
//         type: "GET_REMITOS",
//         payload: data,
//       });

//       return data;
//     } catch (error) {
//       console.error("Error fetching remitos:", error);
//       dispatch({
//         type: "GET_REMITOS",
//         payload: {
//           success: false,
//           remitos: {},
//           paginaUltima: 1,
//         },
//       });
//       throw error;
//     }
//   };

//f with OI_vendedorid
export const getRemitos = (
  clienteid = 0,
  facturaid = 0,
  nrocomprobante = "",
  fechaDesde = "",
  fechaHasta = "",
  page = 1
) => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");
  const vendedorid = localStorage.getItem("idusuario");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    // Admins (profile 7) always see everything
    if (profileValues.includes(7)) return "ALL";
    // Vendedores (profile 18) without admin see only their data
    if (profileValues.includes(18)) return "OWN";
    // Default: see everything
    return "ALL";
  };

  try {
    if (nrocomprobante && !isNaN(nrocomprobante) && facturaid === 0) {
      facturaid = parseInt(nrocomprobante, 10);
    }

    // Determine if we need to include vendedor filter
    const accessLevel = getAccessLevel();
    const vendedorParam = accessLevel === "OWN" 
  ? { OI_vendedorid: parseInt(vendedorid, 10) }
  : {};

    // Format the data according to the required structure
    const remitoData = {
      type: "GTMisRemitos",
      tiendaid: tiendausuario,
      api_key: api_key,
      clienteid: clienteid,
      facturaid: facturaid,
      nrocomprobante: nrocomprobante,
      fecha_desde: fechaDesde,
      fecha_hasta: fechaHasta,
      pagActual: page,
      paginaResultado: 10,
      ...vendedorParam // Spread the vendedor parameter if needed
    };

    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
        },
        body: JSON.stringify(remitoData),
      }
    );

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    const data = await response.json();

    if (!data || !data.success) {
      throw new Error(data?.mensaje || "Error al obtener remitos");
    }

    dispatch({
      type: "GET_REMITOS",
      payload: data,
    });

    return data;
  } catch (error) {
    console.error("Error fetching remitos:", error);
    dispatch({
      type: "GET_REMITOS",
      payload: {
        success: false,
        remitos: {},
        paginaUltima: 1,
      },
    });
    throw error;
  }
};

//f get 1 single remito data
export const getRemito = (id, clienteid) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");

  try {
    dispatch({
      type: "GET_REMITO",
      payload: { loading: true, data: [] },
    });

    const clienteIdNum = parseInt(clienteid, 10);
    const facturaIdNum = parseInt(id, 10);

    const remitoData = {
      type: "GTMiRemito",
      tiendaid: tiendausuario,
      api_key: api_key,
      clienteid: clienteIdNum,
      facturaid: facturaIdNum,
    };

    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
        },
        body: JSON.stringify(remitoData),
      }
    );

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    const data = await response.json();
    // console.log("Received remito details:", data);

    if (!data || !data.success) {
      throw new Error(data?.mensaje || "Error al obtener remito");
    }

    // Format the data to match what the component expects
    if (data && data.cabecera && data.detalle) {
      // Create a formatted remito object that matches the expected structure
      const formattedData = [
        {
          facturaid: data.cabecera.facturaid,
          clienteid: data.cabecera.clienteid,
          nombrecliente: data.cabecera.nombre,
          email: data.cabecera.email,
          telefono: data.cabecera.telefono,
          direccion: data.cabecera.direccion,
          tipo_envio: data.cabecera.tipo_envio,
          fecha_compromiso_entrega: data.cabecera.fecharemito,
          fechaentrega: data.cabecera.fechaentrega,
          descripcionestado: data.cabecera.descripcionestado || "Sin estado",
          total: data.detalle.t_total,
          detalle: Object.entries(data.detalle)
            .filter(
              ([key, value]) =>
                typeof value === "object" && value.facturadetalleid
            )
            .map(([key, item]) => ({
              facturadetalleid: item.facturadetalleid,
              descripcion: item.nombreproducto,
              cantidad: item.cantidad,
              precio: item.precioventa,
              descuento: item.descuento,
              total: (item.precioventa * item.cantidad).toFixed(2),
              pedidoplacaid: item.pedidoplacaid || null,
            })),
        },
      ];

      dispatch({
        type: "GET_REMITO",
        payload: { loading: false, data: formattedData },
      });

      return { success: true, data: formattedData };
    } else {
      throw new Error("Formato de respuesta inválido");
    }
  } catch (error) {
    console.error("Error fetching remito:", error);
    dispatch({
      type: "GET_REMITO",
      payload: { loading: false, data: [] },
    });
    throw error;
  }
};

export const cambiarEstadoRemito = (facturaid, estado) => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  console.log("remito desde action", facturaid, "estado", estado);
  try {
    // Format the data according to the required structure
    const remitoData = {
      type: "GTSetEstadoRemito",
      tiendaid: tiendausuario,
      api_key: api_key,
      facturaid: facturaid,
      facturaestadoid: estado,
    };

    // Use fetch instead of axios to have more control over headers
    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          // Don't set Content-Type to avoid preflight CORS issues
        },
        body: JSON.stringify(remitoData),
      }
    );

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    const data = await response.json();

    dispatch({ type: "CAMBIAR_ESTADO_REMITO", payload: data });

    if (!data.success) {
      throw new Error(data.mensaje || "Error al cambiar estado del remito");
    }

    return data;
  } catch (error) {
    console.error("Error changing remito state:", error);

    if (error instanceof Error) {
      if (!error.message.includes("status")) {
        dispatch({
          type: "CAMBIAR_ESTADO_REMITO",
          payload: {
            success: false,
            mensaje: error.message,
          },
        });
      }
    }

    throw error;
  }
};

export const editarFechaEntregaRemito =
  (facturaid, fechaEntrega) => async (dispatch) => {
    var tiendausuario = localStorage.getItem("tiendausuario");
    var api_key = localStorage.getItem("api_key");

    try {
      console.log("Sending request with params:", {
        tiendaid: tiendausuario,
        api_key: api_key,
        facturaid: facturaid,
        fecha_entrega: fechaEntrega,
      });

      // Format the data according to the required structure
      const remitoData = {
        type: "GTSetFechaEntregaRemito",
        tiendaid: tiendausuario,
        api_key: api_key,
        facturaid: facturaid,
        fecha_entrega: fechaEntrega,
      };

      const response = await fetch(
        `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
        {
          method: "POST",
          headers: {
            Accept: "application/json",
            // Don't set Content-Type to avoid preflight CORS issues
          },
          body: JSON.stringify(remitoData),
        }
      );

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      const data = await response.json();
      console.log("API response:", data);

      dispatch({
        type: "EDITAR_FECHA_ENTREGA_REMITO",
        payload: data,
      });

      if (!data.success) {
        throw new Error(
          data.mensaje || "Error al cambiar la fecha de entrega del remito"
        );
      }

      return data;
    } catch (error) {
      console.error("Error en editarFechaEntregaRemito:", error);

      if (error instanceof Error) {
        if (!error.message.includes("status")) {
          dispatch({
            type: "EDITAR_FECHA_ENTREGA_REMITO",
            payload: {
              success: false,
              mensaje: error.message,
            },
          });
        }
      }

      throw error;
    }
  };

//w export const getRemitosPendientesLiquidacion =
//   (
//     fechaDesde,
//     fechaHasta,
//     page,
//     paginaResultado,
//     clienteid = null,
//     remitoid = 0
//   ) =>
//   async (dispatch) => {
//     const tiendausuario = localStorage.getItem("tiendausuario");
//     const api_key = localStorage.getItem("api_key");

//     const useClientId = clienteid && clienteid !== "0" ? clienteid : "";
//     const useRemitoId = remitoid || 0;

//     try {
//       const response = await axios.get(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisRemitos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${useClientId}&remitoid=${useRemitoId}&busca_fentrega=0&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//       );

//       console.log('API Response:', response);
// console.log('Payload Structure:', response.payload);

//       if (response.data && response.data.success) {
//         dispatch({
//           type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
//           payload: {
//             data: response.data.detalle,
//             paginaUltima: response.data.paginaUltima,
//           },
//         });
//       } else {
//         throw new Error("No data received or request was not successful");
//       }
//     } catch (error) {
//       console.error("Error in getRemitosPendientesLiquidacion:", error);
//       throw error;
//     }
//   };

//w
// export const getRemitosPendientesLiquidacion = (
//   fechaDesde,
//   fechaHasta,
//   page,
//   paginaResultado,
//   clienteid = null,
//   remitoid = 0
// ) =>
// async (dispatch) => {
//   const tiendausuario = localStorage.getItem("tiendausuario");
//   const api_key = localStorage.getItem("api_key");

//   const useClientId = clienteid && clienteid !== "0" ? clienteid : "";
//   const useRemitoId = remitoid || 0;

//   try {
//     const response = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisRemitos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${useClientId}&remitoid=${useRemitoId}&busca_fentrega=0&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//     );

//     if (response.data && response.data.success) {
//       const actionPayload = {
//         type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
//         payload: {
//           data: response.data.detalle,
//           paginaUltima: response.data.paginaUltima,
//         },
//       };
      
//       dispatch(actionPayload);
      
//       // IMPORTANT: Return the action payload so the component can access it
//       return actionPayload;
//     } else {
//       throw new Error("No data received or request was not successful");
//     }
//   } catch (error) {
//     console.error("Error in getRemitosPendientesLiquidacion:", error);
//     throw error;
//   }
// };

//w original
// export const getRemitosPendientesLiquidacion = (
//   fechaDesde,
//   fechaHasta,
//   page,
//   paginaResultado,
//   clienteid = null,
//   numeroremito = "" 
// ) =>
// async (dispatch) => {
//   const tiendausuario = localStorage.getItem("tiendausuario");
//   const api_key = localStorage.getItem("api_key");

//   const useClientId = clienteid && clienteid !== "0" ? clienteid : "";
//   const useNumeroRemito = numeroremito || "";

//   try {
//     const response = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisRemitos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${useClientId}&numerofactura=${useNumeroRemito}&busca_fentrega=0&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//     );

//     if (response.data && response.data.success) {
//       const actionPayload = {
//         type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
//         payload: {
//           data: response.data.detalle,
//           paginaUltima: response.data.paginaUltima,
//         },
//       };
      
//       dispatch(actionPayload);
      
//       return actionPayload;
//     } else {
//       throw new Error("No data received or request was not successful");
//     }
//   } catch (error) {
//     console.error("Error in getRemitosPendientesLiquidacion:", error);
//     throw error;
//   }
// };

//f with OI_vendedorid
export const getRemitosPendientesLiquidacion = (
  fechaDesde,
  fechaHasta,
  page,
  paginaResultado,
  clienteid = null,
  numeroremito = ""
) => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");
  const vendedorid = localStorage.getItem("idusuario");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    // Admins (profile 7) always see everything
    if (profileValues.includes(7)) return "ALL";
    // Vendedores (profile 18) without admin see only their data
    if (profileValues.includes(18)) return "OWN";
    // Default: see everything
    return "ALL";
  };

  const useClientId = clienteid && clienteid !== "0" ? clienteid : "";
  const useNumeroRemito = numeroremito || "";
  const accessLevel = getAccessLevel();
  const vendedorParam = accessLevel === "OWN" 
    ? `&OI_vendedorid=${vendedorid}`
    : "";

  try {
    const response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisRemitos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${useClientId}&numerofactura=${useNumeroRemito}&busca_fentrega=0&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}${vendedorParam}`
    );

    if (response.data && response.data.success) {
      const actionPayload = {
        type: "GET_REMITOS_PENDIENTES_LIQUIDACION",
        payload: {
          data: response.data.detalle,
          paginaUltima: response.data.paginaUltima,
        },
      };
      
      dispatch(actionPayload);
      
      return actionPayload;
    } else {
      throw new Error("No data received or request was not successful");
    }
  } catch (error) {
    console.error("Error in getRemitosPendientesLiquidacion:", error);
    throw error;
  }
};

//w with current time (no file upload)
// export const generarLiquidacion =
//   (clienteid, productos, fechaLiquidacion) => async (dispatch) => {
//     const tiendausuario = parseInt(localStorage.getItem("tiendausuario"));
//     const api_key = localStorage.getItem("api_key");
//     const idusuario = parseInt(localStorage.getItem("idusuario") || 0);

//     try {
//       // Get current time in HH:MM:SS format
//       const now = new Date();
//       const hours = String(now.getHours()).padStart(2, '0');
//       const minutes = String(now.getMinutes()).padStart(2, '0');
//       const seconds = String(now.getSeconds()).padStart(2, '0');
//       const currentTime = `${hours}:${minutes}:${seconds}`;

//       // Format the request data with explicit number conversion
//       const liquidacionData = {
//         type: "GSGeneraLiquidacion",
//         tiendaid: tiendausuario,
//         api_key: api_key,
//         usuarioid: idusuario,
//         cabecera: {
//           clienteid: parseInt(clienteid),
//           facturaid: 0,
//           fecha_liquidacion: `${fechaLiquidacion} ${currentTime}`, 
//           tipo_factura: "factura_remito",
//           metodo_pago: 0,
//         },
//         detalle: productos.map((item) => ({
//           remitoid: parseInt(item.remitoid),
//           remito_detalleid: parseInt(item.remitodetalleid),
//           cantidad: parseInt(item.selectedQuantity),
//         })),
//       };

//       //! NOTA: si te sale CORS... preflight... es porque estas mandando Content-Type: application/json explicitamente y choca con la politica de cors del server.

//       const response = await fetch(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//         {
//           method: "POST",
//           headers: {
//             Accept: "application/json",
//           },
//           body: JSON.stringify(liquidacionData),
//         }
//       );

//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }

//       const responseData = await response.json();

//       if (!responseData.success) {
//         throw new Error(responseData.message || "Error al generar liquidación");
//       }

//       dispatch({
//         type: "GENERATE_LIQUIDACION_SUCCESS",
//         payload: {
//           clienteid,
//           data: responseData,
//         },
//       });

//       return responseData;
//     } catch (error) {
//       console.error("Error generating liquidacion:", error);

//       dispatch({
//         type: "GENERATE_LIQUIDACION_ERROR",
//         payload: {
//           clienteid,
//           error: error.message,
//         },
//       });

//       throw error;
//     }
//   };

//w with file upload
// export const generarLiquidacion =
//   (clienteid, productos, fechaLiquidacion, imagenes = []) => async (dispatch) => {
//     const tiendausuario = parseInt(localStorage.getItem("tiendausuario"));
//     const api_key = localStorage.getItem("api_key");
//     const idusuario = parseInt(localStorage.getItem("idusuario") || 0);

//     try {
//       // Get current time in HH:MM:SS format
//       const now = new Date();
//       const hours = String(now.getHours()).padStart(2, '0');
//       const minutes = String(now.getMinutes()).padStart(2, '0');
//       const seconds = String(now.getSeconds()).padStart(2, '0');
//       const currentTime = `${hours}:${minutes}:${seconds}`;

//       // Format the request data with explicit number conversion
//       const liquidacionData = {
//         type: "GSGeneraLiquidacion",
//         tiendaid: tiendausuario,
//         api_key: api_key,
//         usuarioid: idusuario,
//         cabecera: {
//           clienteid: parseInt(clienteid),
//           facturaid: 0,
//           fecha_liquidacion: `${fechaLiquidacion} ${currentTime}`, 
//           tipo_factura: "factura_remito",
//           metodo_pago: 0,
//         },
//         detalle: productos.map((item) => ({
//           remitoid: parseInt(item.remitoid),
//           remito_detalleid: parseInt(item.remitodetalleid),
//           cantidad: parseInt(item.selectedQuantity),
//         })),
//         imagenes: imagenes
//       };

//       const response = await fetch(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//         {
//           method: "POST",
//           headers: {
//             Accept: "application/json",
//           },
//           body: JSON.stringify(liquidacionData),
//         }
//       );

//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }

//       const responseData = await response.json();

//       if (!responseData.success) {
//         throw new Error(responseData.message || "Error al generar liquidación");
//       }

//       dispatch({
//         type: "GENERATE_LIQUIDACION_SUCCESS",
//         payload: {
//           clienteid,
//           data: responseData,
//         },
//       });

//       return responseData;
//     } catch (error) {
//       console.error("Error generating liquidacion:", error);

//       dispatch({
//         type: "GENERATE_LIQUIDACION_ERROR",
//         payload: {
//           clienteid,
//           error: error.message,
//         },
//       });

//       throw error;
//     }
//   };

//w
// export const generarLiquidacion =
//   (clienteid, productos, fechaLiquidacion, imagenes = [], arcaNumber = "") => async (dispatch) => {
//     const tiendausuario = parseInt(localStorage.getItem("tiendausuario"));
//     const api_key = localStorage.getItem("api_key");
//     const idusuario = parseInt(localStorage.getItem("idusuario") || 0);

//     try {
//       // Get current time in HH:MM:SS format
//       const now = new Date();
//       const hours = String(now.getHours()).padStart(2, '0');
//       const minutes = String(now.getMinutes()).padStart(2, '0');
//       const seconds = String(now.getSeconds()).padStart(2, '0');
//       const currentTime = `${hours}:${minutes}:${seconds}`;

//       // Format the request data with explicit number conversion
//       const liquidacionData = {
//         type: "GSGeneraLiquidacion",
//         tiendaid: tiendausuario,
//         api_key: api_key,
//         usuarioid: idusuario,
//         cabecera: {
//           clienteid: parseInt(clienteid),
//           facturaid: 0,
//           fecha_liquidacion: `${fechaLiquidacion} ${currentTime}`, 
//           tipo_factura: "factura_remito",
//           metodo_pago: 0,
//           nro_liquidacion_arca: arcaNumber
//         },
//         detalle: productos.map((item) => ({
//           remitoid: parseInt(item.remitoid),
//           remito_detalleid: parseInt(item.remitodetalleid),
//           cantidad: parseInt(item.selectedQuantity),
//         })),
//         imagenes: imagenes
//       };

//       const response = await fetch(
//         `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//         {
//           method: "POST",
//           headers: {
//             Accept: "application/json",
//           },
//           body: JSON.stringify(liquidacionData),
//         }
//       );

//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }

//       const responseData = await response.json();

//       if (!responseData.success) {
//         throw new Error(responseData.message || "Error al generar liquidación");
//       }

//       dispatch({
//         type: "GENERATE_LIQUIDACION_SUCCESS",
//         payload: {
//           clienteid,
//           data: responseData,
//         },
//       });

//       return responseData;
//     } catch (error) {
//       console.error("Error generating liquidacion:", error);

//       dispatch({
//         type: "GENERATE_LIQUIDACION_ERROR",
//         payload: {
//           clienteid,
//           error: error.message,
//         },
//       });

//       throw error;
//     }
//   };

//w original
// export const generarLiquidacion = (clienteid, productos, fechaLiquidacion, imagenes = [], arcaNumber = "") => async (dispatch) => {
//   const tiendausuario = parseInt(localStorage.getItem("tiendausuario"));
//   const api_key = localStorage.getItem("api_key");
//   const idusuario = parseInt(localStorage.getItem("idusuario") || 0);

//   try {
//     // Get current time in HH:MM:SS format
//     const now = new Date();
//     const hours = String(now.getHours()).padStart(2, '0');
//     const minutes = String(now.getMinutes()).padStart(2, '0');
//     const seconds = String(now.getSeconds()).padStart(2, '0');
//     const currentTime = `${hours}:${minutes}:${seconds}`;

//     // Format the images with liquidacionid: 0
//     const formattedImages = imagenes.map((img) => ({
//       liquidacionid: 0,
//       nombre: img.nombre,
//       imagen64: img.imagen64,
//     }));

//     // Format the request data with explicit number conversion
//     const liquidacionData = {
//       type: "GSGeneraLiquidacion",
//       tiendaid: tiendausuario,
//       api_key: api_key,
//       usuarioid: idusuario,
//       cabecera: {
//         clienteid: parseInt(clienteid),
//         // facturaid: 0, // Commented out to avoid SQL error; re-enable if backend confirms it's required
//         fecha_liquidacion: `${fechaLiquidacion} ${currentTime}`,
//         tipo_factura: "factura_remito",
//         metodo_pago: 0,
//         nro_liquidacion_arca: arcaNumber || "", // Ensure it's a string
//       },
//       detalle: productos.map((item) => ({
//         remitoid: parseInt(item.remitoid),
//         remito_detalleid: parseInt(item.remitodetalleid),
//         cantidad: parseInt(item.selectedQuantity),
//       })),
//       imagenes: formattedImages, // Include images with liquidacionid: 0
//     };

//     console.log("Sending liquidation request:", JSON.stringify(liquidacionData, null, 2));

//     // Send the liquidation request with images
//     const response = await fetch(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//       {
//         method: "POST",
//         headers: {
//           Accept: "application/json",
//           // "Content-Type": "application/json",
//         },
//         body: JSON.stringify(liquidacionData),
//       }
//     );

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const responseData = await response.json();
//     console.log("Liquidation response:", responseData);

//     if (!responseData.success) {
//       throw new Error(
//         responseData.mensaje || responseData.mensajeError || "Error al generar liquidación"
//       );
//     }

//     dispatch({
//       type: "GENERATE_LIQUIDACION_SUCCESS",
//       payload: {
//         clienteid,
//         data: responseData,
//       },
//     });

//     return responseData;
//   } catch (error) {
//     console.error("Error generating liquidacion:", error);

//     dispatch({
//       type: "GENERATE_LIQUIDACION_ERROR",
//       payload: {
//         clienteid,
//         error: error.message,
//       },
//     });

//     throw error;
//   }
// };

//f

export const generarLiquidacion = (clienteid, productos, fechaLiquidacion, imagenes = [], arcaNumber = "") => async (dispatch) => {
  const tiendausuario = parseInt(localStorage.getItem("tiendausuario"));
  const api_key = localStorage.getItem("api_key");
  const idusuario = parseInt(localStorage.getItem("idusuario") || 0);

  try {
    // Get current time in HH:MM:SS format
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const currentTime = `${hours}:${minutes}:${seconds}`;

    // Format the images with liquidacionid: 0
    const formattedImages = imagenes.map((img) => ({
      liquidacionid: 0,
      nombre: img.nombre,
      imagen64: img.imagen64,
    }));

    // Format the request data with explicit number conversion
    const liquidacionData = {
      type: "GSGeneraLiquidacion",
      tiendaid: tiendausuario,
      api_key: api_key,
      usuarioid: idusuario,
      OI_vendedorid: idusuario, // Added at root level (right below usuarioid)
      cabecera: {
        clienteid: parseInt(clienteid),
        fecha_liquidacion: `${fechaLiquidacion} ${currentTime}`,
        tipo_factura: "factura_remito",
        metodo_pago: 0,
        nro_liquidacion_arca: arcaNumber || "",
      },
      detalle: productos.map((item) => ({
        remitoid: parseInt(item.remitoid),
        remito_detalleid: parseInt(item.remitodetalleid),
        cantidad: parseInt(item.selectedQuantity),
      })),
      imagenes: formattedImages,
    };

    console.log("Sending liquidation request:", JSON.stringify(liquidacionData, null, 2));

    // Send the liquidation request with images
    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
        },
        body: JSON.stringify(liquidacionData),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    console.log("Liquidation response:", responseData);

    if (!responseData.success) {
      throw new Error(
        responseData.mensaje || responseData.mensajeError || "Error al generar liquidación"
      );
    }

    dispatch({
      type: "GENERATE_LIQUIDACION_SUCCESS",
      payload: {
        clienteid,
        data: responseData,
      },
    });

    return responseData;
  } catch (error) {
    console.error("Error generating liquidacion:", error);

    dispatch({
      type: "GENERATE_LIQUIDACION_ERROR",
      payload: {
        clienteid,
        error: error.message,
      },
    });

    throw error;
  }
};


//w mitienda.actions.js
// export const getLiquidaciones = (
//   fechaDesde,
//   fechaHasta,
//   page = 1,
//   clienteid = 0,
//   facturaid = 0,
//   itemsPerPage = 10
// ) => async (dispatch) => {
//   const tiendaid = parseInt(localStorage.getItem("tiendausuario"));
//   const api_key = localStorage.getItem("api_key");

//   dispatch({ type: "GET_LIQUIDACIONES_REQUEST" });

//   try {
//     const requestData = {
//       type: "GSMisLiquidaciones",
//       tiendaid: tiendaid,
//       api_key: api_key,
//       clienteid: parseInt(clienteid) || 0,
//       facturaid: parseInt(facturaid) || 0,
//       fecha_desde: fechaDesde,
//       fecha_hasta: fechaHasta,
//       pagActual: page,
//       paginaResultado: itemsPerPage,
//     };

//     const response = await fetch(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//       {
//         method: "POST",
//         headers: {
//           Accept: "application/json",
//         },
//         body: JSON.stringify(requestData),
//       }
//     );

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const responseData = await response.json();

//     if (!responseData.success) {
//       throw new Error(responseData.message || "Error al obtener liquidaciones");
//     }

//     dispatch({
//       type: "GET_LIQUIDACIONES_SUCCESS",
//       payload: {
//         data: responseData.liquidaciones,
//         paginaUltima: responseData.paginaUltima,
//         paginaSiguiente: responseData.paginaSiguiente,
//         paginaPrevia: responseData.paginaPrevia,
//       },
//     });

//     return responseData;
//   } catch (error) {
//     console.error("Error fetching liquidaciones:", error);

//     dispatch({
//       type: "GET_LIQUIDACIONES_ERROR",
//       payload: error.message,
//     });

//     throw error;
//   }
// };


//f
export const getLiquidaciones = (
  fechaDesde,
  fechaHasta,
  page = 1,
  clienteid = 0,
  facturaid = 0,
  itemsPerPage = 10
) => async (dispatch) => {
  const tiendaid = parseInt(localStorage.getItem("tiendausuario"));
  const api_key = localStorage.getItem("api_key");
  const vendedorid = localStorage.getItem("idusuario");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    // Admins (profile 7) always see everything
    if (profileValues.includes(7)) return "ALL";
    // Vendedores (profile 18) without admin see only their data
    if (profileValues.includes(18)) return "OWN";
    // Default: see everything
    return "ALL";
  };

  dispatch({ type: "GET_LIQUIDACIONES_REQUEST" });

  try {
    const accessLevel = getAccessLevel();
    const vendedorParam = accessLevel === "OWN" 
      ? { OI_vendedorid: parseInt(vendedorid) }
      : {};

    const requestData = {
      type: "GSMisLiquidaciones",
      tiendaid: tiendaid,
      api_key: api_key,
      clienteid: parseInt(clienteid) || 0,
      facturaid: parseInt(facturaid) || 0,
      fecha_desde: fechaDesde,
      fecha_hasta: fechaHasta,
      pagActual: page,
      paginaResultado: itemsPerPage,
      ...vendedorParam // Spread the vendedor parameter conditionally
    };

    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
        },
        body: JSON.stringify(requestData),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();

    if (!responseData.success) {
      throw new Error(responseData.message || "Error al obtener liquidaciones");
    }

    dispatch({
      type: "GET_LIQUIDACIONES_SUCCESS",
      payload: {
        data: responseData.liquidaciones,
        paginaUltima: responseData.paginaUltima,
        paginaSiguiente: responseData.paginaSiguiente,
        paginaPrevia: responseData.paginaPrevia,
      },
    });

    return responseData;
  } catch (error) {
    console.error("Error fetching liquidaciones:", error);

    dispatch({
      type: "GET_LIQUIDACIONES_ERROR",
      payload: error.message,
    });

    throw error;
  }
};

// Add this to your mitienda.actions.js file
export const getLiquidacionesGrafico = (
  fechaDesde,
  fechaHasta,
  vendedorid = ''
) => async (dispatch) => {
  const tiendaid = parseInt(localStorage.getItem("tiendausuario"));
  const api_key = localStorage.getItem("api_key");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    if (profileValues.includes(7)) return "ALL";
    if (profileValues.includes(18)) return "OWN";
    return "ALL";
  };

  try {
    const accessLevel = getAccessLevel();
    const vendedorParam = accessLevel === "OWN" 
      ? { OI_vendedorid: parseInt(vendedorid) }
      : {};

    const requestData = {
      type: "GSMisLiquidaciones",
      tiendaid: tiendaid,
      api_key: api_key,
      clienteid: 0,
      facturaid: 0,
      fecha_desde: fechaDesde,
      fecha_hasta: fechaHasta,
      pagActual: 1,
      paginaResultado: 500,
      ...vendedorParam
    };

    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
        },
        body: JSON.stringify(requestData),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();

    if (!responseData.success) {
      throw new Error(responseData.message || "Error al obtener liquidaciones");
    }

    const liquidaciones = Object.values(responseData.liquidaciones);
    dispatch({ type: "GET_LIQUIDACIONES_GRAFICO", payload: liquidaciones });

    return liquidaciones;
  } catch (error) {
    console.error("Error fetching liquidaciones:", error);
    throw error;
  }
};

//w
// export const uploadComprobantes = (imagenes) => async (dispatch) => {
//   const tiendausuario = parseInt(localStorage.getItem("tiendausuario"));
//   const api_key = localStorage.getItem("api_key");
//   const idusuario = parseInt(localStorage.getItem("idusuario") || 0);

//   try {
//     dispatch({ type: "UPLOAD_COMPROBANTES_START" });

//     const response = await axios.post(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//       {
//         type: "GSAdjuntarImagenes",
//         tiendaid: tiendausuario,
//         api_key: api_key,
//         usuarioid: idusuario,
//         imagenes: imagenes
//       }
//     );

//     if (!response.data.success) {
//       throw new Error(response.data.mensaje || "Error al subir comprobantes");
//     }

//     dispatch({
//       type: "UPLOAD_COMPROBANTES_SUCCESS",
//       payload: response.data
//     });

//     return response.data;
//   } catch (error) {
//     dispatch({
//       type: "UPLOAD_COMPROBANTES_ERROR",
//       payload: error.message
//     });
//     throw error;
//   }
// };

//w
// export const uploadComprobantes = (imagenes) => async (dispatch) => {
//   const tiendausuario = parseInt(localStorage.getItem("tiendausuario"));
//   const api_key = localStorage.getItem("api_key");
//   const idusuario = parseInt(localStorage.getItem("idusuario") || 0);

//   try {
//     dispatch({ type: "UPLOAD_COMPROBANTES_START" });

//     const uploadData = {
//       type: "GSAdjuntarImagenes",
//       tiendaid: tiendausuario,
//       api_key: api_key,
//       usuarioid: idusuario,
//       imagenes: imagenes
//     };

//     const response = await fetch(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
//       {
//         method: "POST",
//         headers: {
//           Accept: "application/json",
//         },
//         body: JSON.stringify(uploadData),
//       }
//     );

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const responseData = await response.json();

//     if (!responseData.success) {
//       throw new Error(responseData.mensaje || "Error al subir comprobantes");
//     }

//     dispatch({
//       type: "UPLOAD_COMPROBANTES_SUCCESS",
//       payload: responseData
//     });

//     return responseData;
//   } catch (error) {
//     dispatch({
//       type: "UPLOAD_COMPROBANTES_ERROR",
//       payload: error.message
//     });
//     throw error;
//   }
// };

export const uploadComprobantes = (liquidacionesid, imagenes) => async (dispatch) => {
  const tiendausuarioid = parseInt(localStorage.getItem("tiendausuario"));
  const api_key = localStorage.getItem("api_key");
  const idusuario = parseInt(localStorage.getItem("idusuario") || 0);

  try {
    dispatch({ type: "UPLOAD_COMPROBANTES_START" });

    // Ensure imagenes includes liquidacionid
    const formattedImagenes = imagenes.map((img) => ({
      liquidacionid: parseInt(liquidacionesid),
      nombre: img.nombre,
      imagen64: img.imagen64,
    }));

    const uploadData = {
      type: "GSAdjuntarImagenes",
      tiendaid: tiendausuarioid,
      api_key: api_key,
      usuarioid: idusuario,
      imagenes: formattedImagenes,
    };

    console.log("Uploading comprobantes request:", JSON.stringify(uploadData, null, 2));

    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          // "Content-Type": "application/json",
        },
        body: JSON.stringify(uploadData),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    console.log("Upload comprobantes response:", responseData);

    if (!responseData.success) {
      throw new Error(responseData.mensaje || "Error al subir comprobantes");
    }

    dispatch({
      type: "UPLOAD_COMPROBANTES_SUCCESS",
      payload: responseData,
    });

    return responseData;
  } catch (error) {
    console.error("Error uploading comprobantes:", error);

    dispatch({
      type: "UPLOAD_COMPROBANTES_ERROR",
      payload: error.message,
    });

    throw error;
  }
};

export const getLiquidacion = (id, clienteid) => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");

  try {
    dispatch({
      type: "GET_LIQUIDACION",
      payload: { loading: true, data: [] },
    });

    const liquidacionData = {
      type: "GSMiLiquidacion",
      tiendaid: tiendausuario,
      api_key: api_key,
      clienteid: parseInt(clienteid, 10),
      facturaid: parseInt(id, 10),
    };

    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
        },
        body: JSON.stringify(liquidacionData),
      }
    );

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    const data = await response.json();

    if (!data || !data.success) {
      throw new Error(data?.mensaje || "Error al obtener liquidación");
    }

    // Format the data to match what the component expects
    if (data && data.cabecera && data.detalle) {
      const formattedData = [
        {
          facturaid: data.cabecera.facturaid,
          clienteid: data.cabecera.clienteid,
          nombrecliente: data.cabecera.nombre,
          cuit: data.cabecera.cuit,
          direccion: data.cabecera.direccion,
          email: data.cabecera.email,
          telefono: data.cabecera.telefono,
          fechaliquidacion: data.cabecera.fechaliquidacion,
          descripcionestado: data.cabecera.descripcionestado || "Sin estado",
          nro_liquidacion_arca: data.cabecera.nro_liquidacion_arca,
          url_comprobante: data.cabecera.url_comprobante,
          total: data.detalle.t_total,
          detalle: Object.entries(data.detalle)
            .filter(
              ([key, value]) =>
                typeof value === "object" && value.facturadetalleid
            )
            .map(([key, item]) => ({
              facturadetalleid: item.facturadetalleid,
              pedidoplacaid: item.pedidoplacaid,
              descripcion: item.nombreproducto,
              codigoarticulo: item.codigoarticulo,
              cantidad: item.cantidad,
              precioventa: item.precioventa,
              preciosiniva: item.preciosiniva,
              iva: item.iva,
              importeiva: item.importeiva,
              descuento: item.descuento,
              total: (item.precioventa * item.cantidad).toFixed(2),
            })),
        },
      ];

      dispatch({
        type: "GET_LIQUIDACION",
        payload: { loading: false, data: formattedData, rawData: data },
      });

      return { success: true, data: formattedData };
    } else {
      throw new Error("Formato de respuesta inválido");
    }
  } catch (error) {
    console.error("Error fetching liquidación:", error);
    dispatch({
      type: "GET_LIQUIDACION",
      payload: { loading: false, data: [] },
    });
    throw error;
  }
};

// export const procesarLiquidacion = (facturaid) => async (dispatch) => {
//   const tiendausuario = localStorage.getItem("tiendausuario");
//   const api_key = localStorage.getItem("api_key");
  
//   try {
//     const liquidacionData = {
//       type: "GTProcesarLiquidacion",
//       tiendaid: tiendausuario,
//       api_key: api_key,
//       facturaid: facturaid
//     };

//     const response = await fetch(
//       `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
//       {
//         method: "POST",
//         headers: {
//           Accept: "application/json",
//         },
//         body: JSON.stringify(liquidacionData),
//       }
//     );

//     if (!response.ok) {
//       throw new Error(`Server responded with status: ${response.status}`);
//     }

//     const data = await response.json();

//     dispatch({ type: "PROCESAR_LIQUIDACION", payload: data });

//     if (!data.success) {
//       throw new Error(data.mensaje || "Error al procesar la liquidación");
//     }

//     return data;
//   } catch (error) {
//     console.error("Error processing liquidacion:", error);

//     if (error instanceof Error) {
//       if (!error.message.includes("status")) {
//         dispatch({
//           type: "PROCESAR_LIQUIDACION",
//           payload: {
//             success: false,
//             mensaje: error.message,
//           },
//         });
//       }
//     }

//     throw error;
//   }
// };

// export const procesarLiquidacion = (facturaid) => async (dispatch) => {
//   const tiendausuario = localStorage.getItem("tiendausuario");
//   const api_key = localStorage.getItem("api_key");
  
//   dispatch({ type: "PROCESAR_LIQUIDACION_REQUEST" });
  
//   try {
//     const liquidacionData = {
//       type: "GTProcesarLiquidacion",
//       tiendaid: tiendausuario,
//       api_key: api_key,
//       facturaid: facturaid
//     };

//     const response = await fetch(
//       `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
//       {
//         method: "POST",
//         headers: {
//           Accept: "application/json",
//         },
//         body: JSON.stringify(liquidacionData),
//       }
//     );

//     if (!response.ok) {
//       throw new Error(`Server responded with status: ${response.status}`);
//     }

//     const data = await response.json();

//     dispatch({ 
//       type: "PROCESAR_LIQUIDACION", 
//       payload: data 
//     });

//     if (!data.success) {
//       throw new Error(data.mensaje || "Error al procesar la liquidación");
//     }

//     return data;
//   } catch (error) {
//     console.error("Error processing liquidacion:", error);

//     dispatch({
//       type: "PROCESAR_LIQUIDACION_FAILURE",
//       payload: error.message
//     });

//     throw error;
//   }
// };

// // Reset action
// export const resetLiquidacionRawData = () => (dispatch) => {
//   dispatch({ type: "RESET_LIQUIDACION_RAW_DATA" });
// };


// export const cambiarEstadoLiquidacion = (facturaid, estado) => async (dispatch) => {
//   var tiendausuario = localStorage.getItem("tiendausuario");
//   var api_key = localStorage.getItem("api_key");
//   console.log("liquidacion desde action", facturaid, "estado", estado);
  
//   // TODO: Remember to implement the endpoint for liquidaciones
//   console.warn("TODO: Implement endpoint for cambiarEstadoLiquidacion");
  
//   try {
//     // Format the data according to the required structure
//     const liquidacionData = {
//       type: "GTSetEstadoLiquidacion", // This will need to be updated with the correct type
//       tiendaid: tiendausuario,
//       api_key: api_key,
//       facturaid: facturaid,
//       liquidacionestadoid: estado, // Changed from facturaestadoid to liquidacionestadoid
//     };

//     // Placeholder response until endpoint is implemented
//     const placeholderResponse = {
//       success: true,
//       mensaje: "Estado de liquidación actualizado correctamente (placeholder)"
//     };

//     dispatch({ type: "CAMBIAR_ESTADO_LIQUIDACION", payload: placeholderResponse });
//     return placeholderResponse;

//     /* 
//     // Actual implementation will look like this when endpoint is ready:
//     const response = await fetch(
//       `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
//       {
//         method: "POST",
//         headers: {
//           Accept: "application/json",
//         },
//         body: JSON.stringify(liquidacionData),
//       }
//     );

//     if (!response.ok) {
//       throw new Error(`Server responded with status: ${response.status}`);
//     }

//     const data = await response.json();

//     dispatch({ type: "CAMBIAR_ESTADO_LIQUIDACION", payload: data });

//     if (!data.success) {
//       throw new Error(data.mensaje || "Error al cambiar estado de la liquidación");
//     }

//     return data;
//     */
//   } catch (error) {
//     console.error("Error changing liquidacion state:", error);

//     if (error instanceof Error) {
//       if (!error.message.includes("status")) {
//         dispatch({
//           type: "CAMBIAR_ESTADO_LIQUIDACION",
//           payload: {
//             success: false,
//             mensaje: error.message,
//           },
//         });
//       }
//     }

//     throw error;
//   }
// };

export const getEstadosLiquidacion = () => async (dispatch) => {
  var tiendausuario = localStorage.getItem("tiendausuario");
  var api_key = localStorage.getItem("api_key");
  
  // TODO: Remember to implement the endpoint for getting liquidacion states
  console.warn("TODO: Implement endpoint for getEstadosLiquidacion");
  
  try {
    // Placeholder data - replace with actual API call when ready
    const placeholderEstados = [
      { liquidacionestadoid: 1, nombre: "Pendiente", orden: 1 },
      { liquidacionestadoid: 2, nombre: "Liquidado", orden: 2 },
      { liquidacionestadoid: 3, nombre: "Vencido", orden: 3 }
    ];

    dispatch({ 
      type: "GET_ESTADOS_LIQUIDACION", 
      payload: placeholderEstados 
    });

    return placeholderEstados;

    /* 
    // Actual implementation will look like this when endpoint is ready:
    const response = await fetch(
      `${process.env.REACT_APP_SERVER}php/GestionTienda.php`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
        },
        body: JSON.stringify({
          type: "GTGetEstadosLiquidacion",
          tiendaid: tiendausuario,
          api_key: api_key
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      dispatch({ type: "GET_ESTADOS_LIQUIDACION", payload: data.data });
    } else {
      throw new Error(data.mensaje || "Error al obtener estados de liquidación");
    }

    return data;
    */
  } catch (error) {
    console.error("Error getting liquidacion states:", error);
    throw error;
  }
};


//w
export const cambiarEstadoLiquidacion = (liquidacionid, liquidacionestadoid) => async (dispatch) => {

  
  const tiendaid = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");

  if (isNaN(liquidacionestadoid)) {
    throw new Error("Invalid estado ID");
  }
  
  try {
    const response = await fetch(`${process.env.REACT_APP_SERVER}php/GestionShop.php`, {
      method: 'POST',
      body: JSON.stringify({
        type: "GSSetEstadoLiquidacion",
        tiendaid: tiendaid,
        api_key: api_key,
       liquidacionid: Number(liquidacionid),  
        liquidacionestadoid: Number(liquidacionestadoid)
      })
    });

    const data = await response.json();
    
    if (data.success) {
      dispatch({ 
        type: "CAMBIAR_ESTADO_LIQUIDACION", 
        payload: data 
      });
      return data;
    } else {
      throw new Error(data.mensaje || "Error al cambiar estado");
    }
  } catch (error) {
    dispatch({ 
      type: "CAMBIAR_ESTADO_LIQUIDACION_ERROR", 
      payload: { success: false, mensaje: error.message } 
    });
    throw error;
  }
};

export const triggerNotificationRefresh = () => ({
  type: 'TRIGGER_NOTIFICATION_REFRESH'
});

//w
// export const getComprobantesByLiquidacion = (liquidacionid) => async (dispatch) => {
//   const tiendaid = localStorage.getItem("tiendausuario");
//   const api_key = localStorage.getItem("api_key");

//   if (!liquidacionid) {
//     throw new Error("Invalid liquidacion ID");
//   }

//   dispatch({ type: 'FETCH_COMPROBANTES_START' });
  
//   try {
//     const response = await fetch(`${process.env.REACT_APP_SERVER}php/GestionShop.php`, {
//       method: 'POST',
//       body: JSON.stringify({
//         type: "GSMisComprobantes",
//         tiendaid: tiendaid,
//         api_key: api_key,
//         liquidacionid: Number(liquidacionid)
//       })
//     });

//     const data = await response.json();
    
//     if (data.success) {
//       dispatch({
//         type: 'FETCH_COMPROBANTES_SUCCESS',
//         payload: data.documentos
//       });
//       return data;
//     } else {
//       throw new Error(data.mensaje || "Error al obtener comprobantes");
//     }
//   } catch (error) {
//     dispatch({
//       type: 'FETCH_COMPROBANTES_FAILURE',
//       payload: { success: false, mensaje: error.message }
//     });
//     throw error;
//   }
// };

export const getComprobantesByLiquidacion = (liquidacionid) => async (dispatch) => {
  const tiendaid = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");

  if (!liquidacionid) {
    throw new Error("Invalid liquidacion ID");
  }

  dispatch({ type: 'FETCH_COMPROBANTES_START' });
  
  try {
    const response = await fetch(`${process.env.REACT_APP_SERVER}php/GestionShop.php`, {
      method: 'POST',
      body: JSON.stringify({
        type: "GSMisComprobantes",
        tiendaid: tiendaid,
        api_key: api_key,
        liquidacionid: Number(liquidacionid)
      })
    });

    const data = await response.json();
    
    if (data.success) {
      // Handle empty documentos case
      const documentos = data.documentos && Object.keys(data.documentos).length > 0 
        ? data.documentos 
        : null;
      
      dispatch({
        type: 'FETCH_COMPROBANTES_SUCCESS',
        payload: documentos
      });
      return data;
    } else {
      throw new Error(data.mensaje || "No se encontraron comprobantes");
    }
  } catch (error) {
    dispatch({
      type: 'FETCH_COMPROBANTES_FAILURE',
      payload: error.message // Just pass the string message
    });
    throw error;
  }
};


//w original
// export const getNotificationRemitosPendientes = (
//   fechaDesde,
//   fechaHasta,
//   page,
//   paginaResultado,
//   clienteid = null,
//   remitoid = 0
// ) =>
// async (dispatch) => {
//   const tiendausuario = localStorage.getItem("tiendausuario");
//   const api_key = localStorage.getItem("api_key");

//   const useClientId = clienteid && clienteid !== "0" ? clienteid : "";
//   const useRemitoId = remitoid || 0;

//   try {
//     const response = await axios.get(
//       `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisRemitos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${useClientId}&remitoid=${useRemitoId}&busca_fentrega=0&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}`
//     );

//     if (response.data && response.data.success) {
//       const actionPayload = {
//         type: "GET_NOTIFICATION_REMITOS_PENDIENTES",
//         payload: {
//           data: response.data.detalle,
//           paginaUltima: response.data.paginaUltima,
//         },
//       };

//       dispatch(actionPayload);
//       return actionPayload;
//     } else {
//       throw new Error("No data received or request was not successful");
//     }
//   } catch (error) {
//     console.error("Error in getNotificationRemitosPendientes:", error);
//     throw error;
//   }
// };

//f OI_vendedorid

export const getNotificationRemitosPendientes = (
  fechaDesde,
  fechaHasta,
  page,
  paginaResultado,
  clienteid = null,
  remitoid = 0
) => async (dispatch) => {
  const tiendausuario = localStorage.getItem("tiendausuario");
  const api_key = localStorage.getItem("api_key");
  const vendedorid = localStorage.getItem("idusuario");

  // Check user profiles and determine access level
  const getAccessLevel = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    
    // Admins (profile 7) always see everything
    if (profileValues.includes(7)) return "ALL";
    // Vendedores (profile 18) without admin see only their data
    if (profileValues.includes(18)) return "OWN";
    // Default: see everything
    return "ALL";
  };

  const useClientId = clienteid && clienteid !== "0" ? clienteid : "";
  const useRemitoId = remitoid || 0;
  const accessLevel = getAccessLevel();
  const vendedorParam = accessLevel === "OWN" 
    ? `&OI_vendedorid=${vendedorid}`
    : "";

  try {
    const response = await axios.get(
      `${process.env.REACT_APP_SERVER}php/GestionShop.php?type=GSDetalleMisRemitos&tiendaid=${tiendausuario}&api_key=${api_key}&clienteid=${useClientId}&remitoid=${useRemitoId}&busca_fentrega=0&fecha_desde=${fechaDesde}&fecha_hasta=${fechaHasta}&paginaResultado=${paginaResultado}&pagActual=${page}${vendedorParam}`
    );

    if (response.data && response.data.success) {
      const actionPayload = {
        type: "GET_NOTIFICATION_REMITOS_PENDIENTES",
        payload: {
          data: response.data.detalle,
          paginaUltima: response.data.paginaUltima,
        },
      };

      dispatch(actionPayload);
      return actionPayload;
    } else {
      throw new Error("No data received or request was not successful");
    }
  } catch (error) {
    console.error("Error in getNotificationRemitosPendientes:", error);
    throw error;
  }
};