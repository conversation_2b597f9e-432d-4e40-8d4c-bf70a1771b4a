import React, { useEffect, useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Autocomplete, Button, MenuItem, Pagination, Paper, Select, TextField } from "@mui/material";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { 
    agregarArticulo, 
    editarDetallePresupuesto, 
    eliminarArticulo, 
    getCarritoDetalle, 
    getDescuentosSinPaginado, 
    getDetallePresupuesto, 
    getProductosByNameSkuNuevaVenta, 
    getProductosByNameSkuPromocion, 
    limpiarModificarProductoEditable, 
    modificarCantidadArticulo
} from "../../../redux/actions/mitienda.actions";
import { useDispatch, useSelector } from "react-redux";
import { Delete } from "@mui/icons-material";
import { ModalProductoEditable } from "../ventas/modalProductoEditable";
import { SelectSearchInput } from "./SelectSearchInput";

Modal.setAppElement("#root")

export const ModalAgregarItems = ({show, handleClose, info}) =>{

  const productsByNameSku = useSelector((state) => state.mitienda.productsByNameSkuPromocion)
  const descuentos = useSelector((state) => state.mitienda.descuentosSinPaginado)
  const detalleCarrito = useSelector((state) => state.mitienda.detallePresupuesto)
  const okAgregarArticulo = useSelector((state) => state.mitienda.okAgregarArticulo)
  const okEditarDetallePresupuesto = useSelector((state) => state.mitienda.okEditarDetallePresupuesto)
  const okEliminarArticulo = useSelector((state) => state.mitienda.okEliminarArticulo)
  const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaDetalleCarrito)

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "80%",
        height: "85vh",
        borderRadius: "8px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };

  const [cantidad, setCantidad] = useState("1")
  const [descuento, setDescuento] = useState("")
  const [product, setProduct] = useState("")
  const [productDetail, setProductDetail] = useState("")
  const [facturaid, setFactura] = useState("")
  const [open, setOpen] = useState(false)
  const [productoEditable, setProductoEditable] = useState("")
  const [input, setInput] = useState('')
  const handleChange = (e) => {
    e.preventDefault()
    setInput(e.target.value)
  }

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
      setPage(value);
  };

  const [showEditarDetalle, setShowEditarDetalle] = useState('');
  const handleCloseEditarDetalle = () => {
    setShowEditarDetalle(false);
    dispatch(limpiarModificarProductoEditable())
    }
  const handleShowEditarDetalle = (row) => {
      setProductoEditable(row)
      setShowEditarDetalle(true);
  }

  const dispatch = useDispatch()
  
  //presupuestoplacaid, cantidad, presupuestoplaca_productoid, descuentoid
  const handleAddToCart =()=>{
    dispatch(editarDetallePresupuesto(info.presupuestoid, productDetail, cantidad, descuento))
    dispatch(getProductosByNameSkuPromocion('')) 
    setProduct('')
    setCantidad('1')
    setInput('')
    setDescuento('')
  }

  const handleDeleteProduct =(p)=>{
    dispatch(eliminarArticulo(info.clienteid, p.facturadetalleid))
  }

  const handleChangeCantidad = (v,p) =>{
    dispatch(modificarCantidadArticulo(p.facturadetalleid, v))
  }

  useEffect(() =>{
    if(input.length > 4){
        dispatch(getProductosByNameSkuPromocion(input)) 
    }
  }, [input])

  useEffect(() =>{
    if(okEditarDetallePresupuesto.success && info.clienteid !== ''){
        dispatch(getDetallePresupuesto(info.presupuestoid))
    }
}, [okEditarDetallePresupuesto])

useEffect(() =>{
    if(okEliminarArticulo !== ''){
        dispatch(getDetallePresupuesto(info.presupuestoid))
    }
}, [okEliminarArticulo])

  useEffect(() =>{
    if(show){
        dispatch(getProductosByNameSkuPromocion(''))
        setInput('')
        dispatch(getDescuentosSinPaginado())
    }
  }, [show])

  useEffect(() =>{
    if(info.clienteid){
        dispatch(getDetallePresupuesto(info.presupuestoid))
    }
  }, [info])

  return (
      show && 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <ModalProductoEditable
            show={showEditarDetalle}
            handleClose={handleCloseEditarDetalle}
            producto={productoEditable}
        />
        <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
            <h4>Agregar items - Presupuesto #{info.presupuestoid}</h4>
            <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
        </div>
        <Divider/>
        <div style={{display:"flex", width:"100%", justifyContent:"space-between", alignItems:"center", height:70, marginTop:20}}>
            <div style={{width:"40%", marginRight:20, marginTop:-20}}>
                <h6><strong>Producto</strong></h6>
                <SelectSearchInput
                    info={productsByNameSku}
                    value={input}
                    setValue={setInput}
                    setProductoId={setProduct}
                    open={open}
                    setOpen={setOpen}
                    setProductDetail={setProductDetail}
                />
            </div>
            <div style={{width:"10%", marginRight:20}}>
                <h6><strong>Cantidad</strong></h6>
                <TextField
                    style={{width:"100%"}}
                    type='number'
                    name='cantidad'
                    value={cantidad}
                    onChange={(e)=>setCantidad(e.target.value)}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
            </div>
            <div style={{width:"40%", marginRight:20}}>
                <h6><strong>Descuento</strong></h6>
                <Select
                    size="small"
                    style={{width:"100%", height:40}}
                    value={descuento|| ""}
                    name='descuento'
                    onChange={(e)=>setDescuento(e.target.value)}
                    MenuProps={{ keepMounted: true, disablePortal: true }}
                    >
                    {               
                        descuentos && descuentos.map((t) =>
                            <MenuItem value={t.descuentovtaid} key={t.descuentovtaid}>{t.nombre}</MenuItem>
                        )
                    }
                </Select>
            </div>
            <Button style={{marginTop:25}} variant='contained'  onClick={(e)=>handleAddToCart(info.clienteid)}>Agregar</Button>
        </div>
        <div style={{padding:20, width:"100%", display:"flex", flexDirection:"column", alignItems:"center"}}>
        <TableContainer component={Paper} style={{marginTop:20}}>
            <Table>
            <TableHead>
                <TableRow>
                    {/* <TableCell style={{fontWeight:"bold", fontSize:"100%"}}>#</TableCell> */}
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Item</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Cantidad</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Descuento</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Total</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center"></TableCell>
                </TableRow>
            </TableHead>
            <TableBody>
                {detalleCarrito && detalleCarrito.map((row) => (
                <TableRow
                    // key={row.facturaid}
                    sx={{ '&:last-child td, &:last-child th': { border: 0 }}}
                >
                    {/* {
                        row.editadetalle === "1" ?
                        <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0, cursor:"pointer"}} align="center"
                            onClick={(e) => handleShowEditarDetalle(row)} 
                        >{row.codigobarras}</TableCell> :
                        <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center"
                        >{row.codigobarras}</TableCell>
                    } */}
                    <TableCell style={{fontSize:"14px", width:"160px", height:"40px", padding: 0, cursor:"pointer"}} align="center"
                    >{row.nombre_producto}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"40px", height:"40px", padding: 0}} align="center"
                    ><TextField
                        style={{width:"45%"}}
                        type='number'
                        value={row.cantidad}
                        onChange={(e) => handleChangeCantidad(e.target.value, row)}
                    /></TableCell>
                    <TableCell style={{fontSize:"14px", width:"140px", height:"40px", padding: 2}} align="center"
                    >{row.descuentos
                    }%
                        </TableCell>
                    <TableCell style={{fontSize:"14px", width:"60px", height:"40px", padding: 0}} align="center"
                    >${row.precioventa}
                    </TableCell>
                    <TableCell style={{fontSize:"14px", width:"60px", height:"40px", padding: 0, cursor:"pointer"}} align="center"
                    onClick={(e)=>handleDeleteProduct(row)}><Delete/></TableCell>
                </TableRow>
                ))}
            </TableBody>
            </Table>
        </TableContainer>
        <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", margin:3}} /> 
        </div>          
      </Modal>
  )
}