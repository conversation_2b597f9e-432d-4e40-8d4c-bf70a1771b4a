import React, { memo, useRef, useState } from "react";
import Box from "@mui/material/Box";
import Drawer from "@mui/material/Drawer";
import List from "@mui/material/List";
import Divider from "@mui/material/Divider";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import ListSubheader from "@mui/material/ListSubheader";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import Collapse from "@mui/material/Collapse";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { styled, useTheme } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import MuiAppBar from "@mui/material/AppBar";
import PersonAdd from "@mui/icons-material/PersonAdd";
import ListItemIcon from "@mui/material/ListItemIcon";

import "./layoutMiTienda.css";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  getConfigPickit,
  getDatosHome,
  getLogo,
  getNombreTienda,
} from "../../../redux/actions/mitienda.actions";
import { useEffect } from "react";
import { crearObjeto, filtrar } from "./utils";
import { getMenu } from "../../../redux/actions/user.actions";
import { Button, Menu, Tooltip } from "@mui/material";
import pdf from "./ManualUsuarioV5.pdf";
import logoDevelshops from "../../../media/logo_develshops.png";
import { ModalOffline } from "./modalOffline";
import { Clear, Person, PointOfSale } from "@mui/icons-material";
import { Avatar, MenuItem, Paper } from "@mui/material";
import { ModalManejoCaja } from "../manejoCaja/modalManejoCaja";
import NotificationSystem from "../notificationSystem/notificationSystem";

const drawerWidth = 290;

const Main = styled("main", { shouldForwardProp: (prop) => prop !== "open" })(
  ({ theme, open }) => ({
    flexGrow: 1,
    transition: theme.transitions.create("margin", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginLeft: `-${drawerWidth}px`,
    ...(open && {
      transition: theme.transitions.create("margin", {
        easing: theme.transitions.easing.easeOut,
        duration: theme.transitions.duration.enteringScreen,
      }),
      marginLeft: 0,
    }),
  })
);

// prevent children components to re-render with sidebar toggle
const MemoizedChildren = memo(({ children }) => {
  return <>{children}</>;
});

export const LayoutMiTienda = (props) => {
  const logo = useSelector((state) => state.mitienda.logo);
  const info = useSelector((state) => state.user.menu);
  const infoTienda = useSelector((state) => state.mitienda.nombreTienda);

  let filtrada = filtrar(info);
  let objeto = crearObjeto(filtrada);

  const [currentURL, setCurrentURL] = React.useState("");
  const [anchorEl, setAnchorEl] = React.useState(false);

  const [openDrawer, setOpenDrawer] = React.useState(false);
  const handleDrawerClose = () => {
    setOpenDrawer(!openDrawer);
  };

  const [aux, setAux] = useState(true);

  const [show, setShow] = useState(false);
  const handleClose = () => {
    setShow(false);
  };
  const handleShow = () => {
    setShow(true);
  };

  const [showCaja, setShowCaja] = useState(false);
  const handleCloseCaja = () => setShowCaja(false);
  const handleShowCaja = () => {
    setShowCaja(true);
  };

  const [open, setOpen] = useState(objeto);
  const handleClick = (e, campo) => {
    setOpen({ ...open, [campo]: !open[campo] });
  };

  const cerrar = () => {
    window.location.href = "/";
    // localStorage.clear();
    setTimeout(() => {
      localStorage.clear();
    }, 100);
  };

  const dispatch = useDispatch();

  let tiendausuario = localStorage.getItem("tiendausuario");
  //w
  // let permisos_acciones_usuarios = Object.values(
  //   JSON.parse(localStorage.getItem("permisos_acciones_usuarios"))
  // )[0];
  // let permisos_acciones_caja = Object.values(
  //   JSON.parse(localStorage.getItem("permisos_acciones_caja"))
  // )[0];

  let permisos_acciones_usuarios = localStorage.getItem(
    "permisos_acciones_usuarios"
  )
    ? Object.values(
        JSON.parse(localStorage.getItem("permisos_acciones_usuarios"))
      )[0]
    : null;

  let permisos_acciones_caja = localStorage.getItem("permisos_acciones_caja")
    ? Object.values(
        JSON.parse(localStorage.getItem("permisos_acciones_caja"))
      )[0]
    : null;

  // checks if user has admin profile
  const hasAdminProfile = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    // console.log(Object.values(perfiles).includes(7))

    return Object.values(perfiles).includes(7);
  };

  const isVendedor = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    return Object.values(perfiles).includes(18); // 18 is vendedor profile
  };

  const h1 = useRef();

  const ti = () => {
    let fechahora = new Date();
    let horaAux = fechahora.getHours();
    let hora = horaAux <= 9 ? "0" + horaAux : horaAux;
    let minutoAux = fechahora.getMinutes();
    let minuto = minutoAux <= 9 ? "0" + minutoAux : minutoAux;
    return `${hora}:${minuto}`;
  };

  useEffect(() => {
    const cl = setInterval(() => {
      h1.current.innerHTML = `${ti()}`;
    }, 1000);
    return () => clearInterval(cl);
  }, []);

  useEffect(() => {
    dispatch(getNombreTienda());
    dispatch(getMenu());
    dispatch(getLogo());
    dispatch(getConfigPickit());
  }, []);

  // closes drawer for any route
  useEffect(() => {
    handleDrawerClose();
  }, [currentURL]);

  useEffect(() => {
    dispatch(getDatosHome());
  }, []);

  const list = () => (
    <Box sx={{ width: 270, margin: 0 }}>
      <List
        subheader={
          <ListSubheader
            component="div"
            id="nested-list-subheader"
            className="header-layout"
          >
            <Link to="/Mitienda/Home" className="link">
              {logo === "" ? (
                <img
                  style={{
                    width: "100px",
                    height: "70px",
                    padding: 0,
                  }}
                  src={logoDevelshops}
                  alt="logo develshops"
                />
              ) : (
                <img
                  style={{
                    width: "100px",
                    height: "60px",
                    padding: 0,
                  }}
                  alt="logo"
                  src={
                    process.env.REACT_APP_SERVER +
                    "archivos/logos/cart/" +
                    tiendausuario +
                    "/" +
                    logo
                  }
                />
              )}
            </Link>
            <Divider />
          </ListSubheader>
        }
      >
        {filtrada &&
          filtrada.map((m) => (
            <div key={m.nombredireccion}>
              {
                <ListItemButton
                  onClick={(e) => handleClick(e, m.nombredireccion)}
                  key={m.titulo}
                >
                  {/* original > this maps the single submenu coming in to the menu itself */}
                  {/* {m.menues.length === 1 && (
                    <Link
                      to={m.menues[0].urlsubmenu}
                      className="link"
                      style={{ width: 230 }}
                    >
                      <ListItemText
                        primary={m.nombredireccion}
                        onClick={(e) => setCurrentURL(m.menues[0].urlsubmenu)}
                      />
                    </Link>
                  )}
                  {m.menues.length > 1 && (
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-evenly",
                        alignItems: "center",
                        width: 230,
                      }}
                    >
                      <ListItemText primary={m.nombredireccion} />
                      {m.menues.length === 0 ? null : open[
                          m.nombredireccion
                        ] ? (
                        <ExpandLess />
                      ) : (
                        <ExpandMore />
                      )}
                    </div>
                  )} */}
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-evenly",
                      alignItems: "center",
                      width: 230,
                    }}
                  >
                    <ListItemText primary={m.nombredireccion} />
                    {m.menues.length === 0 ? null : open[m.nombredireccion] ? (
                      <ExpandLess />
                    ) : (
                      <ExpandMore />
                    )}
                  </div>
                </ListItemButton>
              }
              {/* replaces m.menues.length > 1 by m.menues.length > 0  */}
              {m.menues.length > 0 && (
                <Collapse
                  in={open[m.nombredireccion]}
                  timeout="auto"
                  unmountOnExit
                >
                  {m.menues.length > 0 &&
                    m.menues.map((s) => (
                      <Link
                        to={s.urlsubmenu2}
                        className="link"
                        key={s.nombre}
                        onClick={(e) => {
                          setCurrentURL(s.urlsubmenu2);
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText primary={s.nombre} />
                        </ListItemButton>
                      </Link>
                    ))}

                  {/* submenu Pedidos: hardcoded */}
                  {m.nombredireccion === "Ventas" && (
                    <>
                      <Link
                        to="/MiTienda/Pedidos"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/Pedidos");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="Pedidos"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link>

                      {/* Remitos: hardcoded */}
                      <Link
                        to="/MiTienda/Remitos"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/Remitos");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="Remitos"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link>

                      {/* Liquidaciones: hardcoded */}
                      <Link
                        to="/MiTienda/Liquidaciones"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/Liquidaciones");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="Liquidaciones"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link>

                      {/* Gestion de Entregas (renamed from Gestion Remitos): hardcoded */}
                      {/* <Link
                        to="/MiTienda/GestionRemitos"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/GestionRemitos");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="Gestión de Entregas"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link> */}

                      {/* only admins can see it */}
                      {hasAdminProfile() && (
                        <Link
                          to="/MiTienda/GestionRemitos"
                          className="link"
                          onClick={(e) => {
                            setCurrentURL("/MiTienda/GestionRemitos");
                          }}
                        >
                          <ListItemButton
                            sx={{ pl: 8 }}
                            onClick={() => setAux(!aux)}
                          >
                            <ListItemText
                              primary="Gestión de Entregas"
                              sx={{
                                color: "red",
                                "& .MuiTypography-root": {
                                  fontWeight: "bold",
                                },
                              }}
                            />
                          </ListItemButton>
                        </Link>
                      )}

                      {/* Gestion de Liquidaciones: hardcoded */}
                      <Link
                        to="/MiTienda/GestionLiquidaciones"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/GestionLiquidaciones");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="Gestión de Liquidaciones"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link>
                    </>
                  )}

                  {/* Add Gestion Stock to Productos menu */}
                  {m.nombredireccion === "Productos" && hasAdminProfile() && (
                    <Link
                      to="/MiTienda/GestionStock"
                      className="link"
                      onClick={(e) => {
                        setCurrentURL("/MiTienda/GestionStock");
                      }}
                    >
                      <ListItemButton
                        sx={{ pl: 8 }}
                        onClick={() => setAux(!aux)}
                      >
                        <ListItemText
                          primary="Gestión Stock"
                          sx={{
                            color: "red",
                            "& .MuiTypography-root": {
                              fontWeight: "bold",
                            },
                          }}
                        />
                      </ListItemButton>
                    </Link>
                  )}

                  {m.nombredireccion === "Reportes" && (
                    <>
                      {/* Reporte Pedidos: Hardcoded */}
                      <Link
                        to="/MiTienda/ReportePedidos"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/ReportePedidos");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="REPO1 Reporte Pedidos"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link>

                      {/* reporte Entregas: hardcoded */}
                      <Link
                        to="/MiTienda/ReporteEntregas"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/ReporteEntregas");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="REPO2 Reporte Entregas"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link>

                      {/* Reporte Cuentas Correintes: Hardcoded */}
                      <Link
                        to="/MiTienda/ReporteCuentasCorrientes"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/ReporteCuentasCorrientes");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="REPO3 Cuentas Corrientes"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link>

                      {/* Reporte Liquidaciones: hardcoded */}
                      <Link
                        to="/MiTienda/ReporteStatusLiquidacion"
                        className="link"
                        onClick={(e) => {
                          setCurrentURL("/MiTienda/ReporteStatusLiquidacion");
                        }}
                      >
                        <ListItemButton
                          sx={{ pl: 8 }}
                          onClick={() => setAux(!aux)}
                        >
                          <ListItemText
                            primary="Reporte Liquidación"
                            sx={{
                              color: "red",
                              "& .MuiTypography-root": {
                                fontWeight: "bold",
                              },
                            }}
                          />
                        </ListItemButton>
                      </Link>
                    </>
                  )}
                </Collapse>
              )}
            </div>
          ))}

        <ListItemButton onClick={cerrar}>
          <ListItemText primary="Cerrar sesi&oacute;n" />
        </ListItemButton>
      </List>
    </Box>
  );

  //w (moved outside the component declaration to prevent re-rendering)
  // const Main = styled("main", { shouldForwardProp: (prop) => prop !== "open" })(
  //   ({ theme, open }) => ({
  //     flexGrow: 1,
  //     transition: theme.transitions.create("margin", {
  //       easing: theme.transitions.easing.sharp,
  //       duration: theme.transitions.duration.leavingScreen,
  //     }),
  //     marginLeft: `-${drawerWidth}px`,
  //     ...(openDrawer && {
  //       transition: theme.transitions.create("margin", {
  //         easing: theme.transitions.easing.easeOut,
  //         duration: theme.transitions.duration.enteringScreen,
  //       }),
  //       marginLeft: 0,
  //     }),
  //   })
  // );

  // const drawerWidth = 290;

  const AppBar = styled(MuiAppBar, {
    shouldForwardProp: (prop) => prop !== "open",
  })(({ theme, open }) => ({
    transition: theme.transitions.create(["margin", "width"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    ...(openDrawer && {
      width: `calc(100% - ${drawerWidth}px)`,
      marginLeft: `${drawerWidth}px`,
      transition: theme.transitions.create(["margin", "width"], {
        easing: theme.transitions.easing.easeOut,
        duration: theme.transitions.duration.enteringScreen,
      }),
    }),
  }));

  const DrawerHeader = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    padding: theme.spacing(0, 1),
    // necessary for content to be below app bar
    ...theme.mixins.toolbar,
    justifyContent: "flex-end",
  }));

  const theme = useTheme();

  return (
    <React.Fragment>
      <ModalOffline show={show} handleClose={handleClose} />
      <ModalManejoCaja show={showCaja} handleClose={handleCloseCaja} />
      <Box sx={{ display: "flex" }}>
        <CssBaseline />
        <AppBar
          position="fixed"
          open={openDrawer}
          style={{
            background: "white",
            color: "black",
            boxShadow:
              "0px 2px 4px -1pxrgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)",
          }}
        >
          <Toolbar className="toolbar-mitienda">
            <div style={{ display: "flex", alignItems: "center" }}>
              <IconButton onClick={handleDrawerClose}>
                {openDrawer ? (
                  <Tooltip title="Cerrar menu" placement="bottom">
                    <Clear fontSize="large" />
                  </Tooltip>
                ) : (
                  <Tooltip title="Abrir menu" placement="bottom">
                    <ChevronRightIcon fontSize="large" />
                  </Tooltip>
                )}
              </IconButton>
              <Button
                variant="contained"
                onClick={handleShow}
                style={{ textTransform: "unset", width: 200, marginLeft: 15 }}
              >
                {infoTienda}
              </Button>
            </div>
            <div style={{ marginTop: 10 }}>
              <h3 ref={h1}>{ti()}</h3>
            </div>
            <div>
              {permisos_acciones_caja?.modificar === "1" && (
                <Button
                  style={{ marginRight: 10 }}
                  variant="contained"
                  onClick={handleShowCaja}
                >
                  <PointOfSale />
                </Button>
              )}
              <a
                href={pdf}
                download="manual_usuario.pdf"
                target="_blank"
                rel="noreferrer"
              >
                <Button variant="contained">Manual usuario</Button>
              </a>

              <NotificationSystem />
              {permisos_acciones_caja?.modificar === "1" && (
                <Button
                  style={{ marginRight: 10 }}
                  variant="contained"
                  onClick={handleShowCaja}
                >
                  <PointOfSale />
                </Button>
              )}

              <IconButton
                onClick={(e) => setAnchorEl(!anchorEl)}
                size="large"
                sx={{ ml: 2 }}
                aria-controls={anchorEl ? "account-menu" : undefined}
                aria-haspopup="true"
                aria-expanded={anchorEl ? "true" : undefined}
              >
                <Avatar
                  style={{
                    color: "white",
                    backgroundColor: "grey",
                  }}
                  sx={{
                    width: 38,
                    height: 38,
                  }}
                >
                  <Person />
                </Avatar>
              </IconButton>
            </div>
            {anchorEl && (
              <Paper
                style={{ position: "absolute", right: 40, top: 70, width: 220 }}
              >
                {permisos_acciones_usuarios?.modificar === "1" && (
                  <MenuItem onClick={(e) => setAnchorEl(false)}>
                    <a
                      href="/MiTienda/EditarUsuario"
                      style={{
                        display: "flex",
                        alignItems: "center",
                        color: "black",
                      }}
                    >
                      <Avatar sx={{ mr: 2 }} /> Editar usuario
                    </a>
                  </MenuItem>
                )}
                {permisos_acciones_usuarios?.agregar === "1" &&
                  hasAdminProfile() && (
                    <div>
                      <Divider />
                      <MenuItem
                        onClick={(e) => setAnchorEl(false)}
                        sx={{ mb: 1, mt: 1 }}
                      >
                        <a
                          href="/MiTienda/CrearUsuario"
                          style={{
                            display: "flex",
                            alignItems: "center",
                            color: "black",
                          }}
                        >
                          <ListItemIcon>
                            <PersonAdd />
                          </ListItemIcon>
                          Crear usuario nuevo
                        </a>
                      </MenuItem>
                    </div>
                  )}
                <Divider />
                <MenuItem
                  onClick={(e) => {
                    setAnchorEl(false);
                    cerrar();
                  }}
                  sx={{ mb: 1, mt: 1 }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      color: "black",
                    }}
                  >
                    <ListItemIcon>
                      <Clear />
                    </ListItemIcon>
                    Cerrar sesión
                  </div>
                </MenuItem>
              </Paper>
            )}
          </Toolbar>
        </AppBar>
        <Drawer
          sx={{
            width: drawerWidth,
            flexShrink: 0,
            "& .MuiDrawer-paper": {
              width: drawerWidth,
              boxSizing: "border-box",
            },
          }}
          variant="persistent"
          anchor="left"
          open={openDrawer}
        >
          {list()}
        </Drawer>
        <Main open={openDrawer}>
          <DrawerHeader sx={{ 
    minHeight: '64px', // Match AppBar height
    alignItems: 'flex-start', // Align to top
    paddingTop: '20px' // Add some padding
}} />
          {/* {props.children} */}
          <MemoizedChildren>{props.children}</MemoizedChildren>
        </Main>
      </Box>
    </React.Fragment>
  );
};
