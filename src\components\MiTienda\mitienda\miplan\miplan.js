import React, { useEffect, useState } from "react";
import Modal from "react-modal";

import ArticleIcon from "@mui/icons-material/Article";
import { Button, Paper } from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Pagination from "@mui/material/Pagination";
import logoMercadoPago from "../../../../media/iconomercadopago.png";

import "./miplan.css";
import { useDispatch, useSelector } from "react-redux";

import {
  getAbonos,
  getUrlMercadoPago,
  setGSPagoMP,
} from "../../../../redux/actions/mitienda.actions";
import { Check, Download } from "@mui/icons-material";

Modal.setAppElement("#root");

export const MiPlan = () => {
  const dispatch = useDispatch();

  const abonosInfo = useSelector((state) => state.mitienda.abonos);
  const urlMercadoPago = useSelector((state) => state.mitienda.urlMercadoPago);
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaMiPlan
  );

  const handlePay = (row) => {
    dispatch(
      setGSPagoMP(
        row.clienteid,
        row.pedidoplacaid,
        row.facturaid,
        urlMercadoPago,
        process.env.REACT_APP_SERVER,
        "MiTienda/MiPlan"
      )
    );
  };

  let ip_server = localStorage.getItem("ip_server");

  let pathname = window.location.pathname;
  let permisos_acciones = Object.values(
    JSON.parse(localStorage.getItem("permisos_acciones"))
  ).filter((p) =>
    p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase())
  )[0];

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const formatoFecha = (fecha) => {
    let aux = fecha.slice(0, 10);
    let aux2 = aux.split("-");

    return aux2[2] + "-" + aux2[1] + "-" + aux2[0];
  };

  useEffect(() => {
    dispatch(getAbonos(page));
    dispatch(getUrlMercadoPago());
  }, []);

  return (
    <section className="container-mde">
      <header className="header-miplan">
        <h3>Mi plan</h3>
      </header>
      <TableContainer
        component={Paper}
        className="table-vencimientos"
        style={{ marginBottom: 20 }}
      >
        <Table aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell style={{ fontWeight: "bold", fontSize: 20 }}>
                Mis vencimientos
              </TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Fecha vencimiento
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Cuota
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Monto
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Estado
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Obsevaci&oacute;n
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Pagar
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Descargar factura
              </TableCell>
            </TableRow>
            {abonosInfo.length > 0 && permisos_acciones?.listar !== "0" ? (
              abonosInfo.map((row) => (
                <TableRow>
                  <TableCell
                    style={{ fontSize: 14, padding: 0, height: "50px" }}
                    align="center"
                  >
                    {formatoFecha(row.fechavencimiento)}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: 14, padding: 0, height: "50px" }}
                    align="center"
                  >
                    {row.cuota}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: 14, padding: 0, height: "50px" }}
                    align="center"
                  >
                    ${row.importe2}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: 14, padding: 0, height: "50px" }}
                    align="center"
                  >
                    {row.estado}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: 14, padding: 0, height: "50px" }}
                    align="center"
                  >
                    {row.mensajeestado}
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: 14,
                      padding: 0,
                      height: "50px",
                      cursor: "pointer",
                    }}
                    align="center"
                    onClick={() => handlePay(row)}
                  >
                    {row.estadocajaid === "1" ? (
                      <img
                        style={{ width: 55, height: 55 }}
                        src={logoMercadoPago}
                        alt="MercadoPago"
                      />
                    ) : (
                      <Check color="success" />
                    )}
                  </TableCell>
                  <TableCell
                    style={{ fontWeight: "bold", fontSize: "110%" }}
                    align="center"
                  >
                    {/* <a 
                            href={`${ip_server}/dompdf/generarcomprobante_pdf.php?tipocomprobante=124&codigocomprobante=${row.pedidoplacaid}&tipoaccionpdf=1`}
                            target="_blank"
                            style={{color:"black", fontSize:12}}
                        >  */}
                    <a
                      href={`${ip_server}/dompdf/generarcomprobante_pdf.php?tipocomprobante=124&codigocomprobante=${
                        row.pedidoplacaid
                      }&tipoaccionpdf=1&tiendaid=${localStorage.getItem(
                        "tiendausuario"
                      )}`}
                      target="_blank"
                      style={{ color: "black", fontSize: 12 }}
                    >
                      <Download />
                    </a>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={3}></TableCell>
                <TableCell>
                  <h5>No hay informaci&oacute;n para mostrar</h5>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <Pagination
        count={paginaUltima}
        page={page}
        onChange={handleChangePage}
        size="large"
        sx={{ alignSelf: "center" }}
      />
    </section>
  );
};
