import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { 
    Button, 
    Checkbox, 
    FormControl, 
    FormControlLabel, 
    InputAdornment, 
    InputLabel, 
    ListSubheader, 
    MenuItem, 
    Pagination, 
    Paper, 
    Select, 
    Snackbar, 
    TextField 
} from "@mui/material";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import MuiAlert from '@mui/material/Alert';
import { Album, Article, AttachFile, Check, Clear, Close, Edit, Search } from "@mui/icons-material";
import { 
    crearEncabezadoImportacionListaPrecios,  
    getListadoArchivosListaPrecios, 
    getListadoProveedorByName, 
    getProveedoresSinPaginado 
} from "../../../../redux/actions/mitienda.actions";
import { ModalSubirArchivo } from "./modalSubirArchivo";
import { ModalProcesarArchivo } from "./modalProcesarArchivo";
import { ModalEditarEncabezado } from "./modalEditarEncabezado";
import { ModalModelos } from "./modalModelos";
import { ClipLoader } from "react-spinners";

export const ArchivosImportarListaPrecios = () =>{

    const info = useSelector((state) => state.mitienda.listadoArchivosListaPrecio)
    const paginaUltima = useSelector((state) => state.mitienda.ultimaPaginalistadoArchivosListaPrecio)
    const proveedores = useSelector((state) => state.mitienda.proveedoresByName)
    const ok = useSelector((state) => state.mitienda.okCrearEncabezadoListaPrecio)
    const okEditarEncabezado = useSelector((state) => state.mitienda.okEditarEncabezadoListaPrecio)
    const okSubirArchivo = useSelector((state) => state.mitienda.okSubirArchivoListaPrecio)
    const okProcesarListado = useSelector((state) => state.mitienda.okProcesarListadoListaPrecio)

    const dispatch = useDispatch()

    const [input, setInput] = useState({
        titulo: '',
        descripcion: '',
        proveedorid: '',
        activo: '0'
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value })
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }
    
    const handleClick = () => {
        dispatch(crearEncabezadoImportacionListaPrecios(input))
        setTimeout(function(){
            handleClickAlert()
          }, 1000);
        setInput({
            titulo: '',
            descripcion: '',
            proveedorid: '',
            activo: '0'
        })
    }

    const [productoimportaid, setProductoImportaid] = useState('')

    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = (row) => {
        setProductoImportaid(row)
        setShow(true);
    }

    const [showEditar, setShowEditar] = useState(false);
    const handleCloseEditar = () => setShowEditar(false);
    const handleShowEditar = (row) => {
        setProductoImportaid(row)
        setShowEditar(true);
    }

    const [showProcesar, setShowProcesar] = useState(false);
    const handleCloseProcesar = () => setShowProcesar(false);
    const handleShowProcesar = (row) => {
        setProductoImportaid(row)
        setShowProcesar(true);
    }

    const [showModelos, setShowModelos] = useState(false);
    const handleCloseModelos = () => setShowModelos(false);
    const handleShowModelos = () => {
        setShowModelos(true);
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openSubirArchivo, setOpenSubirArchivo] = React.useState(false);
    const handleClickAlertSubirArchivo = () => {
      setOpenSubirArchivo(true);
    };
    const handleCloseAlertSubirArchivo = () => {
        setOpenSubirArchivo(false);
    };

    const [openProcesarArchivo, setOpenProcesarArchivo] = React.useState(false);
    const handleClickAlertProcesarArchivo = () => {
      setOpenProcesarArchivo(true);
    };
    const handleCloseAlertProcesarArchivo = () => {
        setOpenProcesarArchivo(false);
    };

    const [openEditar, setOpenEditar] = React.useState(false);
    const handleClickAlertEditar = () => {
      setOpenEditar(true);
    };
    const handleCloseAlertEditar = () => {
        setOpenEditar(false);
    };

    const formatoFecha = (f) =>{
        let fecha = f.split(' ')[0]
        fecha = fecha.split('-')[2]+'-'+fecha.split('-')[1]+'-'+fecha.split('-')[0]
        let hora = f.split(' ')[1].slice(0,5)
        
        return fecha+' '+hora
    }
    
    const [nombreAux, setNombreAux] = useState('')
    const [selectedOption, setSelectedOption] = useState('');

    useEffect(() =>{
        if(nombreAux.length > 4){
            dispatch(getListadoProveedorByName(nombreAux))
        }
    }, [nombreAux])

    useEffect(()=>{
        dispatch(getProveedoresSinPaginado())
    },[])

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        setLoading(true)
        dispatch(getListadoArchivosListaPrecios(page))
    },[page, ok, okSubirArchivo, okEditarEncabezado])

    return (
        <div className="container-default">

            <ModalSubirArchivo
                show={show}
                handleClose={handleClose}
                productoimportaid={productoimportaid.listaprecioid}
                handleClickAlert={handleClickAlertSubirArchivo}
            />

            <ModalProcesarArchivo
                show={showProcesar}
                handleClose={handleCloseProcesar}
                datos={productoimportaid}
                handleClickAlert={handleClickAlertProcesarArchivo}
            />

            <ModalEditarEncabezado
                show={showEditar}
                handleClose={handleCloseEditar}
                datos={productoimportaid}
                handleClickAlert={handleClickAlertEditar}
            />

            <ModalModelos
                show={showModelos}
                handleClose={handleCloseModelos}
            />

            <Snackbar
                open={open} 
                autoHideDuration={10000} onClose={handleCloseAlert}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlert} 
                    severity={ok.success ? "success" : "error"}
                    sx={{ width: 400 }}>
                    <h5>{ok.mensaje}</h5>
                </Alert>
            </Snackbar>

            <Snackbar
                open={openSubirArchivo} 
                autoHideDuration={10000} onClose={handleCloseAlertSubirArchivo}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertSubirArchivo} 
                    severity={okSubirArchivo.success ? "success" : "error"}
                    sx={{ width: 400 }}>
                    <h5>{okSubirArchivo.mensaje}</h5>
                </Alert>
            </Snackbar>

            <Snackbar
                open={openProcesarArchivo} 
                autoHideDuration={10000} onClose={handleCloseAlertProcesarArchivo}
                anchorOrigin={
                    {vertical: 'top',
                    horizontal: 'center',}
                }>
                <Alert onClose={handleCloseAlertProcesarArchivo} 
                    severity={okProcesarListado.success ? "success" : "error"}
                    sx={{ width: 600 }}>
                    <h5>{okProcesarListado.mensaje}</h5>
                </Alert>
            </Snackbar>

            {
                <Snackbar
                    open={openEditar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditar} 
                        severity={okEditarEncabezado.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEditarEncabezado.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <header className="header-default">
                <h3>Lista de precios Importaci&oacute;n</h3>
            </header>
            <Paper className="paper-form">
            <FormControl fullWidth style={{marginTop:5, marginBottom:10}} size="small">
                    <InputLabel id="search-select-label">Proveedor</InputLabel>
                        <Select
                            style={{height:40}}
                            MenuProps={{ autoFocus: false,  disablePortal: true }}
                            labelId="search-select-label"
                            id="search-select"
                            value={selectedOption.proveedorid || ''}
                            label="Proveedor"
                            onClose={() => {
                                dispatch(getListadoProveedorByName(""))
                            }}
                            size="small"
                            renderValue={() => selectedOption.nombre}
                        >
                    <ListSubheader>
                    <TextField
                        size="small"
                        autoFocus
                        placeholder="Buscar proveedor..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search/>
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreAux(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {proveedores.map((option, i) => (
                        <MenuItem key={i} value={option.clienteid} onClick={() => {
                            setSelectedOption(option)
                            setNombreAux(option.nombre)
                            setInput({
                                ...input,
                                proveedorid: option.proveedorid
                            })
                        }}>
                        {option.nombre}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>
            <TextField 
                label="T&iacute;tulo"  
                name="titulo"
                value={input.titulo}
                onChange={(e)=> handleChange(e)}
                fullWidth margin="normal" 
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <TextField 
                label="Descripci&oacute;n"  
                name="descripcion"
                value={input.descripcion}
                onChange={(e)=> handleChange(e)}
                fullWidth margin="normal" 
                multiline
                minRows={5}
                InputLabelProps={{
                    style: {
                        marginBottom:10,
                        marginTop: -7
                    },
                }}
                inputProps={{
                    style: {
                    height: 40,
                    fontSize:17
                    },
                }}
            />
            <FormControlLabel
                control={
                <Checkbox/>
                }
                style={{color:"black"}}
                onChange={(e)=> handleCheck(e)}
                checked={input.activo == "1" ? true : false}
                label="Activo"
                name="activo"
            />
            </Paper>
            <div align="right">
                <Button 
                    disabled={
                        input.porcentaje === ''
                    }
                    size="large" 
                    variant="contained" 
                    onClick={(e)=>(handleClick(e))}
                    sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>

            <div align="left">
                <Button 
                    variant="contained"
                    onClick={() => handleShowModelos()} 
                    style={{width:170, marginLeft:50}} 
                >Importar datos</Button>   
            </div>
   
            <TableContainer component={Paper} style={{width:"95%", alignSelf:"center", marginTop:40, marginLeft:40}}>
                <Table aria-label="simple table">
                <TableHead>
                    <TableRow>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            Fecha
                        </TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            T&iacute;tulo
                        </TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            Proveedor
                        </TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            Sucursal
                        </TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            Archivo
                        </TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            Activo
                        </TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            Adjuntar
                        </TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            Procesar
                        </TableCell>
                        <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">
                            Editar
                        </TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                {
                    loading ? 
                    <TableRow sx={{p:20}}>
                        <TableCell colSpan={9} align='center'>
                            <ClipLoader 
                                loading={loading}
                                size={50}
                            />
                        </TableCell>
                    </TableRow> :
                    info.length > 0 ? info.map((row) => (
                    <TableRow
                        key={row.listaprecioid}
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                    >
                        <TableCell style={{fontSize:"80%", padding:0, height:40, width:200}} align="center">
                            {formatoFecha(row.fechaingreso)}
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40, width:200}} align="center">
                            {row.titulo}
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                            {row.nomproveedor}
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                            {row.nomtienda}
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                            {row.url_archivo !== "" ? 
                            <a href={row.url_archivo} target="_blank" style={{color:"black"}}>
                                <Article/>
                            </a> : <Clear/>} 
                            
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                        {
                            row.activo === "1" ? <Check color="success"/> : <Close color="error"/>
                        }
                        </TableCell>
                        <TableCell 
                            onClick={() => handleShow(row)}    
                            style={{fontSize:"80%", padding:0, height:40, width:130, cursor:"pointer"}} align="center">
                            <AttachFile/>
                        </TableCell>
                        <TableCell 
                            onClick={() => handleShowProcesar(row)} 
                            style={{fontSize:"80%", padding:0, height:40, width:130, cursor:"pointer"}} align="center">
                            <Album/>
                        </TableCell>
                        <TableCell style={{fontSize:"80%", padding:0, height:40, width:130, cursor:"pointer"}} align="center">
                            <div onClick={()=>handleShowEditar(row)}><Edit/></div>
                        </TableCell>
                    </TableRow> 
                    )):
                    <TableRow>
                        <TableCell colSpan={4}></TableCell>
                        <TableCell colSpan={2}>
                            <h5>No hay informaci&oacute;n para mostrar</h5>
                        </TableCell>
                        <TableCell></TableCell>
                    </TableRow>}
                </TableBody>
                </Table>
            </TableContainer>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", mt:5}} />
        </div>
    )
}