import React, { useEffect } from "react"
import Modal from 'react-modal';
import { useDispatch, useSelector } from "react-redux";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { getPagosRealizados } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalVerPagos = ({show, handleClose, pedido}) =>{

    const pagosRealizados = useSelector((state) => state.mitienda.pagosRealizados)
    const registrarPago = useSelector((state) => state.mitienda.registrarPago)

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "850px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const formatoMonto = (monto) =>{
        if(!monto.includes(".")){
            return monto+".00"
        }else{
            return monto
        }
    }

    const formatoFecha = (fecha) =>{
        if(fecha){
            let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
        }else{
            return null
        }
    }

    useEffect(() => {
        if(pedido){
            dispatch(getPagosRealizados(pedido))
        }
    },[pedido, registrarPago])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Pagos realizados - Pedido #{pedido}</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <TableContainer sx={{width:"95%", alignSelf:"center", marginLeft:3}}>
                {
                    pagosRealizados.pagos.length === 0 ?
                    <h5 style={{display:"flex", justifyContent:"center"}}>No hay datos</h5> :
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Fecha</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Estado</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%",
                                width:200
                            }} align="center">Tipo pago</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Monto</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {pagosRealizados.pagos && pagosRealizados.pagos.map((row) => (
                        <TableRow
                            key={row.pagosid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                            <TableCell 
                                style={{fontSize:"16px", width:200}} align="center">
                                {formatoFecha(row.fechapago)}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"16px"}} align="center">
                                {row.status}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"16px"}} align="center">
                                {row.tipopago}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"16px"}} align="center">
                                ${formatoMonto(row.montoreal)}
                            </TableCell>
                        </TableRow>
                        ))}
                    </TableBody>
                </Table>
                }
            </TableContainer>
        </Modal>
    )
}