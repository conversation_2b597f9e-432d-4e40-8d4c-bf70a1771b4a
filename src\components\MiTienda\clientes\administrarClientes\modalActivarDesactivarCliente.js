import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { activarDesactivarCliente, desactivarCategoria, getDatosHome } from "../../../../redux/actions/mitienda.actions";
import { useDispatch } from "react-redux";

Modal.setAppElement("#root")

export const ModalActivarDesactivarCliente = ({show, handleClose, cliente,handleClickAlert}) =>{

    const dispatch = useDispatch()

    const handleOnClick = () => {
        if(cliente.activo == '1'){
            cliente.activo = '0'
        }else{
            cliente.activo = '1'
        }
        dispatch(activarDesactivarCliente(cliente))
        dispatch(getDatosHome())
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        handleClose()
    }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>{cliente.activo == '1' ? 'Desactivar' : 'Activar'} cliente</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>
                    {cliente.activo == '1' ? <h5>Esta seguro de desactivar el cliente?
                    </h5> : <h5>Esta seguro de activar el cliente?
                    </h5>}
                    
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="contained" 
                    onClick={handleOnClick}
                    sx={{mt: 3}}>{cliente.activo == '1' ? 'Desactivar' : 'Activar'}</Button>
                </div>
        </Modal>
    )
}