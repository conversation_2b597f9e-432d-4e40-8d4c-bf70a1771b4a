import React, { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux";

import Button from "@mui/material/Button";
import { Paper, Snackbar, Tooltip } from "@mui/material";
import AppsIcon from '@mui/icons-material/Apps';
import EditIcon from '@mui/icons-material/Edit';
import Pagination from '@mui/material/Pagination';

import { Check, Clear, Delete } from "@mui/icons-material";
import MuiAlert from '@mui/material/Alert';
import { EditarMoneda } from "./editarMoneda";
import { getMonedasPaginado, limpiarAgregarColorTienda, limpiarEditarColorTienda } from "../../../redux/actions/mitienda.actions";
import { ModalEliminarMoneda } from "./modalEliminarMoneda";
import { AgregarMoneda } from "./agregarMoneda";
import { ClipLoader } from "react-spinners";

export const Monedas = () =>{

    const info = useSelector((state) => state.mitienda.monedas_paginado)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaMonedasPaginado)
    const okAgregar = useSelector((state) => state.mitienda.okConfigMoneda)
    const okEliminar = useSelector((state) => state.mitienda.eliminarMoneda)
    const okEditar = useSelector((state) => state.mitienda.editarMoneda)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const [datos, setDatos] = useState('');

    const dispatch = useDispatch()

    const [show, setShow] = useState();
    const handleClose = () => {
        dispatch(limpiarAgregarColorTienda())
        setShow(false)
    };
    const handleShow = () => setShow(true);

    const [showEditar, setShowEditar] = useState();
    const handleCloseEditar = () => {
        setShowEditar(false);
        dispatch(limpiarEditarColorTienda())
    }
    const handleShowEditar = (e,a) => {
        setDatos(a)
        setShowEditar(true);
    }

    const [showEliminar, setShowEliminar] = useState();
    const handleCloseEliminar = () => setShowEliminar(false);
    const handleShowEliminar = (e,a) => {
        setDatos(a)
        setShowEliminar(true);
    };

    const [page, setPage] = useState(1);
    const handleChangePage = (e,value) => {
        e.preventDefault()
        setPage(value);
    };

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [openAgregar, setOpenAgregar] = React.useState(false);
    const handleClickAlertAgregar = () => {
      setOpenAgregar(true);
    };
    const handleCloseAlertAgregar = () => {
        setOpenAgregar(false);
    };

    const [openEditar, setOpenEditar] = React.useState(false);
    const handleClickAlertEditar = () => {
      setOpenEditar(true);
    };
    const handleCloseAlertEditar = () => {
        setOpenEditar(false);
        limpiarEditarColorTienda()
    };

    const [openEliminar, setOpenEliminar] = React.useState(false);
    const handleClickAlertEliminar = () => {
      setOpenEliminar(true);
    };
    const handleCloseAlertEliminar = () => {
        setOpenEliminar(false);
    };

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() =>{
        setLoading(true)
        dispatch(getMonedasPaginado(page))
    }, [page, okAgregar, okEliminar, okEditar])
    
    return (
        <section className="container-categorias">
            {
                <AgregarMoneda
                    show={show} 
                    handleClose={handleClose} 
                    handleClickAlert={handleClickAlertAgregar}
                />
            }
            {
                <EditarMoneda 
                    show={showEditar} 
                    handleClose={handleCloseEditar} 
                    datos={datos} 
                    handleClickAlert={handleClickAlertEditar}
                />
            }
            {
                <ModalEliminarMoneda
                    show={showEliminar} 
                    handleClose={handleCloseEliminar} 
                    datos={datos}
                    handleClickAlert={handleClickAlertEliminar}
                />
            }
            {
                <Snackbar
                    open={openAgregar} 
                    autoHideDuration={10000} onClose={handleCloseAlertAgregar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregar} 
                        severity={okAgregar.success === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okAgregar.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEditar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEditar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEditar} 
                        severity={okEditar.success === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEditar.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            {
                <Snackbar
                    open={openEliminar} 
                    autoHideDuration={10000} onClose={handleCloseAlertEliminar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertEliminar} 
                        severity={okEliminar.success === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{okEliminar.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <div  style={{marginBottom:30}}>
                <header className="titulo-categorias">
                    <h3>Monedas</h3>
                    <Button 
                        size="large" 
                        variant="contained"
                        className="btn-miplan"
                        onClick={handleShow}
                        disabled={permisos_acciones?.agregar === "0"}
                    >AGREGAR</Button>
                </header>
                <div className="text-categorias">
                    <h4>En esta secci&oacute;n podr&aacute;s agregar las monedas para luego
                        subir los productos en tu tienda.
                    </h4>
                </div>
            </div>
            {
                permisos_acciones?.listar !== "0" &&
                <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                    <ul className="lista">
                {
                    loading ? 
                    <div style={{display:"flex", justifyContent:"center"}}>
                        <ClipLoader 
                            loading={loading}
                            size={50}
                        />
                    </div> :
                    info.length > 0 ? info.map ((a) => 
                        <li key={a.monedaid} style={{marginTop:"20px"}}>
                            <Paper className="paper">
                                <div style={{display:"flex"}}>
                                    <div className="paper-primer-div"><AppsIcon/></div>
                                    <div className="paper-primer-div">{a.nombre} - {a.simbolo}</div>
                                </div>
                                <div className="icons">
                                    <Tooltip placement="top" title={a.activo === "1" ? "Activada" : "Desactivada"} className="icon">
                                    {
                                        a.activo === "1" ? <Check color="success"/> : <Clear color="error"/>
                                    }
                                    </Tooltip>
                                    <div className="icon">
                                        <Tooltip title="Editar">
                                            <Button 
                                            disabled={permisos_acciones?.modificar === "0"}
                                            onClick={(e) => handleShowEditar(e, a)} style={{color:"black"}}>
                                                <EditIcon style={{color:"black"}}/>
                                            </Button>
                                        </Tooltip>
                                    </div>
                                </div>
                            </Paper>
                        </li>
                    ):
                    <div style={{display:"flex", justifyContent:"center"}}>
                        <h3>No hay informaci&oacute;n para mostrar</h3>
                    </div>
                }
                </ul>
                <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
                </div>
            }
        </section>
    )
}