import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, FormControl, InputLabel, MenuItem, Select, TextField,} from "@mui/material";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { useDispatch, useSelector } from "react-redux";
import { 
    getChequesBanco, 
    getCuentasBanco, 
    getMetodosPago, 
    getMonedas, 
    getPagosRealizadosFacturaProveedor, 
    getTarjetas, 
    getTiposChequesBanco, 
    getTiposTarjeta, 
    registrarPagoFacturaProveedor 
} from "../../../redux/actions/mitienda.actions";
import { useEffect } from "react";

Modal.setAppElement("#root")

export const ModalRegistrarPago = ({show, handleClose, factura}) =>{

    const pagosRealizados = useSelector((state) => state.mitienda.pagosRealizadosFacturaProveedor)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimapagosRealizadosFacturaProveedor)
    const metodosDePago = useSelector((state) => state.mitienda.infoMetodosDepago)
    const tarjetas = useSelector((state) => state.mitienda.tarjetas)
    const tiposTarjetas = useSelector((state) => state.mitienda.tiposDeTarjetas)
    const cuentasBanco= useSelector((state) => state.mitienda.cuentasBanco)
    const cheques = useSelector((state) => state.mitienda.chequesBanco)
    const tiposChequesBanco = useSelector((state) => state.mitienda.tiposChequesBanco)
    const monedas = useSelector((state) => state.mitienda.monedas)
    const okRegistrarPagoFacturaProveedor = useSelector((state) => state.mitienda.okRegistrarPagoFacturaProveedor)    

    const dispatch = useDispatch()

    const [input, setInput] = useState({
        fechapago: new Date().toJSON().slice(0,10),
        tipopagoid: "",
        monedaid: "",
        montoabono: "",
        numerorecibo: "",
        descripcion: "",
        cuentabancoid: "",
        nrocheque: "",
        fechaemisioncheque: new Date().toJSON().slice(0,10),
        fechapagocheque: new Date().toJSON().slice(0,10),
        chequerecibidoid: "",
        proveedorid: "",
        tipocheque: "",
        tarjeta: "",
        tipoTarjeta: "",
        nroTarjeta:"",
        cuotas: "",
        interes: "",
    })

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          height: "95vh",
          borderRadius: "8px",
          maxWidth: "850px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(registrarPagoFacturaProveedor(factura.facturacompraid,input))
        setInput({
            fechapago: new Date().toJSON().slice(0,10),
            tipopagoid: "",
            monedaid: "",
            montoabono: "",
            numerorecibo: "",
            descripcion: "",
            cuentabancoid: "",
            nrocheque: "",
            fechaemisioncheque: new Date().toJSON().slice(0,10),
            fechapagocheque: new Date().toJSON().slice(0,10),
            chequerecibidoid: "",
            proveedorid: "",
            tipocheque: "",
            tarjeta: "",
            tipoTarjeta: "",
            nroTarjeta:"",
            cuotas: "",
            interes: ""
        })
    }

    const handleChange = (e) => {
        e.preventDefault()
        if(e.target.name === "tipopagoid" || e.target.name === "tipoTarjeta"){
            setInput({
                ...input,
                [e.target.name]: e.target.value.toString()
            })
        }
        else{
            setInput({
                ...input,
                [e.target.name]: e.target.value
            })
        }
    }

    const formatoFecha = (fecha) =>{
        if(fecha){
            let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
        }else{
            return null
        }
    }

    useEffect(() =>{
        if(factura !== ''){
            dispatch(getPagosRealizadosFacturaProveedor(page,factura.facturacompraid))
        }
    },[factura,okRegistrarPagoFacturaProveedor])

    useEffect(() =>{
        dispatch(getMetodosPago())
        dispatch(getMonedas())
        dispatch(getCuentasBanco())
        dispatch(getTiposChequesBanco())
        dispatch(getChequesBanco())
        dispatch(getTarjetas())
        dispatch(getTiposTarjeta())
    },[])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Orden de Comrpa Nro #{factura.facturacompraid}: Total ${factura.total}</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>   
                <TextField
                    fullWidth
                    margin="normal"
                    label="Fecha pago"  
                    variant="outlined"  
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="fecha"
                    value={input.fechapago}
                    type="date"
                  />

                    <FormControl fullWidth
                        margin="normal">
                        <InputLabel id="tipodepago-select-label">Tipo de pago</InputLabel>
                        <Select
                        style={{height:40}}
                        labelId="tipodepago-select-label"
                        label="Tipo de pago"
                        size="small"
                        name="tipopagoid"
                        value={input.tipopagoid || ''}
                        onChange={handleChange}
                        MenuProps={{ keepMounted: true, disablePortal: true }}
                        >
                        <MenuItem value="">Seleccione una opcion</MenuItem>
                        {               
                            metodosDePago && metodosDePago.map((t,index) =>
                                <MenuItem value={index+1} key={index+1}>{t.nombre}</MenuItem>
                            )
                        }
                        </Select>
                    </FormControl>
                    {
                        input.tipopagoid === "2" &&
                        <div>

                        <FormControl fullWidth
                            margin="normal">
                            <InputLabel id="cuentabanco-select-label">Cuenta Banco</InputLabel>
                            <Select
                            style={{height:40}}
                            labelId="cuentabanco-select-label"
                            label="Tipo de pago"
                            size="small"
                            name="cuentabancoid"
                            value={input.cuentabancoid || ''}
                            onChange={handleChange}
                            MenuProps={{ keepMounted: true, disablePortal: true }}
                            >
                            <MenuItem value="">Seleccione una opcion</MenuItem>
                            {               
                                cuentasBanco && cuentasBanco.map((t) =>
                                    <MenuItem value={t.cuentabancoid} key={t.cuentabancoid}>{t.cuentabanco}</MenuItem>
                                )
                            }
                            </Select>
                        </FormControl>
                        <TextField
                            fullWidth
                            margin="normal"
                            label="Nro transferencia"  
                            variant="outlined"  
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                            name="nrotransferencia"
                            value={input.nrotransferencia}
                            onChange={handleChange}
                        />

                        </div>
                    }

                    {
                        input.tipopagoid === "3" &&
                        <FormControl fullWidth
                            margin="normal">
                            <InputLabel id="tipodecheque-select-label">Tipo Cheque</InputLabel>
                            <Select
                            style={{height:40}}
                            labelId="tipodecheque-select-label"
                            label="Tipo Cheque"
                            size="small"
                            name="tipocheque"
                            value={input.tipocheque || ''}
                            onChange={handleChange}
                            MenuProps={{ keepMounted: true, disablePortal: true }}
                            >
                            <MenuItem value="">Seleccione una opcion</MenuItem>
                            {               
                                tiposChequesBanco && tiposChequesBanco.map((t) =>
                                    <MenuItem value={t.tipochequeid} key={t.tipochequeid}>{t.tipocheque}</MenuItem>
                                )
                            }
                            </Select>
                        </FormControl>

                    }

                    {
                        input.tipopagoid === "3" && input.tipocheque === "1" ?
                        <div>

                            <FormControl fullWidth
                                margin="normal">
                                <InputLabel id="cuentabanco-select-label">Cuenta Banco</InputLabel>
                                <Select
                                style={{height:40}}
                                labelId="cuentabanco-select-label"
                                label="Tipo de pago"
                                size="small"
                                name="cuentabancoid"
                                value={input.cuentabancoid || ''}
                                onChange={handleChange}
                                MenuProps={{ keepMounted: true, disablePortal: true }}
                                >
                                <MenuItem value="">Seleccione una opcion</MenuItem>
                                {               
                                    cuentasBanco && cuentasBanco.map((t) =>
                                        <MenuItem value={t.cuentabancoid} key={t.cuentabancoid}>{t.cuentabanco}</MenuItem>
                                    )
                                }
                                </Select>
                            </FormControl>

                            <TextField
                                fullWidth
                                margin="normal"
                                label="Nro cheque"  
                                variant="outlined"  
                                InputLabelProps={{
                                    style: {
                                        marginBottom:10,
                                        marginTop: -7
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 40,
                                        fontSize:17
                                    },
                                }}
                                name="nrocheque"
                                value={input.nrocheque}
                                onChange={handleChange}
                            />

                            <TextField
                                fullWidth
                                margin="normal"
                                label="Fecha de emisi&oacute;n"  
                                variant="outlined"  
                                InputLabelProps={{
                                    style: {
                                        marginBottom:10,
                                        marginTop: -7
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 40,
                                        fontSize:17
                                    },
                                }}
                                type="date"
                                name="fechaemisioncheque"
                                value={input.fechaemisioncheque}
                                onChange={handleChange}
                            />

                            <TextField
                                fullWidth
                                margin="normal"
                                label="Fecha de pago"  
                                variant="outlined"  
                                InputLabelProps={{
                                    style: {
                                        marginBottom:10,
                                        marginTop: -7
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 40,
                                        fontSize:17
                                    },
                                }}
                                type="date"
                                name="fechaemisioncheque"
                                value={input.fechapagocheque}
                                onChange={handleChange}
                            />

                        </div>
                        : null

                    }

                    {
                        input.tipopagoid === "3" && input.tipocheque === "2" ?
                        <div>

                            <FormControl fullWidth
                                margin="normal">
                                <InputLabel id="cheque-select-label">Cheque</InputLabel>
                                <Select
                                style={{height:40}}
                                labelId="cheque-select-label"
                                label="Tipo de pago"
                                size="small"
                                name="cuentabancoid"
                                value={input.chequerecibidoid || ''}
                                onChange={handleChange}
                                MenuProps={{ keepMounted: true, disablePortal: true }}
                                >
                                <MenuItem value="">Seleccione una opcion</MenuItem>
                                {               
                                    cheques && cheques.map((t) =>
                                        <MenuItem value={t.cuentabancoid} key={t.cuentabancoid}>{t.cuentabanco}</MenuItem>
                                    )
                                }
                                </Select>
                            </FormControl>
                            
                            <TextField
                                fullWidth
                                margin="normal"
                                label="Fecha de pago"  
                                variant="outlined"  
                                InputLabelProps={{
                                    style: {
                                        marginBottom:10,
                                        marginTop: -7
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 40,
                                        fontSize:17
                                    },
                                }}
                                type="date"
                                name="fechaemisioncheque"
                                value={input.fechapagocheque}
                                onChange={handleChange}
                            />
                        </div>
                        : null

                    }

                    {
                        input.tipopagoid === "4"  ?
                        <div>

                            <FormControl fullWidth
                                margin="normal">
                                <InputLabel id="tarjeta-select-label">Tarjeta</InputLabel>
                                <Select
                                style={{height:40}}
                                labelId="tarjeta-select-label"
                                label="Tarjeta"
                                size="small"
                                name="tarjeta"
                                value={input.tarjeta || ''}
                                onChange={handleChange}
                                MenuProps={{ keepMounted: true, disablePortal: true }}
                                >
                                <MenuItem value="">Seleccione una opcion</MenuItem>
                                {               
                                    tarjetas && tarjetas.map((t) =>
                                        <MenuItem value={t.tipotarjetaproveedorid} key={t.tipotarjetaproveedorid}>{t.nombre}</MenuItem>
                                    )
                                }
                                </Select>
                            </FormControl>

                            <FormControl fullWidth
                                margin="normal">
                                <InputLabel id="tipoTarjeta-select-label">Tipo tarjeta</InputLabel>
                                <Select
                                style={{height:40}}
                                labelId="tipoTarjeta-select-label"
                                label="Tipo tarjeta"
                                size="small"
                                name="tipoTarjeta"
                                value={input.tipoTarjeta || ''}
                                onChange={handleChange}
                                MenuProps={{ keepMounted: true, disablePortal: true }}
                                >
                                <MenuItem value="">Seleccione una opcion</MenuItem>
                                {               
                                    tiposTarjetas && tiposTarjetas.map((t,index) =>
                                        <MenuItem value={index+1} key={index+1}>{t.nombre}</MenuItem>
                                    )
                                }
                                </Select>
                            </FormControl>

                            {
                                input.tipoTarjeta === "1" &&

                                <div>

                                    <TextField
                                        fullWidth
                                        margin="normal"
                                        label="Cuotas"  
                                        variant="outlined"  
                                        InputLabelProps={{
                                            style: {
                                                marginBottom:10,
                                                marginTop: -7
                                            },
                                        }}
                                        inputProps={{
                                            style: {
                                                height: 40,
                                                fontSize:17
                                            },
                                        }}
                                        name="cuotas"
                                        value={input.cuotas}
                                        onChange={handleChange}
                                    />

                                    <TextField
                                        fullWidth
                                        margin="normal"
                                        label="Inter&eacute;s"  
                                        variant="outlined"  
                                        InputLabelProps={{
                                            style: {
                                                marginBottom:10,
                                                marginTop: -7
                                            },
                                        }}
                                        inputProps={{
                                            style: {
                                                height: 40,
                                                fontSize:17
                                            },
                                        }}
                                        name="interes"
                                        value={input.interes}
                                        onChange={handleChange}
                                    />

                                </div>
                            }
                            
                            <TextField
                                fullWidth
                                margin="normal"
                                label="Nro tarjeta"  
                                variant="outlined"  
                                InputLabelProps={{
                                    style: {
                                        marginBottom:10,
                                        marginTop: -7
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 40,
                                        fontSize:17
                                    },
                                }}
                                name="nroTarjeta"
                                value={input.nroTarjeta}
                                onChange={handleChange}
                            />
                        </div>
                        : null

                        }

                    <FormControl                         
                        fullWidth
                        margin="normal">
                        <InputLabel id="moneda-select-label">Moneda</InputLabel>
                        <Select
                        style={{height:40}}
                        labelId="moneda-select-label"
                        label="Moneda"
                        size="small"
                        name="monedaid"
                        value={input.monedaid || ''}
                        onChange={handleChange}
                        MenuProps={{ keepMounted: true, disablePortal: true }}
                        >
                        <MenuItem value="">Seleccione una opcion</MenuItem>
                        {               
                            monedas && monedas.map((t) =>
                                <MenuItem value={t.monedaid} key={t.monedaid}>{t.nombre}</MenuItem>
                            )
                        }
                        </Select>
                    </FormControl>
                    <TextField
                        fullWidth
                        margin="normal"
                        label="Monto de pago"  
                        variant="outlined"  
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        name="montoabono"
                        value={input.montoabono}
                        onChange={handleChange}
                  />
                <TextField
                    fullWidth
                    margin="normal"
                    label="Numero de recibo"  
                    variant="outlined"  
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="numerorecibo"
                    value={input.numerorecibo}
                    onChange={handleChange}
                  />
                <TextField
                    fullWidth
                    margin="normal"
                    label="Descripcion"  
                    variant="outlined"  
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    name="descripcion"
                    value={input.descripcion}
                    onChange={handleChange}
                    multiline
                    minRows={2}
                  />
                <div align="right">
                    <Button 
                    size="large"
                    variant="contained"
                    onClick={handleOnClick}
                    sx={{mt: 3, mr:1}}>Registrar</Button>
                </div>
                </div>
                <TableContainer sx={{width:"95%", alignSelf:"center", marginLeft:3}}>
                {
                    pagosRealizados.length === 0 ?
                    <h5 style={{display:"flex", justifyContent:"center"}}>No hay datos</h5> :
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Fecha</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Nro recibo</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%",
                                width:200
                            }} align="center">Tipo pago</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Monto</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Saldo</TableCell>
                            <TableCell style={{fontWeight:"bold", 
                                fontSize:"110%"
                            }} align="center">Inter&eacute;s</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {pagosRealizados && pagosRealizados.map((row) => (
                        <TableRow
                            key={row.pagosfacturacompraid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        >
                            <TableCell 
                                style={{fontSize:"14px", width:200}} align="center">
                                {formatoFecha(row.fechapago)}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                {row.numerorecibo}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                {row.tipopago}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                ${row.saldo}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                ${row.montoabono}
                            </TableCell>
                            <TableCell 
                                style={{fontSize:"14px"}} align="center">
                                ${row.porcentajeinterestarjeta === null ? "0" : row.porcentajeinterestarjeta }
                            </TableCell>
                        </TableRow>
                        ))}
                    </TableBody>
                </Table>
                }
            </TableContainer>
        </Modal>
    )
}