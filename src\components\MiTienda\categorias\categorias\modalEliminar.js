import React from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button } from "@mui/material";
import { desactivarCategoria } from "../../../../redux/actions/mitienda.actions";
import { useDispatch } from "react-redux";

Modal.setAppElement("#root")

export const ModalEliminarCategoria = ({show, handleClose, categoria}) =>{

    const dispatch = useDispatch()

    const handleOnClick = () => {
        if(categoria.activo == '1'){
            categoria.activo = '0'
        }else{
            categoria.activo = '1'
        }
        dispatch(desactivarCategoria(categoria))
        handleClose()
    }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>{categoria.activo == '1' ? 'Desactivar' : 'Activar'} categoria</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>
                    {categoria.activo == '1' ? <h5>Al desactivar la categoria dejara de ser visible en la tienda hasta volver a activarla. 
                        No se eliminara ni borraran las configuraciones realizadas
                    </h5> : <h5>Al activar la categoria volvera a ser visible en la tienda hasta volver a desactivarla. 
                    </h5>}
                    
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="contained" 
                    onClick={handleOnClick}
                    sx={{mt: 3}}>{categoria.activo == '1' ? 'Desactivar' : 'Activar'}</Button>
                </div>
        </Modal>
    )
}