{"login": {"user": "Usuario", "password": "Clave", "forgot-password": "Olvid<PERSON>e tu clave?", "log-in": "INGRESAR", "welcometo": "Bienvenido a", "recoveryPassword": "Recuperar clave", "backToLogin": "Volver a iniciar sesion", "enterYourEmail": "Ingrese su email", "sendEmail": "Enviar email"}, "menu": {"log-out": "<PERSON><PERSON><PERSON> se<PERSON>"}, "table": {"firstPage": "Primera pagina", "lastPage": "Ultima pagina", "next": "Siguient<PERSON>", "previous": "Anterior", "rows": "<PERSON><PERSON>", "filterBy": "Filtrar por", "search": "Buscar", "loadedDataReport": "Informe de Datos Cargados"}, "abm-menu": {"name": "Nombre", "order": "Orden", "language": "Lenguaje", "typeOfMenu": "<PERSON><PERSON><PERSON>", "address": "Direccion", "active": "Activo", "addRecord": "Agregar registro", "selectAnOption": "Seleccione una opcion", "yes": "Si", "no": "No", "modify": "Modificar", "actions": "Acciones", "nameOfSubmenu": "Nombre del Submenu", "file": "Archivo", "delete": "Eliminar", "loadedDataReport": "Informe de Datos Cargados"}, "submenu": {"nameOfSubmenu": "Nombre del submenu", "active": "Activo", "order": "Orden", "file": "Archivo", "permissions": "<PERSON><PERSON><PERSON>", "modify": "Modificar", "actions": "Acciones", "addRecord": "Agregar registro", "delete": "Eliminar", "loadedDataReport": "Informe de Datos Cargados"}, "parameter": {"parameters": "Parametros", "add": "Agregar", "search": "Buscar", "branchOffice": "Sucursal", "orderShowsIva": "Pedido muestra iva", "billShowsIva": "Factura muestra iva", "orderShowsPayment": "Pedido muestra pago", "billShowsPayment": "Factura muestra pago", "productsPriceWithIva": "Precio del producto con iva", "budgetShowsIva": "Presupuesto muestra iva", "orderWithDiscount": "Pedido con descuento", "budgetShowsPayment": "Presupuesto muestra pago", "delete": "Delete", "loadedDataReport": "Informe de Datos Cargados", "branchOffices": "<PERSON><PERSON><PERSON><PERSON>", "showIvaOnOrders": "Mostrar IVA en Pedidos", "showIvaOnBills": "Mostrar IVA en Facturas", "showPaymentsOnPrintOrders": "Mostrar Pagos en Impresion de Pedidos", "showPaymentsOnPrintBills": "Mostrar Pagos en Impresion de Facturas", "budgetShowsIVA": "Presupuesto muestra IVA", "orderShowsDiscount": "Pedido Muestra Descuento", "budgetShowsDiscount": "Presupuesto Muestra Descuento", "budgetShowsPaymentsOnPrint": "Presupuesto Muestra Pagos en Impresion", "orderShowsCUITonPrint": "Pedido Muestra CUIT en Impresiont", "budgetShowsCUITonPrint": "Presupuesto Muestra CUIT en Impresion", "orderShowsExpiryDate": "Pedido Muestra fecha de Vto", "showBarcode": "Mostrar Barcode", "assignASellerToTheCustomer": "<PERSON><PERSON><PERSON> a Cliente", "showPriceWithoutIVAonOrders": "Mostrar Precio SIN IVA en Pedidos", "showPriceWithoutIVAonBills": "Mostrar Precio SIN IVA en Facturas", "showPriceWithoutIVAonBudgets": "Mostrar Precio SIN IVA en Presupuestos", "showsIVAonBills": "Mostrar IVA en Facturas", "multi-branch": "Multi sucursal", "optional": "Opcionales", "parametersForm": "Formulario de parametros", "manualDataSynchronization": "Sincronizacion manual de datos", "syncUp": "Sincronizar", "withoutSlashAtTheEnd": "sin barra al final", "retailListType": "Tipo de lista minorista", "wholesaleListType": "Tipo de lista mayorista (opcional)", "integrateOnlyProductsWithStock": "Integrar solo productos con stock", "integrateImages": "Integrar imagenes", "GFuserToRunIntegrations": "Usuario Gestion Factura para correr integracion", "GFpasswordToRunIntegrations": "Clave Gestion Factura para correr integracion", "lastIntegration": "Ultima integracion", "productsToIntegrate": "Productos a integrar", "integratedProducts": "Productos integrados", "productsWithErrors": "Productos con error", "updatedProducts": "Productos actualizados", "productsCreated": "Productos creados", "productsRemoved": "Productos eliminados", "result": "<PERSON><PERSON><PERSON><PERSON>", "technicalRequirements": "Requerimientos Técnicos", "showEcommerce": "Mostrar Ecommerce", "ecommerceImage": "Ecommerce imagen", "ecommerceInformation": "Ecommerce informacion", "informationToDisplayInOptionList": "Información para mostrar en lista de opciones", "showTakeAway": "Mostrar Retiro en el Local", "showMP": "Mostrar MercadoPago", "showCashPayment": "Mostrar Pago Efectivo", "ecommerceLogo": "Logo Ecommerce", "showEcommerceIndex": "Mostrar Index Ecommerce", "defaultRounding": "<PERSON><PERSON><PERSON>", "defaultDecimals": "<PERSON><PERSON><PERSON>", "priceList": "Lista de Precios", "showCategories": "Mostrar Categorias", "showBestSeller": "Mostrar Más Vendido", "showProductWithStock": "Mostrar Producto Con Stock", "number": "Número", "emailAddress": "<PERSON><PERSON>", "emailInformation": "Email Información", "catalogConfiguration": "Configuracion de catalogo", "showCatalog": "Mostrar Catálogo"}, "profiles": {"securityProfiles": "<PERSON><PERSON><PERSON> de Seguridad", "profileName": "Nombre del perfil", "discount": "Descuento", "addRecord": "Agregar registro", "listOfSecurityProfiles": "Listado de perfiles de Seguridad", "profile": "Perfil", "permissions": "<PERSON><PERSON><PERSON>", "modify": "Modificar", "delete": "Eliminar"}, "companies": {"companyShopInfo": "Datos de su Empresa o Comercio", "name": "Nombre", "taxIdentificationNumber": "Clave Unica de Identificacion Tributaria", "businessName": "Razon social", "salePointId": "Nro punto de venta de facturacion", "typeOfTaxpayer": "Tipo de contribuyente", "startDate": "Inicio de actividad", "businessAddress": "<PERSON><PERSON><PERSON>", "taxText": "Texto fiscal", "webAddress": "Direccion web", "logoImage": "Imagen logo", "isServer": "<PERSON><PERSON> servidor", "serverIP": "IP servidor", "image": "Imagen", "modify": "Modificar", "delete": "Eliminar", "add": "Agregar", "admissionDate": "<PERSON><PERSON>"}, "branchOffices": {"address": "Direccion", "branchOffices": "<PERSON><PERSON><PERSON><PERSON>", "country": "<PERSON><PERSON>", "company": "Empresa", "province": "Provincia", "name": "Nombre", "phone": "Telefono", "salePoint": "Punto de venta", "active": "Activo", "parameters": "Parametros", "modify": "Modificar", "delete": "Eliminar", "fax": "Fax", "email": "Email", "mpPublishableKey": "Mercado Pago Publishable Key", "mpAccessToken": "Mercado Pago Access Token", "publicEncryptionKey": "Gestion Factura Clave Publica Encriptacion (formato Jwk)", "privateEncryptionKey": "Gestion Factura Clave Privada Encriptacion (formato Jwk)", "generatePairOfPublicAndPrivateKeysForEncryption": "Generar Par de Claves publica y privada para encriptacion de Gestion Factura.", "importantTakeThisActionIfYouBelieveThePrivateKeyHasBeenCompromised": "Importante: Realizar esta accion si cree que la clave privada fue comprometida.", "generateEncryptionKeys": "Generar claves encriptacion", "publicKeySigning": "Gestion Factura Clave Publica Firma (formato Jwk)", "privateKeySigning": "Gestion Factura Clave Privada Firma (formato Jwk)", "generatePairOfPublicAndPrivateKeysForSigning": "Generar Par de Claves publica y privada para firma de Gestion Factura.", "generateSigningKeys": "Generar claves firma", "allDiscounts": "Todos los descuentos ", "isTheMotherStore": "Es Tienda Madre", "AFIPDefaultData": "Datos de Default AFIP", "defaultUseFiscalPrinter": "Default Usar Impresora Fiscal", "salePointId": "Nro Punto de Venta AFIP", "salePointDefaultData": "Datos de Default Punto de Venta", "defaultClient": "Default cliente", "defaultPrinterType": "De<PERSON>ult <PERSON><PERSON> Impresora", "defaultCurrency": "<PERSON><PERSON><PERSON>", "defaultPrinter": "<PERSON><PERSON>ult I<PERSON>ora", "defaultBarCodeReader": "De<PERSON><PERSON> de Codigo Barr<PERSON>", "defaultShowRequests": "Default <PERSON>rar Pedidos", "defaultShowPayments": "Default <PERSON>", "defaultShowRemito": "Default <PERSON>", "defaultShowCreditNote": "De<PERSON>ult <PERSON>rar Nota de Crédito", "defaultShowBudget": "Default <PERSON>", "defaultShowTiempoDeVta": "Default Mostrar Tiempo de Vta", "turneroDefaultData": "Datos de <PERSON>fault <PERSON>", "defaultMechanicTurnero": "Default <PERSON>án<PERSON>", "defaultDevice": "De<PERSON>ult Dispositivo", "add": "Add"}, "clients": {"clients": "Clientes", "add": "Agregar", "search": "Buscar", "idNumber": "Nro de documento", "showClientsWithoutPurchases": "Mostrar clientes sin compras", "name": "Nombre", "exportToExcel": "Exportar a excel", "print": "Imprimir", "idType": "Tipo de documento", "cellphone": "<PERSON><PERSON><PERSON>", "phone": "Telefono", "IVAcondition": "Condicion IVA", "paymentCondition": "Condicion pago", "discount": "Descuentos", "billingEmail": "Email facturacion", "active": "Activo", "recoverPassword": "Recuperar clave", "seller": "<PERSON><PERSON><PERSON>", "addresses": "Direcciones", "modify": "Modificar", "delete": "Eliminar", "branchOffices": "<PERSON><PERSON><PERSON><PERSON>", "assignSeller": "<PERSON><PERSON><PERSON> vendedor", "observations": "Observaciones", "hasPayments": "Tiene abonos"}}