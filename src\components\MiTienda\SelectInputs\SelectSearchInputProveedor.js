import React from "react"
import './style.css'

export const SelectSearchInputProveedor = ({
    info,
    nombreProveedor,
    setNombreProveedor,
    setProveedorID,
    open, 
    setOpen, 
    backgroundColor
}) =>{

    return (
        <div className={`${backgroundColor === "white" ? "input-grup-proveedor-white" : "input-grup-proveedor"}`}>
            <h6>Proveedor</h6>
            <input onClick={(e)=> {
                setOpen(true)
            }} value={nombreProveedor} onChange={(e)=> setNombreProveedor(e.target.value)}/>
            <div className={`${backgroundColor === "white" ? "list-info-proveedor-white" : "list-info-proveedor" }`} > 
                {
                    info.length > 0 && open ? 
                    info.map((i) =>
                        <div className="menu-items-proveedor" onClick={(e)=> {
                            setOpen(false)
                            setProveedorID(i.proveedorid)
                            setNombreProveedor(i.nombre)
                        }}>
                            {i.nombre}
                        </div>
                    ) : null
                }
            </div>  
        </div>
    )
}