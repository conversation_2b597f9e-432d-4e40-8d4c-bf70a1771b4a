const client = {
  clientid: Number,
  locationid: Number,
  firstname: String,
  lastname: String,
  phone: String,
  email: String,
  residence: String,
  purposeexperience: String,
  gender: String,
  isolder: Number,
  acceptterms: Number,
  acceptimage: Number,
  createdate: Date,
  synced: Number,
  source: Number,
  hubspotcontactid: Number,
}

router.route('/clients').post(clientsCtrl.listClients)

router.route('/clients/add').post(clientsCtrl.createClient)

router
  .route('/clients/:id')
  .delete(clientsCtrl.deleteClient)
  .put(clientsCtrl.editClient)
  .get(clientsCtrl.getClient)

router.route('/clients/email/:email').get(clientsCtrl.getClientByEmail)

///////////////////////////HEADSET

const headset = {
  name: String,
  headid: Number,
  headsettypeid: Number,
  headsetname: String,
  ipadress: String,
  macaddress: String,
  serialnumber: String,
  datein: Date,
  locationid: Number,
  wifiid: Number,
}

router
  .route('/headsetsconnected')
  .get(headsetsConnectedCtrl.listHeadsets)
  .post(headsetsConnectedCtrl.saveHeadsets)

router
  .route('/headsets')
  .get(headsetsCtrl.listHeadsets)
  .post(headsetsCtrl.saveHeadset)

router
  .route('/headsets/:id')
  .delete(headsetsCtrl.deleteHeadset)
  .put(headsetsCtrl.editHeadset)
  .get(headsetsCtrl.getHeadsetById)

router
  .route('/headsets/serial/:serial')
  .get(headsetsCtrl.getHeadsetBySerial)

router
  .route('/headsets/ip/:ip')
  .get(headsetsCtrl.getHeadsetByIp)

///////////////////////////LOCATIONS

  Locations
router.route("/locations")
.get(locationCtrl.listLocations)
router.route("/locations/add")
.post(locationCtrl.createLocation)
router.route("/locations/:id")
.delete(locationCtrl.deleteLocation)
.put(locationCtrl.editLocation)
.get(locationCtrl.getLocation)


///////////////////////////PROJECTS

router.route("/projects")
.get(projectCtrl.listProjects)
router.route("/projects/add")
.post(projectCtrl.createProject)
router.route("/projects/:id")
.delete(projectCtrl.deleteProject)
.put(projectCtrl.editProject)
.get(projectCtrl.getProject)

//////////////////////////////////////////////////////

router.route("/wifis")
.get(arenaCtrl.listArenas)
router.route("/wifis/add")
.post(wifiCtrl.createWifi)
router.route("/wifis/:id")
.delete(wifiCtrl.deleteWifi)
.put(wifiCtrl.editWifi)
.get(wifiCtrl.getWifi)

///////////////////////////ARENA

router.route("/arenas")
.get(arenaCtrl.listArenas)
router.route("/arenas/add")
.post(arenaCtrl.createArena)
router.route("/arenas/:id")
.delete(arenaCtrl.deleteArena)
.put(arenaCtrl.editArena)
.get(arenaCtrl.getArena)