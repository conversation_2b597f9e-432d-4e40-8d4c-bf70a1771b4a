import React from "react";
import Modal from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";

Modal.setAppElement("#root");

export const ModalFiltrosReporteEntregas = ({
    show, 
    handleClose, 
    handleChangeFechaDesde,
    fechaDesde,
    handleChangeFechaHasta,
    fechaHasta,
    search,
    reset
}) => {
    const customStyles = {
        content: {
            padding: "40px",
            inset: "unset",
            width: "100%",
            height: "60vh",
            borderRadius: "8px",
            maxWidth: "650px",
            right: "50px",
        },
        overlay: {
            backgroundColor: "rgba(0,0,0,0.5)",
            display: "grid",
            placeItems: "center",
            zIndex: "100000",
        },
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        search();
    };

    return (
        show && (
            <Modal 
                isOpen={show} 
                style={customStyles} 
                onRequestClose={handleClose}
            >
                <form onSubmit={handleSubmit}>
                    <div style={{
                        display: "flex",
                        justifyContent: "space-between",
                        marginBottom: 10,
                    }}>
                        <h4>FILTRAR</h4>
                        <button 
                            type="button"
                            onClick={handleClose} 
                            style={{
                                all: "unset",
                                color: "black",
                                paddingBottom: 10,
                                cursor: "pointer",
                            }}
                        >
                            X
                        </button>
                    </div>
                    
                    <Divider />
                    
                    <div style={{ display: "flex", flexDirection: "column" }}>
                        <div style={{ display: "flex", flexDirection: "column" }}>
                            <h6>Fecha desde</h6>
                            <TextField
                                style={{ marginBottom: 20 }}
                                variant="outlined"
                                InputLabelProps={{
                                    style: {
                                        marginBottom: 10,
                                        marginTop: -7,
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 40,
                                        fontSize: 17,
                                    },
                                }}
                                name="fechaDesde"
                                onChange={handleChangeFechaDesde}
                                value={fechaDesde}
                                type="date"
                                required
                            />
                        </div>

                        <div style={{ display: "flex", flexDirection: "column" }}>
                            <h6>Fecha hasta</h6>
                            <TextField
                                style={{ marginBottom: 20 }}
                                variant="outlined"
                                InputLabelProps={{
                                    style: {
                                        marginBottom: 10,
                                        marginTop: -7,
                                    },
                                }}
                                inputProps={{
                                    style: {
                                        height: 40,
                                        fontSize: 17,
                                    },
                                }}
                                name="fechaHasta"
                                onChange={handleChangeFechaHasta}
                                value={fechaHasta}
                                type="date"
                                required
                            />
                        </div>
                    </div>
                    <div align="center">
                        <Button
                            type="submit"
                            size="large"
                            variant="contained"
                            sx={{ mt: 3 }}
                            fullWidth
                        >
                            Aplicar
                        </Button>
                        <Button
                            type="button"
                            size="large"
                            variant="contained"
                            sx={{ mt: 3 }}
                            fullWidth
                            onClick={reset}
                        >
                            Limpiar
                        </Button>
                    </div>
                </form>
            </Modal>
        )
    );
};
