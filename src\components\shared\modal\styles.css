.modal-outside {
    z-index: 2;
    position: fixed;
    top: 0%;
    left: 0%;
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.25);
  }
  
  .modal-window {
    box-shadow: 4px 5px 10px 3px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    animation: linear show-up 200ms;
    outline: none;
    z-index: 100;
    max-height: 90vh;
    max-width: 95vw;
  }

  .show-up {
    transform: scale(1);
    animation: show-up 200ms;
  }
  
  .show-down {
    transform: scale(0);
    animation: show-down 200ms;
  }
  @keyframes show-up {
    0% {
      transform: scale(0);
    }
    75% {
      transform: scale(1.25);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes show-down {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    75% {
      transform: scale(1.25);
    }
    100% {
      transform: scale(0);
      opacity: 0;
    }
  }