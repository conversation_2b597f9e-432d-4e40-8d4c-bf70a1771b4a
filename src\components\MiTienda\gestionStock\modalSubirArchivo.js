import React, { useState } from "react"
import Modal from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";
import { useDispatch } from "react-redux";
import { subirArchivoImportarStock } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalSubirArchivo = ({show, handleClose,handleClickAlert}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [archivo, setArchivo] = useState('')
    const dispatch = useDispatch() 

    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(subirArchivoImportarStock(archivo))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    }

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Subir archivo</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>
                    <TextField 
                        variant="standard"
                        fullWidth margin="normal" 
                        type="file"
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setArchivo(e.target.files[0])}
                    />
                    <h5>{archivo.filename}</h5>
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large" 
                        variant="contained" 
                        sx={{mt: 3}}
                        onClick={(e) => handleOnClick(e)}> Subir
                    </Button>
                </div>
        </Modal>
    )
}