import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Button, Paper, Snackbar, TextField } from "@mui/material"
import {useTranslation} from "react-i18next"
import { Link } from "react-router-dom"
import ReactCountryFlag from "react-country-flag"

import logo from '../../media/logo_develshops.png'
import logoControl from '../../media/lomasdeezeiza.png'

import { logUser } from '../../redux/actions/user.actions'
import MuiAlert from '@mui/material/Alert';

import "./styles.css"

export const NewLoing = () => {

    const okLogin = useSelector((state) => state.user.okLogin)

    const [t, i18n] = useTranslation("global")

    const [input, setInput] = useState({
        user: '',
        password: ''
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const dispatch = useDispatch()
    var md5 = require('md5');

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });
    const [open, setOpen] = React.useState(false)
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const handleLogin = () => {
        dispatch(logUser({
            user: input.user,
            password: md5(input.password)
        }))
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    }

    return(
        <div className="window">
           <Paper className="form-login">
                <Snackbar
                    open={open && !okLogin.success} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity="error"
                        sx={{ width: 400 }}>
                        <h5>{okLogin.error}</h5>
                    </Alert>
                </Snackbar>
                <img className="logo-login" src={process.env.REACT_APP_USA_TOKEN === "true" ? logoControl : logo} alt="develone"/>
                    <div className="text-field">
                        <h5>{t("login.user")}</h5>
                        <TextField
                            name="user"
                            fullWidth
                            value={input.user}
                            onChange={(e) => handleChange(e)}
                        />  
                    </div>
                    <div className="text-field">
                        <h5>{t("login.password")}</h5>
                        <TextField
                            name="password"
                            type="password"
                            fullWidth
                            value={input.password}
                            onChange={(e) => handleChange(e)}
                            onKeyPress={(ev) => {
                                if (ev.key == 'Enter') {
                                    ev.preventDefault();
                                    handleLogin()
                                }
                            }}
                        />  
                    </div>
                <div className="forgot-password">
                    <Link style={{color:"black"}} to={'/recovery'}><h5>{t("login.forgot-password")}</h5></Link>
                </div>
                <Button variant="contained" size="large" onClick={handleLogin} disabled={input.user == '' || input.password == ''}>Ingresar</Button>
                <div style={{display:"flex", margin:20}}>
                    <button onClick={() => i18n.changeLanguage("es")} className="country-flag">
                        <ReactCountryFlag
                        countryCode="ES"
                        svg
                        style={{
                            fontSize: '2em',
                            lineHeight: '2em',
                        }}
                        />
                    </button>
                    <button onClick={() => i18n.changeLanguage("en")} className="country-flag">
                        <ReactCountryFlag
                        countryCode="US"
                        svg
                        style={{
                            fontSize: '2em',
                            lineHeight: '2em',
                        }}
                        />
                    </button>
                </div>  
                <h6 style={{marginTop:40}}>Desarrollado por Develone</h6>
           </Paper>
            <div className="welcome">
                <h2 className="h2">
                Bienvenido a <br /> 
                    <span className="software">
                    {process.env.REACT_APP_TITLE_LOG_IN}
                    </span>
                </h2>
            </div>
        </div>
    )
}