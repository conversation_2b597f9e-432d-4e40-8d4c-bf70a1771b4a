import React, { useEffect, useState } from "react";
import Mo<PERSON> from "react-modal";
import { Button, Divider, MenuItem, Select } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getDatosTiendaByTiendaId } from "../../../redux/actions/mitienda.actions";
import { Check, Clear } from "@mui/icons-material";
import Alert from "@mui/material/Alert";

Modal.setAppElement("#root");

export const ModalOffline = ({ show, handleClose }) => {
  const datosTiendaById = useSelector(
    (state) => state.mitienda.datosTiendaById
  );

  const dispatch = useDispatch();

  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      maxHeight: "90vh",
      borderRadius: "8px",
      maxWidth: "650px",
      //   backgroundColor: "#D1D1D1"
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  //w
  // let tiendas = Object.values(JSON.parse(localStorage.getItem('tiendas')))
  // let tiendausuario = localStorage.getItem('tiendausuario')

  let tiendas = localStorage.getItem("tiendas")
    ? Object.values(JSON.parse(localStorage.getItem("tiendas")))
    : [];
  let tiendausuario = localStorage.getItem("tiendausuario") || "";

  const [tiendaID, setTiendaID] = useState("");

  const changeStore = () => {
    dispatch(getDatosTiendaByTiendaId(tiendaID));
  };

  useEffect(() => {
    setTiendaID(tiendausuario);
  }, []);

  useEffect(() => {
    if (datosTiendaById.success) {
      window.location.reload();
    }
  }, [datosTiendaById]);

  return (
    show && (
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        {datosTiendaById !== "" ? (
          <Alert
            icon={datosTiendaById.success ? <Check /> : <Clear />}
            severity={datosTiendaById.success ? "success" : "error"}
            style={{ width: "95%" }}
          >
            {datosTiendaById.mensaje}
          </Alert>
        ) : null}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <h4>Sucursal</h4>
          <button
            onClick={handleClose}
            style={{
              all: "unset",
              color: "black",
              paddingBottom: 10,
              cursor: "pointer",
            }}
          >
            X
          </button>
        </div>
        <Divider />

        <Select
          fullWidth
          size="small"
          value={tiendaID}
          onChange={(e) => setTiendaID(e.target.value)}
          MenuProps={{ keepMounted: true, disablePortal: true }}
        >
          {tiendas &&
            tiendas.map((t) => (
              <MenuItem value={t.tiendaid} key={t.tiendaid}>
                {t.nombre}
              </MenuItem>
            ))}
        </Select>

        <Divider />

        <div align="right">
          <Button
            size="large"
            variant="outlined"
            onClick={changeStore}
            disabled={tiendas.length < 2}
            sx={{ mt: 3, mr: 1 }}
          >
            Cambiar
          </Button>
        </div>
      </Modal>
    )
  );
};
