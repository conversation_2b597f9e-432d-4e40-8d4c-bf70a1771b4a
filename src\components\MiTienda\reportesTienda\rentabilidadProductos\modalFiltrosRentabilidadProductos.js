import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { useDispatch, useSelector } from "react-redux";
import { Button, FormControl, InputAdornment, InputLabel, ListSubheader, MenuItem, Select, TextField } from "@mui/material";
import {
    getCategoriasByName,
    getCategoriasSinPag,
    getMarcasByName,
    getMarcasTienda
} from "../../../../redux/actions/mitienda.actions";
import { Search } from "@mui/icons-material";

Modal.setAppElement("#root")

export const ModalFiltrosRentabilidadProductos = ({
  show,
  handleClose,
  handleChangeCodigo,
  codigo,
  handleChangeNombre,
  nombre,
  setMarca,
  setCategoriaid,
  search,
  reset
}) =>{

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };

  const categorias = useSelector((state) => state.mitienda.categoriasByName)
  const marcas = useSelector((state) => state.mitienda.marcasByName)

  const [nombreCategoria, setNombreCategoria] = useState('')
  const [nombreMarca, setNombreMarca] = useState('')
  const [selectedOption, setSelectedOption] = useState('');
  const [selectedOptionMarca, setSelectedOptionMarca] = useState('');

  const dispatch = useDispatch()

  useEffect(() =>{
    if(nombreCategoria.length > 2){
        dispatch(getCategoriasByName(nombreCategoria))
    }
    }, [nombreCategoria])

    useEffect(() =>{
        if(nombreMarca.length > 2){
            dispatch(getMarcasByName(nombreMarca))
        }
    }, [nombreMarca])

  return (
      show &&
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
              <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                  <h4>FILTRAR</h4>
                  <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
              </div>
              <Divider/>
              <div style={{display:"flex", flexDirection:"column"}}>
              <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                    <InputLabel id="search-select-label">Marcas</InputLabel>
                    <Select
                    // Disables auto focus on MenuItems and allows TextField to be in focus
                    MenuProps={{ autoFocus: false,  disablePortal: true }}
                    labelId="search-select-label"
                    id="search-select"
                    value={selectedOptionMarca.marcaid || ''}
                    label="Marcas"
                    onClose={() => {
                        dispatch(getMarcasByName(""))
                    }}
                    size="small"
                    // This prevents rendering empty string in Select's value
                    // if search text would exclude currently selected option.
                    renderValue={() => selectedOptionMarca.nombremarca}
                    >
                    {/* TextField is put into ListSubheader so that it doesn't
                        act as a selectable item in the menu
                        i.e. we can click the TextField without triggering any selection.*/}
                    <ListSubheader>
                    <TextField
                        size="small"
                        // Autofocus on textfield
                        autoFocus
                        placeholder="Buscar marca..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreMarca(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {marcas.map((option, i) => (
                        <MenuItem key={i} value={option.marcaid} onClick={() => {
                            setSelectedOptionMarca(option)
                            setNombreMarca(option.nombremarca)
                            setMarca(option.marcaid)
                        }}>
                        {option.nombremarca}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>

                <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                    <InputLabel id="search-select-label">Categor&iacute;as</InputLabel>
                    <Select
                    // Disables auto focus on MenuItems and allows TextField to be in focus
                    MenuProps={{ autoFocus: false,  disablePortal: true }}
                    labelId="search-select-label"
                    id="search-select"
                    value={selectedOption.categoriaid || ''}
                    label="Categor&iacute;as"
                    onClose={() => {
                        dispatch(getCategoriasByName(""))
                    }}
                    size="small"
                    // This prevents rendering empty string in Select's value
                    // if search text would exclude currently selected option.
                    renderValue={() => selectedOption.nombrecategoria}
                    >
                    {/* TextField is put into ListSubheader so that it doesn't
                        act as a selectable item in the menu
                        i.e. we can click the TextField without triggering any selection.*/}
                    <ListSubheader>
                    <TextField
                        size="small"
                        // Autofocus on textfield
                        autoFocus
                        placeholder="Buscar categor&iacute;a..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreCategoria(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {categorias.map((option, i) => (
                        <MenuItem key={i} value={option.categoriaid} onClick={() => {
                            setSelectedOption(option)
                            setNombreCategoria(option.nombrecategoria)
                            setCategoriaid(option.categoriaid)
                        }}>
                        {option.nombrecategoria}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>

                  <TextField 
                    sx={{mt:2}}
                    label="Codigo"  
                    variant="outlined" 
                    margin="codigo" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nombre"
                    onChange={(e) => handleChangeCodigo(e)}
                    value={codigo}
                  />
                  <TextField 
                    sx={{mt:4}}
                    label="Nombre"  
                    variant="outlined" 
                    margin="normal" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nombre"
                    onChange={(e) => handleChangeNombre(e)}
                    value={nombre}
                  />
              </div>
              <div align="center">
              <Button
                  size="large"
                  variant="contained"
                  sx={{mt: 3}} fullWidth
                  onClick={search}
              >Aplicar filtros</Button>
              <Button
                  size="large"
                  variant="contained"
                  sx={{mt: 3}} fullWidth
                  onClick={() => {
                    reset()
                    setNombreCategoria('')
                    setNombreMarca('')
                    setSelectedOption('')
                    setSelectedOptionMarca('')
                    dispatch(getCategoriasByName(""))
                    dispatch(getMarcasByName(""))
                  }}
              >Limpiar filtros</Button>
              </div>
      </Modal>
  )
}
