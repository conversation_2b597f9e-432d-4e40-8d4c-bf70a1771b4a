import React, { useEffect, useState } from "react"
import { Box, Button, 
    Checkbox, 
    Collapse, 
    FormControlLabel, 
    IconButton, 
    Paper, 
    Snackbar, 
    Table, 
    TableBody, 
    TableCell, 
    TableContainer, 
    TableHead, 
    TableRow, 
TextField, 
Tooltip,
Typography} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { configParametros, getConfigParametros, getDatosFooter, postImagenesParametros1, postImagenesParametros2, postLogo } from "../../../../redux/actions/mitienda.actions";
import MuiAlert from '@mui/material/Alert';
import { AgregarSubtitulo } from "./agregarMenu";
import { EditarMenu } from "./editarMenu";
import { Delete, Edit, KeyboardArrowDown, KeyboardArrowUp } from "@mui/icons-material";
import { ModalEliminarItemFooter } from "./eliminarItemFooter";
import { AgregarTituloFooter } from "./agregarTituloFooter";

function Row ({
    row, 
    showEditarFooter, 
    showEliminarItemFooter, 
    showAgregarSubtitulo
}) {

    const [open, setOpen] = React.useState(false);
  
    return (
      <React.Fragment>
        <TableRow style={{backgroundColor:`${row.color}`}}>
        <TableCell>
            <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
            >
            {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
            </IconButton>
        </TableCell>
        <TableCell 
            style={{fontSize:14, padding:0, height:45, fontWeight:"bold"}} align="center">
                {row.titulo}
        </TableCell>
        </TableRow>
        <TableRow>
          <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={12}>
            <Collapse in={open} timeout="auto" unmountOnExit>
              <Box sx={{ margin: 1 }}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:30}}>
                    <Typography 
                        style={{fontSize:16, fontWeight:"bold"}} 
                        variant="h6" 
                        gutterBottom 
                        component="div"
                    >
                    Subtitulos
                    </Typography>
                    <Tooltip title="Agregar subtitulo">
                        <Button variant="contained" size="small" onClick={()=>showAgregarSubtitulo(row)}>
                            +
                        </Button>
                    </Tooltip>
                </div>
                <Table size="small">
                  <TableHead> 
                    <TableRow>
                    <TableCell align="center" style={{fontWeight:"bold"}}
                        >Titulo
                    </TableCell>
                      <TableCell align="center" style={{fontWeight:"bold"}}
                        >Descripci&oacute;n
                    </TableCell>
                      <TableCell align="center" style={{fontWeight:"bold"}}
                        >Orden
                    </TableCell>
                    <TableCell align="center" style={{fontWeight:"bold"}}
                        >URL
                    </TableCell>
                    <TableCell align="center" style={{fontWeight:"bold"}}
                        >Editar
                    </TableCell>
                    <TableCell align="center" style={{fontWeight:"bold"}}
                        >Eliminar
                    </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {row.hijos.length > 0 ? row.hijos.map((historyRow) => (
                      <TableRow key={historyRow.footerid} >
                        <TableCell align="center" style={{width:130}}>
                            {historyRow.subtitulo}
                        </TableCell>
                        <TableCell align="center" style={{width:300}}>
                            {historyRow.descripcion}
                        </TableCell>
                        <TableCell align="center" style={{width:80}}>
                            {historyRow.orden}
                        </TableCell>
                        <TableCell align="center" style={{width:100}}>
                            {historyRow.url}
                        </TableCell>
                        <TableCell onClick={()=>showEditarFooter({titulo: row.titulo, row:historyRow})} 
                            align="center" style={{width:50}}>
                            <Edit/>
                        </TableCell>
                        <TableCell onClick={()=>showEliminarItemFooter(historyRow.footerid)}
                            align="center" style={{width:50}}>
                            <Delete/>
                        </TableCell>
                      </TableRow>
                    )) :                       
                    <TableRow>
                        <TableCell align="center" component="th" scope="row">
                        </TableCell>
                        <TableCell align="center"></TableCell>
                        <TableCell align="center" 
                            style={{width:300, fontSize:18, margin:10}}
                        >No hay datos cargados</TableCell>
                        <TableCell align="center"></TableCell>
                    </TableRow>
                    }                    
                  </TableBody>
                </Table>
              </Box>
            </Collapse>
          </TableCell>
        </TableRow>
      </React.Fragment>
    );
  }

export const ParametrosTienda =()=>{

    const parametros = useSelector((state) => state.mitienda.configParametros)
    const datosFooter = useSelector((state) => state.mitienda.datosFooter)
    const ok = useSelector((state) => state.mitienda.okConfigParametros)
    const okImagenes1 = useSelector((state) => state.mitienda.agregarImagenesParametros1)
    const okImagenes2 = useSelector((state) => state.mitienda.agregarImagenesParametros2)
    const okLogo = useSelector((state) => state.mitienda.okLogo)
    const okAgregarDatosFooter = useSelector((state) => state.mitienda.okAgregarDatosFooter)
    const okEliminarDatosFooter = useSelector((state) => state.mitienda.okEliminarDatosFooter)
    const okEditarDatosFooter = useSelector((state) => state.mitienda.okEditarDatosFooter)
    const okAgregarSubtitulo = useSelector((state) => state.mitienda.okAgregarSubtitulo)
    
    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const dispatch = useDispatch()

    const [input, setInput] = useState('')

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const [logo, setLogo] = useState('')
    const [logoEnviar, setLogoEnviar] = useState('')

    const [imagenes1, setImagenes1] = useState('')

    const [imagenesEnviar1, setImagenesEnviar1] = useState({
        imagen1: "1",
        img1: '',
        imagen2: "2",
        img2: '',
        imagen3: "3",
        img3: '',
    })
    const handleChangeImagenes1 = (imagen,nombre) => {
        setImagenesEnviar1({
            ...imagenesEnviar1,
            [nombre]: imagen,
        })
    }

    const [imagenes2, setImagenes2] = useState('')

    const [imagenesEnviar2, setImagenesEnviar2] = useState({
        imagen1: "1",
        img1: '',
        imagen2: "2",
        img2: '',
        imagen3: "3",
        img3: '',
    })
    const handleChangeImagenes2 = (imagen,nombre) => {
        setImagenesEnviar2({
            ...imagenesEnviar2,
            [nombre]: imagen,
        })
    }

    const [menu, setMenu] = useState('')

    const [show, setShow] = useState('');
    const handleClose = () => setShow(false);
    const handleShow = (i) => {
        setMenu(i)
        setShow(true);
    }

    const [footerEditar, setFooterEditar] = useState('')
    const [footerEliminar, setFooterEliminar] = useState('')
    const [footerItem, setFooterItem] = useState('')

    const [showAgregarTitulo, setShowAgregarTitulo] = useState('');
    const handleCloseAgregarTitulo = () => setShowAgregarTitulo(false);
    const handleShowAgregarTitulo = () =>{ 
        setShowAgregarTitulo(true);
    }

    const [showAgregarSubtitulo, setShowAgregarSubtitulo] = useState('');
    const handleCloseAgregarSubtitulo = () => setShowAgregarSubtitulo(false);
    const handleShowAgregarSubtitulo = (info) =>{ 
        setFooterItem(info)
        setShowAgregarSubtitulo(true);
    }

    const [showEditarFooter, setShowEditarFooter] = useState('');
    const handleCloseEditarFooter = () => setShowEditarFooter(false);
    const handleShowEditarFooter = (info) =>{ 
        setFooterEditar(info)
        setShowEditarFooter(true);
    }

    const [showEliminarItemFooter, setShowEliminarItemFooter] = useState('');
    const handleCloseEliminarItemFooter = () => setShowEliminarItemFooter(false);
    const handleShowEliminarItemFooter = (id) =>{ 
        setFooterEliminar(id)
        setShowEliminarItemFooter(true);
    }

    const handleClick = () => {
        dispatch(configParametros(input))
        dispatch(postImagenesParametros1(imagenesEnviar1))
        dispatch(postImagenesParametros2(imagenesEnviar2))
        dispatch(postLogo(logoEnviar))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const [openAgregar, setOpenAgregar] = React.useState(false);
    const handleClickAlertAgregar = () => {
      setOpenAgregar(true);
    };
    const handleCloseAlertAgregar = () => {
        setOpenAgregar(false);
    };

    useEffect(() =>{
        setInput(parametros.parametros)
        setImagenes1(parametros.imagenes1)
        setImagenes2(parametros.imagenes2)
        setLogo(parametros.logo)
    }, [parametros])

    useEffect(() =>{
        dispatch(getDatosFooter())
    }, [okAgregarDatosFooter, okEditarDatosFooter, okEliminarDatosFooter, okAgregarSubtitulo])

    useEffect(() =>{
        dispatch(getConfigParametros())
    }, [ok, okImagenes1, okImagenes2, okLogo])

    return (
        <section className="container-chat">

                <AgregarTituloFooter
                    show={showAgregarTitulo}
                    handleClose={handleCloseAgregarTitulo}

                />

                <AgregarSubtitulo
                    show={showAgregarSubtitulo}
                    handleClose={handleCloseAgregarSubtitulo}
                    info={footerItem}
                />

                <EditarMenu
                    show={showEditarFooter}
                    handleClose={handleCloseEditarFooter}
                    info={footerEditar}
                />

                <ModalEliminarItemFooter
                    show={showEliminarItemFooter}
                    handleClose={handleCloseEliminarItemFooter}
                    id={footerEliminar}
                />

                <Snackbar
                    open={openAgregar} 
                    autoHideDuration={6000} onClose={handleCloseAlertAgregar}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlertAgregar} 
                        severity={okAgregarDatosFooter ? "success" : "error"}
                        sx={{ width: 400 }}>
                        {okAgregarDatosFooter.mensaje}
                    </Alert>
                </Snackbar>

                <Snackbar
                    open={open} 
                    autoHideDuration={6000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success === true && okImagenes1 === true && okImagenes2 === true && okLogo === true ? "success" : "error"}
                        sx={{ width: 400 }}>
                        {ok.success === true && okImagenes1 === true && okImagenes2 === true && okLogo === true ? 
                            <h5>Informaci&oacute;n modificada con &eacute;xito!</h5> : 
                            <h5>Hubo un problema al modificar los datos</h5>
                        }
                    </Alert>
                </Snackbar>
                
                <header className="header-chat">
                    <h3 style={{marginBottom:20}}>Par&aacute;metros</h3>
                </header>
                <Paper className="form-chat" sx={{mb:5}}>
                    <h6>Logo Ecommerce</h6>
                    <div style={{display:"flex"}}>
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            value={logo ? logo : ''}
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            onChange={(e)=>setLogoEnviar(e.target.files[0])}
                            type="file"
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                </Paper>
                <Paper className="form-chat" sx={{mb:5}}>
                    <h6>Colores</h6>
                    <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorPrimario"
                            value={input ? input.colorPrimario : ''}
                            label="Barra de navegaci&oacute;n"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorPrimario"
                            value={input ? input.colorPrimario : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorSecundario"
                            value={input ? input.colorSecundario : ''}
                            label="Cuerpo"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorSecundario"
                            value={input ? input.colorSecundario : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorTerciario"
                            value={input ? input.colorTerciario : ''}
                            label="Footer"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorTerciario"
                            value={input ? input.colorTerciario : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>

                    <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorFondoSubNav"
                            value={input ? input.colorFondoSubNav : ''}
                            label="Sub Barra de Navegaci&oacute;n - Fondo"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorFondoSubNav"
                            value={input ? input.colorFondoSubNav : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>

                    {/* <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorHoverFondoSubNav"
                            value={input ? input.colorHoverFondoSubNav : ''}
                            label="Sub Barra de Navegaci&oacute;n - Fondo Hover"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorHoverFondoSubNav"
                            value={input ? input.colorHoverFondoSubNav : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div> */}

                    <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorTextoSubNav"
                            value={input ? input.colorTextoSubNav : ''}
                            label="Sub Barra de Navegac&oacute;n - Texto"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorTextoSubNav"
                            value={input ? input.colorTextoSubNav : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>

                    {/* <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorHoverTextoSubNav"
                            value={input ? input.colorHoverTextoSubNav : ''}
                            label="Sub Barra de Navegac&oacute;n - Texto Hover"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorHoverTextoSubNav"
                            value={input ? input.colorHoverTextoSubNav : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div> */}

                    <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorMenuDesplegable"
                            value={input ? input.colorMenuDesplegable : ''}
                            label="Menu Desplegable - Fondo"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorMenuDesplegable"
                            value={input ? input.colorMenuDesplegable : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>

                    {/* <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorHoverFondoMenuDesplegable"
                            value={input ? input.colorHoverFondoMenuDesplegable : ''}
                            label="Menu Desplegable - Fondo Hover"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorHoverFondoMenuDesplegable"
                            value={input ? input.colorHoverFondoMenuDesplegable : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>  */}

                    <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorTextoMenuDesplegable"
                            value={input ? input.colorTextoMenuDesplegable : ''}
                            label="Menu Desplegable - Texto"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorTextoMenuDesplegable"
                            value={input ? input.colorTextoMenuDesplegable : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div> 

                    {/* <div style={{display:"flex", alignItems:"center", justifyContent:"center" ,width:"100%"}}>
                        <TextField 
                            variant="standard"
                            margin="normal" 
                            name="colorHoverTextoMenuDesplegable"
                            value={input ? input.colorHoverTextoMenuDesplegable : ''}
                            label="Menu Desplegable - Texto Hover"
                            color="secondary"
                            style={{width:"50%"}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="outlined"
                            margin="normal" 
                            name="colorHoverTextoMenuDesplegable"
                            value={input ? input.colorHoverTextoMenuDesplegable : ''}
                            type="color"
                            color="secondary"
                            style={{width:"30%", marginTop:20, marginLeft:10}}
                            onChange={(e)=> handleChange(e)}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 60,
                                    fontSize:17
                                },
                            }}
                        />
                    </div> */}
                </Paper>
                <Paper className="form-chat">
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.mostrar_info == "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="mostrar_info"
                        label="Mostrar informaci&oacute;n"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.ecommerce_info == "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="ecommerce_info"
                        label="Informaci&oacute;n Ecommerce"
                    />
                    <div style={{display:"flex"}}>
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            value={imagenes1 ? imagenes1.imagen1_informacion : ''}
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            onChange={(e) => handleChangeImagenes1(e.target.files[0],'img1','imagen1')}
                            type="file"
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex"}}>
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            value={imagenes1 ? imagenes1.imagen2_informacion : ''}
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            type="file"
                            onChange={(e) => handleChangeImagenes1(e.target.files[0],'img2','imagen2')}
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex"}}>
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            value={imagenes1 ? imagenes1.imagen3_informacion : ''}
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                        variant="standard"
                        fullWidth margin="normal" 
                        onChange={(e) => handleChangeImagenes1(e.target.files[0],'img3','imagen3')}
                        type="file"
                        color="secondary"
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                    />
                    </div>
                    <TextField 
                        name="ecommerce_informacion"
                        label="Informaci&oacute;n"  
                        fullWidth margin="normal" 
                        multiline
                        minRows={5}
                        onChange={(e)=> handleChange(e)}
                        value={input ? input.ecommerce_informacion : ''}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                </Paper>
                <Paper className="form-chat" sx={{mt:5}}>
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.ecommerce_info2 == "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="ecommerce_info2"
                        label="Informaci&oacute;n Ecommerce 2"
                    />
                    <div style={{display:"flex"}}>
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            value={imagenes2 ? imagenes2.imagen1_informacion : ''}
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            onChange={(e) => handleChangeImagenes2(e.target.files[0],'img1','imagen1')}
                            type="file"
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex"}}>
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            value={imagenes2 ? imagenes2.imagen2_informacion : ''}
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            onChange={(e) => handleChangeImagenes2(e.target.files[0],'img2','imagen2')}
                            type="file"
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex"}}>
                        <TextField 
                            variant="standard"
                            fullWidth margin="normal" 
                            name="imagen"
                            value={imagenes2 ? imagenes2.imagen3_informacion : ''}
                            color="secondary"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                        />
                        <TextField 
                        variant="standard"
                        fullWidth margin="normal" 
                        onChange={(e) => handleChangeImagenes2(e.target.files[0],'img2','imagen2')}
                        type="file"
                        color="secondary"
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                    />
                    </div>
                    <TextField 
                        name="ecommerce_informacion2"
                        label="Informaci&oacute;n"  
                        fullWidth margin="normal" 
                        multiline
                        minRows={5}
                        onChange={(e)=> handleChange(e)}
                        value={input ? input.ecommerce_informacion2 : ''}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                </Paper>
                <Paper className="form-chat" sx={{mt:5}}>
                    <TextField 
                        name="informacion_lista"
                        label="Informaci&oacute;n"  
                        fullWidth margin="normal" 
                        onChange={(e)=> handleChange(e)}
                        value={input ? input.informacion_lista : ''}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.default_mantenimiento === "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="default_mantenimiento"
                        label="Poner en mantenimiento"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.default_categorias === "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="default_categorias"
                        label="Mostrar Carousel de Categorias"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.default_marcas === "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="default_marcas"
                        label="Mostrar Carousel de Marcas"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.default_masvendido === "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="default_masvendido"
                        label="Mostrar Carousel M&aacute;s vendidos"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.default_productohome === "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="default_productohome"
                        label="Mostrar Carousel Productos Home"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.default_prodstock === "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="default_prodstock"
                        label="Mostrar Producto con stock"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.menu_desplegable === "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="menu_desplegable"
                        label="Mostrar Menu desplegable"
                    />
                    <FormControlLabel
                        control={
                            <Checkbox color="secondary"/>
                        }
                        checked={input === undefined ?  null : input.default_onlyread === "1" ? true : false}
                        onChange={(e) => handleCheck(e)}
                        name="default_onlyread"
                        label="Only Read"
                    />
                </Paper>     
                <div align="right">
                    <Button 
                        size="large" 
                        variant="contained" 
                        onClick={(e)=>(handleClick(e))}
                        disabled={permisos_acciones?.modificar === "0"}
                        sx={{mt: 3, mr:3}}>Guardar</Button>
                </div>  
                <div className='container-tabla'>
                <TableContainer component={Paper} className='tabla'>
                    <Table aria-label="simple table">
                    <TableHead>
                        <TableCell style={{width:"50px"}} />
                        <TableCell 
                            style={{fontWeight:"bold", 
                            fontSize:"25px", 
                        }} 
                            align="right"
                        >
                        <div style={{display:"flex", alignSelf:"flex-end"}}>
                        <div style={{width:"55%",display:"flex", flexDirection:"row-reverse", justifyContent:"space-between", alignItems:"center", padding:10}}>
                            <h4 style={{fontWeight:"bold"}}>T&iacute;tulos Footer</h4> 
                            <Tooltip title="Agregar t&iacute;tulo">
                                <Button 
                                    variant="contained" 
                                    style={{marginBottom:15}} 
                                    onClick={handleShowAgregarTitulo}
                                >+</Button>
                            </Tooltip>
                        </div>
                        </div>
                        </TableCell>
                    </TableHead>
                    <TableBody>
                        {datosFooter && datosFooter.map((row) => (
                            <Row 
                                key={row.titulo} 
                                row={row} 
                                showEditarFooter={handleShowEditarFooter}
                                showEliminarItemFooter={handleShowEliminarItemFooter}
                                showAgregarSubtitulo={handleShowAgregarSubtitulo}
                            />
                        ))}
                    </TableBody>
                    </Table>
                </TableContainer>
            </div>
        </section>
    )
}