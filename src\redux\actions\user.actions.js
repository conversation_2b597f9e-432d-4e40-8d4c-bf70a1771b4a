import axios from "axios";

export const recoverPassword = (userInfo) => async (dispatch) => {
  var tiendausuario = localStorage.getItem('tiendausuario')

  var configCheckUsuario = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `${process.env.REACT_APP_SERVER}php/GestionSeguridad.php?type=GFExisteUsuario&user_name=${userInfo}&soy_cliente=0`,
    headers: { 
      'Content-Type': 'application/json',
    },
  };

  axios(configCheckUsuario)
    .then(function (response) {
      if(response.data.success){
        var configRecoverPassword = {
          method: 'get',
          maxBodyLength: Infinity,
          url: `${process.env.REACT_APP_SERVER}php/GestionSeguridad.php?type=GFRecovery&user_name=${userInfo}&soy_cliente=0&tiendaid=${tiendausuario}&dominio=${process.env.REACT_APP_DOMINIO}`,
          headers: { 
            'Content-Type': 'application/json',
          },
        };

        axios(configRecoverPassword)
        .then(function (response) {
          dispatch({ type: 'RECOVER_PASSWORD', payload: response.data })
        })
        .catch(function (error) {
          console.log('error ', error)
          console.log(error);
        });
      }else{
        dispatch({ type: 'RECOVER_PASSWORD', payload: response.data })
      }
    })
    .catch(function (error) {
      console.log(error);
    });
}

export const limpiarRecoveryPass = () => (dispatch) => {
  dispatch({ type: 'CLEAN_RECOVERY_PASS', payload: {mensaje: "", status: true} })
}

export const logUser = (userInfo) => async (dispatch) => {
  var axios = require('axios');

    var configTienda = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_SERVER}php/GestionSeguridad.php?type=GFLogin&user_name=${userInfo.user}&password=${userInfo.password}&lenguage_id=2`,
      headers: { 
        'Content-Type': 'application/json',
      },
    };
  
    axios(configTienda)
    .then(function (response) {
      if(response.data.success){
        localStorage.setItem('api_key_develone', response.data.api_key_develone)
        localStorage.setItem('idusuario', response.data.idusuario)
        localStorage.setItem('nombreusuario', response.data.nombreusuario)
        localStorage.setItem('estaLogueado', response.data.success)
        localStorage.setItem('api_key', response.data.api_key)
        localStorage.setItem('tiendausuario', response.data.tiendausuario)
        localStorage.setItem('develoneclienteid', response.data.develoneclienteid)
        localStorage.setItem('ip_server', response.data.ip_server)
        localStorage.setItem('direccionweb', response.data.direccionweb)
        localStorage.setItem('cajaid', response.data.cajaid)
        localStorage.setItem('tiendas', JSON.stringify(response.data.info_tiendas))
        //info perfiles
        localStorage.setItem('perfiles', JSON.stringify(response.data.perfiles))
        localStorage.setItem('info_perfiles', JSON.stringify(response.data.info_perfiles))
        let permisos = Object.values(response.data.permisos_acciones).filter((p) => p.archivo.toLowerCase().includes('mitienda'))
        localStorage.setItem('permisos_acciones', JSON.stringify(permisos))
        let permisosCaja = Object.values(response.data.permisos_acciones).filter((p) => p.archivo.toLowerCase().includes('caja/abm_caja.php'))
        localStorage.setItem('permisos_acciones_caja', JSON.stringify(permisosCaja))
        let permisosUsuarios = Object.values(response.data.permisos_acciones).filter((p) => p.archivo.toLowerCase().includes('usuarios/abm_usuarios.php'))
        localStorage.setItem('permisos_acciones_usuarios', JSON.stringify(permisosUsuarios))
      }
  
      dispatch({ type: 'USER_LOGED', payload: response.data })
    })
    .catch(function (error) {
      console.log(error);
    });
}

export const logUserToken = (userInfo) => async (dispatch) => {

    var data = JSON.stringify({
      "user": "develone",
      "password": "develonepwd"
    });

    var configToken = {
      method: 'post',
      url: `${process.env.REACT_APP_SERVER_CONTROL}token`,
      headers: { 
        'Content-Type': 'application/json'
      },
      data : data
    };

    axios(configToken)
    .then(function (response) {
      localStorage.setItem('tokenControl', response.data.token)
      var configControl = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_SERVER_CONTROL}login?type=GFLogin&user_name=${userInfo.user}&password=${userInfo.password}&lenguage_id=2`,
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer '+response.data.token,
        },
      };
      axios(configControl)
      .then(function (response2) {
        localStorage.setItem('nombreusuario', response2.data.nombreusuario)
        localStorage.setItem('estaLogueado', true)
        localStorage.setItem('tiendausuario', response2.data.tiendausuario)
        localStorage.setItem('idusuario', response2.data.idusuario)
        dispatch({ type: 'USER_LOGED', payload: response2.data })
      })
      .catch(function (error) {
        console.log(error);
      });

    })
    .catch(function (error) {
      console.log(error);
    });
}

export const logOut = () => (dispatch) => {
  localStorage.setItem('estaLogueado', false)
  localStorage.clear()
  dispatch({ type: 'USER_LOGEDOUT' })
}

export const getMenu = () => async (dispatch) => {
  var idusuario = localStorage.getItem('idusuario')
  var tiendausuario = localStorage.getItem('tiendausuario')

    var api_key = localStorage.getItem('api_key')
    try {
      let array = []
      array[0] = await axios.get(
        `${process.env.REACT_APP_SERVER}php/GestionConf.php?type=GCListaMenuSubmenu&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_lenguajeid=2&OI_usuarioid=${idusuario}&pagActual=1&elements=500`)
      let num = array[0].data.paginaUltima
      array[0] = Object.values(array[0].data.listamenusubmenu)
      let aux 
      for(let i=2; i <= num; i++){
        aux = await axios.get(
          `${process.env.REACT_APP_SERVER}php/GestionConf.php?type=GCListaMenuSubmenu&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&OI_lenguajeid=2&OI_usuarioid=${idusuario}&pagActual=${i}&elements=20`)
        array[i] = Object.values(aux.data.listamenusubmenu)
      }
      if (array) {
        dispatch({ type: 'GET_MENU', payload: array.flat()})
  
      } else throw new Error('no hay informacion')
    } catch (error) {
      throw { message: error }
    }
}

export const getMenutoken = () => async(dispatch) => {
  var token = localStorage.getItem('tokenControl')
  var idusuario = localStorage.getItem('idusuario')
  var tiendausuario = localStorage.getItem('tiendausuario')
    try {
      const configMenu = {
        headers: { Authorization: `Bearer ${token}` }
      };
      let array = []
      array[0] = await axios.get(
      `${process.env.REACT_APP_SERVER_CONTROL}gestionConf?type=GCListaMenuSubmenu&OI_tiendaid=${tiendausuario}&OI_lenguajeid=2&OI_usuarioid=${idusuario}&pagActual=${1}&elements=500`,configMenu)
      let num = array[0].data.paginaUltima
      array[0] = Object.values(array[0].data.listamenusubmenu)
      let aux 
      for(let i=2; i <= num; i++){
        aux = await axios.get(
        `${process.env.REACT_APP_SERVER_CONTROL}gestionConf?type=GCListaMenuSubmenu&OI_tiendaid=${tiendausuario}&OI_lenguajeid=2&OI_usuarioid=${idusuario}&pagActual=${i}&elements=20`,configMenu)
        array[i] = Object.values(aux.data.listamenusubmenu)
      }
      if (array) {
        dispatch({ type: 'GET_MENU', payload: array.flat()})
  
      } else throw new Error('no hay informacion')
    } catch (error) {
      throw { message: error }
    }
}

export const registrarUsuario = (userInfo) => async (dispatch) => {
  var axios = require('axios');
  var tiendausuario = localStorage.getItem('tiendausuario')

  var api_key = localStorage.getItem('api_key')
  var configTienda = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `${process.env.REACT_APP_SERVER}php/GestionSeguridad.php?type=GSSetUsuario&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&usuarioid=0&usuario=${userInfo.usuario}&nombre=${userInfo.nombre}&apellido=${userInfo.apellido}&email=${userInfo.email}&telefono=${userInfo.telefono}&domicilio=${userInfo.domicilio}&descuento_producto=${userInfo.descuento_producto}&descuento=${userInfo.descuentoid}&comisionvta=${userInfo.comisionvta}&activo=${userInfo.activo}`,
    headers: { 
      'Content-Type': 'application/json',
    },
  };

  axios(configTienda)
  .then(function (response) {  
    dispatch({ type: 'REGISTRAR_USUARIO_TIENDA', payload: response.data })
  })
  .catch(function (error) {
    console.log(error);
  });
}

export const editarUsuario = (userInfo) => async (dispatch) => {
  var axios = require('axios');
  var tiendausuario = localStorage.getItem('tiendausuario')

  var api_key = localStorage.getItem('api_key')
  var configTienda = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `${process.env.REACT_APP_SERVER}php/GestionSeguridad.php?type=GSSetUsuario&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&usuarioid=${userInfo.usuarioid}&usuario=${userInfo.usuario}&nombre=${userInfo.nombre}&apellido=${userInfo.apellido}&email=${userInfo.email}&telefono=${userInfo.telefono}&domicilio=${userInfo.domicilio}&descuento_producto=${userInfo.descuento_producto}&descuento=${userInfo.descuento}&comisionvta=${userInfo.comisionvta}&activo=${userInfo.activo}&cambiaclave=${userInfo.cambiaclave}&clave=${userInfo.clave}`,
    headers: { 
      'Content-Type': 'application/json',
    },
  };

  axios(configTienda)
  .then(function (response) {  
    dispatch({ type: 'EDITAR_USUARIO_TIENDA', payload: response.data })
  })
  .catch(function (error) {
    console.log(error);
  });
}

export const getUsuario = () => async (dispatch) => {
  var axios = require('axios');
  var tiendausuario = localStorage.getItem('tiendausuario')
  var idusuario = localStorage.getItem('idusuario')

  var api_key = localStorage.getItem('api_key')
  var configTienda = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `${process.env.REACT_APP_SERVER}php/GestionTienda.php?type=GTGetUsuarios&OI_tiendaid=${tiendausuario}&OV_api_key=${api_key}&usuarioid=${idusuario}`,
    headers: { 
      'Content-Type': 'application/json',
    },
  };

  axios(configTienda)
  .then(function (response) {  
    dispatch({ type: 'GET_USUARIO_TIENDA', payload: response.data.usuario[idusuario] })
  })
  .catch(function (error) {
    console.log(error);
  });
}

export const registrarTienda = (userInfo) => async (dispatch) => {
  var axios = require('axios');
  var tiendausuario = localStorage.getItem('tiendausuario')
  var api_key = localStorage.getItem('api_key')

  var configTienda = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `${process.env.REACT_APP_SERVER}php/GestionSeguridad.php?type=GSRegistracion&tiendaid=${tiendausuario}&api_key=${api_key}&nombre=${userInfo.nombredelatienda}&nombre_empresa=${userInfo.nombredelatienda}&email=${userInfo.email}&clave1=${userInfo.clave}&tipo_contribuyente=${userInfo.tipocontribuyente}&lenguage_id=2&sessionid=&soy_cliente=1&es_empresa=1&es_empresa_free=1`,
    headers: { 
      'Content-Type': 'application/json',
    },
  };

  axios(configTienda)
  .then(function (response) {  
    dispatch({ type: 'REGISTRAR_TIENDA', payload: {success: response.data.success, mensaje: response.data.mensaje }})
  })
  .catch(function (error) {
    dispatch({ type: 'REGISTRAR_TIENDA', payload: {success: false, mensaje: 'Hubo un error al crear la tienda'} })
  });
}