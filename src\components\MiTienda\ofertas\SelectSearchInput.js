import React, { useEffect, useState } from "react"
import Modal from 'react-modal';
import './ofertas.css'

export const SelectSearchInput = ({info,value,setValue,setProductoId,open, setOpen}) =>{

    return (
        <div className="input-grup-ofertas">
            <input onClick={(e)=> {
                setOpen(true)
            }} value={value} onChange={(e)=> setValue(e.target.value)}/>
            <div className="list-info"  > 
                {
                    info.length > 0 && open ? 
                    info.map((i) =>
                        <div className="menu-items" onClick={(e)=> {
                            setOpen(false)
                            setProductoId(i.productoid)
                            setValue(i.nombreproducto)
                        }}>
                            {i.nombreproducto}
                        </div>
                    ) : null
                }
            </div>  
        </div>
    )
}