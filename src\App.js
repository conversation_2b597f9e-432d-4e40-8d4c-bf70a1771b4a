import {
  BrowserRouter as Router,
  Switch,
  Route,
  Redirect,
} from "react-router-dom";
import "./styles/App.css";
import "./styles/bootstrap.min.css";
import "./styles/icons/css/all.css";
import Recovery from "./components/auth/recovery";

import Layout from "./components/layout/layout";

import { useSelector } from "react-redux";
import Home from "./components/views/home/<USER>";
import { General } from "./components/MiTienda/mitienda/general/general";
import { LayoutMiTienda } from "./components/MiTienda/layout/layoutMiTienda";
import { HomeMiTienda } from "./components/MiTienda/homeMiTienda/homeMiTienda";
import { MetodosDePago } from "./components/MiTienda/mitienda/metodosDePago/metodosDePago";
import { MetodosDeEnvio } from "./components/MiTienda/mitienda/metodosDeEnvio/metodosDeEnvio";
import { MiPlan } from "./components/MiTienda/mitienda/miplan/miplan";
import { RetiroEnLocal } from "./components/MiTienda/mitienda/metodosDeEnvio/retiroEnLocal";
import { Chat } from "./components/MiTienda/mitienda/chat/chat";
import { RedesSociales } from "./components/MiTienda/mitienda/redesSociales/redesSociales";
import { EtiquetasHTML } from "./components/MiTienda/mitienda/etiquetasHTML/etiquetasHTML";
import { Categorias } from "./components/MiTienda/categorias/categorias/categorias";
import { AgregarProductos } from "./components/MiTienda/productos/agregarProductos/agregarProductos";
import { AdministrarProductos } from "./components/MiTienda/productos/administrarProductos/administrarProductos";
import { EditarProducto } from "./components/MiTienda/productos/administrarProductos/editarProducto";
import { NewLoing } from "./components/auth/newLogIn";
import { ClonarProducto } from "./components/MiTienda/productos/administrarProductos/clonarProducto";
import { Notificaciones } from "./components/MiTienda/productos/notificacionesPorStock/notificacionesPorStock";
import { Ventas } from "./components/MiTienda/ventas/ventas";
import { Venta } from "./components/MiTienda/ventas/venta";
import { AdministrarClientes } from "./components/MiTienda/clientes/administrarClientes/administrarClientes";
import { MarcasProductos } from "./components/MiTienda/marcasProductos/marcasProductos/marcasProductos";
import { AsociarProducto } from "./components/MiTienda/productos/administrarProductos/asociarProducto";
import { GoogleShopping } from "./components/MiTienda/seoMarketing/googleShopping/googleShopping";
import { MetaAds } from "./components/MiTienda/seoMarketing/metaAds/metadAds";
import { GoogleAnalytics } from "./components/MiTienda/seoMarketing/googleAnalytics/googleAnalytics";
import { FacebookPixel } from "./components/MiTienda/seoMarketing/facebookPixel/facebookPixel";
import { GraficoVentas } from "./components/MiTienda/ventas/graficoVentas";
import { ParametrosTienda } from "./components/MiTienda/mitienda/parametros/parametros";
import { AumentoMasivo } from "./components/MiTienda/productos/aumentoMasivo/aumentoMasivo";
import { CrearUsuario } from "./components/auth/crearUsuario.js";
import { Descuentos } from "./components/MiTienda/descuentos/descuentos";
import { Rentabilidad } from "./components/MiTienda/rentabilidad/rentabilidad";
import { Tallas } from "./components/MiTienda/tallas/tallas/tallas";
import { ColoresTienda } from "./components/MiTienda/coloresTienda/coloresTienda";
import { Monedas } from "./components/MiTienda/monedas/monedas";
import { EditarUsuarioTienda } from "./components/auth/editarUsuario";
import { ReporteListaDePrecios } from "./components/MiTienda/reportesTienda/reporteListaDePrecios/reporteListaDePrecios";
import { SubdiarioCompras } from "./components/MiTienda/reportesTienda/subdiarioCompras/subdiarioCompras";
import { SubdiarioVentas } from "./components/MiTienda/reportesTienda/subdiarioVentas/subdiarioVentas";
import { StockVendido } from "./components/MiTienda/reportesTienda/stockVendido/stockVendido";
import { FacturasElectronicas } from "./components/MiTienda/reportesTienda/facturasElectronicas/facturasElectronicas";
import { RegistrarTienda } from "./components/auth/crearTienda";
import { Proveedores } from "./components/MiTienda/proveedores/administrarProveedores.js";
import { Proveedor } from "./components/MiTienda/proveedores/agregarProveedor.js";
import { Cotizacion } from "./components/MiTienda/cotizacion/cotizacion.js";
import { ListadoPresupuestos } from "./components/MiTienda/presupuesto/listadoPresupuestos.js";
import { PuntoDeVenta } from "./components/MiTienda/puntoDeVenta/puntoDeVenta.js";
import { Promociones } from "./components/MiTienda/ofertas/listadoOfertas.js";
import { ProductosHome } from "./components/MiTienda/productosHome/productosHome.js";
import { ReportePorVendedor } from "./components/MiTienda/reportesTienda/reportePorVendedor/reportePorVendedor.js";
import { FacturasProveedor } from "./components/MiTienda/facturaProveedor/facturasProveedor.js";
import { RentabilidadProductos } from "./components/MiTienda/reportesTienda/rentabilidadProductos/rentabilidadProdcutos.js";
import { CuentaCorrienteDeudas } from "./components/MiTienda/reportesTienda/cuentaCorrienteDeudas/cuentaCorrienteDeudas.js";
import { ArchivosImportar } from "./components/MiTienda/productos/importarProductos/listadoArchivos.js";
import { GestionCuentasCorrientes } from "./components/MiTienda/cuentasCorrientes/cuentasVentas/listadoCuentasCorrientes.js";
import { DetalleCuentaCorriente } from "./components/MiTienda/cuentasCorrientes/cuentasVentas/detalleCuentaCorriente.js";
import { GestionCuentasCorrientesProveedor } from "./components/MiTienda/cuentasCorrientes/cuentasProveedores/listadoCuentasCorrientesProveedor.js";
import { DetalleCuentaCorrienteProveedor } from "./components/MiTienda/cuentasCorrientes/cuentasProveedores/detalleCuentaCorrienteProveedor.js";
import { Presupuesto } from "./components/MiTienda/presupuesto/presupuesto.js";
import { ArchivosImportarListaPrecios } from "./components/MiTienda/productos/importarListaPrecios/listadoArchivos.js";
import { GestionStock } from "./components/MiTienda/gestionStock/gestionStock.js";
import { ArchivosImportarStock } from "./components/MiTienda/productos/importarStock/listadoArchivos.js";
import { RentabilidadCategorias } from "./components/MiTienda/rentabilidad/rentabilidadCategorias/rentabilidadCategorias.js";
import { ReportePedidos } from "./components/MiTienda/reportesTienda/reportePedidos/reportePedidos.js";
import { ReporteCuentasCorrientes } from "./components/MiTienda/reportesTienda/reporteCuentasCorrientes/reporteCuentasCorrientes.js";
import { ReporteEntregas } from "./components/MiTienda/reportesTienda/reporteEntregas/reporteEntregas.js";
import { GestionRemitos } from "./components/MiTienda/gestionRemitos/gestionRemitos.js";
import { Remitos } from "./components/MiTienda/remitos/remitos.js";
import { Remito } from "./components/MiTienda/remitos/remito.js";
import { ReporteStatusLiquidacion } from "./components/MiTienda/reportesTienda/reporteStatusLiquidacion/reporteStatusLiquidacion.js";
import { GestionLiquidaciones } from "./components/MiTienda/gestionLiquidaciones/gestionLiquidaciones.js";
import { Liquidaciones } from "./components/MiTienda/liquidaciones/liquidaciones.js";
import { Liquidacion } from "./components/MiTienda/liquidaciones/liquidacion.js";

const App = () => {
  const isAuthenticated = useSelector((state) => state.user.status);
  const estaLogueado = localStorage.getItem("estaLogueado");

  // checks if user has admin profile
  const hasAdminProfile = () => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    return Object.values(perfiles).includes(7);
  };

  return (
    <>
      <div className="App">
        <Router>
          <Switch>
            <Route exact path="/recovery" component={Recovery} />
            <Route path="/RegistrarTienda" exact component={RegistrarTienda} />
            {/* <Route
          path="/"
          exact
          render={() =>
            isAuthenticated.ok ? <Redirect to="/Mitienda/Home" /> : <NewLoing isAuthenticated={isAuthenticated}/>
          }
        /> */}
            {/* <Route
              path="/"
              exact
              render={() =>
                isAuthenticated.ok ? (
                  hasAdminProfile() ? (
                    <Redirect to="/Mitienda/Home" />
                  ) : (
                    <Redirect to="/Mitienda/PuntoDeVenta" />
                  )
                ) : (
                  <NewLoing isAuthenticated={isAuthenticated} />
                )
              }
            /> */}

            <Route
  path="/"
  exact
  render={() =>
    isAuthenticated.ok ? (
      <Redirect to="/Mitienda/Home" />
    ) : (
      <NewLoing isAuthenticated={isAuthenticated} />
    )
  }
/>

            {estaLogueado ? (
              <Route path="/MiTienda/:path?">
                <LayoutMiTienda>
                  <Switch>
                    {/* <Route path="/MiTienda/Home" exact component={HomeMiTienda}/> */}
                    {/* If user is admin, show HomeMiTienda, else redirect to PuntoDeVenta */}
                    {/* <Route
                      path="/MiTienda/Home"
                      exact
                      render={() =>
                        hasAdminProfile() ? (
                          <HomeMiTienda />
                        ) : (
                          <Redirect to="/MiTienda/PuntoDeVenta" />
                        )
                      }
                    /> */}
                    <Route path="/MiTienda/Home" exact component={HomeMiTienda} />
                    <Route path="/MiTienda/General" exact component={General} />
                    <Route path="/MiTienda/MiPlan" exact component={MiPlan} />
                    <Route
                      path="/MiTienda/MetodosPago"
                      exact
                      component={MetodosDePago}
                    />
                    <Route
                      path="/MiTienda/MetodosEnvio"
                      exact
                      component={MetodosDeEnvio}
                    />
                    <Route
                      path="/MiTienda/RetiroEnLocal"
                      exact
                      component={RetiroEnLocal}
                    />
                    <Route path="/MiTienda/Chat" exact component={Chat} />
                    <Route
                      path="/MiTienda/RedesSociales"
                      exact
                      component={RedesSociales}
                    />
                    <Route
                      path="/MiTienda/EtiquetasHTML"
                      exact
                      component={EtiquetasHTML}
                    />
                    <Route
                      path="/MiTienda/Categorias"
                      exact
                      component={Categorias}
                    />
                    {/* <Route
                      path="/MiTienda/Agregar"
                      exact
                      component={AgregarProductos}
                    /> */}
                    <Route
                      path="/Mitienda/Agregar"
                      exact
                      render={(props) =>
                        hasAdminProfile() ? (
                          <AgregarProductos {...props} />
                        ) : (
                          <Redirect to="/Mitienda/Administrar" />
                        )
                      }
                    />
                    {/* <Route
                      path="/MiTienda/EditarProducto/:id"
                      exact
                      component={EditarProducto}
                    /> */}
                    <Route
                      path="/Mitienda/EditarProducto/:id"
                      exact
                      render={(props) =>
                        hasAdminProfile() ? (
                          <EditarProducto {...props} />
                        ) : (
                          <Redirect to="/Mitienda/Administrar" />
                        )
                      }
                    />
                    {/* <Route
                      path="/MiTienda/AsociarProducto/:id"
                      exact
                      component={AsociarProducto}
                    /> */}
                    <Route
                      path="/Mitienda/AsociarProducto/:id"
                      exact
                      render={(props) =>
                        hasAdminProfile() ? (
                          <AsociarProducto {...props} />
                        ) : (
                          <Redirect to="/Mitienda/Administrar" />
                        )
                      }
                    />
                    <Route
                      path="/MiTienda/Administrar"
                      exact
                      component={AdministrarProductos}
                    />
                    {/* <Route
                      path="/MiTienda/ClonarProducto/:id"
                      exact
                      component={ClonarProducto}
                    /> */}
                    <Route
                      path="/Mitienda/ClonarProducto/:id"
                      exact
                      render={(props) =>
                        hasAdminProfile() ? (
                          <ClonarProducto {...props} />
                        ) : (
                          <Redirect to="/Mitienda/Administrar" />
                        )
                      }
                    />
                    <Route
                      path="/MiTienda/NotificacionesStock"
                      exact
                      component={Notificaciones}
                    />
                    <Route path="/MiTienda/Pedidos" exact component={Ventas} />
                    <Route path="/MiTienda/Remitos" exact component={Remitos} />
                    <Route
                      path="/MiTienda/PuntoDeVenta"
                      exact
                      component={PuntoDeVenta}
                    />
                    <Route
                      path="/MiTienda/Pedidos/:id/:factura"
                      exact
                      component={Venta}
                    />
                    <Route
                      path="/MiTienda/Remitos/:id/:clienteid"
                      exact
                      component={Remito}
                    />
                    <Route
                      path="/MiTienda/Presupuestos"
                      exact
                      component={ListadoPresupuestos}
                    />
                    <Route
                      path="/MiTienda/Presupuestos/:id"
                      exact
                      component={Presupuesto}
                    />
                    <Route
                      path="/MiTienda/Clientes"
                      exact
                      component={AdministrarClientes}
                    />
                    <Route
                      path="/MiTienda/Marcas"
                      exact
                      component={MarcasProductos}
                    />
                    <Route
                      path="/Mitienda/GoogleShopping"
                      exact
                      component={GoogleShopping}
                    />
                    <Route path="/Mitienda/MetaAds" exact component={MetaAds} />
                    <Route
                      path="/Mitienda/GoogleAnalytics"
                      exact
                      component={GoogleAnalytics}
                    />
                    <Route
                      path="/Mitienda/FacebookPixel"
                      exact
                      component={FacebookPixel}
                    />
                    <Route
                      path="/Mitienda/GraficoDeVentas"
                      exact
                      component={GraficoVentas}
                    />
                    <Route
                      path="/Mitienda/Parametros"
                      exact
                      component={ParametrosTienda}
                    />
                    {/* <Route
                      path="/Mitienda/AumentoMasivo"
                      exact
                      component={AumentoMasivo}
                    /> */}
                    <Route
                      path="/Mitienda/AumentoMasivo"
                      exact
                      render={(props) =>
                        hasAdminProfile() ? (
                          <AumentoMasivo {...props} />
                        ) : (
                          <Redirect to="/Mitienda/Administrar" />
                        )
                      }
                    />
                    <Route
                      path="/Mitienda/Descuentos"
                      exact
                      component={Descuentos}
                    />
                    <Route
                      path="/Mitienda/Rentabilidad"
                      exact
                      component={Rentabilidad}
                    />
                    <Route
                      path="/Mitienda/CotizarMoneda"
                      exact
                      component={Cotizacion}
                    />
                    <Route path="/Mitienda/Medidas" exact component={Tallas} />
                    <Route
                      path="/Mitienda/Colores"
                      exact
                      component={ColoresTienda}
                    />
                    <Route path="/Mitienda/Monedas" exact component={Monedas} />
                    <Route
                      path="/Mitienda/Proveedores"
                      exact
                      component={Proveedores}
                    />
                    <Route
                      path="/Mitienda/AgregarProveedor"
                      exact
                      component={Proveedor}
                    />
                    <Route
                      path="/Mitienda/ReporteListaDePrecios"
                      exact
                      component={ReporteListaDePrecios}
                    />
                    <Route
                      path="/Mitienda/SubdiarioCompras"
                      exact
                      component={SubdiarioCompras}
                    />
                    <Route
                      path="/Mitienda/SubdiarioVentas"
                      exact
                      component={SubdiarioVentas}
                    />
                    <Route
                      path="/Mitienda/StockVendido"
                      exact
                      component={StockVendido}
                    />
                    <Route
                      path="/Mitienda/FacturasElectronicas"
                      exact
                      component={FacturasElectronicas}
                    />
                    <Route
                      path="/Mitienda/RentabilidadProductos"
                      exact
                      component={RentabilidadProductos}
                    />
                    <Route
                      path="/Mitienda/Rentabilidad"
                      exact
                      component={Rentabilidad}
                    />
                    <Route
                      path="/Mitienda/CuentaCorrienteDeudas"
                      exact
                      component={CuentaCorrienteDeudas}
                    />
                    <Route
                      path="/Mitienda/Promociones"
                      exact
                      component={Promociones}
                    />
                    <Route
                      path="/Mitienda/ProductosHome"
                      exact
                      component={ProductosHome}
                    />
                    <Route
                      path="/Mitienda/ReportePorVendedor"
                      exact
                      component={ReportePorVendedor}
                    />
                    <Route
                      path="/Mitienda/FacturaProveedor"
                      exact
                      component={FacturasProveedor}
                    />
                    {/* <Route
                      path="/Mitienda/CrearUsuario"
                      exact
                      component={CrearUsuario}
                    /> */}
                    <Route
                      path="/Mitienda/CrearUsuario"
                      exact
                      render={(props) =>
                        hasAdminProfile() ? (
                          <CrearUsuario {...props} />
                        ) : (
                          <Redirect to="/Mitienda/Home" />
                        )
                      }
                    />
                    {/* <Route
                      path="/Mitienda/EditarUsuario"
                      exact
                      component={EditarUsuarioTienda}
                    /> */}
                    <Route
                      path="/Mitienda/EditarUsuario"
                      exact
                      render={(props) =>
                        hasAdminProfile() ? (
                          <EditarUsuarioTienda {...props} />
                        ) : (
                          <Redirect to="/Mitienda/Home" />
                        )
                      }
                    />
                    <Route
                      path="/Mitienda/ImportarProducto"
                      exact
                      component={ArchivosImportar}
                    />
                    <Route
                      path="/Mitienda/ImportarListaPrecios"
                      exact
                      component={ArchivosImportarListaPrecios}
                    />
                    <Route
                      path="/Mitienda/ImportarStock"
                      exact
                      component={ArchivosImportarStock}
                    />
                    <Route
                      path="/Mitienda/CuentasCorrientes"
                      exact
                      component={GestionCuentasCorrientes}
                    />
                    <Route
                      path="/Mitienda/CuentasCorrientes/Detalle/:clienteid"
                      exact
                      component={DetalleCuentaCorriente}
                    />
                    <Route
                      path="/Mitienda/CuentasCorrientesProveedores"
                      exact
                      component={GestionCuentasCorrientesProveedor}
                    />
                    <Route
                      path="/Mitienda/CuentasCorrientesProveedores/Detalle/:proveedorid"
                      exact
                      component={DetalleCuentaCorrienteProveedor}
                    />
                    {/* <Route
                      path="/Mitienda/GestionStock"
                      exact
                      component={GestionStock}
                    /> */}
                    <Route
                      path="/MiTienda/GestionStock"
                      exact
                      render={(props) =>
                        hasAdminProfile() ? (
                          <GestionStock {...props} />
                        ) : (
                          <Redirect to="/MiTienda/Home" />
                        )
                      }
                    />
                    <Route
                      path="/Mitienda/ReportePedidos"
                      exact
                      component={ReportePedidos}
                    />
                    <Route
                      path="/Mitienda/ReporteCuentasCorrientes"
                      exact
                      component={ReporteCuentasCorrientes}
                    />
                    <Route
                      path="/Mitienda/ReporteEntregas"
                      exact
                      component={ReporteEntregas}
                    />
                    <Route
                      path="/Mitienda/ReporteStatusLiquidacion"
                      exact
                      component={ReporteStatusLiquidacion}
                    />
                    {/* <Route
                      path="/Mitienda/GestionRemitos"
                      exact
                      component={GestionRemitos}
                    /> */}
                    <Route
                      path="/Mitienda/GestionRemitos"
                      exact
                      render={() =>
                        hasAdminProfile() ? (
                          <GestionRemitos />
                        ) : (
                          <Redirect to="/MiTienda/PuntoDeVenta" />
                        )
                      }
                    />
                    <Route
                      path="/Mitienda/GestionLiquidaciones"
                      exact
                      component={GestionLiquidaciones}
                    />
                    <Route
                      path="/MiTienda/Liquidaciones"
                      exact
                      component={Liquidaciones}
                    />
                    <Route
                      path="/MiTienda/Liquidaciones/:id/:clienteid"
                      exact
                      component={Liquidacion}
                    />
                  </Switch>
                </LayoutMiTienda>
              </Route>
            ) : (
              <NewLoing isAuthenticated={isAuthenticated} />
            )}

            {estaLogueado ? (
              <Route>
                <Layout>
                  <Switch>
                    <Route path="/Home" exact component={Home} />
                    <Route
                      path="/CrearUsuario"
                      exact
                      component={CrearUsuario}
                    />
                    <Route
                      path="/EditarUsuario"
                      exact
                      component={EditarUsuarioTienda}
                    />
                  </Switch>
                </Layout>
              </Route>
            ) : (
              <NewLoing isAuthenticated={isAuthenticated} />
            )}

            <Route path="*">
              <Redirect to="/404" />
            </Route>
          </Switch>
        </Router>
      </div>
    </>
  );
};

export default App;
