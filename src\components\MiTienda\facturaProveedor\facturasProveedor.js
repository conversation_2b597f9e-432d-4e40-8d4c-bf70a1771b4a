import { TextField } from "@material-ui/core";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Delete,
  Edit,
  FilterList,
  FormatListBulleted,
  PictureAsPdf,
  Print,
  Search,
} from "@mui/icons-material";
import React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  crearFacturaProveedor,
  getFacturasProveedor,
  getListadoProveedorByName,
  getMonedas,
  getProveedoresSinPaginado,
  getTiposComprobantes,
} from "../../../redux/actions/mitienda.actions";
import { useSelector } from "react-redux";
import Pagination from "@mui/material/Pagination";
import { useState } from "react";
import {
  Button,
  FormControl,
  InputAdornment,
  InputLabel,
  ListSubheader,
  MenuItem,
  Select,
  Snackbar,
} from "@mui/material";
import MuiAlert from "@mui/material/Alert";
import { ModalEditarFactura } from "./editarFactura";
import { ModalEliminarFactura } from "./eliminarFactura";
import { ModalAgregarItems } from "./modalAgregarItem";
import { ModalRegistrarPago } from "./modalRegistrarPago";
import { ModalPdf } from "./modalPdf";
import { ModalFiltrosFacturaProveedor } from "./modalFiltros";
import ClipLoader from "react-spinners/ClipLoader";

export const FacturasProveedor = () => {
  const info = useSelector((state) => state.mitienda.facturasProveedor);
  const paginaUltima = useSelector(
    (state) => state.mitienda.paginaUltimaFacturasProveedor
  );
  const tiposComprobantes = useSelector(
    (state) => state.mitienda.tiposComprobantes
  );
  const okCrear = useSelector(
    (state) => state.mitienda.okAgregarFacturaProveedor
  );
  const okEditar = useSelector(
    (state) => state.mitienda.okEditarFacturaProveedor
  );
  const monedas = useSelector((state) => state.mitienda.monedas);
  const okAgregarArticulo = useSelector(
    (state) => state.mitienda.okAgregarItemFacturaProveedor
  );
  const okRegistrarPagoFacturaProveedor = useSelector(
    (state) => state.mitienda.okRegistrarPagoFacturaProveedor
  );
  const okEliminarFacturaProveedor = useSelector(
    (state) => state.mitienda.okEliminarFacturaProveedor
  );
  const okEliminarItemFacturaProveedor = useSelector(
    (state) => state.mitienda.okEliminarItemFacturaProveedor
  );

  const proveedores = useSelector((state) => state.mitienda.proveedoresByName);

  const dispatch = useDispatch();

  let fechaActual = new Date().toJSON().slice(0, 10);

  const [factura, setFactura] = useState("");

  const [input, setInput] = useState({
    proveedorid: "",
    fecha_emision: fechaActual,
    monedaid: "",
    letra: "",
    tipo_comprob: "",
    punto_comprob: "",
    numero_comprob: "",
    observaciones: "",
  });

  let pathname = window.location.pathname;

  let permisos_acciones = Object.values(
    JSON.parse(localStorage.getItem("permisos_acciones"))
  ).filter((p) =>
    p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase())
  )[0];

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
    setPage(value);
  };

  const [fecha_desde, setFecha_desde] = useState("");
  const handleChangeFecha_desde = (e) => {
    setFecha_desde(e.target.value);
  };

  const [fecha_hasta, setFecha_hasta] = useState("");
  const handleChangeFecha_hasta = (e) => {
    setFecha_hasta(e.target.value);
  };

  const [nombre, setNombre] = useState("");
  const handleChangeNombre = (e) => {
    setNombre(e.target.value);
  };

  const [numero_comprob, setNumero_comprob] = useState("");
  const handleChangeNumero_comprob = (e) => {
    setNumero_comprob(e.target.value);
  };

  const [proveedorid, setProveedorid] = useState("");
  const handleChangeProveedorid = (e) => {
    setProveedorid(e.target.value);
  };

  const [error, setError] = useState(false);
  const [errorComp, setErrorComp] = useState(false);
  const [errorLetra, setErrorLetra] = useState(true);

  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.name === "letra") {
      let aux = isNaN(e.target.value);
      setErrorLetra(aux);
    }
    if (e.target.name === "numero_comprob") {
      let aux = isNaN(e.target.value);
      setError(aux);
    }
    if (e.target.name === "punto_comprob") {
      let aux = isNaN(e.target.value);
      setErrorComp(aux);
    }
    setInput({ ...input, [e.target.name]: e.target.value });
  };

  const handleCheck = (e) => {
    e.preventDefault();
    if (e.target.checked === true) {
      setInput({ ...input, [e.target.name]: "1" });
    } else {
      setInput({ ...input, [e.target.name]: "0" });
    }
  };

  const [show, setShow] = useState("");
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const [showEditar, setShowEditar] = useState(false);
  const handleCloseEditar = () => setShowEditar(false);
  const handleShowEditar = (e, row) => {
    e.preventDefault();
    setShowEditar(true);
    setFactura(row);
  };

  const [showEliminar, setShowEliminar] = useState(false);
  const handleCloselimintar = () => setShowEliminar(false);
  const handleShowEliminar = (e, row) => {
    e.preventDefault();
    setShowEliminar(true);
    setFactura(row);
  };

  const [showPdf, setShowPdf] = useState("");
  const handleClosePdf = () => setShowPdf(false);
  const handleShowPdf = (p) => {
    setFactura(p);
    setShowPdf(true);
  };

  const [showPagos, setShowPagos] = useState("");
  const handleClosePagos = () => setShowPagos(false);
  const handleShowPagos = (p) => {
    setFactura(p);
    setShowPagos(true);
  };

  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });

  const [openAgregar, setOpenAgregar] = React.useState(false);
  const handleClickAlertAgregar = () => {
    setOpenAgregar(true);
  };
  const handleCloseAlertAgregar = () => {
    setOpenAgregar(false);
  };

  const [openEditar, setOpenEditar] = React.useState(false);
  const handleClickAlertEditar = () => {
    setOpenEditar(true);
  };
  const handleCloseAlertEditar = () => {
    setOpenEditar(false);
  };

  const [openEliminar, setOpenEliminar] = React.useState(false);
  const handleClickAlertEliminar = () => {
    setOpenEliminar(true);
  };
  const handleCloseAlertEliminar = () => {
    setOpenEliminar(false);
  };

  const handleClickAgregar = () => {
    dispatch(crearFacturaProveedor(input));
    setTimeout(function () {
      handleClickAlertAgregar();
    }, 1000);
    setInput({
      proveedorid: "",
      fecha_emision: fechaActual,
      monedaid: "",
      letra: "",
      tipo_comprob: "",
      punto_comprob: "",
      numero_comprob: "",
      observaciones: "",
    });
  };

  const [showAgregarItems, setShowAgregarItems] = useState("");
  const handleCloseAgregarItems = () => setShowAgregarItems(false);
  const handleShowAgregarItems = (e, row) => {
    setShowAgregarItems(true);
    setFactura(row);
  };
  const formatoFecha = (fecha) => {
    let aux = fecha.slice(0, 10);
    let aux2 = aux.split("-");

    return aux2[2] + "-" + aux2[1] + "-" + aux2[0];
  };

  const [nombreProveedor, setNombreProveedor] = useState("");

  const searchByFilters = () => {
    setPage(1);
    dispatch(
      getFacturasProveedor(
        page,
        numero_comprob,
        nombre,
        proveedorid,
        fecha_desde,
        fecha_hasta
      )
    );
    handleClose();
  };

  const reset = (e) => {
    setFecha_desde("");
    setFecha_hasta("");
    setNombre("");
    setNumero_comprob("");
    setProveedorid("");
    setPage(1);
    setNombreProveedor("");
    dispatch(getListadoProveedorByName(""));
    dispatch(getFacturasProveedor(1, "", "", "", "", ""));
    handleClose();
  };

  const [nombreAux, setNombreAux] = useState("");
  const [selectedOption, setSelectedOption] = useState("");

  useEffect(() => {
    if (nombreAux.length > 4) {
      dispatch(getListadoProveedorByName(nombreAux));
    }
  }, [nombreAux]);

  useEffect(() => {
    dispatch(getProveedoresSinPaginado());
    dispatch(getTiposComprobantes());
    dispatch(getMonedas());
  }, []);

  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(false);
  }, [info]);

  useEffect(() => {
    setLoading(true);
    dispatch(
      getFacturasProveedor(
        page,
        numero_comprob,
        nombre,
        proveedorid,
        fecha_desde,
        fecha_hasta
      )
    );
  }, [
    page,
    okCrear,
    okEditar,
    okEliminarItemFacturaProveedor,
    okEliminarFacturaProveedor,
    okAgregarArticulo,
    okRegistrarPagoFacturaProveedor,
  ]);

  return (
    <div className="ventas-container">
      <ModalFiltrosFacturaProveedor
        show={show}
        handleClose={handleClose}
        search={searchByFilters}
        reset={reset}
        fecha_desde={fecha_desde}
        handleChangeFecha_desde={handleChangeFecha_desde}
        fecha_hasta={fecha_hasta}
        handleChangeFecha_hasta={handleChangeFecha_hasta}
        numero_comprob={numero_comprob}
        handleChangeNumero_comprob={handleChangeNumero_comprob}
        proveedorid={proveedorid}
        setProveedorID={setProveedorid}
        nombreProveedor={nombreProveedor}
        setNombreProveedor={setNombreProveedor}
      />

      <ModalEditarFactura
        show={showEditar}
        handleClickAlert={handleClickAlertEditar}
        handleClose={handleCloseEditar}
        info={factura}
      />

      <ModalEliminarFactura
        show={showEliminar}
        handleClickAlert={handleClickAlertEliminar}
        handleClose={handleCloselimintar}
        id={factura}
      />

      <ModalAgregarItems
        show={showAgregarItems}
        handleClose={handleCloseAgregarItems}
        info={factura}
      />

      <ModalRegistrarPago
        show={showPagos}
        handleClose={handleClosePagos}
        factura={factura}
      />

      <ModalPdf show={showPdf} handleClose={handleClosePdf} info={factura} />

      <Snackbar
        open={openAgregar}
        autoHideDuration={10000}
        onClose={handleCloseAlertAgregar}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseAlertAgregar}
          severity={okCrear.success ? "success" : "error"}
          sx={{ width: 500 }}
        >
          <h5>{okCrear.mensaje}</h5>
        </Alert>
      </Snackbar>
      <Snackbar
        open={openEditar}
        autoHideDuration={10000}
        onClose={handleCloseAlertEditar}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseAlertEditar}
          severity={okEditar.success ? "success" : "error"}
          sx={{ width: 500 }}
        >
          <h5>{okEditar.mensaje}</h5>
        </Alert>
      </Snackbar>
      <Snackbar
        open={openEliminar}
        autoHideDuration={10000}
        onClose={handleCloseAlertEliminar}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseAlertEliminar}
          severity={okEliminarFacturaProveedor.success ? "success" : "error"}
          sx={{ width: 500 }}
        >
          <h5>{okEliminarFacturaProveedor.mensaje}</h5>
        </Alert>
      </Snackbar>

      <div className="titulo-ventas">
        <h3 className="text-ventas">Facturas de compra</h3>
      </div>

      <Paper className="paper-form" style={{ marginLeft: "15px" }}>
        <FormControl
          fullWidth
          style={{ marginTop: 5, marginBottom: 10 }}
          size="small"
        >
          <InputLabel id="search-select-label">Proveedor</InputLabel>
          <Select
            style={{ height: 40 }}
            MenuProps={{ autoFocus: false, disablePortal: true }}
            labelId="search-select-label"
            id="search-select"
            value={selectedOption.proveedorid || ""}
            label="Proveedor"
            onClose={() => {
              dispatch(getListadoProveedorByName(""));
            }}
            size="small"
            renderValue={() => selectedOption.nombre}
          >
            <ListSubheader>
              <TextField
                size="small"
                autoFocus
                placeholder="Buscar proveedor..."
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                inputProps={{
                  style: {
                    height: 35,
                    fontSize: 17,
                  },
                }}
                onChange={(e) => setNombreAux(e.target.value)}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            </ListSubheader>
            {proveedores.map((option, i) => (
              <MenuItem
                key={i}
                value={option.clienteid}
                onClick={() => {
                  setSelectedOption(option);
                  setNombreAux(option.nombre);
                  setInput({
                    ...input,
                    proveedorid: option.proveedorid,
                  });
                }}
              >
                {option.nombre}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl fullWidth style={{ marginTop: 15, marginBottom: 15 }}>
          <InputLabel id="tipo_comprob-select-label">
            Tipo comprobante
          </InputLabel>
          <Select
            labelId="tipo_comprob-select-label"
            id="tipo_comprob"
            label="Tipo comprobante"
            size="small"
            name="tipo_comprob"
            value={input.tipo_comprob}
            onChange={handleChange}
            style={{ height: 45 }}
          >
            <MenuItem value="">Seleccione una opcion</MenuItem>
            {tiposComprobantes &&
              tiposComprobantes.map((t) => (
                <MenuItem value={t.tipofacturaid} key={t.tipofacturaid}>
                  {t.nombre}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
        <FormControl style={{ marginTop: 15, marginBottom: 15 }} fullWidth>
          <InputLabel id="monedalabel">Moneda</InputLabel>
          <Select
            labelId="monedalabel"
            id="moneda"
            label="Moneda"
            size="small"
            name="monedaid"
            value={input.monedaid || ""}
            onChange={handleChange}
            style={{ height: 45 }}
          >
            <MenuItem value={0}>Seleccione una opcion</MenuItem>
            {monedas &&
              monedas.map((m) => (
                <MenuItem value={m.monedaid} key={m.monedaid}>
                  {m.nombre}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          type="date"
          variant="outlined"
          name="fecha_emision"
          label="Fecha de factura"
          fullWidth
          margin="normal"
          onChange={(e) => handleChange(e)}
          value={input.fecha_emision}
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          variant="outlined"
          label="Letra"
          fullWidth
          margin="normal"
          onChange={(e) => handleChange(e)}
          name="letra"
          value={input.letra}
          error={errorLetra === false}
          helperText={errorLetra === false && "Ingrese una letra"}
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          variant="outlined"
          label="Punto comprobante"
          fullWidth
          margin="normal"
          onChange={(e) => handleChange(e)}
          name="punto_comprob"
          value={input.punto_comprob}
          error={errorComp}
          helperText={errorComp && "Ingrese un numero valido"}
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          variant="outlined"
          label="N&uacute;mero comprobante"
          fullWidth
          margin="normal"
          onChange={(e) => handleChange(e)}
          name="numero_comprob"
          value={input.numero_comprob}
          error={error}
          helperText={error && "Ingrese un numero valido"}
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          inputProps={{
            style: {
              height: 40,
              fontSize: 17,
            },
          }}
        />
        <TextField
          style={{ marginTop: 15, marginBottom: 15 }}
          variant="outlined"
          label="Observaci&oacute;n"
          fullWidth
          margin="normal"
          onChange={(e) => handleChange(e)}
          name="observaciones"
          value={input.observaciones}
          InputLabelProps={{
            style: {
              marginBottom: 10,
              marginTop: -7,
            },
          }}
          multiline
          minRows={3}
        />
      </Paper>
      <div align="right">
        <Button
          disabled={
            input.numero_comprob === "" ||
            input.proveedorid === "" ||
            input.punto_comprob === "" ||
            permisos_acciones.agregar === "0"
          }
          size="large"
          variant="contained"
          onClick={(e) => {
            handleClickAgregar(e);
            setNombreAux("");
            setSelectedOption("");
            dispatch(getListadoProveedorByName(""));
          }}
          sx={{ mt: 3, mr: 3 }}
        >
          Guardar
        </Button>
      </div>

      <div style={{ display: "flex", marginLeft: 50 }}>
        <Button
          variant="outlined"
          sx={{ height: 40, width: 150, marginTop: 2 }}
          onClick={handleShow}
        >
          <FilterList /> Filtrar
        </Button>
        {/* <Button variant="outlined" 
                sx={{height:40,width:150, marginTop:2, marginLeft:5}} 
                onClick={downloadExcel}>
                    <Download/> Exportar
                </Button> */}
      </div>

      <TableContainer
        component={Paper}
        sx={{
          zIndex: 99,
          marginTop: 5,
          width: "95%",
          alignSelf: "center",
          marginLeft: 3,
          marginBottom: 5,
        }}
      >
        <Table aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                N&uacute;mero
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Fecha factura
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Comprobante
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Proveedor
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Monto
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Pago
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Saldo
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Agregar items
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                PDF
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Imprimir
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Pagos
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Modificar
              </TableCell>
              <TableCell
                style={{ fontWeight: "bold", fontSize: "110%" }}
                align="center"
              >
                Borrar
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow sx={{ p: 20 }}>
                <TableCell colSpan={13} align="center">
                  <ClipLoader loading={loading} size={50} />
                </TableCell>
              </TableRow>
            ) : info.data.length > 0 ? (
              info.data.map((row) => (
                <TableRow
                  key={row.facturacompraid}
                  sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                >
                  <TableCell
                    style={{
                      fontSize: "15px",
                      width: "100px",
                      height: "40px",
                      padding: 1,
                    }}
                    align="center"
                  >
                    {row.numero_comprob}
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "15px",
                      width: "150px",
                      height: "40px",
                      padding: 0,
                    }}
                    align="center"
                  >
                    {formatoFecha(row.fechafactura)}
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "15px",
                      width: "200px",
                      height: "40px",
                      padding: 0,
                    }}
                    align="center"
                  >
                    {row.comprobante}
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "15px",
                      width: "200px",
                      height: "40px",
                      padding: 0,
                    }}
                    align="center"
                  >
                    {row.nombreproveedor}
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "175x",
                      width: "150px",
                      height: "40px",
                      padding: 0,
                    }}
                    align="center"
                  >
                    ${row.total}
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "15px",
                      width: "150px",
                      height: "40px",
                      padding: 0,
                    }}
                    align="center"
                  >
                    ${row.totalpago}
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "15px",
                      width: "150px",
                      height: "40px",
                      padding: 0,
                    }}
                    align="center"
                  >
                    ${row.saldo}
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "100%" }}
                    align="center"
                    onClick={(e) => handleShowAgregarItems(e, row)}
                  >
                    <div>
                      <FormatListBulleted />
                    </div>
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "17px",
                      width: "50px",
                      height: "40px",
                      padding: 0,
                      cursor: "pointer",
                    }}
                    align="center"
                    onClick={(e) => handleShowPdf(row)}
                  >
                    <PictureAsPdf />
                  </TableCell>
                  <TableCell
                    style={{
                      fontSize: "17px",
                      width: "50px",
                      height: "40px",
                      padding: 0,
                    }}
                    align="center"
                  >
                    {/* <a 
                                href={`${process.env.REACT_APP_SERVER}dompdf/generarcomprobante_pdf.php?tipocomprobante=136&codigocomprobante=${row.facturacompraid}&tipoaccionpdf=1`}
                                target="_blank"
                                style={{color:"black"}}
                            >  */}
                    <a
                      href={`${
                        process.env.REACT_APP_SERVER
                      }dompdf/generarcomprobante_pdf.php?tipocomprobante=136&codigocomprobante=${
                        row.facturacompraid
                      }&tipoaccionpdf=1&tiendaid=${localStorage.getItem(
                        "tiendausuario"
                      )}`}
                      target="_blank"
                      style={{ color: "black" }}
                    >
                      <Print />
                    </a>
                  </TableCell>
                  <TableCell
                    style={{ fontSize: "100%" }}
                    align="center"
                    onClick={() => handleShowPagos(row)}
                  >
                    <div>
                      <AttachMoney />
                    </div>
                  </TableCell>
                  <TableCell style={{ fontSize: "100%" }} align="center">
                    <Button
                      style={{ color: "black" }}
                      disabled={permisos_acciones.modificar === "0"}
                      onClick={(e) => handleShowEditar(e, row)}
                    >
                      <Edit />
                    </Button>
                  </TableCell>
                  <TableCell style={{ fontSize: "100%" }} align="center">
                    <Button
                      onClick={(e) => handleShowEliminar(e, row)}
                      style={{ color: "black" }}
                      disabled={permisos_acciones.eliminar === "0"}
                    >
                      <Delete />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4}></TableCell>
                <TableCell colSpan={4} align="center">
                  <h5>No hay informaci&oacute;n para mostrar</h5>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <Pagination
        count={paginaUltima}
        page={page}
        onChange={handleChangePage}
        size="large"
        sx={{ alignSelf: "center" }}
      />
    </div>
  );
};
