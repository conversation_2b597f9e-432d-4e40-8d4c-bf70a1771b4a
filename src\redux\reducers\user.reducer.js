const initialState = {
  status: {
    ok: false,
    info: ""
  },
  okLogin: '',
  menu:[],
  recover: "",
  registrar_usuario:"",
  usuario_tienda:"",
  editar_usuario:"",
  okRegistrarTienda: ""
};


const userReducer = (state = initialState, { type, payload }) => {
  switch (type) {
    case "USER_LOGED":
      return {
        ...state,
        status: {
          ok: payload.success,
          info: payload.error || payload.nombreusuario
        },
        okLogin: payload
      };
    case "USER_LOGEDOUT":
      return {
        ...state,
        status: {
          ok: false,
        }
      };
    case "GET_MENU":
      return {
        ...state,
        menu: payload
      };
    case "RECOVER_PASSWORD":
      return {
        ...state,
        recover: payload
      };
    case "CLEAN_RECOVERY_PASS":
      return {
        ...state,
        recover: payload
      };
    case "REGISTRAR_USUARIO_TIENDA":
      return {
        ...state,
        registrar_usuario: payload
      };
    case "EDITAR_USUARIO_TIENDA":
      return {
        ...state,
        editar_usuario: payload
      };
    case "GET_USUARIO_TIENDA":
      return {
        ...state,
        usuario_tienda: payload
      };
    case "REGISTRAR_TIENDA":
      return {
        ...state,
        okRegistrarTienda: payload
      };
    default:
      return state;
  }
};


export default userReducer