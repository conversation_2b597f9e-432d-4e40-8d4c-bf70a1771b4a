import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { enviarMensaje } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalMensaje = ({show, handleClose, pedido, handleClickAlert}) =>{

    const dispatch = useDispatch()



    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [input, setInput] = useState({
        titulo: '',
        mensaje: '',
        usuariodestinoid: '',
        pedidoid: ''
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(enviarMensaje(input))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        setInput({
            titulo: `Pedido #${pedido[0].pedidoplacaid}`,
            mensaje: '',
            usuariodestinoid: pedido[0].clienteid,
            pedidoid: pedido[0].pedidoplacaid
        })
    }

    useEffect(() =>{
        if(pedido){
            setInput({
                titulo: `Pedido #${pedido[0].pedidoplacaid}`,
                mensaje: '',
                usuariodestinoid: pedido[0].clienteid,
                pedidoid: pedido[0].pedidoplacaid
            })
        }
    },[pedido])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Pedido #{pedido[0].pedidoplacaid} - Enviar mensaje</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>   
                <TextField
                    fullWidth
                    style={{marginTop:20,marginBottom:20}}
                    label="Titulo"  
                    variant="outlined"  
                    name="titulo"
                    value={input.titulo}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                  />
                <TextField
                    fullWidth
                    style={{marginTop:20,marginBottom:20}}
                    label="Mensaje"  
                    variant="outlined"  
                    name="mensaje"
                    value={input.mensaje}
                    onChange={(e)=>handleChange(e)}
                    multiline
                    minRows={8}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                  />
                </div>
                <Divider/>
                <div align="right">
                    <Button 
                    disabled={input.mensaje === ""}
                    size="large"
                    variant="contained"
                    onClick={handleOnClick}
                    sx={{mt: 3, mr:1}}>Enviar</Button>
                </div>
        </Modal>
    )
}