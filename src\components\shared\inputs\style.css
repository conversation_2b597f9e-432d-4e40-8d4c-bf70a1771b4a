
.input-wrapper{
  text-align: left!important;
}
input,
textarea,
select {
  border: none;
  background-color: transparent;
  padding: 4px 0px;
  outline: none;
  font-size: 14px;
}

label{
  margin: 0%;
  text-transform: capitalize;
  font-size: 14px;
}

.input-wrapper option{
  border-radius: 0%;
  background-color: #383838;
}
.input-wrapper option::selection{
  background-color: orange;
  color: black;
}


.input-wrapper {
  border:1px solid rgba(223, 223, 223, 0.15);
  padding:4px;
  min-width: 150px;
  min-height:41px
}
.input-wrapper textarea {
  border:1px solid rgba(223, 223, 223, 0.15);
  padding:12px
}
input[type="file"] {
  display: none;
}
.file-input {
  border: dashed 1px orange;
  border-radius: 5px;
}