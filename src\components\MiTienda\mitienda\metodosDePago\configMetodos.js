import React, { useState, useEffect } from "react"
import <PERSON><PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel, TextField } from "@mui/material";
import "./metodosDePago.css"
import { useDispatch, useSelector } from "react-redux";
import { getConfigMP, modificarMetodosDePago } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ConfigMetodos = ({show, nombre, handleClose, handleClickAlert}) => {

    
    const config = useSelector((state) => state.mitienda.configMP)
    const defaultMp = useSelector((state) => state.mitienda.defaultMp)

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()) || p.archivo.toLowerCase().includes("Mitienda/MetodosDePago".toLocaleLowerCase()))[0]

    const dispatch = useDispatch()

    const [input, setInput] = useState('')
    const [contado, setContado] = useState()
    const [mercadoLibre, setMercadoLibre] = useState('')

    const handleCheckContado = (e) => {
        e.preventDefault()
        if(e.target.checked === true){
            setContado("1")
        }else{
            setContado("0")
        }
    }

    const handleCheckMercadoLibre = (e) => {
        e.preventDefault()
        if(e.target.checked === true){
            setMercadoLibre("1")
        }else{
            setMercadoLibre("0")
        }
    }

    const handleKeys = (e) => {
        e.preventDefault()
        setInput({...input, [e.target.name]: e.target.value})
    }

    const handleConfigContado = () => {
        dispatch(modificarMetodosDePago(1,contado,'',''))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
          }, 1000);
    }

    const handleConfigMercadoLibre = () => {
        dispatch(modificarMetodosDePago(2,mercadoLibre,input.publicKey,input.privateKey))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
          }, 1000);
    }

    useEffect(() =>{
            dispatch(getConfigMP())
    }, [])

    useEffect(() => {
        setContado(defaultMp.default_efectivo)
        setMercadoLibre(defaultMp.default_mercadopago)
    }, [])

    useEffect(() => {
        setInput({publicKey: config.mppublickey, 
            privateKey: config.mpprivatekey })
    }, [config])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Configuraci&oacute;n {nombre}</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                {
                    nombre === "Mercado Pago" ?
                    <div style={{marginTop:15, marginBottom:15}}>
                        <div style={{marginTop:15, marginBottom:15}}>
                            <FormControlLabel
                                control={
                                <Checkbox disabled={permisos_acciones?.modificar === "0"}/>
                                }
                                style={{color:"black"}}
                                onChange={(e)=> handleCheckMercadoLibre(e)}
                                checked={mercadoLibre == "1" ? true : false}
                                label="Activar/Desactivar Mercado Pago"
                            />
                        </div>
                        <TextField 
                            id="nombre-tienda" 
                            label="Publichable Key (Credenciales Mercado Pago)"  
                            variant="outlined" 
                            fullWidth margin="normal" 
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                                }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                            value={input.publicKey || ''}
                            onChange={(e) => handleKeys(e)}
                            name="publicKey"
                        />
                        <TextField
                            id="nombre-tienda" 
                            label="Access Token (Credenciales Mercado Pago)"    
                            variant="outlined" 
                            fullWidth margin="normal" 
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                                }}
                            inputProps={{
                                style: {
                                    height: 40,
                                    fontSize:17
                                },
                            }}
                            value={input.privateKey || ''}
                            onChange={(e) => handleKeys(e)}
                            name="privateKey"
                        />
                    </div> :

                    nombre == "Contado" ?
                    <div style={{marginTop:15, marginBottom:15}}>
                        <FormControlLabel
                            control={
                            <Checkbox disabled={permisos_acciones?.modificar === "0"}/>
                            }
                            style={{color:"black"}}
                            onChange={(e)=> handleCheckContado(e)}
                            checked={contado == "1" ? true : false}
                            label="Activar/Desactivar contado"
                        />
                    </div> 

                    // nombre == "TRANSFERENCIA" ?
                    // <div style={{marginTop:15, marginBottom:15}}>
                    //     <FormControlLabel
                    //         control={
                    //         <Checkbox/>
                    //         }
                    //         style={{color:"black"}}
                    //         onChange={(e)=> handleCheck(e)}
                    //         // value={input.sucursal}
                    //         label="Activar/Desactivar transferencia"
                    //     />
                    // </div>  :

                    // nombre == "CHEQUE" ?
                    // <div style={{marginTop:15, marginBottom:15}}>
                    //     <FormControlLabel
                    //         control={
                    //         <Checkbox/>
                    //         }
                    //         style={{color:"black"}}
                    //         onChange={(e)=> handleCheck(e)}
                    //         // value={input.sucursal}
                    //         label="Activar/Desactivar cheque"
                    //     />
                    // </div>  :

                    // nombre == "TARJETA" ?
                    // <div style={{marginTop:15, marginBottom:15}}>
                    //     <FormControlLabel
                    //         control={
                    //         <Checkbox/>
                    //         }
                    //         style={{color:"black"}}
                    //         onChange={(e)=> handleCheck(e)}
                    //         // value={input.sucursal}
                    //         label="Activar/Desactivar tarjeta"
                    //     />
                    // </div>  : 

                    // nombre == "CUENTA CORRIENTE" ?
                    // <div style={{marginTop:15, marginBottom:15}}>
                    //     <FormControlLabel
                    //         control={
                    //         <Checkbox/>
                    //         }
                    //         style={{color:"black"}}
                    //         onChange={(e)=> handleCheck(e)}
                    //         // value={input.sucursal}
                    //         label="Activar/Desactivar cuenta corriente"
                    //     />
                    // </div>  :

                    // nombre == "RETENCION" ?
                    // <div style={{marginTop:15, marginBottom:15}}>
                    //     <FormControlLabel
                    //         control={
                    //         <Checkbox/>
                    //         }
                    //         style={{color:"black"}}
                    //         onChange={(e)=> handleCheck(e)}
                    //         // value={input.sucursal}
                    //         label="Activar/Desactivar retencion"
                    //     />
                    // </div>  :

                    // nombre == "DEUDA" ?
                    // <div style={{marginTop:15, marginBottom:15}}>
                    //     <FormControlLabel
                    //         control={
                    //         <Checkbox/>
                    //         }
                    //         style={{color:"black"}}
                    //         onChange={(e)=> handleCheck(e)}
                    //         // value={input.sucursal}
                    //         label="Activar/Desactivar deuda"
                    //     />
                    // </div>  : 

                    // nombre == "AJUSTE" ?
                    // <div style={{marginTop:15, marginBottom:15}}>
                    //     <FormControlLabel
                    //         control={
                    //         <Checkbox/>
                    //         }
                    //         style={{color:"black"}}
                    //         onChange={(e)=> handleCheck(e)}
                    //         // value={input.sucursal}
                    //         label="Activar/Desactivar ajuste"
                    //     />
                    // </div>  
                    :  null
                }                
                <Divider/>
                {
                    nombre === "Mercado Pago" ? 
                    <div align="right">
                    <Button 
                        size="large" 
                        variant="contained" 
                        sx={{mt: 3}}
                        onClick={handleConfigMercadoLibre}
                    >Guardar</Button>
                    </div> :
                     <div align="right">
                     <Button 
                         size="large" 
                         variant="contained" 
                         sx={{mt: 3}}
                         onClick={handleConfigContado}
                     >Guardar</Button>
                 </div>
                }
        </Modal>
    )
}