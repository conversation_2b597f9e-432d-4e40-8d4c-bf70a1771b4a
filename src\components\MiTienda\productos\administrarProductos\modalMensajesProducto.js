import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { useDispatch, useSelector } from "react-redux";
import { getMensajesProducto } from "../../../../redux/actions/mitienda.actions";
import Alert from '@mui/material/Alert';
import { Check, Clear, Reply } from "@mui/icons-material";
import { ModalResponderMensaje } from "./modalResponderMensaje";
import { Button } from "@mui/material";

Modal.setAppElement("#root")

export const ModalMensajesProducto = ({show, handleClose, producto}) =>{

    const mensajes = useSelector((state) => state.mitienda.messagesByProductoID)
    const okMensaje = useSelector((state) => state.mitienda.okSendMessageProducto)

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "95vh",
          overflow:"hidden",
          borderRadius: "8px",
          maxWidth: "850px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [mensaje, setMensaje] = useState('')
    const [showResponder, setShow] = useState(false);
    const handleCloseResponder = () => setShow(false);
    const handleShow = (row) => {
        setMensaje(row)
        setShow(true);
    }

    const formatoFecha = (fecha) =>{
        let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
    }

    useEffect(() =>{
        if(producto !== ''){
            dispatch(getMensajesProducto(producto.productoid))
        }
    },[producto,okMensaje])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <ModalResponderMensaje
                show={showResponder}
                handleClose={handleCloseResponder}
                mensaje={mensaje}
            />
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Preguntas - Producto {producto.nombreproducto}</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>

                <div style={{height:300, display:"flex", flexDirection:"column", margin:5, overflow:"scroll", overflowX:"hidden"}}>
                    {
                        mensajes.length > 0 ? mensajes.map((row) => 
                            
                            <div style={{margin:5, width:500,
                                alignSelf: `${row.titulo === "Producto" ? "self-start" : "self-end"}`, display:"flex", alignItems:"center"
                            }}>
                                <div style={{margin:5, width:470,
                                    alignSelf: `${row.titulo === "Producto" ? "self-start" : "self-end"}`
                                }}>
                                    <p style={{fontSize:11,
                                        textAlign:`${row.titulo === "Producto" ? "start" : "end"}`
                                    }}>{formatoFecha(row.fecha)}</p>
                                    <Alert icon={false} severity="info" style={{width:470,
                                        alignSelf: `${row.titulo === "Producto" ? "self-start" : "self-end"}`
                                    }}>
                                        <div style={{width:400}}>
                                            <p 
                                                style={{fontSize:13,
                                                textAlign:`${row.titulo === "Producto" ? "start" : "end"}`
                                            }}>{row.descripcion}</p>
                                        </div>

                                        <p style={{fontSize:12,
                                            textAlign:`${row.titulo === "Producto" ? "end" : "start"}`
                                        }}>{row.hora}</p>
                                        
                                    </Alert>
                                </div>
                                {
                                    row.titulo === "Producto" && <Button onClick={() => handleShow(row)} ><Reply/></Button>
                                }
                                {
                                    row.titulo === "Producto" && row.leido === "1" ?
                                    <Button style={{color:"green"}}><Check/></Button> :
                                    row.titulo === "Producto" && row.leido === "0" ?
                                    <Button style={{color:"red"}}><Clear/></Button> : null
                                }
                            </div>

                        ) :
                        <div style={{display:"flex", justifyContent:"center", marginTop:20}}>
                            <h4>No hay preguntas para responder.</h4>
                        </div>
                    }
                </div>
        </Modal>
    )
}