import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, Checkbox, FormControlLabel } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getLocales } from "../../../../redux/actions/mitienda.actions";
import { configRetiroEnLocal } from "../../../../redux/actions/mitienda.actions";
import { useEffect } from "react";

Modal.setAppElement("#root")

export const ModalRetiroLocal = ({show, handleClose, handleClickAlert}) => {

    const metodos = useSelector((state) => state.mitienda.metodos)
    const locales = useSelector((state) => state.mitienda.locales)

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const [input, setInput] = useState()

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const dispatch = useDispatch()

    const handleClick = (e) => {
        e.preventDefault()
        dispatch(configRetiroEnLocal(input))
        handleClose()
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
    }

    const handleCheck = (e) => {
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }

    useEffect(() => {
        setInput(metodos)
    },[])

    useEffect(() =>{
        dispatch(getLocales())
    },[])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Configuracion Retiro en local</h4>
                    <button 
                        onClick={handleClose} 
                        style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}
                    >X</button>
                </div> 
                <Divider/>
                    <div style={{marginTop:15, marginBottom:15}}>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            disabled={permisos_acciones?.activar === "0"}
                            name="default_retirolocal"
                            style={{color:"black"}}
                            onChange={(e)=> handleCheck(e)}
                            checked={input === undefined ?  null : input.default_retirolocal == "1" ? true : false}
                            label="Activar/Desactivar envio a domicilio"
                        />
                    </div>    
                <Divider/>
                <div sx={{height:"50%", marginRight:10, marginTop:6}}>
                    <h4 style={{padding:10}}>Mis locales</h4>                    
                </div>
                {
                    input && input.default_retirolocal == "1" ? 
                    <div>
                        {
                        locales && locales.map((l) =>
                            <div style={{display:"flex"}}>
                                <h5 style={{padding:10}}>{l.nombre}</h5>
                                <h6 style={{padding:10}}>{l.direccion}</h6>
                            </div>
                        )}
                    </div> :
                    <h5 style={{padding:10}}>No hay datos de tus locales</h5>
                }
                <Divider/>
                <div align="right">
                <Button size="large" variant="contained" sx={{mt: 3}} onClick={(e)=> handleClick(e)}>Guardar</Button>
                </div>
                
                {/* <div className="container">
                    <div className="header-miplan">
                        <h3>Retiros en local</h3>
                        {retiroEnLocal && ok === 1 ? <h5 style={{color:"green"}}>Activado con exito!</h5> : null}
                        <Button 
                            size="large" 
                            variant="contained"
                            className="btn-miplan"
                            onClick={handleClick}
                        >{ok === 0 ? "Activar" : "Desactivar"}</Button>
                        </div>
                <h4 style={{color:"grey"}}>En el momento de la compra, los clientes tienen la opcion
                    de elegir en que local o domicilio poder retirarla
                </h4>
                {
                    ok === 1 ?
                    <Paper sx={{height:"50%", marginRight:10, marginTop:5}}>
                        <h4 style={{padding:10}}>Mis locales</h4>
                        <Divider/>
                        <h5 style={{padding:10}}>No hay datos de tus locales</h5>
                    </Paper>
                    : null
                }
                </div> */}
        </Modal>
    )
}