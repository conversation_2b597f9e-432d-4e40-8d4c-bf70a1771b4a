import React, { useEffect, useState } from "react"
import <PERSON>dal from 'react-modal';
import Divider from '@mui/material/Divider';
import pdf from './modelos/listadeprecios.xlsx'

Modal.setAppElement("#root")

export const ModalModelos = ({show, handleClose}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "850px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Importación de datos desde un archivo Excel</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{padding:10}}>
                    <p style={{fontSize:16}}>
                        El formato del archivo creado debe tener la siguiente estructura de datos:
                    </p>
                
                <div style={{padding:10}}> 

                    <p style={{fontSize:14}}>
                        <strong>Ejemplo:</strong> Haga Click&nbsp;
                        <a
                        href={pdf}
                        download="ejemplolistadeprecios.xlsx"
                        target="_blank"
                        rel="noreferrer"
                        >
                        Aquí&nbsp;
                        </a>
                        para bajar un ejemplo del archivo Excel.
                    </p>

                    <p style={{fontSize:15, paddingTop:10}}>
                        Una vez confeccionado el archivo, cree una nueva lista de precios desde el ABM, 
                        adjunte el archivo creado, y luego haga click en el icono con la leyenda procesar. 
                        Esto actualizará los precios que contenga el archivo.
                    </p>
                    </div>
                </div>
        </Modal>
    )
}