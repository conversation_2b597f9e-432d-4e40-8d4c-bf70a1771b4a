import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  IconButton,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';

export const ModalCart = ({
  show,
  handleClose,
  cart,
  updateQuantity,
  removeItem,
  generateRemitos,
}) => {
  return (
    <Dialog
      open={show}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>Carrito de Remitos</DialogTitle>
      <DialogContent>
        {Object.entries(cart).map(([clientId, clientData]) => (
          <div key={clientId}>
            <h4>Cliente: {clientData.clientName}</h4>
            <TableContainer component={Paper}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Código</TableCell>
                    <TableCell>Descripción</TableCell>
                    <TableCell align="center">Cantidad</TableCell>
                    <TableCell align="center">Acciones</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {clientData.products.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>{product.codigo}</TableCell>
                      <TableCell>{product.descripcion}</TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={product.quantity}
                          onChange={(e) => updateQuantity(clientId, product.id, e.target.value)}
                          size="small"
                          style={{ width: '80px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          onClick={() => removeItem(clientId, product.id)}
                          size="small"
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </div>
        ))}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cerrar</Button>
        <Button 
          onClick={generateRemitos} 
          variant="contained"
          disabled={Object.keys(cart).length === 0}
        >
          Generar Remitos
        </Button>
      </DialogActions>
    </Dialog>
  );
};