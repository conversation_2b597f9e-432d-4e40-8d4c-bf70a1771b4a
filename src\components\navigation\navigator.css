.background-nav{
    text-decoration: none;
    height: 75px;
    position: fixed;
    top:0;
    left: 0;
    right: 0;
    background-color: #0093E9;
    box-shadow: 0 5px 10px rgba(0, 0, 0, .1);
    padding: 20px 7%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1000;
}

.background-nav img{
    height: 40px
}

.nav-menu ul {
    list-style: none;
    margin-top:0;
    padding:0;
    text-align:center;
}

.nav-menu ul li{
    position: relative;
    float: left;
}

.nav-menu ul li a{
    font-size: 20px;
    padding: 20px;
    color: white;
    display: block;
}

.nav-menu ul li a:hover{
    background-color: #6ec0ef;
}

.nav-menu ul li ul{
    position: absolute;
    left: 0;
    width: 200px;
    background: #0093E9;
    display: none;
}

.nav-menu ul li ul li{
    width: 100%;
}

.nav-menu ul li ul li ul{
    left: 200px;
    top:0;
    max-height: 400px;
    overflow-y: auto;
}

.nav-menu ul li:focus-within > ul,
.nav-menu ul li:hover > ul{
    display: initial;
}

.background-nav label{
    font-size: 20px;
    cursor: pointer;
    color: white;
    display: none;
}

#menu-bar{
    display: none;
}

.user{
    margin-top: 10px;
    margin-right: 20px;
    color: white;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #0093E9;
    width: 230px;
    z-index: 1;
    padding:10px;
    margin-top: 2px;
    margin-left:-65px;
    height: 170px;
    padding: 30px;
    color: white;
    justify-content: center;
    align-items: center;
}

.dropdown-content a{
    color: white;
}

.user:hover .dropdown-content {
    display: initial;
}  

.cerrarSesion{
   margin-top: 5px;
}

@media only screen and (max-width: 1070px){
    .background-nav{
        padding: 20px;
    }

    .background-nav label {
        display: initial;
    }

    .nav-menu{
        position: absolute;
        top:100%;
        left: 0;
        right: 0;
        background: #0093E9;
        border-top: 1px solid rgba(0, 0, 0, .1);
        display: none;
    }

    .nav-menu ul li{
        width: 100%;
    }

    .nav-menu ul li ul{
        position: relative;
        width: 100%;
    }

    .nav-menu ul li ul li{
        background-color:  #6ec0ef;
    }

    .nav-menu ul li ul li ul{
        width: 100%;
        left: 0;
    }

    #menu-bar:checked ~ .nav-menu{
        display: initial;
    }

}
