import React from "react"

export const SelectSearchInputCategoria = ({
    info,
    nombreCategoria,
    setNombreCategoria,
    setCategoriaID,
    open, 
    setOpen, 
}) =>{

    return (
        <div className="input-grup-ofertas" >
            <h6>Categor&iacute;a</h6>
            <input onClick={(e)=> {
                setOpen(true)
            }} value={nombreCategoria} onChange={(e)=> setNombreCategoria(e.target.value)}/>
            <div className="list-info"  > 
                {
                    info.length > 0 && open ? 
                    info.map((i) =>
                        <div className="menu-items" onClick={(e)=> {
                            setOpen(false)
                            setCategoriaID(i.categoriaid)
                            setNombreCategoria(i.nombrecategoria)
                        }}>
                            {i.nombrecategoria}
                        </div>
                    ) : null
                }
            </div>  
        </div>
    )
}