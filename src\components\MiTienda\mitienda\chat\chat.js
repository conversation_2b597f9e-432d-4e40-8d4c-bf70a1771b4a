import React, { useEffect, useState } from "react"

import { Button, 
    Checkbox, 
    FormControlLabel, 
    InputAdornment, 
    Paper, 
    Snackbar, 
TextField } from "@mui/material";
import WhatsAppIcon from '@mui/icons-material/WhatsApp';

import "./chat.css"
import { useDispatch, useSelector } from "react-redux";
import { configChat, getConfigChat, limpiarChat } from "../../../../redux/actions/mitienda.actions";
import MuiAlert from '@mui/material/Alert';

export const Chat =()=>{

    const chat = useSelector((state) => state.mitienda.configChat)
    const okChat = useSelector((state) => state.mitienda.okChat)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const dispatch = useDispatch()

    const [input, setInput] = useState({
        usarapi: '',
        url: ''
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const handleClick = () => {
        dispatch(configChat(input))
        setTimeout(function(){
            handleClickAlert()
          }, 1000);
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
      }

    useEffect(() =>{
        setInput({
            usarapi: chat.default_whatsapp_api,
            url: chat.url_whatsapp_api === '' ? "https://wa.me/message/" : chat.url_whatsapp_api
        })
    }, [chat])

    useEffect(() =>{
        let isMounted = true;
        if(isMounted){
            dispatch(getConfigChat())
            dispatch(limpiarChat())
        }
        return () => {
            isMounted = false
        }
    }, [])

    return (
        <section className="container-chat">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={okChat? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>Informaci&oacute;n modificada con &eacute;xito!</h5>
                    </Alert>
                </Snackbar>
            }
                <header className="header-chat">
                    <h3 style={{marginBottom:20}}>Chat</h3>
                    <div>
                        <h4>Un chat en tu tienda online te permitir&aacute; comunicarte mejor
                            con tus potenciales clientes y concretar ventas.
                        </h4>
                        <h4>
                            El chat lo podes vincular con tu n&uacute;mero de WhatsApp.
                        </h4>
                    </div>
                </header>
                <Paper className="form-chat">
                    <TextField 
                        name="url"
                        label="Whatsapp URL"  
                        fullWidth margin="normal" 
                        placeholder="https://wa.me/message/54911222333"
                        onChange={(e)=> handleChange(e)}
                        value={input.url || ''}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <WhatsAppIcon/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 40,
                            fontSize:17
                            },
                        }}
                    />
                    {/* <div style={{marginTop:15, marginBottom:5}}>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black"}}
                            onChange={(e)=> handleCheck(e)}
                            checked={input.usarapi == "1" ? true : false}
                            label="Usar Whatsapp Api"
                            name="usarapi"
                        />
                    </div> */}
                </Paper>
                <div align="right">
                    <Button 
                        size="large" 
                        variant="contained" 
                        onClick={(e)=>(handleClick(e))}
                        disabled={permisos_acciones?.agregar === "0" || permisos_acciones?.modificar === "0"}
                        sx={{mt: 3, mr:3}}>Guardar</Button>
                </div>
        </section>
    )
}