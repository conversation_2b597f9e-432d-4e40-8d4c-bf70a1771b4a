//f with getRemitosPendientesLiquidacion action ( the same used in Gestion Liquidaciones)

import React, { useState, useEffect, useCallback } from "react";
import IconButton from "@mui/material/IconButton";
import Badge from "@mui/material/Badge";
import NotificationsIcon from "@mui/icons-material/Notifications";
import {
  Modal,
  Box,
  Typography,
  Button,
  TextField,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  Divider,
  Pagination,
  Tooltip,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getNotificationRemitosPendientes, getRemitosPendientesLiquidacion } from "../../../redux/actions/mitienda.actions";
import { Link } from "react-router-dom";

const NotificationSystem = () => {
  const POLLING_INTERVAL = 3600000; // 1 hour
  const entriesPerPage = 10;
  const dispatch = useDispatch();

  const [rawData, setRawData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [paginatedData, setPaginatedData] = useState([]);
  const [totalPages, setTotalPages] = useState(1);
  const [openModal, setOpenModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [totalBadgeCount, setTotalBadgeCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [exactMatch, setExactMatch] = useState(false);

  const notificationRefreshTrigger = useSelector(
    (state) => state.mitienda.notificationRefreshTrigger
  );

  const calculateDeadlineInfo = (fechaEntrega) => {
    if (!fechaEntrega) return null;

    const deliveryDate = new Date(fechaEntrega.split(" ")[0]);
    const deadlineDate = new Date(deliveryDate);
    deadlineDate.setDate(deadlineDate.getDate() + 60);

    const today = new Date();
    today.setHours(0, 0, 0, 0); // normalize today's date

    const timeDiff = deadlineDate - today;
    const daysRemaining = Math.floor(timeDiff / (1000 * 60 * 60 * 24)); // Use floor instead of ceil

    //   console.log('Date Calculation:', {
    //     deliveryDate,
    //     deadlineDate,
    //     today,
    //     daysRemaining
    //   });

    let urgency = "safe";
    if (daysRemaining <= 0) urgency = "overdue";
    else if (daysRemaining <= 5) urgency = "critical";
    else if (daysRemaining <= 15) urgency = "warning";

    return {
      daysRemaining,
      urgency,
      deadlineDate: deadlineDate.toISOString().split("T")[0],
    };
  };

  // Get color based on urgency
  const getUrgencyColor = (urgency) => {
    const colors = {
      overdue: "#ffebee", // red
      critical: "#fff3e0", // orange
      warning: "#fffde7", // yellow
      safe: "#e8f5e9", // green
    };
    return colors[urgency] || "#ffffff";
  };

  // Get text color based on urgency
  const getTextColor = (urgency) => {
    const colors = {
      overdue: "#d32f2f",
      critical: "#f57c00",
      warning: "#fbc02d",
      safe: "#388e3c",
    };
    return colors[urgency] || "#000000";
  };

  //f
  const fetchAllRemitos = useCallback(
    async (page = 1, accumulatedData = []) => {
      setLoading(true);
      try {
        // Calculate date range (6 months back to today)
        const today = new Date();
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const fechaDesde = sixMonthsAgo.toISOString().split("T")[0];
        const fechaHasta = today.toISOString().split("T")[0];

        // const response = await dispatch(
        //   getRemitosPendientesLiquidacion(
        //     fechaDesde,
        //     fechaHasta,
        //     page,
        //     100,
        //     0,
        //     0
        //   )
        // );

        const response = await dispatch(
  getNotificationRemitosPendientes(
    fechaDesde,
    fechaHasta,
    page,
    100,
    0,
    0
  )
);

        // FIXED: Access response.payload.data directly (data IS the detalle from the API)
        if (response?.payload?.data) {
          const newData = Object.values(response.payload.data)
            .filter((remito) => {
              return remito.fechaentrega;
            })
            .map((remito) => {
              const deadlineInfo = calculateDeadlineInfo(remito.fechaentrega);

              return {
                ...remito,
                deadlineInfo,
              };
            });

          const allData = [...accumulatedData, ...newData];

          // Check if there are more pages
          if (page < (response.payload.paginaUltima || 1)) {
            return fetchAllRemitos(page + 1, allData);
          }

          return allData;
        }
        return accumulatedData;
      } catch (error) {
        console.error("Error fetching remitos:", error);
        return accumulatedData;
      } finally {
        setLoading(false);
      }
    },
    [dispatch]
  );

  // Filter and paginate data
  const calculatePaginatedData = useCallback(
    (allData, term, page) => {
      // Filter by search term if exists
      // const filteredData = term.trim()
      //   ? allData.filter(
      //       (remito) =>
      //         remito.nombrecliente.toLowerCase().includes(term.toLowerCase()) ||
      //         remito.numeroremito.includes(term) ||
      //         remito.remitoid.toString().includes(term)
      //     )
      //   : allData;

      // const filteredData = term.trim()
      // ? allData.filter(
      //     (remito) =>
      //       remito.nombrecliente.toLowerCase().includes(term.toLowerCase()) ||
      //       remito.numeroremito.includes(term)
      //   )
      // : allData;

      const filteredData = term.trim()
        ? allData.filter((remito) => {
            // Client name always uses partial match
            const clientMatch = remito.nombrecliente
              .toLowerCase()
              .includes(term.toLowerCase());

            // Remito number uses exact or partial match based on toggle
            const remitoMatch = exactMatch
              ? remito.numeroremito.toString() === term
              : remito.numeroremito.toString().includes(term);

            return clientMatch || remitoMatch;
          })
        : allData;

      // Only show remitos with deadlines approaching or overdue
      const urgentRemitos = filteredData.filter(
        (remito) =>
          remito.deadlineInfo && remito.deadlineInfo.urgency !== "safe"
      );

      // Calculate total pages
      const total = Math.max(
        1,
        Math.ceil(urgentRemitos.length / entriesPerPage)
      );
      setTotalPages(total);

      // Ensure current page is valid
      const validPage = Math.min(page, total);
      if (validPage !== page) {
        setCurrentPage(validPage);
        return;
      }

      // Calculate slice indexes
      const startIndex = (validPage - 1) * entriesPerPage;
      const dataForCurrentPage = urgentRemitos.slice(
        startIndex,
        startIndex + entriesPerPage
      );

      setPaginatedData(dataForCurrentPage);
      setTotalBadgeCount(urgentRemitos.length);
    },
    [entriesPerPage, exactMatch]
  );

  //w Initial data fetch and polling setup
  // useEffect(() => {
  //   const loadData = async () => {
  //     const allRemitos = await fetchAllRemitos();
  //     setRawData(allRemitos);
  //     calculatePaginatedData(allRemitos, searchTerm, currentPage);
  //   };

  //   loadData();
  //   const interval = setInterval(loadData, POLLING_INTERVAL);
  //   return () => clearInterval(interval);
  // }, [fetchAllRemitos, calculatePaginatedData, searchTerm, currentPage]);

  //f
  useEffect(() => {
    const loadData = async () => {
      const allRemitos = await fetchAllRemitos();
      setRawData(allRemitos);
      calculatePaginatedData(allRemitos, searchTerm, currentPage);
    };

    loadData();
    const interval = setInterval(loadData, POLLING_INTERVAL);
    return () => clearInterval(interval);
  }, [
    fetchAllRemitos,
    calculatePaginatedData,
    searchTerm,
    currentPage,
    notificationRefreshTrigger,
  ]);

  // Update paginated data when raw data or search term changes
  useEffect(() => {
    calculatePaginatedData(rawData, searchTerm, currentPage);
  }, [rawData, searchTerm, currentPage, calculatePaginatedData]);

  const handleNotificationClick = () => {
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setSearchTerm("");
    setCurrentPage(1);
  };

  const handleSearch = () => {
    setCurrentPage(1);
  };

  const handleReset = () => {
    setSearchTerm("");
    setCurrentPage(1);
  };

  const handleChangePage = (_, newPage) => {
    setCurrentPage(newPage);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString("es-AR");
  };

  return (
    <>
      <IconButton
        color="inherit"
        onClick={handleNotificationClick}
        sx={{ mr: 2 }}
      >
        <Badge badgeContent={totalBadgeCount} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>

      <Modal open={openModal} onClose={handleCloseModal}>
        <Box
          sx={{
            backgroundColor: "white",
            borderRadius: "8px",
            padding: "20px",
            boxShadow: 24,
            maxWidth: "900px",
            width: "90%",
            margin: "auto",
            marginTop: "5vh",
          }}
        >
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <Typography variant="h6">Vencimiento de Remitos a Liquidar</Typography>
            <button
              onClick={handleCloseModal}
              style={{ all: "unset", color: "black", cursor: "pointer" }}
            >
              X
            </button>
          </div>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mb: 2, display: "flex", gap: 2, alignItems: "center" }}>
            <Typography variant="body2">Estado:</Typography>
            {[
              { label: "Vencido", urgency: "overdue" },
              { label: "≤ 5 días", urgency: "critical" },
              { label: "6-15 días", urgency: "warning" },
            ].map(({ label, urgency }) => (
              <Box
                key={urgency}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 0.5,
                }}
              >
                <Box
                  sx={{
                    width: 16,
                    height: 16,
                    backgroundColor: getUrgencyColor(urgency),
                    border: `1px solid ${getTextColor(urgency)}`,
                    borderRadius: 1,
                  }}
                />
                <Typography variant="body2">{label}</Typography>
              </Box>
            ))}
          </Box>

          {/* <TextField
            fullWidth
            label="Buscar por cliente o número de remito"
            variant="outlined"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{
              mb: 2,
              "& label": { top: "-6px" },
              "& label.Mui-focused": { top: 0 },
              "& .MuiInputBase-root": { height: "56px" },
            }}
          /> */}

          <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
            <TextField
              fullWidth
              label="Buscar por cliente o número de remito"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{
                "& label": { top: "-6px" },
                "& label.Mui-focused": { top: 0 },
                "& .MuiInputBase-root": { height: "56px" },
              }}
            />

            <Tooltip title="Búsqueda exacta para números de remito">
              <Button
                variant={exactMatch ? "contained" : "outlined"}
                onClick={() => setExactMatch(!exactMatch)}
                sx={{ whiteSpace: "nowrap" }}
              >
                Exacto
              </Button>
            </Tooltip>
          </Box>

          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              marginBottom: "20px",
            }}
          >
            <Button
              variant="contained"
              color="primary"
              onClick={handleSearch}
              sx={{ width: "48%" }}
            >
              Buscar
            </Button>
            <Button
              variant="outlined"
              onClick={handleReset}
              sx={{ width: "48%" }}
            >
              Limpiar
            </Button>
          </div>

          <TableContainer component={Paper} sx={{ maxHeight: "400px" }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell align="center">Cliente</TableCell>
                  <TableCell align="center">Numero Remito</TableCell>
                  <TableCell align="center">ID Remito</TableCell>
                  <TableCell align="center">Entrega</TableCell>
                  <TableCell align="center">Vencimiento</TableCell>
                  <TableCell align="center">Días Restantes</TableCell>
                  <TableCell align="center">Monto</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      Cargando datos...
                    </TableCell>
                  </TableRow>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((remito) => {
                    const urgency = remito.deadlineInfo?.urgency || "safe";
                    const daysRemaining =
                      remito.deadlineInfo?.daysRemaining ?? 0;

                    let displayText = "";
                    if (daysRemaining <= 0) {
                      displayText = `Vencido hace ${Math.abs(
                        daysRemaining
                      )} días`;
                    } else {
                      displayText = `${daysRemaining} días`;
                    }

                    return (
                      // <TableRow
                      //   key={`${remito.remitoid}-${remito.remitodetalleid}`}
                      //   sx={{
                      //     backgroundColor: getUrgencyColor(urgency),
                      //     "&:hover": {
                      //       opacity: 0.9,
                      //     },
                      //   }}
                      // >
                      //   <TableCell align="center">
                      //     {remito.nombrecliente}
                      //   </TableCell>
                      //   <TableCell align="center">
                      //     {remito.numeroremito}
                      //   </TableCell>
                      //   <TableCell align="center">{remito.remitoid}</TableCell>
                      //   <TableCell align="center">
                      //     {formatDate(remito.fechaentrega)}
                      //   </TableCell>
                      //   <TableCell align="center">
                      //     {remito.deadlineInfo?.deadlineDate
                      //       ? formatDate(remito.deadlineInfo.deadlineDate)
                      //       : "N/A"}
                      //   </TableCell>
                      //   <TableCell
                      //     align="center"
                      //     sx={{
                      //       color: getTextColor(urgency),
                      //       fontWeight: "bold",
                      //     }}
                      //   >
                      //     {displayText}
                      //   </TableCell>
                      //   <TableCell align="center">
                      //     ${remito.totalventa.toLocaleString("es-AR")}
                      //   </TableCell>
                      // </TableRow>
                      <TableRow
                        key={`${remito.remitoid}-${remito.remitodetalleid}`}
                        component={Link}
                        onClick={handleCloseModal}
                        to={`/MiTienda/Remitos/${remito.remitoid}/${remito.clienteid}`}
                        // sx={{
                        //   backgroundColor: getUrgencyColor(urgency),
                        //   textDecoration: "none",
                        //   "&:hover": {
                        //     opacity: 0.9,
                        //     cursor: "pointer",
                        //   },
                        // }}
                        sx={{
                          backgroundColor: getUrgencyColor(urgency),
                          textDecoration: "none",
                          transition: "background-color 0.2s ease",
                          "&:hover": {
                            backgroundColor: "#f5f5f5",
                            opacity: 0.95,
                            cursor: "pointer",
                          },
                        }}
                      >
                        <TableCell align="center">
                          {remito.nombrecliente}
                        </TableCell>
                        <TableCell align="center">
                          {remito.numeroremito}
                        </TableCell>
                        <TableCell align="center">{remito.remitoid}</TableCell>
                        <TableCell align="center">
                          {formatDate(remito.fechaentrega)}
                        </TableCell>
                        <TableCell align="center">
                          {remito.deadlineInfo?.deadlineDate
                            ? formatDate(remito.deadlineInfo.deadlineDate)
                            : "N/A"}
                        </TableCell>
                        <TableCell
                          align="center"
                          sx={{
                            color: getTextColor(urgency),
                            fontWeight: "bold",
                          }}
                        >
                          {displayText}
                        </TableCell>
                        <TableCell align="center">
                          ${remito.totalventa.toLocaleString("es-AR")}
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      No hay vencimientos próximos
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {totalPages > 1 && (
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={handleChangePage}
              color="primary"
              sx={{ mt: 2, display: "flex", justifyContent: "center" }}
            />
          )}
        </Box>
      </Modal>
    </>
  );
};

export default NotificationSystem;
