import { Button, Divider } from "@mui/material";
import React from "react"
import Modal from 'react-modal';
import { eliminarImagenCategoria } from "../../../../redux/actions/mitienda.actions";
import { useDispatch } from "react-redux";

Modal.setAppElement("#root")

export const ModalImagenCategoria = ({show, handleClose, categoria, handleClickAlert}) => {

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
          display:"flex",
          justifyContent:"center"
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const dispatch = useDispatch()

    const handleEliminarImagen = (e) => {
      e.preventDefault()
      dispatch(eliminarImagenCategoria(categoria.categoriaid, categoria.imagenes[1].producto_imagenid))
      handleClose()
      setTimeout(function(){
        handleClickAlert()
    }, 2000);
    }

    return (
        show ?
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", marginBottom:10, flexDirection:"column"}}>
            <div align="right">
            <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <img
             style={{width:300}}    
             src={process.env.REACT_APP_SERVER+categoria.imagenes[1].pathImagenCategoria+(categoria.cant_img === 0 ? '' : categoria.categoriaid)+'/'+categoria.imagenes[1].file}/>
            <Divider/>
            <Button 
              size="large" 
              variant="contained" 
              sx={{mt: 3}}
              onClick={handleEliminarImagen}
              >Eliminar</Button>
            </div>
        </Modal> : null
    )
}