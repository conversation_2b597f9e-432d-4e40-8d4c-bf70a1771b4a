import React, { useEffect, useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { Autocomplete, Checkbox, FormControlLabel, Button, MenuItem, Pagination, Paper, Select, TextField, InputAdornment } from "@mui/material";
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { 
    crearDetalleFacturaProveedor, 
    eliminarArticulo, 
    eliminarItemFactura, 
    getConceptos, 
    getDescuentosSinPaginado, 
    getItemsFacturaProveedor, 
    getProductosByNameSkuPromocion, 
    limpiarModificarProductoEditable, 
} from "../../../redux/actions/mitienda.actions";
import { useDispatch, useSelector } from "react-redux";
import { AttachMoney, Delete, Edit } from "@mui/icons-material";
import { ModalProductoEditable } from "../ventas/modalProductoEditable";
import { SelectSearchInput } from "./SelectSearchInput";
import { ModalEditarItem } from "./editarItem";

Modal.setAppElement("#root")

export const ModalAgregarItems = ({show, handleClose, info}) =>{

  const productsByNameSku = useSelector((state) => state.mitienda.productsByNameSkuPromocion)
  const items = useSelector((state) => state.mitienda.itemsFactura)
  const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaItemsFacturaProveedor)
  const descuentos = useSelector((state) => state.mitienda.descuentosSinPaginado)
  const okAgregarArticulo = useSelector((state) => state.mitienda.okAgregarItemFacturaProveedor)
  const okEliminarItemFacturaProveedor = useSelector((state) => state.mitienda.okEliminarItemFacturaProveedor)
  const conceptosProveedor = useSelector((state) => state.mitienda.conceptosProveedor)
  const okEditarItemFacturaProveedor = useSelector((state) => state.mitienda.okEditarItemFacturaProveedor)

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "80%",
        height: "85vh",
        borderRadius: "8px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };

  const [cantidad, setCantidad] = useState("1")
  const [descuento, setDescuento] = useState("")
  const [product, setProduct] = useState("")
  const [concepto, setConcepto] = useState("")
  const [precioCompra, setPrecioCompra] = useState("")
  const [precioConcepto, setPrecioConcepto] = useState("")
  const [open, setOpen] = useState(false)
  const [esProducto, setEsProducto] = useState(false)
  const [esConcepto, setEsConcepto] = useState(false)
  const [productoEditable, setProductoEditable] = useState("")
  const [input, setInput] = useState('')
  const handleChange = (e) => {
    e.preventDefault()
    setInput(e.target.value)
  }

  const [page, setPage] = useState(1);
  const handleChangePage = (event, value) => {
      setPage(value);
  };

  const [showEditarDetalle, setShowEditarDetalle] = useState('');
  const handleCloseEditarDetalle = () => {
    setShowEditarDetalle(false);
    dispatch(limpiarModificarProductoEditable())
    }
  const handleShowEditarDetalle = (row) => {
      setProductoEditable(row)
      setShowEditarDetalle(true);
  }

  const dispatch = useDispatch()
  
  //facturacompraid,productoid,concepto,cantidad,descuento,preciocompra
  const handleAddToCart =()=>{
    if(esConcepto){
        dispatch(crearDetalleFacturaProveedor(info.facturacompraid,'',concepto,'1','',precioConcepto))
        setConcepto('')
        setPrecioConcepto('')
    }else{
        dispatch(crearDetalleFacturaProveedor(info.facturacompraid,product,concepto,cantidad,descuento,precioCompra))
    }
    dispatch(getProductosByNameSkuPromocion('')) 
    setProduct('')
    setCantidad('1')
    setInput('')
    setDescuento('')
  }

  //facturacompraid, facturacompraproductoid
  const handleDeleteProduct =(p)=>{
    dispatch(eliminarItemFactura(info.facturacompraid, p.facturacompraproductoid))
  }

  const [infoitem, setInfo] = useState('')

  const [showEditar, setShowEditar] = useState(false);
  const handleCloseEditar = () => setShowEditar(false);
  const handleShowEditar = (row) => {
    setShowEditar(true);
    setInfo(row)
  }

  useEffect(() => {
    dispatch(getDescuentosSinPaginado())
    dispatch(getConceptos())
  },[])

  useEffect(() =>{
    if(input.length > 4){
        dispatch(getProductosByNameSkuPromocion(input)) 
    }
  }, [input])

  useEffect(() =>{
    if(info.facturacompraid){
        dispatch(getItemsFacturaProveedor(
            page, 
            info.facturacompraid,
        ))
    }
  }, [info,okAgregarArticulo,okEliminarItemFacturaProveedor,okEditarItemFacturaProveedor])

  return (
      show && 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
        <ModalEditarItem
            show={showEditar}
            handleClose={handleCloseEditar}
            info={infoitem}
        />
        <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
            <h4>Agregar items - Factura #{info.facturacompraid}</h4>
            <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
        </div>
        <Divider/>
        <div style={{display:"flex", alignItems:"center", width:"100%"}}>
            <FormControlLabel
                control={
                <Checkbox/>
                }
                style={{color:"black", marginLeft:1}}
                onChange={(e) => {
                    setEsProducto(e.target.checked)
                    setEsConcepto(false)
                }}
                checked={esProducto}
                label="Agregar producto"
            />
            <FormControlLabel
                control={
                <Checkbox/>
                }
                style={{color:"black", marginLeft:1}}
                onChange={(e) => {
                    setEsConcepto(e.target.checked)
                    setEsProducto(false)
                }}
                checked={esConcepto}
                label="Agregar concepto"
            />
        </div>
        {

            esConcepto &&
            <div style={{display:"flex", alignItems:"center"}}>

            <div style={{width:"70%", marginRight:20}}>
                <h6><strong>Concepto</strong></h6>
                <Select
                    size="small"
                    style={{width:"100%", height:40}}
                    value={concepto || ""}
                    name='concepto'
                    onChange={(e)=>setConcepto(e.target.value)}
                    MenuProps={{ keepMounted: true, disablePortal: true }}
                    >
                    {               
                        conceptosProveedor && conceptosProveedor.map((t) =>
                            <MenuItem value={t.conceptoid} key={t.conceptoid}>{t.descripcion+' | '+t.iva+'%'}</MenuItem>
                        )
                    }
                </Select>
            </div>
            <div style={{width:"30%", marginLeft:10, marginRight:20}}>
                <h6><strong>Precio</strong></h6>
                <TextField
                    style={{width:"100%"}}
                    name='precioConcepto'
                    value={precioConcepto}
                    onChange={(e)=>setPrecioConcepto(e.target.value)}
                    InputProps={{
                        startAdornment:<InputAdornment position="start">
                        <AttachMoney/>
                        </InputAdornment>,
                    }}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
            </div>
            <Button style={{height:40, marginTop:30}} variant='contained'  onClick={()=>handleAddToCart()}>Agregar</Button>        
        </div>

        }
        {

            esProducto &&
            <div style={{display:"flex", width:"100%", justifyContent:"space-between", alignItems:"center", height:70, marginTop:20}}>

            <div style={{width:"45%", marginRight:20, marginTop:-20}}>
                <h6><strong>Producto</strong></h6>
                <SelectSearchInput
                    info={productsByNameSku}
                    value={input}
                    setValue={setInput}
                    setProductoId={setProduct}
                    open={open}
                    setOpen={setOpen}
                    setPrecio={setPrecioCompra}
                />
            </div>

            <div style={{width:"10%", marginRight:20}}>
                <h6><strong>Cantidad</strong></h6>
                <TextField
                    style={{width:"100%"}}
                    type='number'
                    name='cantidad'
                    value={cantidad}
                    onChange={(e)=>setCantidad(e.target.value)}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
            </div>
            <div style={{width:"40%", marginRight:20}}>
                <h6><strong>Descuento</strong></h6>
                <Select
                    size="small"
                    style={{width:"100%", height:40}}
                    value={descuento|| ""}
                    name='descuento'
                    onChange={(e)=>setDescuento(e.target.value)}
                    MenuProps={{ keepMounted: true, disablePortal: true }}
                    >
                    {               
                        descuentos && descuentos.map((t) =>
                            <MenuItem value={t.descuentovtaid} key={t.descuentovtaid}>{t.nombre}</MenuItem>
                        )
                    }
                </Select>
            </div>
            <Button style={{marginTop:25}} variant='contained'  onClick={()=>handleAddToCart()}>Agregar</Button>
        </div>
        }
        <div style={{padding:20, width:"100%", display:"flex", flexDirection:"column", alignItems:"center"}}>
        <TableContainer component={Paper} style={{marginTop:20}}>
            <Table>
            <TableHead>
                <TableRow>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">C&oacute;digo</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Producto</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Marca</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Categor&iacute;a</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Color</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Medida</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Cantidad</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Precio costo</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Precio compra</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Editar</TableCell>
                    <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Eliminar</TableCell>
                </TableRow>
            </TableHead>
            <TableBody>
                {items.length > 0 ? items.map((row) => (
                <TableRow
                    // key={row.facturaid}
                    sx={{ '&:last-child td, &:last-child th': { border: 0 }}}
                >
                    <TableCell style={{fontSize:"14px", width:"160px", height:"40px", padding: 0}} align="center"
                    >{row.codigoarticulo}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"200px", height:"40px", padding: 0}} align="center"
                    >{row.nombreproducto}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"160px", height:"40px", padding: 0}} align="center"
                    >{row.nombremarca}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"160px", height:"40px", padding: 0}} align="center"
                    >{row.nombrecategoria}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"160px", height:"40px", padding: 0}} align="center"
                    >{row.nombrecolor}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"160px", height:"40px", padding: 0}} align="center"
                    >{row.nombretalla}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"160px", height:"40px", padding: 0}} align="center"
                    >{row.cantidad}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"70px", height:"40px", padding: 0}} align="center"
                    >${row.preciocosto}</TableCell>
                    <TableCell style={{fontSize:"14px", width:"60px", height:"40px", padding: 0}} align="center"
                    >${row.preciocompra}
                    </TableCell>
                    <TableCell style={{fontSize:"14px", width:"60px", height:"40px", padding: 0, cursor:"pointer"}} align="center"
                    onClick={(e)=>handleShowEditar(row)}><Edit/></TableCell>
                    <TableCell style={{fontSize:"14px", width:"60px", height:"40px", padding: 0, cursor:"pointer"}} align="center"
                    onClick={(e)=>handleDeleteProduct(row)}><Delete/></TableCell>
                </TableRow>
                )) :
                <TableRow>
                    <TableCell colSpan={4}></TableCell>
                    <TableCell colSpan={4} align="center">
                        <h5>No hay informaci&oacute;n para mostrar</h5>
                    </TableCell>
                </TableRow>

            }
            </TableBody>
            </Table>
        </TableContainer>
        <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", margin:3}} /> 
        </div>          
      </Modal>
  )
}