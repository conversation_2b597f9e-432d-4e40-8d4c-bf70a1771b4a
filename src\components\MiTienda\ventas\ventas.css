.ventas-container {
    width: 100%;
    min-height: 110vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.columnas{
    display: flex;
    width: 100%;
    justify-content: center;
}

.titulo-ventas{
    width: 80%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 10px;
}

.text-ventas{
    margin-left: 40px;
}

.div-titulo{
    display: flex;
    justify-content: space-between;
}

.paper-metodos-pago{
    width: 60%;
    margin: 20px;
    min-height: 200px;
    padding: 20px;
}

.paper-metodos-envio{
    width: 60%;
    margin: 20px;
    min-height: 200px;
    padding: 20px;
}

.columna1{
    width: 70%;
}

.columna1-tabla{
    width: 95%;
    margin-left:20px;
}

.columna2{
    width: 50%;
    display: flex;
    flex-direction: column;
    float: left;
}

.columna2-papper-info-cliente{
    width: 95%;
    margin: 20px;
    height: 230px;
    padding: 20px;
}

.columna2-papper-info-envio{
    margin: 0;
    width: 95%;
    margin: 20px;
    min-height: 300px;
    height: auto;
    padding: 20px;
}


.columnas-nueva-venta{
    display: flex;
    width: 100%;
    justify-content: center;
}

.columna1-nueva-venta{
    width: 90%;
}

.columna2-nueva-venta{
    width: 30%;
    display: flex;
    flex-direction: column;
    float: left;
    margin-right: 20px;
}


.columna2-papper-info-nueva-venta{
    width: 100%;
    margin-bottom: 20px;
    margin-right: 20px;
    margin-left: 20px;
    height: 220px;
    padding: 20px;
}

.nueva-venta-button{
    width: 40%;
    cursor: pointer;
    border-radius: 0.5rem;
    border: solid 1px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1);
    margin-left: 1rem;
    margin-right: 1rem;
}

@media only screen and (max-width: 1000px) {
    .ventas-container {
        padding: 20px;
        width: 100%;
        min-height: 150vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }

    .columnas{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        float: left;
        width: 100%;
    }

    .columna1{
        width: 100%;
    }
    
    .columna2{
        width: 100%;
    }

    .columna1-fila1{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        float: left;
    }

    .columna1-tabla{
        width: 95%;
    }

    .columna2-papper-info-cliente{
        margin: 0;
        width: 95%;
        margin: 20px;
        min-height: 100px;
        height: auto;
        padding: 20px;
    }

    .columna2-papper-info-envio{
        margin: 0;
        width: 95%;
        margin: 20px;
        min-height: 300px;
        height: auto;
        padding: 20px;
    }

    .paper-metodos-envio{
        width: 95%;
        margin: 20px;
        min-height: 100px;
        padding: 20px;
    }

    .paper-metodos-pago{
        width: 95%;
        margin: 20px;
        min-height: 200px;
        padding: 20px;
    }
}

@media only screen and (max-width: 765px) {
    .ventas-container {
        padding: 20px;
        width: 100%;
        min-height: 150vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }

    .columnas{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        float: left;
        width: 100%;
    }

    .columna1{
        width: 100%;
    }
    
    .columna2{
        width: 100%;
    }

    .columna1-fila1{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        float: left;
    }

    .columna1-tabla{
        width: 95%;
        padding-right: 15px;
    }

    .columna2-papper-info-cliente{
        margin: 0;
        width: 92%;
        margin-top: 20px;
        min-height: 100px;
        height: auto;
        margin-left: 20px;
    }

    .columna2-papper-info-envio{
        margin: 0;
        width: 92%;
        min-height: 200px;
        height: auto;
        margin-top: 20px;
        margin-left: 20px;
    }

    .paper-metodos-envio{
        width: 95%;
        min-height: 100px;
        padding: 20px;
    }

    .paper-metodos-pago{
        width: 95%;
        min-height: 200px;
        padding: 20px;
    }
    
}


@media only screen and (max-width: 375px) {
    .ventas-container {
        padding: 20px;
        width: 100%;
        min-height: 150vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
    }

    .columnas{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        float: left;
        width: 100%;
    }

    .columna1{
        width: 100%;
    }
    
    .columna2{
        width: 100%;
    }

    .columna1-fila1{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        float: left;
    }

    .columna1-tabla{
        width: 95%;
    }

    .columna2-papper-info-cliente{
        margin: 0;
        width: 95%;
        margin: 20px;
        min-height: 100px;
        height: auto;
    }

    .columna2-papper-info-envio{
        margin: 0;
        width: 95%;
        margin: 20px;
        min-height: 300px;
        height: auto;
        padding: 20px;
    }

    .paper-metodos-envio{
        width: 95%;
        margin: 20px;
        min-height: 100px;
        padding: 20px;
    }

    .paper-metodos-pago{
        width: 95%;
        margin: 20px;
        min-height: 200px;
        padding: 20px;
    }
    
}

.container-listado-puntos-mapa{
    display: flex;
    height: 80vh;
}

.container-listado-puntos{
    width: 58%;
    overflow: auto;
    margin: 5px;
    list-style: none;
}

.info-punto{
    padding:10px;
    cursor:pointer
}

.info-punto h5{
    color:deepskyblue;
}

.info-punto:hover{
    background-color: black;
    color: #EEEEEE;
}

