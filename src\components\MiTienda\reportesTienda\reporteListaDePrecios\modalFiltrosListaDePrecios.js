import React, { useEffect, useState } from "react"
import Modal from 'react-modal';
import Divider from '@mui/material/Divider';
import Select from '@mui/material/Select';
import { useDispatch, useSelector } from "react-redux";
import { Button, FormControl, InputAdornment, InputLabel, ListSubheader, MenuItem, TextField } from "@mui/material";
import {
    getCategoriasByName,
    getCategoriasSinPag,
    getDescuentosSinPaginado,
    getEstilosTienda,
    getMarcasByName,
    getMarcasTienda
} from "../../../../redux/actions/mitienda.actions";
import { SelectSearchInputCategoria } from "../../SelectInputs/SelectSearchInputCategoria";
import { SelectSearchInputMarca } from "../../SelectInputs/SelectSearchInputMarca";
import { Search } from "@mui/icons-material";

Modal.setAppElement("#root")

export const ModalFiltrosListaDePrecios = ({
  show, 
  handleClose, 
  setMarca, 
  setCategoriaid, 
  handleChangeNombre, 
  nombre,
  handleChangeLista,
  lista,
  handleChangeCodigo, 
  codigo,
  search, 
  reset
}) =>{

    const listas = useSelector((state) => state.mitienda.descuentosSinPaginado)

  const customStyles = {
      content: {
        padding: "40px",
        inset: "unset",
        width: "100%",
        height: "60vh",
        borderRadius: "8px",
        maxWidth: "650px",
        right: '50px',
      //   backgroundColor: "#D1D1D1"
      },
      overlay: {
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "grid",
        placeItems: "center",
        zIndex: "100000",
      },
  };


  const categorias = useSelector((state) => state.mitienda.categoriasByName)
  const marcas = useSelector((state) => state.mitienda.marcasByName)

  const [nombreCategoria, setNombreCategoria] = useState('')
  const [nombreMarca, setNombreMarca] = useState('')
  const [selectedOption, setSelectedOption] = useState('');
  const [selectedOptionMarca, setSelectedOptionMarca] = useState('');

  const dispatch = useDispatch()

    useEffect(() =>{
        if(nombreCategoria.length > 2){
            dispatch(getCategoriasByName(nombreCategoria))
        }
    }, [nombreCategoria])

    useEffect(() =>{
        if(nombreMarca.length > 2){
            dispatch(getMarcasByName(nombreMarca))
        }
    }, [nombreMarca])

  //esta funcion se ejecuta una sola vez al montar el componente
  //aca obtenemos la informacion para los selects de los filtros
  useEffect(() =>{
    if(show){
        dispatch(getDescuentosSinPaginado())
    }
  }, [show])

  return (
      show && 
      <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
              <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                  <h4>FILTRAR</h4>
                  <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
              </div>
              <Divider/>
              <div style={{display:"flex", flexDirection:"column"}}>
              <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                    <InputLabel id="search-select-label">Marcas</InputLabel>
                    <Select
                    // Disables auto focus on MenuItems and allows TextField to be in focus
                    MenuProps={{ autoFocus: false,  disablePortal: true }}
                    labelId="search-select-label"
                    id="search-select"
                    value={selectedOptionMarca.marcaid || ''}
                    label="Marcas"
                    onClose={() => {
                        dispatch(getMarcasByName(""))
                    }}
                    size="small"
                    // This prevents rendering empty string in Select's value
                    // if search text would exclude currently selected option.
                    renderValue={() => selectedOptionMarca.nombremarca}
                    >
                    {/* TextField is put into ListSubheader so that it doesn't
                        act as a selectable item in the menu
                        i.e. we can click the TextField without triggering any selection.*/}
                    <ListSubheader>
                    <TextField
                        size="small"
                        // Autofocus on textfield
                        autoFocus
                        placeholder="Buscar marca..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreMarca(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {marcas.map((option, i) => (
                        <MenuItem key={i} value={option.marcaid} onClick={() => {
                            setSelectedOptionMarca(option)
                            setNombreMarca(option.nombremarca)
                            setMarca(option.marcaid)
                        }}>
                        {option.nombremarca}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>

                <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                    <InputLabel id="search-select-label">Categor&iacute;as</InputLabel>
                    <Select
                    // Disables auto focus on MenuItems and allows TextField to be in focus
                    MenuProps={{ autoFocus: false,  disablePortal: true }}
                    labelId="search-select-label"
                    id="search-select"
                    value={selectedOption.categoriaid || ''}
                    label="Categor&iacute;as"
                    onClose={() => {
                        dispatch(getCategoriasByName(""))
                    }}
                    size="small"
                    // This prevents rendering empty string in Select's value
                    // if search text would exclude currently selected option.
                    renderValue={() => selectedOption.nombrecategoria}
                    >
                    {/* TextField is put into ListSubheader so that it doesn't
                        act as a selectable item in the menu
                        i.e. we can click the TextField without triggering any selection.*/}
                    <ListSubheader>
                    <TextField
                        size="small"
                        // Autofocus on textfield
                        autoFocus
                        placeholder="Buscar categor&iacute;a..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreCategoria(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {categorias.map((option, i) => (
                        <MenuItem key={i} value={option.categoriaid} onClick={() => {
                            setSelectedOption(option)
                            setNombreCategoria(option.nombrecategoria)
                            setCategoriaid(option.categoriaid)
                        }}>
                        {option.nombrecategoria}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>

                <FormControl sx={{ mt:2, mb:2}}>
                    <InputLabel id="lista-select-label">Descuentos</InputLabel>
                    <Select
                        labelId="lista-select-label"
                        id="lista-select"
                        label="Descuentos"
                        size="small"
                        name="lista"
                        value={lista}
                        onChange={handleChangeLista}
                        MenuProps={{ keepMounted: true, disablePortal: true }}
                    >
                        <MenuItem value={0}>Seleccione una opcion</MenuItem>
                        {
                            listas && listas.map((c) =>
                                <MenuItem value={c.descuentovtaid} key={c.descuentovtaid}>{c.nombre}</MenuItem>
                            )
                        }
                    </Select>
                </FormControl>

                <TextField 
                    sx={{mt:2, mb:2}}
                    fullWidth 
                    label="C&oacute;digo"
                    variant="outlined" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nombre"
                    onChange={(e) => handleChangeCodigo(e)}
                    value={codigo}
                />
                <TextField 
                    sx={{mt:2, mb:2}}
                    fullWidth
                    label="Nombre"  
                    variant="outlined" 
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                            height: 40,
                            fontSize:17
                        },
                    }}
                    name="nombre"
                    onChange={(e) => handleChangeNombre(e)}
                    value={nombre}
                />

              </div>
              <div align="center">
              <Button 
                  size="large" 
                  variant="contained" 
                  sx={{mt: 3}} fullWidth 
                  onClick={search}
              >Aplicar filtros</Button>
              <Button 
                  size="large" 
                  variant="contained" 
                  sx={{mt: 3}} fullWidth 
                  onClick={() => {
                    reset()
                    setNombreCategoria('')
                    setNombreMarca('')
                    setSelectedOption('')
                    setSelectedOptionMarca('')
                    dispatch(getCategoriasByName(""))
                    dispatch(getMarcasByName(""))
                  }}
              >Limpiar filtros</Button>
              </div>
      </Modal>
  )
}