import React, {useEffect} from "react";
import { useDispatch, useSelector } from "react-redux";

import { getMenu } from "../../redux/actions/user.actions";
import { sort } from "./utils";


const Menu = () => {

    const info = useSelector((state) => state.user.menu)
    let lista = sort(info)
    const dispatch = useDispatch()

    useEffect(()=>{
        let isMounted = true;
        if(isMounted){
            dispatch(getMenu())
        }
        return () => {
            isMounted = false
        }
    }, [])

    let id = 0

    return(
        <ul id='nav' key='nav'>
            {
                lista && lista.map(l => 
                <li key={id++}><a href='/a'>{l.nombredireccion}</a>
                    <ul> 
                       {
                           l.menues.map(aux =>
                                <li key={id++}><a href='/a'><div>{aux.nombremenu} ⮞</div></a>
                                    <ul>
                                        {
                                            aux.submenues.map(b => 
                                                <li key={id++}><a href={b.url}>
                                                <div>{b.nombresubmenu}</div>    
                                                </a></li>    
                                            )
                                        }
                                    </ul>
                                </li>
                            )
                       } 
                    </ul>
                </li> 
            )
            }
        </ul>
    )
}

export default Menu;