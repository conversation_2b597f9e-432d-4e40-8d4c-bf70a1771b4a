.list{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    float: left;
}

.label-input-mdp{
    display: flex;
    margin-top: 20px;
}

.title-mdp{
    width: 650px;
}

.container-mp{
    width: 100%;
    min-height: 110vh;
    height: auto;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

@media only screen and (max-width: 1200px) {
    .container-mp{
        width: 100%;
        min-height: 110vh;
        height: auto;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
    }
}