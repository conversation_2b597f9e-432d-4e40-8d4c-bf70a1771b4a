import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {useTranslation} from "react-i18next"
import { useDispatch, useSelector } from 'react-redux'

import TextField from "@mui/material/TextField";
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper'

import { limpiarRecoveryPass, recoverPassword } from "../../redux/actions/user.actions";

import "./styles.css"
import { useEffect } from "react";
import { Snackbar } from "@mui/material";
import MuiAlert from '@mui/material/Alert';

const Recovery = () => {

  const [t, i18n] = useTranslation("global");
  const [mail, setEmail] = useState("");
  const [error, setError] = useState(false)

  const ok = useSelector((state) => state.user.recover);

  const dispatch = useDispatch()

  const handleChange = (e) => {
    e.preventDefault()
    setEmail(e.target.value)
    if(mail.includes("@")){
      setError(false)
    }else{
      setError(true)
    }
  }

  const handleRecoverPassword = (e) => {
    e.preventDefault()
    dispatch(recoverPassword(mail))
    setEmail('')
    setTimeout(function(){
      handleClickAlert()
    }, 2000);
  }

  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });

  const [open, setOpen] = React.useState(false);

    const handleClickAlert = () => {
      setOpen(true);
    };
  
    const handleCloseAlert = () => {
        setOpen(false);
    };

  useEffect(() => {
    dispatch(limpiarRecoveryPass())
  },[])

  return (
    <div className="backgroundRecovery">
      {
        <Snackbar
            open={open} 
            autoHideDuration={10000} onClose={handleCloseAlert}
            anchorOrigin={
                {vertical: 'top',
                horizontal: 'center',}
            }>
            <Alert onClose={handleCloseAlert} 
                severity={ok.success === true ? "success" : "error"}
                sx={{ width: 400 }}>
                <h5>{ok.mensaje}</h5>
            </Alert>
        </Snackbar>
      }
       <Paper className="formRecovery">
          <h4>{t("login.recoveryPassword")}</h4>
          <div className="labelInputRecovery">
              <h5 className="titleRecovery">Email</h5>
              <TextField
                required
                id="outlined-required"
                name="nombre"
                className="textFieldRecovery"
                value={mail}
                placeholder={t("login.enterYourEmail")}
                onChange={(e) => handleChange(e)}
              />  
          </div>
          <Button variant="contained" 
            disabled={!mail || error}
            onClick={(e) => handleRecoverPassword(e)}
          >{t("login.sendEmail")}</Button>
          <div className="text-center">
              <Link to={"/"}>
              {t("login.backToLogin")}
              </Link>
          </div>
      </Paper>
    </div>
  );
};

export default Recovery;
