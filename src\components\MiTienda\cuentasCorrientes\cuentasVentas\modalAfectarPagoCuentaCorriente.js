import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';

import Divider from '@mui/material/Divider';
import { Button, InputAdornment, TextField, Select, FormControl, InputLabel, MenuItem } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { AttachMoney } from "@mui/icons-material";
import { afectarPagoCuentaCorriente, getPagosCuentaCorriente } from "../../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root")

export const ModalAfectar = ({show, handleClose, info, cliente}) =>{

    const pagosCuentaCorriente = useSelector((state) => state.mitienda.pagosCuentaCorriente)
    
    const dispatch = useDispatch()

    const [monto, setMonto] = useState('')
    const [pagosctaxcobrarid, setPagosctaxcobrarid] = useState('')

    //clienteid, pedidoid, pagosctaxcobrarid, montoabono
    const handleAfectar =()=> {
        dispatch(afectarPagoCuentaCorriente(cliente.clienteid, info.nro, monto ))
        handleClose()
    }

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    useEffect(() => {
        dispatch(getPagosCuentaCorriente(1,cliente.cliente))
    },[])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <div style={{display:"flex", flexDirection:"column"}}>
                        <h4>Cuentas Corrientes - Afectar pago</h4>
                        <p style={{fontSize:18}}>Cliente: {cliente.nombre}</p>
                    </div>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>
                <div style={{margin:15}}>   
                    <p style={{fontSize:18}}>Pedido #{info.nro} Monto: ${info.totalcomprobante}</p>
                    <p style={{fontSize:16}}>Pendiente Afectar: {info.totalrestanteafectar}</p>
                </div>
                <div style={{margin:15}}>
                    <FormControl fullWidth style={{marginTop:15, marginBottom:15}} >
                        <InputLabel id="pagos-select-label">Pagos CC</InputLabel>
                        <Select
                            labelId="pagos-select-label"
                            id="pagos"
                            label="Pagos CC"
                            size="small"
                            name="pagosctaxcobrarid"
                            value={pagosctaxcobrarid}
                            onChange={(e) => setPagosctaxcobrarid(e.target.value)}
                            MenuProps={{ keepMounted: true, disablePortal: true }}
                            >
                            <MenuItem value="">Seleccione una opcion</MenuItem>
                            {               
                                pagosCuentaCorriente && pagosCuentaCorriente.map((t) =>
                                    <MenuItem value={t.pagosctaxcobrarid} key={t.pagosctaxcobrarid}>
                                        {t.fechapago+' - '+t.tipopago+' | Monto afectar: $'+t.montoabono}
                                    </MenuItem>
                                )
                            }
                        </Select>
                    </FormControl>
                    <TextField 
                        label="Monto afectar"  
                        variant="outlined" 
                        margin="normal" 
                        sx={{width:300, marginRight:5}}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <AttachMoney/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                                height: 40,
                                fontSize:17
                            },
                        }}
                        value={monto}
                        onChange={(e) => setMonto(e.target.value)}
                    />
                </div>
                <Divider/>
                <div align="right">
                    <Button size="large"
                    variant="contained" 
                    onClick={handleAfectar}
                    sx={{mt: 3}}>Afectar</Button>
                </div>
        </Modal>
    )
}