import React, { useEffect, useState } from 'react';
import { Checkbox, FormControlLabel, Button, Divider, TextField, InputAdornment } from "@mui/material";
import Modal from 'react-modal';
import { getEstadoCaja, getTotalCaja, grabarMovimiento } from '../../../redux/actions/mitienda.actions';
import { useDispatch, useSelector } from 'react-redux';
import { AttachMoney } from '@mui/icons-material';

Modal.setAppElement("#root")

export const ModalManejoCaja = ({show, handleClose, info}) => {

    const estado = useSelector((state) => state.mitienda.estadoCaja)
    const montoTotal = useSelector((state) => state.mitienda.totalCaja)
    const okGrabarMovimiento = useSelector((state) => state.mitienda.okGrabarMovimiento)

    let permisos_acciones_caja = localStorage.getItem('permisos_acciones_caja')

    const [tipoConfCaja, setTipoConfCaja] = useState({
        abrir: false,
        cerrar: false,
        retiro: false,
        refuerzo: false,
    })
    const [monto, setMonto] = useState('0')
    const [observacion, setObservacion] = useState('')

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "90vh",
          borderRadius: "8px",
          maxWidth: "650px",
          display:"flex",
          flexDirection:"column",
          justifyContent:"center"
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const dispatch = useDispatch()

    const handleGrabarMovimiento = () => {
        let tipo = tipoConfCaja.cerrar ? "CERRAR" :
        tipoConfCaja.abrir ? "ABRIR" :
        tipoConfCaja.retiro ? "RETIRAR" : "REFORZAR"
        dispatch(grabarMovimiento({
            monto: monto, 
            observacion: observacion,
            tipoConfCaja: tipo
        }))
        setMonto('')
        setObservacion('')
    }

    useEffect(() => {
        if(show){
            dispatch(getEstadoCaja())
            dispatch(getTotalCaja())
        }
    },[show,okGrabarMovimiento])

    return (
        show &&
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>Configuraci&oacute;n de la caja</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{display:"flex", flexDirection:"column", justifyContent:"center", padding:20}}>
                <div style={{display:"flex", flexDirection:"column", margin: 5}}>
                    <h5>Estado actual de la caja: <strong>{estado}</strong></h5>
                    <h5>Saldo: ${montoTotal}</h5>
                </div>
                <div style={{display:"flex"}}>
                    {
                        estado === "CERRADA" ?
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black", marginLeft:1}}
                            onChange={(e)=> setTipoConfCaja({
                                abrir: true,
                                cerrar: false,
                                retiro: false,
                                refuerzo: false,
                            })}
                            checked={tipoConfCaja.abrir}
                            label="Abrir"
                        /> :
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black", marginLeft:1}}
                            onChange={(e)=> setTipoConfCaja({
                                abrir: false,
                                cerrar: true,
                                retiro: false,
                                refuerzo: false,
                            })}
                            checked={tipoConfCaja.cerrar}
                            label="Cerrar"
                        />
                    }
                    <FormControlLabel
                        control={
                        <Checkbox/>
                        }
                        style={{color:"black", marginLeft:1}}
                        onChange={(e)=> setTipoConfCaja({
                            abrir: false,
                            cerrar: false,
                            retiro: true,
                            refuerzo: false,
                        })}
                        checked={tipoConfCaja.retiro}
                        label="Retiro"
                    />
                    <FormControlLabel
                        control={
                        <Checkbox/>
                        }
                        style={{color:"black", marginLeft:1}}
                        onChange={(e)=> setTipoConfCaja({
                            abrir: false,
                            cerrar: false,
                            retiro: false,
                            refuerzo: true,
                        })}
                        checked={tipoConfCaja.refuerzo}
                        label="Refuerzo"
                    />
                </div>
                <div>
                    <TextField
                        style={{margin:5, marginTop:20}}
                        label="Monto"
                        fullWidth
                        value={monto}
                        onChange={(e) => setMonto(e.target.value)}
                        InputProps={{
                            startAdornment:<InputAdornment position="start">
                            <AttachMoney/>
                            </InputAdornment>,
                        }}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        inputProps={{
                            style: {
                            height: 20,
                            fontSize:17
                            },
                        }}
                    />  
                    <TextField
                        style={{margin:5, marginTop:20}}
                        label="Observaci&oacute;n"
                        fullWidth
                        multiline
                        value={observacion}
                        onChange={(e) => setObservacion(e.target.value)}
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        minRows={3}
                    />  
                </div>
            </div>
            <Divider/>
            <div align="right">
                <Button size="large"
                variant="contained"
                disabled={
                    monto <= 0 || 
                    (monto > montoTotal && tipoConfCaja.retiro) ||
                    (tipoConfCaja.abrir === false 
                        && tipoConfCaja.cerrar  === false 
                        && tipoConfCaja.retiro === false 
                        && tipoConfCaja.refuerzo === false 
                    )
                }
                onClick={handleGrabarMovimiento}
                sx={{mt: 3, mr:1}}>Guardar</Button>
            </div>
        </Modal> 
    )
}