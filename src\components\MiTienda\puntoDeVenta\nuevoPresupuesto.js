import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Delete, Search, ShoppingCart } from '@mui/icons-material';
import { 
    Autocomplete, 
    Button, 
    Divider, 
    FormControl, 
    InputLabel, 
    MenuItem, 
    Pagination, 
    Paper, 
    Select, 
    Snackbar, 
    Table, 
    TableBody, 
    TableCell, 
    TableContainer, 
    TableHead, 
    TableRow, 
    TextField, 
    Tooltip
} from '@mui/material';
import { 
    agregarArticulo, 
    eliminarArticulo, 
    finalizarPedido, 
    getCalcularCarrito, 
    getCarritoDetalle, 
    getCarritoDetalleVerificado, 
    getCuotasTarjeta, 
    getDatosTienda, 
    getDescuentosSinPaginado, 
    getListadoClientesByName, 
    getMetodosEnvio, 
    getPresupuesto, 
    getProductosByNameSkuNuevaVenta, 
    getTiposDePago, 
    getTiposTarjeta, 
    modificarCantidadArticulo,
    modificarDescuentoArticulo
} from '../../../redux/actions/mitienda.actions';
import { ModalImagenProducto } from '../ventas/modalImagenProducto';
import { ModalAgregarCliente } from '../clientes/agregarCliente';
import { ModalFinalizarCompra } from '../ventas/modalFinalizarCompra';
import { ModalProductoEditable } from '../ventas/modalProductoEditable';
import MuiAlert from '@mui/material/Alert';
import { ModalCancelarFactura } from './modalCancelarFactura';

export const Presupuesto = ({tipofactura, tipocomprobante, setOk}) => {    

    const productsByNameSku = useSelector((state) => state.mitienda.productosNuevaVenta)
    const descuentos = useSelector((state) => state.mitienda.descuentosSinPaginado)
    const clientes = useSelector((state) => state.mitienda.clientesByName)
    const okAgregarArticulo = useSelector((state) => state.mitienda.okAgregarArticulo)
    const detalleCarrito = useSelector((state) => state.mitienda.detalleCarrito)
    const calcularCarrito = useSelector((state) => state.mitienda.calcularCarrito)
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaDetalleCarrito)
    const tiposDePago = useSelector((state) =>  state.mitienda.tiposDePago)
    const tiposDeTarjetas = useSelector((state) =>  state.mitienda.tiposDeTarjetas)
    const cuotasTarjetas = useSelector((state) =>  state.mitienda.cuotasTarjetas)
    const okFinalizarPedido = useSelector((state) =>  state.mitienda.okFinalizarPedido)
    const okAgregarCliente = useSelector((state) => state.mitienda.okAgregarCliente)
    const okEliminarArticulo = useSelector((state) => state.mitienda.okEliminarArticulo)
    const okModificarDescuentoArticulo = useSelector((state) => state.mitienda.modificarDescuentoArticulo)
    const okModificarCantidadArticulo = useSelector((state) => state.mitienda.modificarCantidadArticulo)
    const okModificarProductoEditable = useSelector((state) => state.mitienda.modificarProductoEditable)
    const presupuestoCreado = useSelector((state) => state.mitienda.presupuesto)
    const okCancelar = useSelector((state) => state.mitienda.okCancelarFactura)
    const okTraerFactura = useSelector((state) => state.mitienda.okTraerFactura)

    const [clienteid, setClienteid] = useState("")
    const [product, setProduct] = useState("")
    const [productoEditable, setProductoEditable] = useState("")
    const [cantidad, setCantidad] = useState("1")
    const [descuento, setDescuento] = useState("")
    const [detalle, setDetalle] = useState("")
    const [facturaid, setFactura] = useState("")
    const [tipoPago, setTipoPago] = useState("")
    const [tipoTarjeta, setTipoTarjeta] = useState("")
    const [tipoCuotas, setTipoCuotas] = useState("")

    const [tipoEnvio, setTipoEnvio] = useState("")
    const handleTipoEnvio = (method) => {
        setTipoEnvio(method)
    }

    const [clienteInput, setClienteInput] = useState("")
    const handleChangeClienteInput = (e) => {
        e.preventDefault()
        setClienteInput(e.target.value)
    }

    const [input, setInput] = useState('')
    const handleChange = (e) => {
        e.preventDefault()
        setInput(e.target.value)
    }

    const [nrofactura, setNrofactura] = useState('')
    const handleChangeNrofactura = (e) => {
        e.preventDefault()
        setNrofactura(e.target.value)
    }

    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    const [show, setShow] = useState('');
    const handleClose = () => setShow(false);
    const handleShow = (row) => {
        setProductoEditable(row)
        setShow(true);
    }

    const [showAgregar, setShowAgregar] = useState(false);
    const handleCloseAgregar = () => setShowAgregar(false);
    const handleShowAgregar = (e,row) => {
        e.preventDefault()
        setShowAgregar(true);
    }

    const [showFinalizarPedido, setShowFinalizarPedido] = useState(false);
    const handleCloseFinalizarPedido = () => {
        setShowFinalizarPedido(false);
        window.location.reload()
    }
    const handleShowFinalizarPedido = () => {
        setShowFinalizarPedido(true);
    }

    const [productoAMostar, setProductoAMostrar] = useState('')
    const [showImagen, setShowImagen] = useState('');
    const handleCloseImagen = () => setShowImagen(false);
    const handleShowImagen = (producto) => {
        setProductoAMostrar(producto)
        setShowImagen(true);
    }

    const dispatch = useDispatch()

    const handleChangeAutocomplete = (e,v,p) =>{
        e.preventDefault()
        dispatch(modificarDescuentoArticulo(p.productoid, p.facturadetalleid, v))
    }

    const handleChangeCantidad = (v,p) =>{
        dispatch(modificarCantidadArticulo(p.facturadetalleid, v))
    }

    const handleAddToCart =(cliente)=>{
        dispatch(agregarArticulo(cliente, product, cantidad, descuento, tipofactura, facturaid))
        dispatch(getProductosByNameSkuNuevaVenta('')) 
        setProduct('')
        setCantidad('1')
    }

    //clienteid, facturadetalleid, tipofactura, facturaid
    const handleDeleteProduct =(p)=>{
        dispatch(eliminarArticulo(clienteid, p.facturadetalleid, tipofactura, facturaid))
    }

    //clienteid, tipoenvio, tipopago, facturaid, tipofactura
    const handleFinalizarPedido =()=>{
        dispatch(finalizarPedido(clienteid, tipoEnvio, tipoPago, facturaid, tipofactura))
        setOk({ok: false, tipo: ''})
        dispatch(getProductosByNameSkuNuevaVenta('')) 
        handleShowFinalizarPedido()
        setProduct('')
        setCantidad('1')
    }

    const matchDescuento = (porcentaje) =>{
        let aux = descuentos.filter((d) => d.descuento === porcentaje)
        return aux[0]
    }
    
    const [showCancelar, setShowCancelar] = useState('');
    const handleCloseCancelar = () => setShowCancelar(false);
    const handleShowCancelar = () => {
        setShowCancelar(true);
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [openCancelar, setOpenCancelar] = React.useState(false);
    const handleClickAlertCancelar = () => {
      setOpenCancelar(true);
    };
    const handleCloseAlertCancelar = () => {
        setOpenCancelar(false);
    };

    const [openDetalle, setOpenDetalle] = React.useState(false);
    const handleClickAlertDetalle = () => {
      setOpenDetalle(true);
    };
    const handleCloseAlertDetalle = () => {
        setOpenDetalle(false);
    };

    const handleSearch =()=>{
        dispatch(getCarritoDetalleVerificado(clienteid, nrofactura, page,tipofactura))
        setTimeout(function(){
            handleClickAlertDetalle()
        }, 2000);
    }

    useEffect(() => {
        if(okCancelar.success){
            window.location.reload()
        }
    },[okCancelar])

    useEffect(() =>{
        dispatch(getDescuentosSinPaginado())
        dispatch(getMetodosEnvio())
        dispatch(getTiposDePago())
        dispatch(getTiposTarjeta())
        dispatch(getCuotasTarjeta())
        dispatch(getDatosTienda())
    }, [])

    useEffect(() =>{
        if(okAgregarCliente !== ''){
            dispatch(getListadoClientesByName('',okAgregarCliente.clienteid))
            setClienteid(okAgregarCliente.clienteid)
        }
    }, [okAgregarCliente])

    useEffect(() =>{
        if(clienteInput.length > 4){
            dispatch(getListadoClientesByName(clienteInput,''))
        }
    }, [clienteInput])

    useEffect(() =>{
        if(input.length > 4){
            dispatch(getProductosByNameSkuNuevaVenta(input)) 
        }
    }, [input])

    useEffect(() =>{
        if(okAgregarArticulo.success && clienteid !== ''){
            setOk({ok: true, tipo: tipofactura})
            setFactura(okAgregarArticulo.facturaid)
            dispatch(getCarritoDetalle(clienteid, okAgregarArticulo.facturaid, 1,tipofactura))
            dispatch(getCalcularCarrito(clienteid, descuento, tipoCuotas.interes,tipofactura,okAgregarArticulo.facturaid))
        }
    }, [okAgregarArticulo])

    useEffect(() => {
        if(clienteid !== ''){
            dispatch(getCarritoDetalle(clienteid, facturaid, page, tipofactura))
        }
    },[page])

    useEffect(() => {
        if(clienteid !== '' && okModificarDescuentoArticulo !== ''){
            dispatch(getCarritoDetalle(clienteid, facturaid, page, tipofactura))
            dispatch(getCalcularCarrito(clienteid, descuento, tipoCuotas.interes,tipofactura,okAgregarArticulo.facturaid))
        }
    },[okModificarDescuentoArticulo])

    useEffect(() => {
        if(clienteid !== '' && okModificarCantidadArticulo !== ''){
            dispatch(getCarritoDetalle(clienteid, facturaid, page, tipofactura))
            dispatch(getCalcularCarrito(clienteid, descuento, tipoCuotas.interes,tipofactura,okAgregarArticulo.facturaid))
        }
    },[okModificarCantidadArticulo])

    useEffect(() =>{
        if(okEliminarArticulo !== ''){
            dispatch(getCarritoDetalle(clienteid, facturaid, page, tipofactura))
        }
    }, [okEliminarArticulo])

    useEffect(() =>{
        if(presupuestoCreado !== ''){
            setClienteInput(presupuestoCreado.cabecera.nombrecliente)
            setClienteid(presupuestoCreado.cabecera.clienteid)
            dispatch(getCalcularCarrito(presupuestoCreado.cabecera.clienteid, 
                descuento, tipoCuotas.interes,tipofactura,presupuestoCreado.cabecera.presupuestoplacaid))
            setDetalle()
        }
    }, [presupuestoCreado])

    return (
       <div className='ventas-container'>
        <ModalImagenProducto
            show={showImagen}
            handleClose={handleCloseImagen}
            producto={productoAMostar}
        />
        <ModalAgregarCliente  
            show={showAgregar}   
            handleClose={handleCloseAgregar}   
        />
        <ModalFinalizarCompra
            show={showFinalizarPedido}   
            handleClose={handleCloseFinalizarPedido}   
            okFinalizarPedido={okFinalizarPedido} 
            tipocomprobante={tipocomprobante}
            nombretipocomprobante={tipofactura}
        />
        <ModalProductoEditable
            show={show}
            handleClose={handleClose}
            producto={productoEditable}
        />
        <ModalCancelarFactura
            show={showCancelar}
            handleClose={handleCloseCancelar}
            info={facturaid}
            tipofactura={tipofactura}
            handleOnClickAlert={handleClickAlertCancelar}
        />
        <Snackbar
            open={openCancelar} 
            autoHideDuration={10000} onClose={handleCloseAlertCancelar}
            anchorOrigin={
                {vertical: 'top',
                horizontal: 'center',}
            }>
            <Alert onClose={handleCloseAlertCancelar} 
                severity={okCancelar.success === true ? "success" : "error"}
                sx={{ width: 400 }}>
                <h5>{okCancelar.mensaje}</h5>
            </Alert>
        </Snackbar>
        <Snackbar
            open={openDetalle} 
            autoHideDuration={10000} onClose={handleCloseAlertDetalle}
            anchorOrigin={
                {vertical: 'top',
                horizontal: 'center',}
            }>
            <Alert onClose={handleCloseAlertDetalle} 
                severity={okTraerFactura === true ? "success" : "error"}
                sx={{ width: 400 }}>
                {okTraerFactura === true ? <h5>Se encontr&oacute; una factura!</h5> 
                : <h5>No se encontr&oacute; una factura</h5>}
            </Alert>
        </Snackbar>
        <div style={{display:"flex"}}>
        <Paper style={{display:"flex", flexDirection:"column", justifyContent: "space-between", padding:20, width:"30%", marginRight:10}}>
            <div style={{width:"100%"}}>
                <h4><strong>Presupuesto</strong></h4>

                <div style={{display:"flex", width:"100%"}}>
                    <FormControl fullWidth sx={{mt:3}}>
                        <InputLabel id="cliente-select-label">Cliente</InputLabel>
                        <Select
                            labelId="cliente-select-label"
                            id="cliente"
                            label="Cliente"
                            size="small"
                            name="clienteid"
                            value={clienteid || ''}
                            onChange={(e)=>setClienteid(e.target.value)}
                            >
                            <TextField
                                value={clienteInput}
                                name="clienteInput"
                                onChange={handleChangeClienteInput}
                                fullWidth
                                style={{padding:10}}
                            />
                            {               
                                clientes && clientes.map((c) =>
                                    <MenuItem value={c.clienteid} key={c.clienteid}>
                                    {(c.nombre.length + c.apellido.length) > 30 ?
                                        c.cuit+'-'+c.nombre.slice(0,10)+'.. '+c.apellido.slice(0,10)+'..' :
                                        c.cuit+'-'+c.nombre+' '+c.apellido
                                    }</MenuItem>
                                )
                            }
                        </Select>
                    </FormControl>
                    <Button variant='contained'
                        onClick={handleShowAgregar}
                        sx={{mt:3, ml:1}}
                    >+</Button>
                </div>
                <div style={{display:"flex", width:"100%", alignItems:"center"}}>
                    <TextField 
                        label="N&uacute;mero de factura"
                        fullWidth
                        sx={{mt:3}}
                        variant="outlined" 
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                        name="nrofactura"
                        value={nrofactura || ''}
                        onChange={handleChangeNrofactura}
                    />
                    <Button variant='contained'
                        onClick={handleSearch}
                        sx={{mt:3, ml:1}}
                    ><Search/></Button>
                </div>
                <Autocomplete
                    options={descuentos}
                    getOptionLabel={(option) => option.nombre}
                    isOptionEqualToValue={(option, value) => option.descuentovtaid === value.descuentovtaid}
                    onChange={(event,value) => setDescuento(value.descuentovtaid)}
                    renderInput={(params) => (
                    <TextField
                        sx={{mt:3}}
                        {...params}
                        variant="outlined" 
                        label="Descuento"
                        InputLabelProps={{
                            style: {
                                marginBottom:10,
                                marginTop: -7
                            },
                        }}
                    />
                    )}
                /> 
                <div style={{display:"flex", width:"100%"}}>
                    <FormControl fullWidth sx={{mt:3}}>
                        <InputLabel id="metododepago-select-label">M&eacute;todo de pago</InputLabel>
                        <Select
                            labelId="metododepago-select-label"
                            id="metododepago"
                            label="M&eacute;todo de pago"
                            size="small"
                            name="tipoPago"
                            value={tipoPago || ''}
                            onChange={(e)=>setTipoPago(e.target.value.toString())}
                            >
                            {               
                                tiposDePago && tiposDePago.map((c, index) =>
                                    <MenuItem value={index+1} key={index+1}>{c.nombre}</MenuItem>
                                )
                            }
                        </Select>
                    </FormControl>
                </div>
                {
                    tipoPago === "4" &&
                    <Autocomplete
                        options={tiposDeTarjetas}
                        getOptionLabel={(option) => option.nombre}
                        isOptionEqualToValue={(option, value) => option.nombre === value.nombre}
                        onChange={(event,value) => setTipoTarjeta(value)}
                        renderInput={(params) => (
                        <TextField
                            sx={{mt:3}}
                            {...params}
                            variant="outlined" 
                            label="Tipo de tarjeta"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                        />
                        )}
                    />
                }
                {
                    tipoTarjeta.nombre === "CREDITO" &&
                    <Autocomplete
                        options={cuotasTarjetas}
                        getOptionLabel={(option) => option.cuotas+' ('+option.interes+'%)'}
                        onChange={(event,value) => setTipoCuotas(value)}
                        renderInput={(params) => (
                        <TextField
                            sx={{mt:3}}
                            {...params}
                            variant="outlined" 
                            label="Cuotas"
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                        />
                        )}
                    />
                }
                </div> 
                <div>
                    <Divider style={{margin:20}}/>
                    <h5 style={{display:"flex", justifyContent:"space-between"}}>
                        <strong>Sub total</strong>{calcularCarrito.simbolo}{calcularCarrito.subtotal}</h5>
                    <h5 style={{display:"flex", justifyContent:"space-between"}}>
                        <strong>Interes %</strong>{calcularCarrito.intereses}</h5>
                    <h5 style={{display:"flex", justifyContent:"space-between"}}>
                        <strong>Total a pagar</strong>{calcularCarrito.simbolo}{calcularCarrito.total}</h5>
                    <Divider style={{margin:20}}/>
                </div>
                <div style={{display:"flex", width:"100%", justifyContent:"space-around", alignItems:"center", padding:10}}>
                    <Button variant="contained" color='error' style={{fontSize:16}} onClick={handleShowCancelar}>
                        Cancelar
                    </Button>
                    <Button variant="contained" style={{fontSize:16}} onClick={handleFinalizarPedido} >Finalizar</Button>
                </div>
            </Paper>
            <Paper style={{padding:20, width:"70%", display:"flex", flexDirection:"column", alignItems:"center"}}>
                <div style={{display:"flex", width:"100%", justifyContent:"space-between", alignItems:"center", height:70}}>
                    <div style={{width:"80%", marginRight:20}}>
                        <h6><strong>Producto</strong></h6>
                        <Select
                            size="small"
                            fullWidth
                            value={product || ""}
                            name='product'
                            style={{height:35}}
                            onChange={(e)=>setProduct(e.target.value)}
                            >
                            <TextField
                                value={input}
                                name="input"
                                onChange={handleChange}
                                fullWidth
                                style={{padding:10}}
                            />
                            {               
                                productsByNameSku && productsByNameSku.map((t) =>
                                    <MenuItem 
                                    value={t.codigoproducto} 
                                    key={t.codigoproducto}>
                                    {t.codigoproducto} - {t.nombreproducto.length > 70 ? t.nombreproducto.slice(0,65)+'..' : t.nombreproducto}</MenuItem>
                                )
                            }
                        </Select>
                    </div>
                    <div style={{width:"10%", marginRight:20}}>
                        <h6><strong>Cantidad</strong></h6>
                        <TextField
                            style={{width:"100%"}}
                            type='number'
                            name='cantidad'
                            value={cantidad}
                            onChange={(e)=>setCantidad(e.target.value)}
                        />
                    </div>
                    <Tooltip title="Agregar al carrito">
                    <Button 
                        style={{marginTop:25}} 
                        variant='contained'  
                        onClick={(e)=>handleAddToCart(clienteid)}
                    ><ShoppingCart/></Button>
                    </Tooltip>  
                </div>
                <TableContainer component={Paper} style={{marginTop:20}}>
                    <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}}>#</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Item</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Cantidad</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Precio unitario</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Descuento</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center">Total</TableCell>
                            <TableCell style={{fontWeight:"bold", fontSize:"100%"}} align="center"></TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {detalleCarrito && detalleCarrito.map((row) => (
                        <TableRow
                            // key={row.facturaid}
                            sx={{ '&:last-child td, &:last-child th': { border: 0 }}}
                        >
                            {
                                row.editadetalle === "1" ?
                                <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0, cursor:"pointer"}} align="center"
                                    onClick={(e) => handleShow(row)} 
                                >{row.codigobarras}</TableCell> :
                                <TableCell style={{fontSize:"14px", width:"80px", height:"40px", padding: 0}} align="center"
                                >{row.codigobarras}</TableCell>
                            }
                            <TableCell style={{fontSize:"14px", width:"160px", height:"40px", padding: 0, cursor:"pointer"}} align="center"
                                onClick={(e) => handleShowImagen(row)}
                            >{row.nombre}</TableCell>
                            <TableCell style={{fontSize:"14px", width:"40px", height:"40px", padding: 0}} align="center"
                            ><TextField
                                style={{width:"45%"}}
                                type='number'
                                value={row.cantidad}
                                onChange={(e) => handleChangeCantidad(e.target.value, row)}
                            /></TableCell>
                            <TableCell style={{fontSize:"14px", width:"70px", height:"40px", padding: 0}} align="center"
                            >${row.costounitario}</TableCell>
                            <TableCell style={{fontSize:"14px", width:"140px", height:"40px", padding: 2}} align="center"
                            >
                            <Autocomplete
                                onChange={(event,value) => handleChangeAutocomplete(event,value,row)}
                                options={descuentos}
                                defaultValue={matchDescuento(row.descuento)}
                                getOptionLabel={(option) => option.nombre}
                                isOptionEqualToValue={(option, value) => option.descuento === value.descuento}
                                renderInput={(params) => (
                                <TextField
                                    {...params}
                                    variant="outlined" 
                                    InputLabelProps={{
                                        style: {
                                            marginBottom:10,
                                            marginTop: -7
                                        },
                                    }}
                                />)}
                            />
                             </TableCell>
                            <TableCell style={{fontSize:"14px", width:"60px", height:"40px", padding: 0}} align="center"
                            >${row.montototal}
                            </TableCell>
                            <TableCell style={{fontSize:"14px", width:"60px", height:"40px", padding: 0, cursor:"pointer"}} align="center"
                            onClick={(e)=>handleDeleteProduct(row)}><Delete/></TableCell>
                        </TableRow>
                        ))}
                    </TableBody>
                    </Table>
                </TableContainer>
                <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center", margin:3}} />
            </Paper>
        </div>
       </div>
    )
}