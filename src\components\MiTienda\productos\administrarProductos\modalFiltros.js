import React, { useState } from "react"
import Modal from 'react-modal';

import Divider from '@mui/material/Divider';
import { 
    Button, 
    Checkbox, 
    FormControl, 
    FormControlLabel, 
    InputAdornment, 
    InputLabel, 
    ListSubheader, 
    MenuItem, 
    Select, 
    TextField 

} from "@mui/material";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { 
    getCategoriasByName, 
    getMarcasByName, 
    getTallasByName 
} from "../../../../redux/actions/mitienda.actions";
import { useSelector } from "react-redux";
import { Search } from "@mui/icons-material";

Modal.setAppElement("#root")

export const ModalFiltros = ({show, 
    handleClose, 
    setMarca, 
    setCategoriaid, 
    setTalla,
    handleChangeStock,
    stock,
    search, 
    reset
}) =>{

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          height: "80vh",
          borderRadius: "8px",
          maxWidth: "650px",
          right: '50px',
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const categorias = useSelector((state) => state.mitienda.categoriasByName)
    const marcas = useSelector((state) => state.mitienda.marcasByName)
    const tallas = useSelector((state) => state.mitienda.tallasByName)

    const [nombreCategoria, setNombreCategoria] = useState('')
    const [nombreMarca, setNombreMarca] = useState('')
    const [nombreTalla, setNombreTalla] = useState('')
    const [selectedOption, setSelectedOption] = useState('');
    const [selectedOptionMarca, setSelectedOptionMarca] = useState('');
    const [selectedOptionTalla, setSelectedOptionTalla] = useState('');

    const dispatch = useDispatch()

    useEffect(() =>{
        if(nombreCategoria.length > 2){
            dispatch(getCategoriasByName(nombreCategoria))
        }
    }, [nombreCategoria])

    useEffect(() =>{
        if(nombreMarca.length > 2){
            dispatch(getMarcasByName(nombreMarca))
        }
    }, [nombreMarca])

    useEffect(() =>{
        if(nombreTalla.length > 1){
            dispatch(getTallasByName(nombreTalla))
        }
    }, [nombreTalla])

    useEffect(() =>{
        if(show){
            setNombreCategoria('')
            setNombreMarca('')
            setNombreTalla('')
        }
    }, [show])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
            <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                <h4>FILTRAR</h4>
                <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
            </div>
            <Divider/>
            <div style={{display:"flex", flexDirection:"column"}}>
                <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                    <InputLabel id="search-select-label">Marcas</InputLabel>
                    <Select
                    // Disables auto focus on MenuItems and allows TextField to be in focus
                    MenuProps={{ autoFocus: false,  disablePortal: true }}
                    labelId="search-select-label"
                    id="search-select"
                    value={selectedOptionMarca.marcaid || ''}
                    label="Marcas"
                    onClose={() => {
                        dispatch(getMarcasByName(""))
                    }}
                    size="small"
                    // This prevents rendering empty string in Select's value
                    // if search text would exclude currently selected option.
                    renderValue={() => selectedOptionMarca.nombremarca}
                    >
                    {/* TextField is put into ListSubheader so that it doesn't
                        act as a selectable item in the menu
                        i.e. we can click the TextField without triggering any selection.*/}
                    <ListSubheader>
                    <TextField
                        size="small"
                        // Autofocus on textfield
                        autoFocus
                        placeholder="Buscar marca..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreMarca(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {marcas.map((option, i) => (
                        <MenuItem key={i} value={option.marcaid} onClick={() => {
                            setSelectedOptionMarca(option)
                            setNombreMarca(option.nombremarca)
                            setMarca(option.marcaid)
                        }}>
                        {option.nombremarca}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>

                <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                    <InputLabel id="search-select-label">Categor&iacute;as</InputLabel>
                    <Select
                    // Disables auto focus on MenuItems and allows TextField to be in focus
                    MenuProps={{ autoFocus: false,  disablePortal: true }}
                    labelId="search-select-label"
                    id="search-select"
                    value={selectedOption.categoriaid || ''}
                    label="Categor&iacute;as"
                    onClose={() => {
                        dispatch(getCategoriasByName(""))
                    }}
                    size="small"
                    // This prevents rendering empty string in Select's value
                    // if search text would exclude currently selected option.
                    renderValue={() => selectedOption.nombrecategoria}
                    >
                    {/* TextField is put into ListSubheader so that it doesn't
                        act as a selectable item in the menu
                        i.e. we can click the TextField without triggering any selection.*/}
                    <ListSubheader>
                    <TextField
                        size="small"
                        // Autofocus on textfield
                        autoFocus
                        placeholder="Buscar categor&iacute;a..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreCategoria(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {categorias.map((option, i) => (
                        <MenuItem key={i} value={option.categoriaid} onClick={() => {
                            setSelectedOption(option)
                            setNombreCategoria(option.nombrecategoria)
                            setCategoriaid(option.categoriaid)
                        }}>
                        {option.nombrecategoria}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>

                <FormControl fullWidth sx={{marginTop:2, marginBottom:2}}>
                    <InputLabel id="search-select-label">Medidas</InputLabel>
                    <Select
                    // Disables auto focus on MenuItems and allows TextField to be in focus
                    MenuProps={{ autoFocus: false,  disablePortal: true }}
                    labelId="search-select-label"
                    id="search-select"
                    value={selectedOptionTalla.tallaid || ''}
                    label="Medidas"
                    onClose={() => {
                        dispatch(getTallasByName(""))
                    }}
                    size="small"
                    // This prevents rendering empty string in Select's value
                    // if search text would exclude currently selected option.
                    renderValue={() => selectedOptionTalla.nombretalla}
                    >
                    {/* TextField is put into ListSubheader so that it doesn't
                        act as a selectable item in the menu
                        i.e. we can click the TextField without triggering any selection.*/}
                    <ListSubheader>
                    <TextField
                        size="small"
                        // Autofocus on textfield
                        autoFocus
                        placeholder="Buscar medida..."
                        fullWidth
                        InputProps={{
                            startAdornment: (
                            <InputAdornment position="start">
                                <Search />
                            </InputAdornment>
                            )
                        }}
                        inputProps={{
                            style: {
                                height: 35,
                                fontSize:17
                            },
                        }}
                        onChange={(e) => setNombreTalla(e.target.value)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    />
                    </ListSubheader>
                    {tallas.map((option, i) => (
                        <MenuItem key={i} value={option.tallaid} onClick={() => {
                            setSelectedOptionTalla(option)
                            setNombreTalla(option.nombretalla)
                            setTalla(option.tallaid)
                        }}>
                        {option.nombretalla}
                        </MenuItem>
                    ))}
                    </Select>
                </FormControl>

                <div style={{display:"flex", width:400, marginLeft:5, marginTop:30, alignItems:"center"}}>
                    <h5>Trae con y sin stock</h5>
                    <FormControlLabel
                        control={
                        <Checkbox/>
                        }
                        style={{color:"black", marginLeft:1}}
                        onChange={(e)=> handleChangeStock(e)}
                        checked={stock === "1" ? true : false}
                        name="stock"
                    />
                </div>
            </div>
            <div align="center">
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={search}
            >Aplicar filtros</Button>
            <Button 
                size="large" 
                variant="contained" 
                sx={{mt: 3}} fullWidth 
                onClick={() => {
                    reset()
                    setNombreCategoria('')
                    setNombreMarca('')
                    setNombreTalla('')
                    setSelectedOption('')
                    setSelectedOptionMarca('')
                    setSelectedOptionTalla('')
                    dispatch(getTallasByName(""))
                    dispatch(getMarcasByName(""))
                    dispatch(getTallasByName(""))
                }}
            >Limpiar filtros</Button>
        </div>
        </Modal>
    )
}