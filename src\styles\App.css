* {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  
}

p {
  font-size: 14px;
}
.column {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.scroll-area {
  overflow-y: auto;
  overflow-x: auto;
}
.scroll-area::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: rgba(126, 126, 126, 0.5);
}
.scroll-area::-webkit-scrollbar-thumb {
  width: 15px;
  background-color: rgba(151, 151, 151);
}
.flex-1 {
  flex: 1;
}
.center-all {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  flex-direction: column;
}

.strong-text {
  font-weight: bolder;
}
.touchable {
  cursor: pointer;
  transition: all 200ms;
}
.touchable:active {
  opacity: 0.5;
}
.animate {
  transition: all 200ms;
}
.shadow {
  box-shadow: -5px 1px 15px rgba(0, 0, 0, 0.2);
}
.opacity-low {
  opacity: 0.5;
}

.fadein {
  animation: 300ms linear fadein;
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
