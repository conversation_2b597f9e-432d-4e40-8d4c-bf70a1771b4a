.table-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: auto;
}
.styled-table {
    width: 100%;
}
td {
  vertical-align: super;
  border: solid 1px rgb(80, 80, 80);
  padding: 0%;
  margin: 0%;
  padding: 12px 8px;
}
th {
  padding: 12px 8px;
  border: solid 1px rgb(80, 80, 80);
  font-weight: lighter;
}
thead {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #383838;
  border: solid 1px rgb(80, 80, 80);
}
tbody tr:hover {
  background-color: rgba(117, 117, 117, 0.1);
  opacity: 1;
}
.thead-dark th {
  background-color: #272727;
}
.thead-success th {
  background-color: #b5cf3e;
  color: #272727;
}
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  outline: none;
}