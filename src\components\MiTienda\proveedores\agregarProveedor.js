import React, { useEffect, useState } from "react"

import { Button, 
    Checkbox, 
    FormControl, 
    FormControlLabel, 
    InputLabel, 
    MenuItem, 
    Paper, 
    Select, 
    Snackbar, 
TextField } from "@mui/material";

import { useDispatch, useSelector } from "react-redux";
import MuiAlert from '@mui/material/Alert';
import { agregarProveedorTienda, getCondicionesIVA, getTiposDocumentoTienda } from "../../../redux/actions/mitienda.actions";

export const Proveedor =()=>{

    const ok = useSelector((state) => state.mitienda.okConfigProveedor)
    const tiposDocumentos = useSelector((state) => state.mitienda.tiposDocumentos)
    const condicionesIva = useSelector((state) => state.mitienda.condicionesIva)

    let pathname = window.location.pathname 
    let permisos_acciones = Object.values(JSON.parse(localStorage.getItem('permisos_acciones')))
    .filter((p) => p.archivo.toLowerCase().includes(pathname.toLocaleLowerCase()))[0]

    const dispatch = useDispatch()

    const [input, setInput] = useState({
        nombre: '',
        tipodocumentoid:'',
        numerodocumento: '',
        telefono:'',
        email:'',
        emailfacturacion:'',
        tipocondicioniva:'',
        descuento:'',
        activo:"0"
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const handleClick = () => {
        dispatch(agregarProveedorTienda(input))
        setTimeout(function(){
            handleClickAlert()
        }, 2000);
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });
    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }

    useEffect(() =>{
        let isMounted = true;
        if(isMounted){
            dispatch(getTiposDocumentoTienda())
            dispatch(getCondicionesIVA())
        }
        return () => {
            isMounted = false
        }
    }, [])

    return (
        <section className="container-chat">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{ok.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <header className="header-chat">
                <h3 style={{marginBottom:20}}>Proveedor</h3>
            </header>
            <Paper className="form-chat">
                <TextField 
                    label="Nombre"  
                    name="nombre"
                    value={input.nombre || ''}
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <FormControl fullWidth sx={{mb:2, mt:2}}>
                    <InputLabel id="tipodocumento-select-label">Tipo documento</InputLabel>
                    <Select
                    labelId="tipodocumento-select-label"
                    label="Tipo documento"
                    size="small"
                    name="tipodocumentoid"
                    value={input.tipodocumentoid || ''}
                    onChange={(e) => handleChange(e)}
                    color="secondary"
                    >
                    <MenuItem value="">Seleccione una opcion</MenuItem>
                    {               
                        tiposDocumentos && tiposDocumentos.map((t) =>
                            <MenuItem value={t.tipodocidentidadid} key={t.tipodocidentidadid}>{t.nombre}</MenuItem>
                        )
                    }
                    </Select>
                </FormControl>
                <TextField 
                    label="N&uacute;mero de documento"  
                    name="numerodocumento"
                    value={input.numerodocumento || ''}
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <TextField 
                    label="Tel&eacute;fono"  
                    name="telefono"
                    value={input.telefono || ''}
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <TextField 
                    label="Email"  
                    name="email"
                    value={input.email || ''}
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <TextField 
                    label="Email facturaci&oacute;n"  
                    name="emailfacturacion"
                    value={input.emailfacturacion || ''}
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <FormControl fullWidth sx={{mb:2, mt:2}}>
                    <InputLabel id="condicioniva-select-label">Condicion IVA</InputLabel>
                    <Select
                    labelId="condicioniva-select-label"
                    label="Condicion IVA"
                    size="small"
                    name="tipocondicioniva"
                    value={input.tipocondicioniva || ''}
                    onChange={(e) => handleChange(e)}
                    color="secondary"
                    >
                    <MenuItem value="">Seleccione una opcion</MenuItem>
                    {               
                        condicionesIva && condicionesIva.map((t) =>
                            <MenuItem value={t.tipocondicionivaid} key={t.tipocondicionivaid}>{t.nombre}</MenuItem>
                        )
                    }
                    </Select>
                </FormControl>
                <TextField 
                    label="Descuento"  
                    name="descuento"
                    value={input.descuento || ''}
                    fullWidth margin="normal" 
                    onChange={(e)=> handleChange(e)}
                    InputLabelProps={{
                        style: {
                        marginBottom:10,
                        marginTop: -7
                        },
                    }}
                    inputProps={{
                        style: {
                        height: 40,
                        fontSize:17
                        },
                    }}
                />
                <div style={{marginTop:15, marginBottom:5}}>
                <FormControlLabel
                    control={
                    <Checkbox/>
                    }
                    style={{color:"black"}}
                    onChange={(e)=> handleCheck(e)}
                    checked={input.activo === "1" ? true : false}
                    label="Activo"
                    name="activo"
                />
                </div>
            </Paper>
            <div align="right">
                <Button 
                    size="large" 
                    variant="contained" 
                    onClick={(e)=>(handleClick(e))}
                    disabled={permisos_acciones.agregar === "0"}
                    sx={{mt: 3, mr:3}}>Guardar</Button>
            </div>
        </section>
    )
}