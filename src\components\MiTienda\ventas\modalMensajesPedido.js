import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { But<PERSON>, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { enviarMensaje, getMensajesPedido } from "../../../redux/actions/mitienda.actions";
import Alert from '@mui/material/Alert';

Modal.setAppElement("#root")

export const ModalMensajesPedido = ({show, handleClose, pedido}) =>{

    const mensajes = useSelector((state) => state.mitienda.messagesByPedido)
    const okMensaje = useSelector((state) => state.mitienda.okSendMessage)
    
    var idusuario = localStorage.getItem('idusuario')

    const dispatch = useDispatch()

    const customStyles = {
        content: {
          padding: "40px",
          inset: "unset",
          width: "100%",
          maxHeight: "95vh",
          overflow:"hidden",
          borderRadius: "8px",
          maxWidth: "650px",
        //   backgroundColor: "#D1D1D1"
        },
        overlay: {
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "grid",
          placeItems: "center",
          zIndex: "100000",
        },
    };

    const formatoFecha = (fecha) =>{
        let aux = fecha.slice(0,10)
        let aux2 = aux.split('-')
        
        return aux2[2]+'-'+aux2[1]+'-'+aux2[0]
    }

    const [input, setInput] = useState({
        titulo: '',
        mensaje: '',
        usuariodestinoid: '',
        pedidoid: ''
    })

    const handleChange = (e) => {
        e.preventDefault()
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }
    

    const handleOnClick = (e) => {
        e.preventDefault()
        dispatch(enviarMensaje(input))
        setInput({
            titulo: `Pedido #${pedido[0].pedidoplacaid}`,
            mensaje: '',
            usuariodestinoid: pedido[0].clienteid,
            pedidoid: pedido[0].pedidoplacaid
        })
    }

    useEffect(() =>{
        if(pedido){
            dispatch(getMensajesPedido(pedido[0].pedidoplacaid))
        }
    },[okMensaje])

    useEffect(() =>{
        if(pedido){
            dispatch(getMensajesPedido(pedido[0].pedidoplacaid))
            setInput({
                titulo: `Pedido #${pedido[0].pedidoplacaid}`,
                mensaje: '',
                usuariodestinoid: pedido[0].clienteid,
                pedidoid: pedido[0].pedidoplacaid
            })
        }
    },[show,pedido])

    return (
        show && 
        <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
                <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
                    <h4>Mensajes - Pedido {pedido[0].pedidoplacaid}</h4>
                    <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
                </div>
                <Divider/>

                <div style={{height:300, display:"flex", flexDirection:"column", margin:5, overflow:"scroll", overflowX:"hidden"}}>
                    {
                        mensajes && mensajes.map((row) => 

                        <div style={{margin:5, width:450,
                            alignSelf: `${row.usuariodestinoid === "0" ? "self-end" : "self-start"}`
                        }}>
                            <p style={{fontSize:11,
                                textAlign:`${row.usuariodestinoid === "0" ? "end" : "start"}`
                            }}>{formatoFecha(row.fecha)}</p>
                            <Alert icon={false} severity="info" style={{width:450,
                                alignSelf: `${row.usuariodestinoid === "0" ? "self-end" : "self-start"}`
                            }}>
                                <div style={{width:400}}>
                                    {/* <AlertTitle
                                        style={{textAlign:`${row.usuariodestinoid === idusuario ? "end" : "start"}`}}
                                    >{row.usuariodestinoid === idusuario ? "Yo" : pedido[0].nombre}</AlertTitle> */}
                                    {/* <p 
                                        style={{fontSize:14, 
                                        textAlign:`${row.usuariodestinoid === idusuario ? "end" : "start"}`
                                    }}>{row.titulo}</p> */}
                                    <p 
                                        style={{fontSize:13,
                                        textAlign:`${row.usuariodestinoid === "0" ? "end" : "start"}`
                                    }}>{row.descripcion}</p>
                                </div>

                                <p style={{fontSize:12,
                                    textAlign:`${row.usuariodestinoid === "0" ? "start" : "end"}`
                                }}>{row.hora}</p>
                                
                                {/* {row.usuariodestinoid === pedido[0].clienteid ? 
                                <div style={{display:"flex"}}>
                                <p style={{fontSize:11, marginRight:5
                                }}>
                                    <DoneAll style={{fontSize:18, 
                                        color:`${row.leido === "1" ? "blue" : "grey"}`
                                    }}/>
                                </p> 
                                <p style={{fontSize:12,
                                    textAlign:`${row.usuariodestinoid === idusuario ? "start" : "end"}`
                                }}>{row.hora}</p>
                                </div>
                                : 
                                <p style={{fontSize:12,
                                    textAlign:`${row.usuariodestinoid === idusuario ? "start" : "end"}`
                                }}>{row.hora}</p>
                                } */}
                            </Alert>

                        </div>

                        )
                    }
                </div>

                <TextField
                    fullWidth
                    style={{marginTop:20,marginBottom:20}}
                    label="Mensaje"  
                    variant="outlined"  
                    name="mensaje"
                    value={input.mensaje}
                    onChange={(e)=>handleChange(e)}
                    multiline
                    minRows={3}
                    InputLabelProps={{
                        style: {
                            marginBottom:10,
                            marginTop: -7
                        },
                    }}
                />
                
                <Divider/>

                <div align="right">
                    <Button 
                    disabled={input.mensaje === ""}
                    size="large"
                    variant="contained"
                    onClick={handleOnClick}
                    sx={{mt: 3, mr:1}}>Enviar</Button>
                </div>
        </Modal>
    )
}