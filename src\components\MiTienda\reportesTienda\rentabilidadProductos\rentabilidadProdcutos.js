import React, { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Pagination from '@mui/material/Pagination';
import { useDispatch, useSelector } from 'react-redux';
import { 
    getReporteRentabilidadProductos 

} from '../../../../redux/actions/mitienda.actions'
import { 
    ModalFiltrosRentabilidadProductos 

} from './modalFiltrosRentabilidadProductos';
import ClipLoader from "react-spinners/ClipLoader";


export const RentabilidadProductos = () => {

    //Obtengo la informacion para la tabla
    const info = useSelector((state) => state.mitienda.reporteRentabilidadProductos)

    //Obtengo la ultima pagina para el componente Pagination
    const paginaUltima = useSelector((state) => state.mitienda.paginaUltimaReporteRentabilidadProductos)
    //Creo la constante que usare para cambiar las paginas y su handle change
    const [page, setPage] = useState(1);
    const handleChangePage = (event, value) => {
        setPage(value);
    };

    //Declaro la variable para despachar acciones
    const dispatch = useDispatch()

    //Manejo el abrir y cerrar Modal de Filtros
    const [show, setShow] = useState(false);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);

    //Declaro las constantes que utilizare para filtrar la informacion
    const [codigo, setCodigo] = useState('')
    const handleChangeCodigo = (e) =>{
        setCodigo(e.target.value)
    }

    const [nombreProducto, setNombreProducto] = useState('')
    const handleChangeNombreProducto = (e) =>{
        setNombreProducto(e.target.value)
    }

    const [marcaid, setMarcaid] = useState('')
    const handleChangeMarcaid = (e) =>{
        setMarcaid(e.target.value)
    }

    const [categoriaid, setCategoriaid] = useState('')
    const handleChangeCategoriaid = (e) =>{
        setCategoriaid(e.target.value)
    }

    //Funcion para el boton Aplicar filtrps
    const searchByFilters = () =>{
        setPage(1)
        dispatch(getReporteRentabilidadProductos(codigo,nombreProducto,marcaid,categoriaid,page))
        handleClose()
    }


    //Funcion para el boton Limpiar filtros, se reinician las constantes a sus valores iniciales
    const reset = (e) =>{
        setCodigo('')
        setNombreProducto('')
        setMarcaid('')
        setCategoriaid('')
        dispatch(getReporteRentabilidadProductos('','','','',1))
        handleClose()
    }

    const [loading, setLoading] = useState(true)
    useEffect(() => {
        setLoading(false)
    },[info])

    useEffect(() => {
        setLoading(true)
        dispatch(getReporteRentabilidadProductos(codigo,nombreProducto,marcaid,categoriaid,page))
    },[page])

    return (
        <div className="notificaciones-container">
            {
                <ModalFiltrosRentabilidadProductos
                    show={show}
                    handleClose={handleClose}
                    search={searchByFilters}
                    reset={reset}
                    handleChangeCodigo={handleChangeCodigo}
                    codigo={codigo}
                    handleChangeNombre={handleChangeNombreProducto}
                    nombre={nombreProducto}
                    setCategoriaid={setCategoriaid}
                    setMarca={setMarcaid}
                />
            }
            <div>
                <header className="titulo-notificaciones">
                    <h3>Reporte: Rentabilidad Productos</h3>
                    <Button
                        variant="outlined"
                        sx={{height:40,width:150}}
                        onClick={handleShow}>
                        <FilterListIcon/> Filtrar
                    </Button>
                </header>
            </div>
            <div style={{display:"flex", flexDirection:"column", justifyItems:"center"}}>
                <TableContainer component={Paper} sx={{marginTop:5,width:"95%", alignSelf:"center", marginLeft:3, marginBottom:5}}>
                    <Table aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">C&oacute;digo</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Marca</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Categor&iacute;a</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Nombre</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Color</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Medida</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Rentabilidad</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Precio costo</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Precio S/IVA</TableCell>
                                <TableCell style={{fontWeight:"bold", fontSize:"90%", padding:2}} align="center">Precio VTA</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                        {   
                            loading ? 
                            <TableRow sx={{p:20}}>
                                <TableCell colSpan={10} align='center'>
                                    <ClipLoader 
                                        loading={loading}
                                        size={50}
                                    />
                                </TableCell>
                            </TableRow> :
                            info.data.length > 0 ? info.data.map((row) => (
                                <TableRow
                                    key={row.productoid}
                                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                    style={{backgroundColor:`${row.color.replace(';','')}`}}
                                >
                                    <TableCell style={{fontSize:"80%", padding:0, height:40, width:130}} align="center">
                                        {row.codigoarticulo}
                                    </TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:150}} align="center">
                                        {row.nombremarca}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.nombrecategoria}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:150}} align="center">
                                        {row.nombre}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.nombrecolor}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:90}} align="center">
                                        {row.nombremedida}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.rentabilidad}</TableCell>
                                     <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.preciocosto}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.preciosiniva}</TableCell>
                                    <TableCell style={{fontSize:"80%", padding:0, height:45, width:80}} align="center">
                                        {row.precioVenta}</TableCell>
                                </TableRow>
                            )):
                            <TableRow>
                                <TableCell colSpan={10} align='center'>
                                    <h5>No hay informaci&oacute;n para mostrar</h5>
                                </TableCell>
                            </TableRow>}

                        </TableBody>
                    </Table>
                </TableContainer>
            </div>
            <Pagination count={paginaUltima} page={page} onChange={handleChangePage} size="large" sx={{alignSelf:"center"}} />
        </div>
    )
}
