import React, { useState, useEffect } from "react";
import Mo<PERSON> from 'react-modal';
import Divider from '@mui/material/Divider';
import { Button, FormControl, InputLabel, MenuItem, Select } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getEstadosPedido, cambiarEstadoRemito } from "../../../redux/actions/mitienda.actions";

Modal.setAppElement("#root");

export const ModalCambiarEstadoRemito = ({ show, handleClose, remito, handleClickAlert }) => {
  const estados = useSelector((state) => state.mitienda.lista_estado_pedido);
  const [estado, setEstado] = useState('');
  const dispatch = useDispatch();

  const customStyles = {
    content: {
      padding: "40px",
      inset: "unset",
      width: "100%",
      maxHeight: "90vh",
      borderRadius: "8px",
      maxWidth: "650px",
    },
    overlay: {
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "grid",
      placeItems: "center",
      zIndex: "100000",
    },
  };

  const handleChange = () => {
    dispatch(cambiarEstadoRemito(remito[0].facturaid, estado))
      .then((response) => {
        handleClose();
        setTimeout(function(){
          handleClickAlert();
        }, 2000);
      })
      .catch(error => {
        console.error("Error al cambiar estado del remito:", error);
        handleClose();
        // Still trigger the alert for error cases
        setTimeout(function(){
          handleClickAlert();
        }, 2000);
      });
  };
  
  useEffect(() => {
    dispatch(getEstadosPedido());
  }, [dispatch]);

  useEffect(() => {
    if(remito && remito[0]){
      // Use the correct field from remito
      setEstado(remito[0].estadoid || '');
    }
  }, [remito]);

  return (
    show && 
    <Modal isOpen={show} style={customStyles} onRequestClose={handleClose}>
      <div style={{display:"flex", justifyContent:"space-between", marginBottom:10}}>
        <h4>Cambiar estado del remito</h4>
        <button onClick={handleClose} style={{all:"unset", color:"black", paddingBottom:10, cursor:"pointer"}}>X</button>
      </div>
      <Divider/>
      <div style={{margin:15}}>   
        <FormControl style={{marginTop:20}} fullWidth>
          <InputLabel id="estado">Estado</InputLabel>
          <Select
            labelId="estadolabel"
            id="estado"
            label="Estado"  
            size="small"
            name="estado"
            value={estado}
            onChange={e => setEstado(e.target.value)}
            MenuProps={{ keepMounted: true, disablePortal: true }}
          >
            <MenuItem value={0}>Seleccione una opción</MenuItem>
            {
              estados && estados.map((m) => 
                <MenuItem value={m.pedidoplacaestadoid} key={m.orden}>{m.nombre.toUpperCase()}</MenuItem>
              )
            }
          </Select>
        </FormControl>
      </div>
      <Divider/>
      <div align="right">
        <Button 
          size="large"
          variant="contained"
          onClick={handleChange}
          sx={{mt: 3, mr:1}}
        >
          Cambiar
        </Button>
      </div>
    </Modal>
  );
};
