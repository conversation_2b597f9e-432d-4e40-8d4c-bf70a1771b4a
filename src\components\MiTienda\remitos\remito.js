import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  CircularProgress,
  <PERSON>vider,
  IconButton,
  Snackbar,
  Tooltip,
  Typography,
  TextField,
} from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import "../ventas/ventas.css";
import { useDispatch } from "react-redux";
import {
  editarFechaEntrega,
  getRemito,
  getDatosTienda,
  cambiarEstadoRemito,
  editarFechaEntregaRemito,
} from "../../../redux/actions/mitienda.actions";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";
import MuiAlert from "@mui/material/Alert";
import { Edit } from "@mui/icons-material";
import { ModalCambiarEstadoRemito } from "./modalCambiarEstadoRemito";

export const Remito = (props) => {
  const id = props.match.params.id;
  const clienteid = props.match.params.clienteid;

  const data = useSelector((state) => state.mitienda.remito);
  // console.log('data remito', data);
  const direcciontienda = useSelector((state) => state.mitienda.datostienda);
  const okFechaEntrega = useSelector((state) => state.mitienda.okFechaEntrega);

  const dispatch = useDispatch();

  const [remito, setRemito] = useState("");
  // console.log('remito', remito);
  const [isLoading, setIsLoading] = useState(true);
  const [showEstadoModal, setShowEstadoModal] = useState(false);
  const handleCloseEstadoModal = () => setShowEstadoModal(false);
  const handleShowEstadoModal = () => setShowEstadoModal(true);

  const [isVendedor, setIsVendedor] = useState(false);

  useEffect(() => {
    const perfiles = localStorage.getItem("perfiles")
      ? JSON.parse(localStorage.getItem("perfiles"))
      : {};
    const profileValues = Object.values(perfiles);
    setIsVendedor(profileValues.includes(18) && !profileValues.includes(7));
  }, []);

  // Add alert for estado changes
  const [openAlertEstado, setOpenAlertEstado] = useState(false);
  const handleClickAlertEstado = () => {
    setOpenAlertEstado(true);
  };
  const handleCloseAlertEstado = () => {
    setOpenAlertEstado(false);
  };

  // Alert setup
  const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
  });

  const [openAlertFechaEntrega, setOpenAlertFechaEntrega] = useState(false);
  const handleClickAlertFechaEntrega = () => {
    setOpenAlertFechaEntrega(true);
  };
  const handleCloseAlertFechaEntrega = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setOpenAlertFechaEntrega(false);
  };

  // Add a state to track state changes
  const [currentEstado, setCurrentEstado] = useState("");
  const estadoRemito = useSelector(
    (state) => state.mitienda.cambio_estado_remito
  );
  const fechaEntregaResponse = useSelector(
    (state) => state.mitienda.editar_fecha_entrega_remito
  );

  // console.log('fechaEntregaResponse', fechaEntregaResponse);

  // Add this state to store the selected date
  const [fechaCompromiso, setFechaCompromiso] = useState("");
  // console.log('fechaCompromiso', fechaCompromiso);

  // Change the handler to only update the state
  const handleFechaCompromisoChange = (date) => {
    setFechaCompromiso(date);
  };

  // Simplify the save function
  const saveFechaCompromiso = async () => {
    try {
      if (!remito || !Array.isArray(remito) || !remito[0]) {
        throw new Error("Datos del remito no disponibles");
      }

      const facturaId = remito[0].facturaid;

      // console.log("Sending date to API:", fechaCompromiso);

      // Call the API to update the date
      await dispatch(editarFechaEntregaRemito(facturaId, fechaCompromiso));

      // After successful API call, force refresh the data
      await dispatch(getRemito(id, clienteid));

      handleClickAlertFechaEntrega();
    } catch (error) {
      console.error("Error al guardar fecha de compromiso:", error);
      handleClickAlertFechaEntrega();
    }
  };

  // Completely separate the data loading from the date state
  useEffect(() => {
    dispatch(getRemito(id, clienteid));
    dispatch(getDatosTienda());
  }, [dispatch, id, clienteid]);

  // Set initial date when remito data is first loaded
  useEffect(() => {
    if (remito && remito[0]) {
      // Use fechaentrega if available, otherwise fall back to fecha_compromiso_entrega
      if (remito[0].fechaentrega) {
        // console.log("Setting initial date from remito[0].fechaentrega:", remito[0].fechaentrega.substring(0, 10));
        setFechaCompromiso(remito[0].fechaentrega.substring(0, 10));
      } else if (remito[0].fecha_compromiso_entrega) {
        // console.log("Setting initial date from remito[0].fecha_compromiso_entrega:", remito[0].fecha_compromiso_entrega.substring(0, 10));
        setFechaCompromiso(remito[0].fecha_compromiso_entrega.substring(0, 10));
      } else {
        // Default to today's date if no date exists
        const today = new Date();
        const formattedDate = today.toISOString().split("T")[0];
        // console.log("Setting default date:", formattedDate);
        setFechaCompromiso(formattedDate);
      }
    }
  }, [remito]);

  // Add debugging to see the structure of the remito data
  useEffect(() => {
    if (remito && remito[0]) {
      // console.log("Remito structure:", remito[0]);
      // console.log("fechaentrega value:", remito[0].fechaentrega);
    }
  }, [remito]);

  // Add this useEffect to update currentEstado when cambio_estado_remito changes
  useEffect(() => {
    if (estadoRemito && estadoRemito.success) {
      // console.log("Estado changed successfully, refreshing data");
      // Refresh the remito data to get the updated state
      dispatch(getRemito(id, clienteid));
    }
  }, [estadoRemito, dispatch, id, clienteid]);

  // Update remito state when data changes
  useEffect(() => {
    if (!data.loading) {
      if (data.data && data.data.length > 0) {
        // console.log("Received new remito data:", data.data);
        // console.log("fechaentrega in new data:", data.data[0].fechaentrega);
        setRemito(data.data);
        // Update currentEstado whenever remito data changes
        setCurrentEstado(data.data[0].descripcionestado || "Sin estado");
        setIsLoading(false);
      }
    }
  }, [data]);

  // Add this to force update after a successful date change
  useEffect(() => {
    if (fechaEntregaResponse && fechaEntregaResponse.success) {
      // console.log("Date change successful, refreshing data");
      dispatch(getRemito(id, clienteid));
    }
  }, [fechaEntregaResponse, dispatch, id, clienteid]);

  // Get the raw data directly from Redux
  const rawRemitoData = useSelector((state) => state.mitienda.remito.rawData);

  // Add this useEffect to log the raw data
  useEffect(() => {
    if (rawRemitoData && rawRemitoData.cabecera) {
      // console.log("Raw remito data from Redux:", rawRemitoData);
      // console.log("fechaentrega from raw data:", rawRemitoData.cabecera.fechaentrega);

      // Update the fechaCompromiso directly from the raw data
      if (rawRemitoData.cabecera.fechaentrega) {
        setFechaCompromiso(
          rawRemitoData.cabecera.fechaentrega.substring(0, 10)
        );
      }
    }
  }, [rawRemitoData]);

  // Convert number helper
  const convertirANumero = (n) => {
    if (!n) return 0;

    // If n is already a number, return it directly
    if (typeof n === "number") return n;

    // If n is not a string, convert it to string
    if (typeof n !== "string") {
      n = String(n);
    }

    let split = n.split(/\.|\,/);
    if (split.length > 2) {
      let aux = split[0] + split[1];
      let aux2 = parseFloat(aux + "." + split[2]);
      return aux2;
    } else if (split.length === 2) {
      let nuevo = parseFloat(split[0] + "." + split[1]);
      return nuevo;
    }
    return parseFloat(n);
  };

  // Add the formatoFecha function
  const formatoFecha = (fecha) => {
    if (fecha === null || fecha === "") {
      return null;
    } else {
      let aux = fecha.slice(0, 10);
      let aux2 = aux.split("-");

      return aux2[2] + "-" + aux2[1] + "-" + aux2[0];
    }
  };

  return (
    <div className="ventas-container">
      {isLoading ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh",
          }}
        >
          <CircularProgress />
        </div>
      ) : remito === "" ? (
        <div style={{ textAlign: "center", marginTop: "2rem" }}>
          <Typography variant="h6">
            No se encontró información del remito
          </Typography>
          <Button
            component={Link}
            to="/MiTienda/Remitos"
            variant="contained"
            startIcon={<ArrowBackIcon />}
            style={{ marginTop: "1rem" }}
          >
            Volver a Remitos
          </Button>
        </div>
      ) : (
        <div>
          <div>
            <ModalCambiarEstadoRemito
              show={showEstadoModal}
              handleClose={handleCloseEstadoModal}
              remito={remito}
              handleClickAlert={handleClickAlertEstado}
            />

            <Snackbar
              open={openAlertEstado}
              autoHideDuration={6000}
              onClose={handleCloseAlertEstado}
              anchorOrigin={{ vertical: "top", horizontal: "center" }}
            >
              <Alert
                onClose={handleCloseAlertEstado}
                severity={
                  estadoRemito && estadoRemito.success ? "success" : "error"
                }
                sx={{ width: 400 }}
              >
                <h5>
                  {estadoRemito && estadoRemito.mensaje
                    ? estadoRemito.mensaje
                    : estadoRemito && !estadoRemito.success
                    ? "Error al actualizar el estado"
                    : "Estado actualizado correctamente"}
                </h5>
              </Alert>
            </Snackbar>

            <Snackbar
              open={openAlertFechaEntrega}
              autoHideDuration={6000}
              onClose={handleCloseAlertFechaEntrega}
              anchorOrigin={{ vertical: "top", horizontal: "center" }}
            >
              <Alert
                onClose={handleCloseAlertFechaEntrega}
                severity={
                  fechaEntregaResponse && fechaEntregaResponse.success
                    ? "success"
                    : "error"
                }
                sx={{ width: 400 }}
              >
                <h5>
                  {fechaEntregaResponse && fechaEntregaResponse.mensaje
                    ? fechaEntregaResponse.mensaje
                    : fechaEntregaResponse && !fechaEntregaResponse.success
                    ? "Error al actualizar la fecha de entrega"
                    : "Fecha de entrega actualizada correctamente"}
                </h5>
              </Alert>
            </Snackbar>
          </div>

          <div style={{ margin: 20 }}>
            <Link
              to="/Mitienda/Remitos"
              style={{ color: "black", display: "inline-block" }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  fontSize: "130%",
                  marginBottom: 20,
                  width: "fit-content",
                }}
              >
                <ArrowBackIcon style={{ fontSize: "130%" }} />
                <span style={{ marginLeft: "5px" }}>Volver a Remitos</span>
              </div>
            </Link>

            <div className="div-titulo">
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  width: "25%",
                }}
              >
                <h3>Remito #{remito && remito[0].facturaid}</h3>
                <h4
                  style={{
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <Button
                      variant="contained"
                      color={
                        remito && remito[0].descripcionestado === "Cancelado"
                          ? "error"
                          : "primary"
                      }
                      style={{ width: 150, justifyContent: "center" }}
                    >
                      {currentEstado}
                    </Button>
                    {/* <div onClick={handleShowEstadoModal} style={{ cursor: "pointer", marginLeft: "5px" }}>
                      <Tooltip title="Editar estado">
                        <IconButton size="large" color="primary">
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    </div> */}
                    <div
                      onClick={!isVendedor ? handleShowEstadoModal : undefined}
                      style={{
                        cursor: isVendedor ? "not-allowed" : "pointer",
                        marginLeft: "5px",
                      }}
                    >
                      <Tooltip
                        title={
                          isVendedor
                            ? "No tiene permisos para editar estado"
                            : "Editar estado"
                        }
                      >
                        <IconButton
                          size="large"
                          color="primary"
                          disabled={isVendedor}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    </div>
                  </div>
                </h4>
              </div>

              <div>
                <Button
                  variant="contained"
                  style={{ width: 150, justifyContent: "center", margin: 5 }}
                >
                  <a
                    href={`${
                      process.env.REACT_APP_SERVER
                    }dompdf/generarcomprobante_pdf.php?tipocomprobante=130&codigocomprobante=${
                      remito[0].facturaid
                    }&tipoaccionpdf=1&tiendaid=${localStorage.getItem(
                      "tiendausuario"
                    )}`}
                    target="_blank"
                    style={{ color: "white" }}
                    rel="noopener noreferrer"
                  >
                    Imprimir
                  </a>
                </Button>
                <Button
                  variant="contained"
                  style={{ width: 150, justifyContent: "center", margin: 5 }}
                >
                  Facturar
                </Button>
              </div>
            </div>
          </div>

          <div className="columnas">
            <div
              style={{
                width: "100%",
                display: "flex",
                flexWrap: "wrap",
                gap: "20px",
              }}
            >
              {/* Top Left Card - Envío */}
              <Paper
                style={{
                  flex: "1 1 calc(50% - 10px)",
                  minWidth: "300px",
                  padding: "20px",
                  minHeight: "200px",
                  alignSelf: "flex-start",
                }}
              >
                <div style={{ display: "flex" }}>
                  <LocalShippingIcon />
                  <h5 style={{ marginLeft: 10 }}>
                    <strong>Envío:</strong>{" "}
                    {(remito && remito[0].tipo_envio) || "Sin asignar"}
                  </h5>
                </div>
                <Divider />

                <div style={{ padding: 10 }}>
                  <h6>
                    <strong>Fecha de compromiso de entrega:</strong>
                  </h6>
                  <TextField
                    name="fechaCompromiso"
                    type="date"
                    fullWidth
                    margin="normal"
                    value={fechaCompromiso}
                    onChange={(e) => {
                      // console.log("Date field changed to:", e.target.value);
                      handleFechaCompromisoChange(e.target.value);
                    }}
                    inputProps={{
                      style: {
                        height: 40,
                        fontSize: 17,
                      },
                    }}
                  />
                  <Button
                    style={{ marginTop: 10, width: "100%" }}
                    variant="contained"
                    color="primary"
                    onClick={saveFechaCompromiso}
                  >
                    Guardar fecha de compromiso
                  </Button>
                </div>
              </Paper>

              {/* Top Right Card - Información del cliente */}
              <Paper
                style={{
                  flex: "1 1 calc(50% - 10px)",
                  minWidth: "300px",
                  padding: "20px",
                  minHeight: "200px",
                  alignSelf: "flex-start",
                }}
              >
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <h4>
                    <strong>Información del cliente</strong>
                  </h4>
                </div>
                <Divider />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    overflow: "hidden",
                    marginTop: 10,
                    padding: "10px 0",
                  }}
                >
                  <h6>
                    <strong>Nombre:</strong>{" "}
                    {remito && remito[0].nombrecliente
                      ? remito[0].nombrecliente
                      : "No especificado"}
                  </h6>
                  <h6>
                    <strong>CUIT:</strong>{" "}
                    {remito && remito[0].cuit
                      ? remito[0].cuit
                      : "No especificado"}
                  </h6>
                  <h6>
                    <strong>Dirección:</strong>{" "}
                    {remito && remito[0].direccion
                      ? remito[0].direccion
                      : "No especificada"}
                  </h6>
                  <h6>
                    <strong>Teléfono:</strong>{" "}
                    {remito && remito[0].telefono
                      ? remito[0].telefono
                      : "No especificado"}
                  </h6>
                  <h6>
                    <strong>Email:</strong>{" "}
                    {remito && remito[0].email
                      ? remito[0].email
                      : "No especificado"}
                  </h6>
                </div>
              </Paper>

              {/* Bottom Left Card - Detalle del Remito */}
              <Paper
                style={{
                  flex: "1 1 calc(50% - 10px)",
                  minWidth: "300px",
                  padding: "20px",
                  minHeight: "300px",
                  alignSelf: "flex-start",
                }}
              >
                <h4>
                  <strong>Detalle del Remito</strong>
                </h4>
                <Divider style={{ marginBottom: "15px" }} />
                <TableContainer style={{ width: "100%" }}>
                  <Table aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell
                          style={{ fontWeight: "bold", fontSize: "100%" }}
                        >
                          Descripción
                        </TableCell>
                        <TableCell
                          style={{ fontWeight: "bold", fontSize: "100%" }}
                          align="center"
                        >
                          Pedido
                        </TableCell>
                        <TableCell
                          style={{ fontWeight: "bold", fontSize: "100%" }}
                          align="center"
                        >
                          Cantidad
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {remito &&
                        remito[0].detalle &&
                        remito[0].detalle.map((row) => (
                          <TableRow
                            key={row.facturadetalleid}
                            sx={{
                              "&:last-child td, &:last-child th": {
                                border: 0,
                              },
                            }}
                          >
                            <TableCell
                              style={{
                                fontSize: "100%",
                                padding: "8px 4px",
                                maxWidth: "70%", // Limit width to prevent wrapping
                                whiteSpace: "normal", // Allow text to wrap within the cell
                                wordBreak: "break-word", // Break long words if needed
                              }}
                            >
                              {row.descripcion}
                            </TableCell>
                            <TableCell
                              style={{ fontSize: "100%", padding: "8px 4px" }}
                              align="center"
                            >
                              {row.pedidoplacaid || "-"}
                            </TableCell>
                            <TableCell
                              style={{ fontSize: "100%", padding: "8px 4px" }}
                              align="center"
                            >
                              {row.cantidad}
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>

              {/* Bottom Right Card - Información del envío */}
              <Paper
                style={{
                  flex: "1 1 calc(50% - 10px)",
                  minWidth: "300px",
                  padding: "20px",
                  minHeight: "300px",
                  alignSelf: "flex-start",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    overflow: "hidden",
                  }}
                >
                  <h4>
                    <strong>Información del envío</strong>
                  </h4>
                  <Divider />
                  <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                    <h6>
                      <strong>Método de envío:</strong>{" "}
                      {(remito && remito[0].tipo_envio) || "Sin asignar"}
                    </h6>
                  </div>
                  <Divider />
                  {remito && remito[0].direccion && (
                    <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                      <h5>Dirección de envío</h5>
                      <h6>Dirección: {remito[0].direccion}</h6>
                    </div>
                  )}
                  <div style={{ paddingTop: 10, paddingBottom: 10 }}>
                    <h5>Información de entrega</h5>
                    <h6>
                      Fecha compromiso:{" "}
                      {remito && remito[0].fecha_compromiso_entrega
                        ? formatoFecha(remito[0].fecha_compromiso_entrega)
                        : "Sin asignar"}
                    </h6>
                  </div>
                </div>
              </Paper>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
