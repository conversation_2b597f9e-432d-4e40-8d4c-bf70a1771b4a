import { Button, Checkbox, FormControlLabel, MenuItem, Paper, Select, Snackbar, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import './styles.css'
import { useDispatch, useSelector } from "react-redux";
import { registrarUsuario } from "../../redux/actions/user.actions";
import MuiAlert from '@mui/material/Alert';
import { getDescuentosSinPaginado } from "../../redux/actions/mitienda.actions";
import { Link } from 'react-router-dom';
import { ArrowBack } from "@mui/icons-material";

export const CrearUsuario = () =>{

    const ok = useSelector((state) => state.user.registrar_usuario)
    const descuentos = useSelector((state) => state.mitienda.descuentosSinPaginado)

    const [input, setInput] = useState({
        nombre:'',
        apellido:'',
        usuario:'',
        email:'',
        domicilio: '',
        telefono: '',
        descuento_producto: "0",
        descuentoid: 0,
        comisionvta: 0,
        activo: "1"
    })

    const [error, setError] = useState(false)
    
    const handleChange = (e) => {
        e.preventDefault()
        if(e.target.name === "telefono"){
            let aux = isNaN(e.target.value)
            setError(aux)
        }
        setInput({
            ...input,
            [e.target.name]: e.target.value
        })
    }

    const handleCheck = (e) =>{
        e.preventDefault()
        if(e.target.checked === true){
            setInput({...input, [e.target.name] : "1"})
        }else{
            setInput({...input, [e.target.name] : "0"})
        }
    }

    const dispatch = useDispatch()

    const handleClick = () => {
        dispatch(registrarUsuario(input))
        setTimeout(function(){
            handleClickAlert()
        }, 1000);
        setInput({
            nombre:'',
            apellido:'',
            usuario:'',
            email:'',
            domicilio: '',
            telefono: '',
            descuento_producto: "0",
            descuentoid: 0,
            comisionvta: 0,
            activo: "1"
        })
    }

    const Alert = React.forwardRef(function Alert(props, ref) {
        return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
    });

    const [open, setOpen] = React.useState(false);
    const handleClickAlert = () => {
      setOpen(true);
    };
    const handleCloseAlert = () => {
        setOpen(false);
    };

    useEffect(()=> {
        dispatch(getDescuentosSinPaginado())
    },[])

    return (
        <div className="container-crear-usuario">
            {
                <Snackbar
                    open={open} 
                    autoHideDuration={10000} onClose={handleCloseAlert}
                    anchorOrigin={
                        {vertical: 'top',
                        horizontal: 'center',}
                    }>
                    <Alert onClose={handleCloseAlert} 
                        severity={ok.success ? "success" : "error"}
                        sx={{ width: 400 }}>
                        <h5>{ok.mensaje}</h5>
                    </Alert>
                </Snackbar>
            }
            <Paper style={{width:"65%", minHeight:"80vh", height:"80%", margin:20, padding:30, display:"flex", justifyContent:"center", flexDirection:"column"}}>
                <div className="titulo-registrar">Crear usuario</div>
                <div className="form-paper">
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Usuario</h4>
                        <TextField
                            fullWidth
                            name="usuario"
                            value={input.usuario}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Nombre</h4>
                        <TextField
                            fullWidth
                            name="nombre"
                            value={input.nombre}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Apellido</h4>
                        <TextField
                            fullWidth
                            name="apellido"
                            value={input.apellido}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Email</h4>
                        <TextField
                            fullWidth
                            name="email"
                            value={input.email}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Tel&eacute;fono</h4>
                        <TextField
                            fullWidth
                            type="tel"
                            name="telefono"
                            error={error === true}
                            helperText={error === true && "Debe ser un numero"}
                            value={input.telefono}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Domicilio</h4>
                        <TextField
                            fullWidth
                            name="domicilio"
                            value={input.domicilio}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>
                    <div style={{display:"flex", width:400, margin:10, marginTop:35, alignItems:"center"}}>
                        <h4>Usuario activo</h4>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black", marginLeft:1}}
                            onChange={(e)=> handleCheck(e)}
                            checked={input.activo === "1" ? true : false}
                            name="activo"
                        />
                    </div>
                    <div style={{display:"flex", width:400, margin:10, marginTop:35, alignItems:"center"}}>
                        <h4>Aplica descuento producto</h4>
                        <FormControlLabel
                            control={
                            <Checkbox/>
                            }
                            style={{color:"black", marginLeft:1}}
                            onChange={(e)=> handleCheck(e)}
                            checked={input.descuento_producto === "1" ? true : false}
                            name="descuento_producto"
                        />
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Descuento</h4>
                        <Select
                            size="small"
                            name="descuentoid"
                            value={input.descuentoid || ''}
                            onChange={handleChange}
                            style={{height:35}}
                            >
                            <MenuItem value={0}>Seleccione una opcion</MenuItem>
                            {               
                                descuentos && descuentos.map((t) =>
                                    <MenuItem value={t.descuentovtaid} key={t.descuentovtaid}>{t.nombre}</MenuItem>
                                )
                            }
                        </Select>
                    </div>
                    <div style={{display:"flex", flexDirection:"column", width:400, margin:10}}>
                        <h4>Comisi&oacute;n</h4>
                        <TextField
                            type="number"
                            fullWidth
                            name="comisionvta"
                            value={input.comisionvta}
                            onChange={handleChange}
                            InputLabelProps={{
                                style: {
                                    marginBottom:10,
                                    marginTop: -7
                                },
                            }}
                            inputProps={{
                                style: {
                                    height: 13,
                                    fontSize:17
                                },
                            }}
                        />
                    </div>

                </div>
                <Button 
                    disabled={
                        input.usuario === '' ||
                        input.nombre === '' ||
                        input.apellido === '' ||
                        input.telefono === '' ||
                        input.email === ''
                    }
                    onClick={handleClick}
                    className="button-registrar" 
                    sx={{mt:5}}
                ><div className="texto-button">Crear usuario</div></Button>
            </Paper>
        </div>
    )
}