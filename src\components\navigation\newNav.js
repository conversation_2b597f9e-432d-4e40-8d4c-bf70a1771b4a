import React, {useEffect} from "react";
import { useDispatch, useSelector } from "react-redux";
import {Link} from "react-router-dom"

import { getMenu, getMenutoken } from "../../redux/actions/user.actions";
import { sort } from "./utils";
import './navigator.css'
import {useTranslation} from "react-i18next"
import { getLogo } from "../../redux/actions/mitienda.actions";
import logoControl from "../../media/lomasdeezeiza.png"
import logoDevelshops from '../../media/logo_develshops.png'

import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import AccountCircle from '@mui/icons-material/AccountCircle';
import MenuItem from '@mui/material/MenuItem';
import Menu from '@mui/material/Menu';

export const NewNav= () => {

    const [t, i18n] = useTranslation("global")

    const info = useSelector((state) => state.user.menu)
    let lista = sort(info)
    const dispatch = useDispatch()
    const logo = useSelector((state) => state.mitienda.logo)

    const cerrar = () => {
        localStorage.clear();
        window.location.href = '/';
    }
    let id = 0

    let a = localStorage.getItem('nombreusuario')

    const match = (a) => {
        if(a < 10){
            return "0"+a
        }else{
            return a
        }
    }

    let hora = (new Date().getHours())+":"+(match(new Date().getMinutes()))

    
    let tiendausuario = localStorage.getItem('tiendausuario')

    useEffect(()=>{
        let isMounted = true;
        if(isMounted){
            if(process.env.REACT_APP_USA_TOKEN === "true"){
                dispatch(getMenutoken())
            }
            if(process.env.REACT_APP_USA_TOKEN === "false"){
                dispatch(getMenu())
                dispatch(getLogo())
            }
        }
        return () => {
            isMounted = false
        }
    }, [])

    const [auth, setAuth] = React.useState(true);
    const [anchorEl, setAnchorEl] = React.useState(null);

    const handleChange = (event) => {
        setAuth(event.target.checked);
    };

    const handleMenu = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return(
        <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static" style={{backgroundColor:"black"}}>
          <Toolbar style={{display:"flex", justifyContent:"space-between"}}>
            <IconButton
              size="large"
              edge="start"
              color="inherit"
              aria-label="menu"
              sx={{ mr: 2 }}
            >
            <Link to="/Home">
            {
              logo === "" ?
              <img style={{width:"100px", height:"70px", padding:0}} src={logoDevelshops}/> :
              <img 
              style={{width:50}}
              src={process.env.REACT_APP_USA_TOKEN === "true" ? logoControl : process.env.REACT_APP_SERVER+"archivos/logos/cart/"+tiendausuario+"/"+logo} alt="develone"/>
            }
            </Link>
            </IconButton>
              <div>
                <IconButton
                  size="large"
                  aria-label="account of current user"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleMenu}
                  color="inherit"
                >
                  <AccountCircle fontSize="large"/>
                </IconButton>
                <Menu
                  id="menu-appbar"
                  anchorEl={anchorEl}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                  keepMounted
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  open={Boolean(anchorEl)}
                  onClose={handleClose}
                >
                  <MenuItem>Hola, {a}! <br/> Hora actual {hora}</MenuItem>
                  <MenuItem onClick={handleClose}><a href="/EditarUsuario">Editar usuario</a></MenuItem>
                  <MenuItem onClick={handleClose}><a href="/CrearUsuario">Crear usuario</a></MenuItem>
                  <MenuItem><a href="#" onClick={cerrar}>{t('menu.log-out')}</a></MenuItem>
                </Menu>
              </div>
          </Toolbar>
        </AppBar>
      </Box>
    )
};
