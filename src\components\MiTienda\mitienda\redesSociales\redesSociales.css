.container-redes {
    width: 100%;
    height: 110vh;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.form-redes{
    background-color: white;
    height: auto;
    width: 95%;
    padding: 20px;
    padding-top: 20px;
    align-self: center;
    border-radius:10px;
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 10px;
}

.header-redes{
    width: 95%;
    margin-left: 10px;
}

@media only screen and (max-width: 1200px) {
    .container-redes{
        width: 100%;
        height: 110vh;
        background-color: #EEEEEE;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}